{"version": 3, "sources": ["../../svelte-toolbelt/dist/utils/compose-handlers.js", "../../svelte-toolbelt/dist/utils/strings.js", "../../svelte-toolbelt/dist/utils/css-to-style-obj.js", "../../svelte-toolbelt/dist/utils/is.js", "../../svelte-toolbelt/dist/utils/execute-callbacks.js", "../../svelte-toolbelt/dist/utils/style-to-css.js", "../../svelte-toolbelt/dist/utils/style.js", "../../svelte-toolbelt/dist/utils/merge-props.js", "../../svelte-toolbelt/dist/box/box.svelte.js", "../../runed/dist/utilities/watch/watch.svelte.js", "../../runed/dist/utilities/previous/previous.svelte.js", "../../runed/dist/internal/configurable-globals.js", "../../runed/dist/internal/utils/dom.js", "../../runed/dist/utilities/active-element/active-element.svelte.js", "../../runed/dist/internal/utils/is.js", "../../runed/dist/utilities/use-debounce/use-debounce.svelte.js", "../../runed/dist/internal/utils/get.js", "../../runed/dist/utilities/element-size/element-size.svelte.js", "../../runed/dist/utilities/is-mounted/is-mounted.svelte.js", "../../runed/dist/utilities/context/context.js", "../../svelte-toolbelt/dist/utils/on-destroy-effect.svelte.js", "../../svelte-toolbelt/dist/utils/use-ref-by-id.svelte.js", "../../svelte-toolbelt/dist/utils/after-sleep.js", "../../svelte-toolbelt/dist/utils/after-tick.js", "../../svelte-toolbelt/dist/utils/on-mount-effect.svelte.js"], "sourcesContent": ["/**\n * Composes event handlers into a single function that can be called with an event.\n * If the previous handler cancels the event using `event.preventDefault()`, the handlers\n * that follow will not be called.\n */\nexport function composeHandlers(...handlers) {\n    return function (e) {\n        for (const handler of handlers) {\n            if (!handler)\n                continue;\n            if (e.defaultPrevented)\n                return;\n            if (typeof handler === \"function\") {\n                handler.call(this, e);\n            }\n            else {\n                handler.current?.call(this, e);\n            }\n        }\n    };\n}\n", "const NUMBER_CHAR_RE = /\\d/;\nconst STR_SPLITTERS = [\"-\", \"_\", \"/\", \".\"];\nfunction isUppercase(char = \"\") {\n    if (NUMBER_CHAR_RE.test(char))\n        return undefined;\n    return char !== char.toLowerCase();\n}\nfunction splitByCase(str) {\n    const parts = [];\n    let buff = \"\";\n    let previousUpper;\n    let previousSplitter;\n    for (const char of str) {\n        // Splitter\n        const isSplitter = STR_SPLITTERS.includes(char);\n        if (isSplitter === true) {\n            parts.push(buff);\n            buff = \"\";\n            previousUpper = undefined;\n            continue;\n        }\n        const isUpper = isUppercase(char);\n        if (previousSplitter === false) {\n            // Case rising edge\n            if (previousUpper === false && isUpper === true) {\n                parts.push(buff);\n                buff = char;\n                previousUpper = isUpper;\n                continue;\n            }\n            // Case falling edge\n            if (previousUpper === true && isUpper === false && buff.length > 1) {\n                const lastChar = buff.at(-1);\n                parts.push(buff.slice(0, Math.max(0, buff.length - 1)));\n                buff = lastChar + char;\n                previousUpper = isUpper;\n                continue;\n            }\n        }\n        // Normal char\n        buff += char;\n        previousUpper = isUpper;\n        previousSplitter = isSplitter;\n    }\n    parts.push(buff);\n    return parts;\n}\nexport function pascalCase(str) {\n    if (!str)\n        return \"\";\n    return splitByCase(str)\n        .map((p) => upperFirst(p))\n        .join(\"\");\n}\nexport function camelCase(str) {\n    return lowerFirst(pascalCase(str || \"\"));\n}\nexport function kebabCase(str) {\n    return str\n        ? splitByCase(str)\n            .map((p) => p.toLowerCase())\n            .join(\"-\")\n        : \"\";\n}\nfunction upperFirst(str) {\n    return str ? str[0].toUpperCase() + str.slice(1) : \"\";\n}\nfunction lowerFirst(str) {\n    return str ? str[0].toLowerCase() + str.slice(1) : \"\";\n}\n", "import parse from \"style-to-object\";\nimport { camelCase, pascalCase } from \"./strings.js\";\nexport function cssToStyleObj(css) {\n    if (!css)\n        return {};\n    const styleObj = {};\n    function iterator(name, value) {\n        if (name.startsWith(\"-moz-\") ||\n            name.startsWith(\"-webkit-\") ||\n            name.startsWith(\"-ms-\") ||\n            name.startsWith(\"-o-\")) {\n            styleObj[pascalCase(name)] = value;\n            return;\n        }\n        if (name.startsWith(\"--\")) {\n            styleObj[name] = value;\n            return;\n        }\n        styleObj[camelCase(name)] = value;\n    }\n    parse(css, iterator);\n    return styleObj;\n}\n", "export function isFunction(value) {\n    return typeof value === \"function\";\n}\nexport function isObject(value) {\n    return value !== null && typeof value === \"object\";\n}\nconst CLASS_VALUE_PRIMITIVE_TYPES = [\"string\", \"number\", \"bigint\", \"boolean\"];\nexport function isClassValue(value) {\n    // handle primitive types\n    if (value === null || value === undefined)\n        return true;\n    if (CLASS_VALUE_PRIMITIVE_TYPES.includes(typeof value))\n        return true;\n    // handle arrays (ClassArray)\n    if (Array.isArray(value))\n        return value.every((item) => isClassValue(item));\n    // handle objects (ClassDictionary)\n    if (typeof value === \"object\") {\n        // ensure it's a plain object and not some other object type\n        if (Object.getPrototypeOf(value) !== Object.prototype)\n            return false;\n        return true;\n    }\n    return false;\n}\nconst ELEMENT_NODE = 1;\nconst DOCUMENT_NODE = 9;\nconst DOCUMENT_FRAGMENT_NODE = 11;\nexport function isHTMLElement(v) {\n    return isObject(v) && v.nodeType === ELEMENT_NODE && typeof v.nodeName === \"string\";\n}\nexport function isDocument(v) {\n    return isObject(v) && v.nodeType === DOCUMENT_NODE;\n}\nexport function isWindow(v) {\n    return isObject(v) && v === v.window;\n}\nexport function getNodeName(node) {\n    if (isHTMLElement(node))\n        return node.localName || \"\";\n    return \"#document\";\n}\nexport function isRootElement(node) {\n    return [\"html\", \"body\", \"#document\"].includes(getNodeName(node));\n}\nexport function isNode(v) {\n    return isObject(v) && v.nodeType !== undefined;\n}\nexport function isShadowRoot(v) {\n    return isNode(v) && v.nodeType === DOCUMENT_FRAGMENT_NODE && \"host\" in v;\n}\n", "/**\n * Executes an array of callback functions with the same arguments.\n * @template T The types of the arguments that the callback functions take.\n * @param callbacks array of callback functions to execute.\n * @returns A new function that executes all of the original callback functions with the same arguments.\n */\nexport function executeCallbacks(...callbacks) {\n    return (...args) => {\n        for (const callback of callbacks) {\n            if (typeof callback === \"function\") {\n                callback(...args);\n            }\n        }\n    };\n}\n", "function createParser(matcher, replacer) {\n    const regex = RegExp(matcher, \"g\");\n    return (str) => {\n        // throw an error if not a string\n        if (typeof str !== \"string\") {\n            throw new TypeError(`expected an argument of type string, but got ${typeof str}`);\n        }\n        // if no match between string and matcher\n        if (!str.match(regex))\n            return str;\n        // executes the replacer function for each match\n        return str.replace(regex, replacer);\n    };\n}\nconst camelToKebab = createParser(/[A-Z]/, (match) => `-${match.toLowerCase()}`);\nexport function styleToCSS(styleObj) {\n    if (!styleObj || typeof styleObj !== \"object\" || Array.isArray(styleObj)) {\n        throw new TypeError(`expected an argument of type object, but got ${typeof styleObj}`);\n    }\n    return Object.keys(styleObj)\n        .map((property) => `${camelToKebab(property)}: ${styleObj[property]};`)\n        .join(\"\\n\");\n}\n", "import { styleToCSS } from \"./style-to-css.js\";\nexport function styleToString(style = {}) {\n    return styleToCSS(style).replace(\"\\n\", \" \");\n}\nexport const srOnlyStyles = {\n    position: \"absolute\",\n    width: \"1px\",\n    height: \"1px\",\n    padding: \"0\",\n    margin: \"-1px\",\n    overflow: \"hidden\",\n    clip: \"rect(0, 0, 0, 0)\",\n    whiteSpace: \"nowrap\",\n    borderWidth: \"0\",\n    transform: \"translateX(-100%)\"\n};\nexport const srOnlyStylesString = styleToString(srOnlyStyles);\n", "/**\n * Modified from https://github.com/adobe/react-spectrum/blob/main/packages/%40react-aria/utils/src/mergeProps.ts (see NOTICE.txt for source)\n */\nimport { clsx } from \"clsx\";\nimport { composeHandlers } from \"./compose-handlers.js\";\nimport { cssToStyleObj } from \"./css-to-style-obj.js\";\nimport { isClassValue } from \"./is.js\";\nimport { executeCallbacks } from \"./execute-callbacks.js\";\nimport { styleToString } from \"./style.js\";\nfunction isEventHandler(key) {\n    // we check if the 3rd character is uppercase to avoid merging our own\n    // custom callbacks like `onValueChange` and strictly merge native event handlers\n    return key.length > 2 && key.startsWith(\"on\") && key[2] === key[2]?.toLowerCase();\n}\n/**\n * Given a list of prop objects, merges them into a single object.\n * - Automatically composes event handlers (e.g. `onclick`, `oninput`, etc.)\n * - Chains regular functions with the same name so they are called in order\n * - Merges class strings with `clsx`\n * - Merges style objects and converts them to strings\n * - Handles a bug with Svel<PERSON> where setting the `hidden` attribute to `false` doesn't remove it\n * - Overrides other values with the last one\n */\nexport function mergeProps(...args) {\n    const result = { ...args[0] };\n    for (let i = 1; i < args.length; i++) {\n        const props = args[i];\n        for (const key in props) {\n            const a = result[key];\n            const b = props[key];\n            const aIsFunction = typeof a === \"function\";\n            const bIsFunction = typeof b === \"function\";\n            // compose event handlers\n            if (aIsFunction && typeof bIsFunction && isEventHandler(key)) {\n                // handle merging of event handlers\n                const aHandler = a;\n                const bHandler = b;\n                result[key] = composeHandlers(aHandler, bHandler);\n            }\n            else if (aIsFunction && bIsFunction) {\n                // chain non-event handler functions\n                result[key] = executeCallbacks(a, b);\n            }\n            else if (key === \"class\") {\n                // handle merging acceptable class values from clsx\n                const aIsClassValue = isClassValue(a);\n                const bIsClassValue = isClassValue(b);\n                if (aIsClassValue && bIsClassValue) {\n                    result[key] = clsx(a, b);\n                }\n                else if (aIsClassValue) {\n                    result[key] = clsx(a);\n                }\n                else if (bIsClassValue) {\n                    result[key] = clsx(b);\n                }\n            }\n            else if (key === \"style\") {\n                const aIsObject = typeof a === \"object\";\n                const bIsObject = typeof b === \"object\";\n                const aIsString = typeof a === \"string\";\n                const bIsString = typeof b === \"string\";\n                if (aIsObject && bIsObject) {\n                    // both are style objects, merge them\n                    result[key] = { ...a, ...b };\n                }\n                else if (aIsObject && bIsString) {\n                    // a is style object, b is string, convert b to style object and merge\n                    const parsedStyle = cssToStyleObj(b);\n                    result[key] = { ...a, ...parsedStyle };\n                }\n                else if (aIsString && bIsObject) {\n                    // a is string, b is style object, convert a to style object and merge\n                    const parsedStyle = cssToStyleObj(a);\n                    result[key] = { ...parsedStyle, ...b };\n                }\n                else if (aIsString && bIsString) {\n                    // both are strings, convert both to objects and merge\n                    const parsedStyleA = cssToStyleObj(a);\n                    const parsedStyleB = cssToStyleObj(b);\n                    result[key] = { ...parsedStyleA, ...parsedStyleB };\n                }\n                else if (aIsObject) {\n                    result[key] = a;\n                }\n                else if (bIsObject) {\n                    result[key] = b;\n                }\n                else if (aIsString) {\n                    result[key] = a;\n                }\n                else if (bIsString) {\n                    result[key] = b;\n                }\n            }\n            else {\n                // override other values\n                result[key] = b !== undefined ? b : a;\n            }\n        }\n    }\n    // convert style object to string\n    if (typeof result.style === \"object\") {\n        result.style = styleToString(result.style).replaceAll(\"\\n\", \" \");\n    }\n    // handle weird svelte bug where `hidden` is not removed when set to `false`\n    if (result.hidden !== true) {\n        result.hidden = undefined;\n        delete result.hidden;\n    }\n    // handle weird svelte bug where `disabled` is not removed when set to `false`\n    if (result.disabled !== true) {\n        result.disabled = undefined;\n        delete result.disabled;\n    }\n    return result;\n}\n", "import { isFunction, isObject } from \"../utils/is.js\";\nconst BoxSymbol = Symbol(\"box\");\nconst isWritableSymbol = Symbol(\"is-writable\");\n/**\n * @returns Whether the value is a Box\n *\n * @see {@link https://runed.dev/docs/functions/box}\n */\nfunction isBox(value) {\n    return isObject(value) && BoxSymbol in value;\n}\n/**\n * @returns Whether the value is a WritableBox\n *\n * @see {@link https://runed.dev/docs/functions/box}\n */\nfunction isWritableBox(value) {\n    return box.isBox(value) && isWritableSymbol in value;\n}\nexport function box(initialValue) {\n    let current = $state(initialValue);\n    return {\n        [BoxSymbol]: true,\n        [isWritableSymbol]: true,\n        get current() {\n            return current;\n        },\n        set current(v) {\n            current = v;\n        }\n    };\n}\nfunction boxWith(getter, setter) {\n    const derived = $derived.by(getter);\n    if (setter) {\n        return {\n            [BoxSymbol]: true,\n            [isWritableSymbol]: true,\n            get current() {\n                return derived;\n            },\n            set current(v) {\n                setter(v);\n            }\n        };\n    }\n    return {\n        [BoxSymbol]: true,\n        get current() {\n            return getter();\n        }\n    };\n}\nfunction boxFrom(value) {\n    if (box.isBox(value))\n        return value;\n    if (isFunction(value))\n        return box.with(value);\n    return box(value);\n}\n/**\n * Function that gets an object of boxes, and returns an object of reactive values\n *\n * @example\n * const count = box(0)\n * const flat = box.flatten({ count, double: box.with(() => count.current) })\n * // type of flat is { count: number, readonly double: number }\n *\n * @see {@link https://runed.dev/docs/functions/box}\n */\nfunction boxFlatten(boxes) {\n    return Object.entries(boxes).reduce((acc, [key, b]) => {\n        if (!box.isBox(b)) {\n            return Object.assign(acc, { [key]: b });\n        }\n        if (box.isWritableBox(b)) {\n            Object.defineProperty(acc, key, {\n                get() {\n                    return b.current;\n                },\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                set(v) {\n                    b.current = v;\n                }\n            });\n        }\n        else {\n            Object.defineProperty(acc, key, {\n                get() {\n                    return b.current;\n                }\n            });\n        }\n        return acc;\n    }, {});\n}\n/**\n * Function that converts a box to a readonly box.\n *\n * @example\n * const count = box(0) // WritableBox<number>\n * const countReadonly = box.readonly(count) // ReadableBox<number>\n *\n * @see {@link https://runed.dev/docs/functions/box}\n */\nfunction toReadonlyBox(b) {\n    if (!box.isWritableBox(b))\n        return b;\n    return {\n        [BoxSymbol]: true,\n        get current() {\n            return b.current;\n        }\n    };\n}\nbox.from = boxFrom;\nbox.with = boxWith;\nbox.flatten = boxFlatten;\nbox.readonly = toReadonlyBox;\nbox.isBox = isBox;\nbox.isWritableBox = isWritableBox;\n", "import { untrack } from \"svelte\";\nfunction runEffect(flush, effect) {\n    switch (flush) {\n        case \"post\":\n            $effect(effect);\n            break;\n        case \"pre\":\n            $effect.pre(effect);\n            break;\n    }\n}\nfunction runWatcher(sources, flush, effect, options = {}) {\n    const { lazy = false } = options;\n    // Run the effect immediately if `lazy` is `false`.\n    let active = !lazy;\n    // On the first run, if the dependencies are an array, pass an empty array\n    // to the previous value instead of `undefined` to allow destructuring.\n    //\n    // watch(() => [a, b], ([a, b], [prevA, prevB]) => { ... });\n    let previousValues = Array.isArray(sources)\n        ? []\n        : undefined;\n    runEffect(flush, () => {\n        const values = Array.isArray(sources) ? sources.map((source) => source()) : sources();\n        if (!active) {\n            active = true;\n            previousValues = values;\n            return;\n        }\n        const cleanup = untrack(() => effect(values, previousValues));\n        previousValues = values;\n        return cleanup;\n    });\n}\nfunction runWatcherOnce(sources, flush, effect) {\n    const cleanupRoot = $effect.root(() => {\n        let stop = false;\n        runWatcher(sources, flush, (values, previousValues) => {\n            if (stop) {\n                cleanupRoot();\n                return;\n            }\n            // Since `lazy` is `true`, `previousValues` is always defined.\n            const cleanup = effect(values, previousValues);\n            stop = true;\n            return cleanup;\n        }, \n        // Running the effect immediately just once makes no sense at all.\n        // That's just `onMount` with extra steps.\n        { lazy: true });\n    });\n    $effect(() => {\n        return cleanupRoot;\n    });\n}\nexport function watch(sources, effect, options) {\n    runWatcher(sources, \"post\", effect, options);\n}\nfunction watchPre(sources, effect, options) {\n    runWatcher(sources, \"pre\", effect, options);\n}\nwatch.pre = watchPre;\nexport function watchOnce(source, effect) {\n    runWatcherOnce(source, \"post\", effect);\n}\nfunction watchOncePre(source, effect) {\n    runWatcherOnce(source, \"pre\", effect);\n}\nwatchOnce.pre = watchOncePre;\n", "/**\n * Holds the previous value of a getter.\n *\n * @see {@link https://runed.dev/docs/utilities/previous}\n */\nexport class Previous {\n    #previous = $state(undefined);\n    #curr;\n    constructor(getter) {\n        $effect(() => {\n            this.#previous = this.#curr;\n            this.#curr = getter();\n        });\n    }\n    get current() {\n        return this.#previous;\n    }\n}\n", "import { BROWSER } from \"esm-env\";\nexport const defaultWindow = BROWSER && typeof window !== \"undefined\" ? window : undefined;\nexport const defaultDocument = BROWSER && typeof window !== \"undefined\" ? window.document : undefined;\nexport const defaultNavigator = BROWSER && typeof window !== \"undefined\" ? window.navigator : undefined;\nexport const defaultLocation = BROWSER && typeof window !== \"undefined\" ? window.location : undefined;\n", "import { defaultDocument } from \"../configurable-globals.js\";\n/**\n * <PERSON>les getting the active element in a document or shadow root.\n * If the active element is within a shadow root, it will traverse the shadow root\n * to find the active element.\n * If not, it will return the active element in the document.\n *\n * @param document A document or shadow root to get the active element from.\n * @returns The active element in the document or shadow root.\n */\nexport function getActiveElement(document) {\n    let activeElement = document.activeElement;\n    while (activeElement?.shadowRoot) {\n        const node = activeElement.shadowRoot.activeElement;\n        if (node === activeElement)\n            break;\n        else\n            activeElement = node;\n    }\n    return activeElement;\n}\n/**\n * Returns the owner document of a given element.\n *\n * @param node The element to get the owner document from.\n * @returns\n */\nexport function getOwnerDocument(node, fallback = defaultDocument) {\n    return node?.ownerDocument ?? fallback;\n}\n/**\n * Checks if an element is or is contained by another element.\n *\n * @param node The element to check if it or its descendants contain the target element.\n * @param target The element to check if it is contained by the node.\n * @returns\n */\nexport function isOrContainsTarget(node, target) {\n    return node === target || node.contains(target);\n}\n", "import { defaultWindow, } from \"../../internal/configurable-globals.js\";\nimport { getActiveElement } from \"../../internal/utils/dom.js\";\nimport { on } from \"svelte/events\";\nimport { createSubscriber } from \"svelte/reactivity\";\nexport class ActiveElement {\n    #document;\n    #subscribe;\n    constructor(options = {}) {\n        const { window = defaultWindow, document = window?.document } = options;\n        if (window === undefined)\n            return;\n        this.#document = document;\n        this.#subscribe = createSubscriber((update) => {\n            const cleanupFocusIn = on(window, \"focusin\", update);\n            const cleanupFocusOut = on(window, \"focusout\", update);\n            return () => {\n                cleanupFocusIn();\n                cleanupFocusOut();\n            };\n        });\n    }\n    get current() {\n        this.#subscribe?.();\n        if (!this.#document)\n            return null;\n        return getActiveElement(this.#document);\n    }\n}\n/**\n * An object holding a reactive value that is equal to `document.activeElement`.\n * It automatically listens for changes, keeping the reference up to date.\n *\n * If you wish to use a custom document or shadowRoot, you should use\n * [useActiveElement](https://runed.dev/docs/utilities/active-element) instead.\n *\n * @see {@link https://runed.dev/docs/utilities/active-element}\n */\nexport const activeElement = new ActiveElement();\n", "export function isFunction(value) {\n    return typeof value === \"function\";\n}\nexport function isObject(value) {\n    return value !== null && typeof value === \"object\";\n}\nexport function isElement(value) {\n    return value instanceof Element;\n}\n", "/**\n * Function that takes a callback, and returns a debounced version of it.\n * When calling the debounced function, it will wait for the specified time\n * before calling the original callback. If the debounced function is called\n * again before the time has passed, the timer will be reset.\n *\n * You can await the debounced function to get the value when it is eventually\n * called.\n *\n * The second parameter is the time to wait before calling the original callback.\n * Alternatively, it can also be a getter function that returns the time to wait.\n *\n * @see {@link https://runed.dev/docs/utilities/use-debounce}\n *\n * @param callback The callback to call when the time has passed.\n * @param wait The length of time to wait in ms, defaults to 250.\n */\nexport function useDebounce(callback, wait = 250) {\n    let context = $state(null);\n    function debounced(...args) {\n        if (context) {\n            // Old context will be reused so callers awaiting the promise will get the\n            // new value\n            if (context.timeout) {\n                clearTimeout(context.timeout);\n            }\n        }\n        else {\n            // No old context, create a new one\n            let resolve;\n            let reject;\n            const promise = new Promise((res, rej) => {\n                resolve = res;\n                reject = rej;\n            });\n            context = {\n                timeout: null,\n                runner: null,\n                promise,\n                resolve: resolve,\n                reject: reject,\n            };\n        }\n        context.runner = async () => {\n            // Grab the context and reset it\n            // -> new debounced calls will create a new context\n            if (!context)\n                return;\n            const ctx = context;\n            context = null;\n            try {\n                ctx.resolve(await callback.apply(this, args));\n            }\n            catch (error) {\n                ctx.reject(error);\n            }\n        };\n        context.timeout = setTimeout(context.runner, typeof wait === \"function\" ? wait() : wait);\n        return context.promise;\n    }\n    debounced.cancel = async () => {\n        if (!context || context.timeout === null) {\n            // Wait one event loop to see if something triggered the debounced function\n            await new Promise((resolve) => setTimeout(resolve, 0));\n            if (!context || context.timeout === null)\n                return;\n        }\n        clearTimeout(context.timeout);\n        context.reject(\"Cancelled\");\n        context = null;\n    };\n    debounced.runScheduledNow = async () => {\n        if (!context || !context.timeout) {\n            // Wait one event loop to see if something triggered the debounced function\n            await new Promise((resolve) => setTimeout(resolve, 0));\n            if (!context || !context.timeout)\n                return;\n        }\n        clearTimeout(context.timeout);\n        context.timeout = null;\n        await context.runner?.();\n    };\n    Object.defineProperty(debounced, \"pending\", {\n        enumerable: true,\n        get() {\n            return !!context?.timeout;\n        },\n    });\n    return debounced;\n}\n", "import { isFunction } from \"./is.js\";\nexport function get(value) {\n    if (isFunction(value)) {\n        return value();\n    }\n    return value;\n}\n", "import { defaultWindow } from \"../../internal/configurable-globals.js\";\nimport { get } from \"../../internal/utils/get.js\";\n/**\n * Returns a reactive value holding the size of `node`.\n *\n * Accepts an `options` object with the following properties:\n * - `initialSize`: The initial size of the element. Defaults to `{ width: 0, height: 0 }`.\n * - `box`: The box model to use. Can be either `\"content-box\"` or `\"border-box\"`. Defaults to `\"border-box\"`.\n *\n * @returns an object with `width` and `height` properties.\n *\n * @see {@link https://runed.dev/docs/utilities/element-size}\n */\nexport class ElementSize {\n    #size = $state({\n        width: 0,\n        height: 0,\n    });\n    constructor(node, options = { box: \"border-box\" }) {\n        const window = options.window ?? defaultWindow;\n        this.#size = {\n            width: options.initialSize?.width ?? 0,\n            height: options.initialSize?.height ?? 0,\n        };\n        $effect(() => {\n            if (!window)\n                return;\n            const node$ = get(node);\n            if (!node$)\n                return;\n            const observer = new window.ResizeObserver((entries) => {\n                for (const entry of entries) {\n                    const boxSize = options.box === \"content-box\" ? entry.contentBoxSize : entry.borderBoxSize;\n                    const boxSizeArr = Array.isArray(boxSize) ? boxSize : [boxSize];\n                    this.#size.width = boxSizeArr.reduce((acc, size) => Math.max(acc, size.inlineSize), 0);\n                    this.#size.height = boxSizeArr.reduce((acc, size) => Math.max(acc, size.blockSize), 0);\n                }\n            });\n            observer.observe(node$);\n            return () => {\n                observer.disconnect();\n            };\n        });\n    }\n    get current() {\n        return this.#size;\n    }\n    get width() {\n        return this.#size.width;\n    }\n    get height() {\n        return this.#size.height;\n    }\n}\n", "import { untrack } from \"svelte\";\n/**\n * Returns an object with the mounted state of the component\n * that invokes this function.\n *\n * @see {@link https://runed.dev/docs/utilities/is-mounted}\n */\nexport class IsMounted {\n    #isMounted = $state(false);\n    constructor() {\n        $effect(() => {\n            untrack(() => (this.#isMounted = true));\n            return () => {\n                this.#isMounted = false;\n            };\n        });\n    }\n    get current() {\n        return this.#isMounted;\n    }\n}\n", "import { getContext, has<PERSON>ontex<PERSON>, setContext } from \"svelte\";\nexport class Context {\n    #name;\n    #key;\n    /**\n     * @param name The name of the context.\n     * This is used for generating the context key and error messages.\n     */\n    constructor(name) {\n        this.#name = name;\n        this.#key = Symbol(name);\n    }\n    /**\n     * The key used to get and set the context.\n     *\n     * It is not recommended to use this value directly.\n     * Instead, use the methods provided by this class.\n     */\n    get key() {\n        return this.#key;\n    }\n    /**\n     * Checks whether this has been set in the context of a parent component.\n     *\n     * Must be called during component initialisation.\n     */\n    exists() {\n        return hasContext(this.#key);\n    }\n    /**\n     * Retrieves the context that belongs to the closest parent component.\n     *\n     * Must be called during component initialisation.\n     *\n     * @throws An error if the context does not exist.\n     */\n    get() {\n        const context = getContext(this.#key);\n        if (context === undefined) {\n            throw new Error(`Context \"${this.#name}\" not found`);\n        }\n        return context;\n    }\n    /**\n     * Retrieves the context that belongs to the closest parent component,\n     * or the given fallback value if the context does not exist.\n     *\n     * Must be called during component initialisation.\n     */\n    getOr(fallback) {\n        const context = getContext(this.#key);\n        if (context === undefined) {\n            return fallback;\n        }\n        return context;\n    }\n    /**\n     * Associates the given value with the current component and returns it.\n     *\n     * Must be called during component initialisation.\n     */\n    set(context) {\n        return setContext(this.#key, context);\n    }\n}\n", "export function onDestroyEffect(fn) {\n    $effect(() => {\n        return () => {\n            fn();\n        };\n    });\n}\n", "import { watch } from \"runed\";\nimport { onDestroyEffect } from \"./on-destroy-effect.svelte.js\";\n/**\n * Finds the node with that ID and sets it to the boxed node.\n * Reactive using `$effect` to ensure when the ID or deps change,\n * an update is triggered and new node is found.\n */\nexport function useRefById({ id, ref, deps = () => true, onRefChange, getRootNode }) {\n    watch([() => id.current, deps], ([_id]) => {\n        const rootNode = getRootNode?.() ?? document;\n        const node = rootNode?.getElementById(_id);\n        if (node)\n            ref.current = node;\n        else\n            ref.current = null;\n        onRefChange?.(ref.current);\n    });\n    onDestroyEffect(() => {\n        ref.current = null;\n        onRefChange?.(null);\n    });\n}\n", "/**\n * A utility function that executes a callback after a specified number of milliseconds.\n */\nexport function afterSleep(ms, cb) {\n    return setTimeout(cb, ms);\n}\n", "import { tick } from \"svelte\";\nexport function afterTick(fn) {\n    tick().then(fn);\n}\n", "import { untrack } from \"svelte\";\nexport function onMountEffect(fn) {\n    $effect(() => {\n        const cleanup = untrack(() => fn());\n        return cleanup;\n    });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKO,SAAS,mBAAmB,UAAU;AACzC,SAAO,SAAU,GAAG;AANxB;AAOQ,eAAW,WAAW,UAAU;AAC5B,UAAI,CAAC;AACD;AACJ,UAAI,EAAE;AACF;AACJ,UAAI,OAAO,YAAY,YAAY;AAC/B,gBAAQ,KAAK,MAAM,CAAC;AAAA,MACxB,OACK;AACD,sBAAQ,YAAR,mBAAiB,KAAK,MAAM;AAAA,MAChC;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACpBA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,CAAC,KAAK,KAAK,KAAK,GAAG;AACzC,SAAS,YAAY,OAAO,IAAI;AAC5B,MAAI,eAAe,KAAK,IAAI;AACxB,WAAO;AACX,SAAO,SAAS,KAAK,YAAY;AACrC;AACA,SAAS,YAAY,KAAK;AACtB,QAAM,QAAQ,CAAC;AACf,MAAI,OAAO;AACX,MAAI;AACJ,MAAI;AACJ,aAAW,QAAQ,KAAK;AAEpB,UAAM,aAAa,cAAc,SAAS,IAAI;AAC9C,QAAI,eAAe,MAAM;AACrB,YAAM,KAAK,IAAI;AACf,aAAO;AACP,sBAAgB;AAChB;AAAA,IACJ;AACA,UAAM,UAAU,YAAY,IAAI;AAChC,QAAI,qBAAqB,OAAO;AAE5B,UAAI,kBAAkB,SAAS,YAAY,MAAM;AAC7C,cAAM,KAAK,IAAI;AACf,eAAO;AACP,wBAAgB;AAChB;AAAA,MACJ;AAEA,UAAI,kBAAkB,QAAQ,YAAY,SAAS,KAAK,SAAS,GAAG;AAChE,cAAM,WAAW,KAAK,GAAG,EAAE;AAC3B,cAAM,KAAK,KAAK,MAAM,GAAG,KAAK,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC;AACtD,eAAO,WAAW;AAClB,wBAAgB;AAChB;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ;AACR,oBAAgB;AAChB,uBAAmB;AAAA,EACvB;AACA,QAAM,KAAK,IAAI;AACf,SAAO;AACX;AACO,SAAS,WAAW,KAAK;AAC5B,MAAI,CAAC;AACD,WAAO;AACX,SAAO,YAAY,GAAG,EACjB,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC,EACxB,KAAK,EAAE;AAChB;AACO,SAAS,UAAU,KAAK;AAC3B,SAAO,WAAW,WAAW,OAAO,EAAE,CAAC;AAC3C;AAQA,SAAS,WAAW,KAAK;AACrB,SAAO,MAAM,IAAI,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC,IAAI;AACvD;AACA,SAAS,WAAW,KAAK;AACrB,SAAO,MAAM,IAAI,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC,IAAI;AACvD;;;ACnEO,SAAS,cAAc,KAAK;AAC/B,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,QAAM,WAAW,CAAC;AAClB,WAAS,SAAS,MAAM,OAAO;AAC3B,QAAI,KAAK,WAAW,OAAO,KACvB,KAAK,WAAW,UAAU,KAC1B,KAAK,WAAW,MAAM,KACtB,KAAK,WAAW,KAAK,GAAG;AACxB,eAAS,WAAW,IAAI,CAAC,IAAI;AAC7B;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,IAAI,GAAG;AACvB,eAAS,IAAI,IAAI;AACjB;AAAA,IACJ;AACA,aAAS,UAAU,IAAI,CAAC,IAAI;AAAA,EAChC;AACA,cAAM,KAAK,QAAQ;AACnB,SAAO;AACX;;;ACtBO,SAAS,WAAW,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC5B;AACO,SAAS,SAAS,OAAO;AAC5B,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC9C;AACA,IAAM,8BAA8B,CAAC,UAAU,UAAU,UAAU,SAAS;AACrE,SAAS,aAAa,OAAO;AAEhC,MAAI,UAAU,QAAQ,UAAU;AAC5B,WAAO;AACX,MAAI,4BAA4B,SAAS,OAAO,KAAK;AACjD,WAAO;AAEX,MAAI,MAAM,QAAQ,KAAK;AACnB,WAAO,MAAM,MAAM,CAAC,SAAS,aAAa,IAAI,CAAC;AAEnD,MAAI,OAAO,UAAU,UAAU;AAE3B,QAAI,OAAO,eAAe,KAAK,MAAM,OAAO;AACxC,aAAO;AACX,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;AClBO,SAAS,oBAAoB,WAAW;AAC3C,SAAO,IAAI,SAAS;AAChB,eAAW,YAAY,WAAW;AAC9B,UAAI,OAAO,aAAa,YAAY;AAChC,iBAAS,GAAG,IAAI;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACdA,SAAS,aAAa,SAAS,UAAU;AACrC,QAAM,QAAQ,OAAO,SAAS,GAAG;AACjC,SAAO,CAAC,QAAQ;AAEZ,QAAI,OAAO,QAAQ,UAAU;AACzB,YAAM,IAAI,UAAU,gDAAgD,OAAO,GAAG,EAAE;AAAA,IACpF;AAEA,QAAI,CAAC,IAAI,MAAM,KAAK;AAChB,aAAO;AAEX,WAAO,IAAI,QAAQ,OAAO,QAAQ;AAAA,EACtC;AACJ;AACA,IAAM,eAAe,aAAa,SAAS,CAAC,UAAU,IAAI,MAAM,YAAY,CAAC,EAAE;AACxE,SAAS,WAAW,UAAU;AACjC,MAAI,CAAC,YAAY,OAAO,aAAa,YAAY,MAAM,QAAQ,QAAQ,GAAG;AACtE,UAAM,IAAI,UAAU,gDAAgD,OAAO,QAAQ,EAAE;AAAA,EACzF;AACA,SAAO,OAAO,KAAK,QAAQ,EACtB,IAAI,CAAC,aAAa,GAAG,aAAa,QAAQ,CAAC,KAAK,SAAS,QAAQ,CAAC,GAAG,EACrE,KAAK,IAAI;AAClB;;;ACrBO,SAAS,cAAc,QAAQ,CAAC,GAAG;AACtC,SAAO,WAAW,KAAK,EAAE,QAAQ,MAAM,GAAG;AAC9C;AACO,IAAM,eAAe;AAAA,EACxB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AACf;AACO,IAAM,qBAAqB,cAAc,YAAY;;;ACP5D,SAAS,eAAe,KAAK;AAT7B;AAYI,SAAO,IAAI,SAAS,KAAK,IAAI,WAAW,IAAI,KAAK,IAAI,CAAC,QAAM,SAAI,CAAC,MAAL,mBAAQ;AACxE;AAUO,SAAS,cAAc,MAAM;AAChC,QAAM,SAAS,EAAE,GAAG,KAAK,CAAC,EAAE;AAC5B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,UAAM,QAAQ,KAAK,CAAC;AACpB,eAAW,OAAO,OAAO;AACrB,YAAM,IAAI,OAAO,GAAG;AACpB,YAAM,IAAI,MAAM,GAAG;AACnB,YAAM,cAAc,OAAO,MAAM;AACjC,YAAM,cAAc,OAAO,MAAM;AAEjC,UAAI,eAAe,OAAO,eAAe,eAAe,GAAG,GAAG;AAE1D,cAAM,WAAW;AACjB,cAAM,WAAW;AACjB,eAAO,GAAG,IAAI,gBAAgB,UAAU,QAAQ;AAAA,MACpD,WACS,eAAe,aAAa;AAEjC,eAAO,GAAG,IAAI,iBAAiB,GAAG,CAAC;AAAA,MACvC,WACS,QAAQ,SAAS;AAEtB,cAAM,gBAAgB,aAAa,CAAC;AACpC,cAAM,gBAAgB,aAAa,CAAC;AACpC,YAAI,iBAAiB,eAAe;AAChC,iBAAO,GAAG,IAAI,KAAK,GAAG,CAAC;AAAA,QAC3B,WACS,eAAe;AACpB,iBAAO,GAAG,IAAI,KAAK,CAAC;AAAA,QACxB,WACS,eAAe;AACpB,iBAAO,GAAG,IAAI,KAAK,CAAC;AAAA,QACxB;AAAA,MACJ,WACS,QAAQ,SAAS;AACtB,cAAM,YAAY,OAAO,MAAM;AAC/B,cAAM,YAAY,OAAO,MAAM;AAC/B,cAAM,YAAY,OAAO,MAAM;AAC/B,cAAM,YAAY,OAAO,MAAM;AAC/B,YAAI,aAAa,WAAW;AAExB,iBAAO,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,QAC/B,WACS,aAAa,WAAW;AAE7B,gBAAM,cAAc,cAAc,CAAC;AACnC,iBAAO,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,YAAY;AAAA,QACzC,WACS,aAAa,WAAW;AAE7B,gBAAM,cAAc,cAAc,CAAC;AACnC,iBAAO,GAAG,IAAI,EAAE,GAAG,aAAa,GAAG,EAAE;AAAA,QACzC,WACS,aAAa,WAAW;AAE7B,gBAAM,eAAe,cAAc,CAAC;AACpC,gBAAM,eAAe,cAAc,CAAC;AACpC,iBAAO,GAAG,IAAI,EAAE,GAAG,cAAc,GAAG,aAAa;AAAA,QACrD,WACS,WAAW;AAChB,iBAAO,GAAG,IAAI;AAAA,QAClB,WACS,WAAW;AAChB,iBAAO,GAAG,IAAI;AAAA,QAClB,WACS,WAAW;AAChB,iBAAO,GAAG,IAAI;AAAA,QAClB,WACS,WAAW;AAChB,iBAAO,GAAG,IAAI;AAAA,QAClB;AAAA,MACJ,OACK;AAED,eAAO,GAAG,IAAI,MAAM,SAAY,IAAI;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,OAAO,OAAO,UAAU,UAAU;AAClC,WAAO,QAAQ,cAAc,OAAO,KAAK,EAAE,WAAW,MAAM,GAAG;AAAA,EACnE;AAEA,MAAI,OAAO,WAAW,MAAM;AACxB,WAAO,SAAS;AAChB,WAAO,OAAO;AAAA,EAClB;AAEA,MAAI,OAAO,aAAa,MAAM;AAC1B,WAAO,WAAW;AAClB,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX;;;ICnHM,YAAY,OAAO,KAAK;IACxB,mBAAmB,OAAO,aAAa;SAMpC,MAAM,OAAO;SACX,SAAS,KAAK,KAAK,aAAa;AAC3C;SAMS,cAAc,OAAO;SACnB,IAAI,MAAM,KAAK,KAAK,oBAAoB;AACnD;SACgB,IAAI,cAAc;MAC1B,UAAO,MAAA,MAAU,YAAY,CAAA;;KAE5B,SAAS,GAAG;KACZ,gBAAgB,GAAG;QAChB,UAAU;iBACH,OAAO;IAClB;QACI,QAAQ,GAAG;UACX,SAAU,GAAC,IAAA;IACf;;AAER;SACS,QAAQ,QAAQ,QAAQ;QACvB,UAAO,aAAe,MAAM;MAC9B,QAAQ;;OAEH,SAAS,GAAG;OACZ,gBAAgB,GAAG;UAChB,UAAU;mBACH,OAAO;MAClB;UACI,QAAQ,GAAG;AACX,eAAO,CAAC;MACZ;;EAER;;KAEK,SAAS,GAAG;QACT,UAAU;aACH,OAAM;IACjB;;AAER;SACS,QAAQ,OAAO;MAChB,IAAI,MAAM,KAAK,EAAA,QACR;MACP,WAAW,KAAK,EAAA,QACT,IAAI,KAAK,KAAK;SAClB,IAAI,KAAK;AACpB;SAWS,WAAW,OAAO;SAChB,OAAO,QAAQ,KAAK,EAAE;KAAQ,KAAG,CAAG,KAAK,CAAC,MAAM;WAC9C,IAAI,MAAM,CAAC,GAAG;eACR,OAAO,OAAO,KAAG,EAAA,CAAK,GAAG,GAAG,EAAC,CAAA;MACxC;UACI,IAAI,cAAc,CAAC,GAAG;AACtB,eAAO,eAAe,KAAK,KAAG;UAC1B,MAAM;mBACK,EAAE;UACb;UAEA,IAAI,GAAG;AACH,cAAE,UAAU;UAChB;;MAER,OACK;AACD,eAAO,eAAe,KAAK,KAAG;UAC1B,MAAM;mBACK,EAAE;UACb;;MAER;aACO;IACX;;;AACJ;SAUS,cAAc,GAAG;OACjB,IAAI,cAAc,CAAC,EAAA,QACb;;KAEN,SAAS,GAAG;QACT,UAAU;aACH,EAAE;IACb;;AAER;AACA,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,gBAAgB;;;SCvHX,UAAU,OAAO,QAAQ;UACtB,OAAK;SACJ;AACD,MAAA,YAAQ,MAAM;;SAEb;AACD,MAAA,gBAAY,MAAM;;;AAG9B;SACS,WAAW,SAAS,OAAO,QAAQ,UAAO,CAAA,GAAO;UAC9C,OAAO,MAAK,IAAK;MAErB,SAAM,CAAI;MAKV,iBAAiB,MAAM,QAAQ,OAAO,IAAA,CAAA,IAEpC;AACN,YAAU,OAAK,MAAQ;UACb,SAAS,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAG,CAAE,WAAW,OAAM,CAAA,IAAM,QAAO;SAC9E,QAAQ;AACT,eAAS;AACT,uBAAiB;;IAErB;UACM,UAAU,QAAO,MAAO,OAAO,QAAQ,cAAc,CAAA;AAC3D,qBAAiB;WACV;EACX,CAAC;AACL;SACS,eAAe,SAAS,OAAO,QAAQ;QACtC,cAAW,YAAA,MAAsB;QAC/B,OAAO;AACX;MAAW;MAAS;OAAQ,QAAQ,mBAAmB;YAC/C,MAAM;AACN,sBAAW;;QAEf;cAEM,UAAU,OAAO,QAAQ,cAAc;AAC7C,eAAO;eACA;MACX;;;QAGE,MAAM,KAAI;;EAChB,CAAC;AACD,EAAA,YAAO,MAAO;WACH;EACX,CAAC;AACL;SACgB,MAAM,SAAS,QAAQ,SAAS;AAC5C,aAAW,SAAS,QAAQ,QAAQ,OAAO;AAC/C;SACS,SAAS,SAAS,QAAQ,SAAS;AACxC,aAAW,SAAS,OAAO,QAAQ,OAAO;AAC9C;AACA,MAAM,MAAM;SACI,UAAU,QAAQ,QAAQ;AACtC,iBAAe,QAAQ,QAAQ,MAAM;AACzC;SACS,aAAa,QAAQ,QAAQ;AAClC,iBAAe,QAAQ,OAAO,MAAM;AACxC;AACA,UAAU,MAAM;;;;IC/DH,iBAAS;EAGlB,YAAY,QAAQ;kCAFX,MAAU,MAAS;;AAGxB,IAAA,YAAO,MAAO;UACV,mBAAI,YAAa,mBAAI,QAAM,IAAA;AAC3B,yBAAI,OAAS,OAAM;IACvB,CAAC;EACL;MACI,UAAU;eACH,mBAAI,UAAU;EACzB;AACJ;;;;;AChBO,IAAM,gBAAgB,gBAAW,OAAO,WAAW,cAAc,SAAS;AAC1E,IAAM,kBAAkB,gBAAW,OAAO,WAAW,cAAc,OAAO,WAAW;AACrF,IAAM,mBAAmB,gBAAW,OAAO,WAAW,cAAc,OAAO,YAAY;AACvF,IAAM,kBAAkB,gBAAW,OAAO,WAAW,cAAc,OAAO,WAAW;;;ACMrF,SAAS,iBAAiBA,WAAU;AACvC,MAAIC,iBAAgBD,UAAS;AAC7B,SAAOC,kBAAA,gBAAAA,eAAe,YAAY;AAC9B,UAAM,OAAOA,eAAc,WAAW;AACtC,QAAI,SAASA;AACT;AAAA;AAEA,MAAAA,iBAAgB;AAAA,EACxB;AACA,SAAOA;AACX;;;;IChBa,sBAAc;EAGvB,YAAY,UAAO,CAAA,GAAO;;;;MACd,QAAAC,UAAS;MAAe,UAAAC,YAAWD,WAAA,gBAAAA,QAAQ;QAAa;sBAC5DA,SAAW,MAAS,EAAA;AAExB,uBAAI,WAAaC;AACjB,uBAAI,YAAc,iBAAgB,CAAE,WAAW;YACrC,iBAAiB,GAAGD,SAAQ,WAAW,MAAM;YAC7C,kBAAkB,GAAGA,SAAQ,YAAY,MAAM;mBACxC;AACT,uBAAc;AACd,wBAAe;MACnB;IACJ,CAAC;EACL;MACI,UAAU;;AACV,6BAAI,gBAAJ;SACK,mBAAI,WAAU,QACR;WACJ,iBAAiB,mBAAI,UAAU;EAC1C;AACJ;;;IAUa,gBAAa,IAAO,cAAa;;;ACrCvC,SAASE,YAAW,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC5B;;;SCegB,YAAY,UAAU,OAAO,KAAK;MAC1C,UAAO,MAAU,IAAI;WAChB,aAAa,MAAM;YACpB,OAAO,GAAE;cAGL,OAAO,EAAC,SAAS;AACjB,qBAAY,IAAC,OAAO,EAAC,OAAO;MAChC;IACJ,OACK;UAEG;UACA;YACE,UAAO,IAAO,QAAO,CAAE,KAAK,QAAQ;AACtC,kBAAU;AACV,iBAAS;MACb,CAAC;;QACD;;UACI,SAAS;UACT,QAAQ;UACR;UACS;UACD;;;;IAEhB;QACA,OAAO,EAAC,SAAM,YAAe;eAGpB,OAAO,EAAA;YAEN,MAAG,IAAG,OAAO;UACnB,SAAU,IAAI;UACV;AACA,YAAI,QAAO,MAAO,SAAS,MAAM,MAAM,IAAI,CAAA;MAC/C,SACO,OAAO;AACV,YAAI,OAAO,KAAK;MACpB;IACJ;QACA,OAAO,EAAC,UAAU,WAAU,IAAC,OAAO,EAAC,QAAM,cAAA,OAAS,MAAS,UAAU,IAAG,KAAI,IAAK,IAAI;eAChF,OAAO,EAAC;EACnB;AACA,YAAU,SAAM,YAAe;aACtB,OAAO,KAAA,cAAA,IAAI,OAAO,EAAC,SAAY,IAAI,GAAE;gBAE5B,QAAO,CAAE,YAAY,WAAW,SAAS,CAAC,CAAA;eAC/C,OAAO,KAAA,cAAA,IAAI,OAAO,EAAC,SAAY,IAAI,EAAA;IAE5C;AACA,iBAAY,IAAC,OAAO,EAAC,OAAO;QAC5B,OAAO,EAAC,OAAO,WAAW;QAC1B,SAAU,IAAI;EAClB;AACA,YAAU,kBAAe,YAAe;;aAC/B,OAAO,KAAA,CAAA,IAAK,OAAO,EAAC,SAAS;gBAEpB,QAAO,CAAE,YAAY,WAAW,SAAS,CAAC,CAAA;eAC/C,OAAO,KAAA,CAAA,IAAK,OAAO,EAAC,QAAO;IAEpC;AACA,iBAAY,IAAC,OAAO,EAAC,OAAO;QAC5B,OAAO,EAAC,UAAU;2BACZ,OAAO,GAAC;EAClB;AACA,SAAO,eAAe,WAAW,WAAS;IACtC,YAAY;IACZ,MAAM;;0BACO,OAAO,yBAAE;IACtB;;SAEG;AACX;;;ACxFO,SAASC,KAAI,OAAO;AACvB,MAAIC,YAAW,KAAK,GAAG;AACnB,WAAO,MAAM;AAAA,EACjB;AACA,SAAO;AACX;;;;ICOa,oBAAY;EAKrB,YAAY,MAAM,UAAO,EAAK,KAAK,aAAY,GAAI;8BAJ9C,MAAA,MAAA,EACD,OAAO,GACP,QAAQ,EAAC,CAAA,CAAA;;UAGHC,UAAS,QAAQ,UAAU;;MACjC,mBAAI;;QACA,SAAO,aAAQ,gBAAR,mBAAqB,UAAS;QACrC,UAAQ,aAAQ,gBAAR,mBAAqB,WAAU;;;;AAE3C,IAAA,YAAO,MAAO;WACLA,QAAM;YAEL,QAAQC,KAAI,IAAI;WACjB,MAAK;YAEJ,WAAQ,IAAOD,QAAO,eAAc,CAAE,YAAY;mBACzC,SAAS,SAAS;gBACnB,UAAO,cAAG,QAAQ,KAAQ,aAAa,IAAG,MAAM,iBAAiB,MAAM;gBACvE,aAAa,MAAM,QAAQ,OAAO,IAAI,UAAO,CAAI,OAAO;cAC9D,mBAAI,MAAM,EAAC,QAAQ,WAAW,OAAM,CAAE,KAAK,SAAS,KAAK,IAAI,KAAK,KAAK,UAAU,GAAG,CAAC;cACrF,mBAAI,MAAM,EAAC,SAAS,WAAW,OAAM,CAAE,KAAK,SAAS,KAAK,IAAI,KAAK,KAAK,SAAS,GAAG,CAAC;QACzF;MACJ,CAAC;AACD,eAAS,QAAQ,KAAK;mBACT;AACT,iBAAS,WAAU;MACvB;IACJ,CAAC;EACL;MACI,UAAU;eACH,mBAAI,MAAM;EACrB;MACI,QAAQ;eACD,mBAAI,MAAM,EAAC;EACtB;MACI,SAAS;eACF,mBAAI,MAAM,EAAC;EACtB;AACJ;;;;;IC9Ca,kBAAU;EAEnB,cAAc;mCADJ,MAAU,KAAK;AAErB,IAAA,YAAO,MAAO;AACV,cAAO,MAAA,IAAQ,mBAAI,aAAc,IAAI,CAAA;mBACxB;YACT,mBAAI,aAAc,KAAK;MAC3B;IACJ,CAAC;EACL;MACI,UAAU;eACH,mBAAI,WAAW;EAC1B;AACJ;;;;ACpBA;AACO,IAAM,UAAN,MAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,YAAY,MAAM;AANlB;AACA;AAMI,uBAAK,OAAQ;AACb,uBAAK,MAAO,OAAO,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,MAAM;AACN,WAAO,mBAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,WAAO,WAAW,mBAAK,KAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM;AACF,UAAM,UAAU,WAAW,mBAAK,KAAI;AACpC,QAAI,YAAY,QAAW;AACvB,YAAM,IAAI,MAAM,YAAY,mBAAK,MAAK,aAAa;AAAA,IACvD;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,UAAU;AACZ,UAAM,UAAU,WAAW,mBAAK,KAAI;AACpC,QAAI,YAAY,QAAW;AACvB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACT,WAAO,WAAW,mBAAK,OAAM,OAAO;AAAA,EACxC;AACJ;AA9DI;AACA;;;SCHY,gBAAgB,IAAI;AAChC,EAAA,YAAO,MAAO;iBACG;AACT,SAAE;IACN;EACJ,CAAC;AACL;;;SCCgB;EAAa;EAAI;EAAK,OAAI,MAAS;EAAM;EAAa;GAAe;AACjF,QAAK,CAAA,MAAQ,GAAG,SAAS,IAAI,GAAA,CAAA,CAAK,GAAG,MAAM;UACjC,YAAW,iDAAmB;UAC9B,OAAO,qCAAU,eAAe;QAClC,KACA,KAAI,UAAU;QAEd,KAAI,UAAU;AAClB,+CAAc,IAAI;EACtB,CAAC;AACD,kBAAe,MAAO;AAClB,QAAI,UAAU;AACd,+CAAc;EAClB,CAAC;AACL;;;AClBO,SAAS,WAAW,IAAI,IAAI;AAC/B,SAAO,WAAW,IAAI,EAAE;AAC5B;;;ACJO,SAAS,UAAU,IAAI;AAC1B,OAAK,EAAE,KAAK,EAAE;AAClB;;;SCFgB,cAAc,IAAI;AAC9B,EAAA,YAAO,MAAO;UACJ,UAAU,QAAO,MAAO,GAAE,CAAA;WACzB;EACX,CAAC;AACL;", "names": ["document", "activeElement", "window", "document", "isFunction", "get", "isFunction", "window", "get"]}