import {
  asClassComponent,
  createB<PERSON>bler,
  createClassComponent,
  handlers,
  nonpassive,
  once,
  passive,
  preventDefault,
  run,
  self,
  stopImmediatePropagation,
  stopPropagation,
  trusted
} from "./chunk-4D5HC5JT.js";
import "./chunk-2PJMTVJS.js";
import "./chunk-NXGWPDOQ.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-RVAV4ZRS.js";
import "./chunk-UQOTJTBP.js";
export {
  asClassComponent,
  createBubbler,
  createClassComponent,
  handlers,
  nonpassive,
  once,
  passive,
  preventDefault,
  run,
  self,
  stopImmediatePropagation,
  stopPropagation,
  trusted
};
//# sourceMappingURL=svelte_legacy.js.map
