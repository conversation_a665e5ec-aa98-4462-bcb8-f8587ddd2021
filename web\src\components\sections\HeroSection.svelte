<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import {
    ArrowUpRight,
    Play,
    CheckCircle,
    Bot,
    LoaderCircle,
    Shell,
    Search,
    Workflow,
    Send,
    MonitorCheck,
    Check,
  } from 'lucide-svelte';

  let streamingJobs = $state([] as any[]); // will become [job1,job2,…job40,job1,job2,…job40]
  let isLoading = $state(true);

  // Process logo URL through worker for proper CORS and fallback handling
  function getCompanyLogo(logoUrl: string | null): string | null {
    if (!logoUrl) return null;

    // Check if it's a Clearbit URL - use directly (has proper CORS)
    if (logoUrl.includes('logo.clearbit.com')) {
      return logoUrl;
    }

    // Check if it's already a worker URL - use directly
    if (logoUrl.includes('hirli-static-assets.christopher-eugene-rodriguez.workers.dev')) {
      return logoUrl;
    }

    // Handle R2 URLs - extract the path and route through worker
    let logoPath = logoUrl;

    // If it's a direct R2 URL, extract the path
    if (logoPath.includes('pub-') && logoPath.includes('.r2.dev/')) {
      logoPath = logoPath.split('.r2.dev/')[1];
    }

    // Always use Worker URL for proper CORS support
    const workerUrl = 'https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev';
    return `${workerUrl}/${logoPath}`;
  }

  // Fetch jobs from database with reduced limit for faster loading
  async function fetchJobs() {
    try {
      console.log('Fetching jobs from API...');
      const response = await fetch('/api/jobs?limit=200&random=true');
      console.log('Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API response data:', data);
      console.log('Jobs count:', data.jobs?.length || 0);

      // Debug: Check what fields are available in the first job
      if (data.jobs?.length > 0) {
        console.log('First job sample:', data.jobs[0]);
        console.log('Available fields:', Object.keys(data.jobs[0]));
        console.log('Description type:', typeof data.jobs[0].description);
        console.log('Description content:', data.jobs[0].description);
      }

      return data.jobs || [];
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
      return [];
    }
  }

  const statusStages = [
    { status: 'scanning', color: 'blue', text: 'Scanning', icon: Search },
    { status: 'matching', color: 'yellow', text: 'Matching', icon: Workflow },
    { status: 'applying', color: 'orange', text: 'Applying', icon: Send },
    { status: 'applied', color: 'green', text: 'Applied', icon: MonitorCheck },
  ];

  function getJobStatus(jobAge: number) {
    if (jobAge < 5) return statusStages[0]; // scanning
    if (jobAge < 10) return statusStages[1]; // matching
    if (jobAge < 15) return statusStages[2]; // applying
    return statusStages[3]; // applied
  }

  function transformJobForDisplay(job: any, index: number) {
    const company = job?.company || 'Unknown Company';
    const companyInitial = company.charAt(0).toUpperCase();
    const logoColors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-red-500',
      'bg-yellow-500',
      'bg-indigo-500',
      'bg-pink-500',
      'bg-teal-500',
    ];
    const logoColor = logoColors[company.length % logoColors.length];

    // Get company logo from R2 storage if available
    const rawLogoUrl = job?.companyRelation?.logoUrl || null;
    const companyName = job?.companyRelation?.name || company;

    const companyLogo = getCompanyLogo(rawLogoUrl);

    // Extract industry from available database fields or infer from job data
    let industry = job?.industry || job?.industryTags?.[0];
    if (!industry) {
      // Infer from company name or job title if database fields are empty
      const titleLower = (job?.title || '').toLowerCase();
      const companyLower = (job?.company || '').toLowerCase();

      if (
        /(health|medical|nurse|doctor|therapist|clinical)/.test(titleLower) ||
        /(health|medical|hospital)/.test(companyLower)
      ) {
        industry = 'Healthcare';
      } else if (
        /(finance|bank|accounting|banker)/.test(titleLower) ||
        /(bank|financial|wells fargo)/.test(companyLower)
      ) {
        industry = 'Finance';
      } else if (/(market|brand|social media)/.test(titleLower)) {
        industry = 'Marketing';
      } else if (/(sales|account manager|business development)/.test(titleLower)) {
        industry = 'Sales';
      } else if (/(design|ui|ux|graphic)/.test(titleLower)) {
        industry = 'Design';
      } else if (
        /(engineer|developer|programmer|software|scientist|biostatistician)/.test(titleLower)
      ) {
        industry = 'Technology';
      } else if (
        /(teacher|education|instructor|professor|assistant)/.test(titleLower) ||
        /(university|school)/.test(companyLower)
      ) {
        industry = 'Education';
      } else if (/(operations|logistics|supply chain|coordinator|clerk)/.test(titleLower)) {
        industry = 'Operations';
      } else if (/(consultant|advisor)/.test(titleLower)) {
        industry = 'Consulting';
      } else if (/(hr|human resources|benefits)/.test(titleLower)) {
        industry = 'Human Resources';
      } else if (/(behavioral|behavior|therapist)/.test(titleLower)) {
        industry = 'Healthcare';
      } else {
        industry = 'Other';
      }
    }

    // Extract seniority from available database fields or infer from job title
    let seniority = job?.experienceLevel || job?.seniorityLevel;
    if (!seniority) {
      const titleLower = (job?.title || '').toLowerCase();
      if (/(senior|sr\.|lead|principal|staff)/.test(titleLower)) {
        seniority = 'Senior Level';
      } else if (/(junior|jr\.|entry|assistant|aide|technician)/.test(titleLower)) {
        seniority = 'Entry Level';
      } else if (/(director|head of|vp|vice president|manager)/.test(titleLower)) {
        seniority = 'Management';
      } else if (/(coordinator|specialist|analyst)/.test(titleLower)) {
        seniority = 'Mid-Level';
      } else {
        seniority = 'Mid-Level';
      }
    }

    const benefits = Array.isArray(job?.benefits) ? job.benefits.slice(0, 3) : [];

    return {
      id: job?.id || Math.random().toString(),
      uniqueKey: `${job?.id || 'job'}_${index}_${Date.now()}_${Math.random().toString(36).slice(2, 9)}`,
      company: companyName, // Use the company name from relation if available
      role: job?.title || 'Software Engineer',
      location: job?.location || 'Remote',
      salary: job?.salary || 'Competitive',
      companyInitial,
      logoColor,
      companyLogo, // Add the R2 logo URL
      companyRelation: job?.companyRelation, // Preserve the original company relation data
      matchScore: Math.floor(Math.random() * 30) + 70, // random 70–99%
      industry,
      seniority,
      benefits,
      description:
        job?.description && typeof job.description === 'string'
          ? job.description.slice(0, 100) + '...'
          : '',
      age: Math.floor(Math.random() * 20),
    };
  }

  // On client‐only, fetch once and initialize a **doubled** list
  if (typeof window !== 'undefined') {
    (async function initializeStream() {
      console.log('Starting initialization...');
      isLoading = true;

      const jobs = await fetchJobs();
      console.log('Fetched jobs:', jobs.length);

      if (jobs.length > 0) {
        // Pick the first 40 (or whatever you need to fill the screen)
        const initialCount = Math.min(40, jobs.length);
        const baseSlice = jobs.slice(0, initialCount);
        console.log('Base slice:', baseSlice.length);

        // Transform those 40 into display objects
        const firstBatch = baseSlice.map((job: any, i: number) => transformJobForDisplay(job, i));

        // Create second batch with different unique keys to avoid duplicates
        const secondBatch = baseSlice.map((job: any, i: number) =>
          transformJobForDisplay(job, i + initialCount)
        );

        // Then concat both batches so that we have 80 total items with unique keys
        streamingJobs = [...firstBatch, ...secondBatch];
        console.log('Final streaming jobs:', streamingJobs.length);

        isLoading = false;
      } else {
        console.warn('No jobs found in database');
        isLoading = false;
      }
    })();

    // Every 1.5s, randomize some ages to flicker statuses (just to keep icons moving)
    setInterval(() => {
      if (!streamingJobs.length) return;
      for (let i = 0; i < streamingJobs.length; i++) {
        if (Math.random() < 0.2) {
          streamingJobs[i].age = Math.floor(Math.random() * 20);
        }
      }
      // Trigger Svelte reactivity
      streamingJobs = streamingJobs;
    }, 1500);
  }
</script>

<style>
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }

  @keyframes scroll-up {
    0% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(-50%);
    }
  }

  .animate-scroll-up {
    animation: scroll-up 40s linear infinite;
  }
</style>

<section class="-mt-17 pl-15 bg-accent -z-50 mx-auto h-screen min-h-[55rem] max-w-[1600px]">
  <div class="grid h-screen min-h-[55rem] lg:grid-cols-[2fr_4fr]">
    <!-- Left side - Cleaner content layout (40%) -->
    <div class="relative overflow-hidden">
      <!-- Main content -->
      <div
        class="align-items-end relative z-10 flex flex h-full max-w-md flex-col flex-col justify-center gap-10 p-10">
        <!-- Cleaner header section -->
        <div class="space-y-6">
          <!-- Simplified badge -->
          <div
            class="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-4 py-2 text-xs font-medium text-blue-700 xl:text-sm">
            <Bot class="h-3 w-3 xl:h-4 xl:w-4" />
            <span>AI-Powered Automation</span>
          </div>

          <h1
            class="font-bold leading-tight text-gray-900 sm:text-2xl md:text-4xl lg:text-5xl xl:text-6xl">
            Apply to
            <span class="relative text-blue-600">
              Hundreds
              <div
                class="absolute -bottom-1 left-0 h-2 w-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-60">
              </div>
            </span>
            of Jobs on Autopilot
          </h1>

          <p class="text-normal text-gray-600 xl:text-lg">
            Let AI handle your job applications while you focus on what matters. Smart matching,
            personalized applications, and real-time tracking.
          </p>
        </div>

        <!-- Streamlined key benefits -->
        <div class="mt-8 space-y-2 text-sm">
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
              <Check class="h-3 w-3 text-green-600" />
            </div>
            <span class="text-gray-700">100+ applications in minutes</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
              <Check class="h-3 w-3 text-blue-600" />
            </div>
            <span class="text-gray-700">AI-powered resume matching</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-purple-100">
              <Check class="h-3 w-3 text-purple-600" />
            </div>
            <span class="text-gray-700">Real-time tracking & analytics</span>
          </div>
        </div>

        <!-- Simplified CTA section -->
        <div class="space-y-4">
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button
              href="/auth/sign-up"
              class="group bg-gradient-to-r from-blue-600 to-indigo-600 font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <span class="flex items-center gap-2">
                Get Started
                <ArrowUpRight class="h-4 w-4 transition-transform group-hover:translate-x-0.5" />
              </span>
            </Button>
            <Button
              variant="ghost"
              class="group flex items-center gap-2 px-4 py-3 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <Play class="h-4 w-4 group-hover:text-blue-600" />
              <span>Watch Demo</span>
            </Button>
          </div>

          <div class="flex items-center gap-3 text-xs text-gray-500">
            <div class="flex items-center gap-2">
              <div class="h-1.5 w-1.5 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <span>Setup in minutes</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Clean Job Application Stream (60%) -->
    <div class="bg-accent relative overflow-hidden opacity-80">
      <!-- Fade gradient overlay at top to make header legible -->
      <div
        class="from-accent via-accent/90 pointer-events-none absolute left-0 right-0 top-0 z-10 h-24 bg-gradient-to-b to-transparent">
      </div>

      <!-- Fade gradient overlay at bottom for visual balance -->
      <div
        class="from-accent via-accent/70 pointer-events-none absolute bottom-0 left-0 right-0 z-10 h-16 bg-gradient-to-t to-transparent">
      </div>

      <!-- Main content area -->
      <div class="relative flex h-full flex-col justify-center">
        <!-- Endless Vertical Scrolling Stream -->
        <div class="h-full overflow-hidden p-4">
          {#if isLoading}
            <div class="columns-1 gap-4 space-y-4 sm:columns-2 lg:columns-3 xl:columns-4">
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
              <Skeleton class="bg-primary/10 h-40 w-full" />
            </div>
            <!-- Loading state -->
          {:else if streamingJobs.length === 0}
            <!-- No jobs state -->
            <div class="flex h-full items-center justify-center">
              <div class="text-center">
                <p class="text-gray-600">No jobs found in database</p>
              </div>
            </div>
          {:else}
            <div class="animate-scroll-up columns-1 gap-3 sm:columns-2 lg:columns-3 xl:columns-4">
              {#each streamingJobs as job (job.uniqueKey)}
                {@const currentStatus = getJobStatus(job.age || 0)}
                {@const Icon = currentStatus.icon}
                <Card.Root
                  class="mb-2 break-inside-avoid gap-0 p-0 !shadow-none transition-all duration-500 ease-in-out"
                  style="animation-delay: {job.age * 100}ms">
                  <Card.Header class="border-border border-b !p-2">
                    <div class="flex items-start gap-3">
                      <div class="h-8 w-8 overflow-hidden rounded-lg">
                        {#if getCompanyLogo(job?.companyRelation?.logoUrl)}
                          <!-- Display R2 company logo -->
                          <img
                            src={getCompanyLogo(job?.companyRelation?.logoUrl)}
                            alt="{job.company} logo"
                            class="h-full w-full bg-white object-contain p-0.5"
                            loading="lazy"
                            onerror={(e) => {
                              const img = e.target as HTMLImageElement;
                              const fallback = img.nextElementSibling as HTMLElement;
                              img.style.display = 'none';
                              if (fallback) fallback.style.display = 'flex';
                            }} />
                          <!-- Fallback to colored initial (hidden by default) -->
                          <div
                            class="hidden h-full w-full items-center justify-center {job.logoColor} text-xs font-bold text-white">
                            {job.companyInitial}
                          </div>
                        {:else}
                          <!-- Fallback to colored initial -->
                          <div
                            class="flex h-full w-full items-center justify-center {job.logoColor} text-xs font-bold text-white">
                            {job.companyInitial}
                          </div>
                        {/if}
                      </div>
                      <div class="min-w-0 flex-1">
                        <div class="text-sm font-semibold text-gray-900">{job.role}</div>
                        <div class="text-xs text-gray-600">{job.company}</div>
                        <div class="mt-1 flex flex-wrap gap-1 text-xs">
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700"
                            >{job.industry}</span>
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700"
                            >{job.seniority}</span>
                        </div>
                        <div class="mt-1 text-xs text-gray-500">
                          <div>{job.location} • {job.salary}</div>
                        </div>
                      </div>
                    </div>
                  </Card.Header>
                  <Card.Content class="!p-2">
                    {#if job.description}
                      <div class="mt-2 text-xs text-gray-600">
                        {job.description}
                      </div>
                    {/if}
                    {#if job.benefits && job.benefits.length > 0}
                      <div class="mt-2 flex flex-wrap gap-1">
                        {#each job.benefits as benefit}
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-700">
                            {benefit}
                          </span>
                        {/each}
                      </div>
                    {/if}
                  </Card.Content>
                  <Card.Footer
                    class="border-border flex items-center justify-between border-t !p-2">
                    <div class="flex items-center gap-1.5">
                      <div
                        class="flex h-4 w-4 items-center justify-center rounded-full bg-{currentStatus.color}-100">
                        <Icon class="h-3 w-3 text-{currentStatus.color}-600" />
                      </div>
                      <span class="text-xs font-medium text-gray-700">
                        {currentStatus.text}
                      </span>
                    </div>
                    <div
                      class="rounded-full bg-{currentStatus.color}-50 px-2 py-0.5 text-xs font-bold text-{currentStatus.color}-700">
                      {job.matchScore}% match
                    </div>
                  </Card.Footer>
                </Card.Root>
              {/each}
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
</section>
