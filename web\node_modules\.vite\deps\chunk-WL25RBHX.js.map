{"version": 3, "sources": ["../../svelte/src/transition/index.js"], "sourcesContent": ["/** @import { BlurParams, CrossfadeParams, DrawParams, FadeParams, FlyParams, ScaleParams, SlideParams, TransitionConfig } from './public' */\n\nimport { DEV } from 'esm-env';\nimport * as w from '../internal/client/warnings.js';\n\n/** @param {number} x */\nconst linear = (x) => x;\n\n/** @param {number} t */\nfunction cubic_out(t) {\n\tconst f = t - 1.0;\n\treturn f * f * f + 1.0;\n}\n\n/**\n * @param {number} t\n * @returns {number}\n */\nfunction cubic_in_out(t) {\n\treturn t < 0.5 ? 4.0 * t * t * t : 0.5 * Math.pow(2.0 * t - 2.0, 3.0) + 1.0;\n}\n\n/** @param {number | string} value\n * @returns {[number, string]}\n */\nfunction split_css_unit(value) {\n\tconst split = typeof value === 'string' && value.match(/^\\s*(-?[\\d.]+)([^\\s]*)\\s*$/);\n\treturn split ? [parseFloat(split[1]), split[2] || 'px'] : [/** @type {number} */ (value), 'px'];\n}\n\n/**\n * Animates a `blur` filter alongside an element's opacity.\n *\n * @param {Element} node\n * @param {BlurParams} [params]\n * @returns {TransitionConfig}\n */\nexport function blur(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubic_in_out, amount = 5, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst f = style.filter === 'none' ? '' : style.filter;\n\tconst od = target_opacity * (1 - opacity);\n\tconst [value, unit] = split_css_unit(amount);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_t, u) => `opacity: ${target_opacity - od * u}; filter: ${f} blur(${u * value}${unit});`\n\t};\n}\n\n/**\n * Animates the opacity of an element from 0 to the current opacity for `in` transitions and from the current opacity to 0 for `out` transitions.\n *\n * @param {Element} node\n * @param {FadeParams} [params]\n * @returns {TransitionConfig}\n */\nexport function fade(node, { delay = 0, duration = 400, easing = linear } = {}) {\n\tconst o = +getComputedStyle(node).opacity;\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t) => `opacity: ${t * o}`\n\t};\n}\n\n/**\n * Animates the x and y positions and the opacity of an element. `in` transitions animate from the provided values, passed as parameters to the element's default values. `out` transitions animate from the element's default values to the provided values.\n *\n * @param {Element} node\n * @param {FlyParams} [params]\n * @returns {TransitionConfig}\n */\nexport function fly(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubic_out, x = 0, y = 0, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst od = target_opacity * (1 - opacity);\n\tconst [x_value, x_unit] = split_css_unit(x);\n\tconst [y_value, y_unit] = split_css_unit(y);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t, u) => `\n\t\t\ttransform: ${transform} translate(${(1 - t) * x_value}${x_unit}, ${(1 - t) * y_value}${y_unit});\n\t\t\topacity: ${target_opacity - od * u}`\n\t};\n}\n\nvar slide_warning = false;\n\n/**\n * Slides an element in and out.\n *\n * @param {Element} node\n * @param {SlideParams} [params]\n * @returns {TransitionConfig}\n */\nexport function slide(node, { delay = 0, duration = 400, easing = cubic_out, axis = 'y' } = {}) {\n\tconst style = getComputedStyle(node);\n\n\tif (DEV && !slide_warning && /(contents|inline|table)/.test(style.display)) {\n\t\tslide_warning = true;\n\t\tPromise.resolve().then(() => (slide_warning = false));\n\t\tw.transition_slide_display(style.display);\n\t}\n\n\tconst opacity = +style.opacity;\n\tconst primary_property = axis === 'y' ? 'height' : 'width';\n\tconst primary_property_value = parseFloat(style[primary_property]);\n\tconst secondary_properties = axis === 'y' ? ['top', 'bottom'] : ['left', 'right'];\n\tconst capitalized_secondary_properties = secondary_properties.map(\n\t\t(e) => /** @type {'Left' | 'Right' | 'Top' | 'Bottom'} */ (`${e[0].toUpperCase()}${e.slice(1)}`)\n\t);\n\tconst padding_start_value = parseFloat(style[`padding${capitalized_secondary_properties[0]}`]);\n\tconst padding_end_value = parseFloat(style[`padding${capitalized_secondary_properties[1]}`]);\n\tconst margin_start_value = parseFloat(style[`margin${capitalized_secondary_properties[0]}`]);\n\tconst margin_end_value = parseFloat(style[`margin${capitalized_secondary_properties[1]}`]);\n\tconst border_width_start_value = parseFloat(\n\t\tstyle[`border${capitalized_secondary_properties[0]}Width`]\n\t);\n\tconst border_width_end_value = parseFloat(\n\t\tstyle[`border${capitalized_secondary_properties[1]}Width`]\n\t);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t) =>\n\t\t\t'overflow: hidden;' +\n\t\t\t`opacity: ${Math.min(t * 20, 1) * opacity};` +\n\t\t\t`${primary_property}: ${t * primary_property_value}px;` +\n\t\t\t`padding-${secondary_properties[0]}: ${t * padding_start_value}px;` +\n\t\t\t`padding-${secondary_properties[1]}: ${t * padding_end_value}px;` +\n\t\t\t`margin-${secondary_properties[0]}: ${t * margin_start_value}px;` +\n\t\t\t`margin-${secondary_properties[1]}: ${t * margin_end_value}px;` +\n\t\t\t`border-${secondary_properties[0]}-width: ${t * border_width_start_value}px;` +\n\t\t\t`border-${secondary_properties[1]}-width: ${t * border_width_end_value}px;` +\n\t\t\t`min-${primary_property}: 0`\n\t};\n}\n\n/**\n * Animates the opacity and scale of an element. `in` transitions animate from the provided values, passed as parameters, to an element's current (default) values. `out` transitions animate from an element's default values to the provided values.\n *\n * @param {Element} node\n * @param {ScaleParams} [params]\n * @returns {TransitionConfig}\n */\nexport function scale(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubic_out, start = 0, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst sd = 1 - start;\n\tconst od = target_opacity * (1 - opacity);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_t, u) => `\n\t\t\ttransform: ${transform} scale(${1 - sd * u});\n\t\t\topacity: ${target_opacity - od * u}\n\t\t`\n\t};\n}\n\n/**\n * Animates the stroke of an SVG element, like a snake in a tube. `in` transitions begin with the path invisible and draw the path to the screen over time. `out` transitions start in a visible state and gradually erase the path. `draw` only works with elements that have a `getTotalLength` method, like `<path>` and `<polyline>`.\n *\n * @param {SVGElement & { getTotalLength(): number }} node\n * @param {DrawParams} [params]\n * @returns {TransitionConfig}\n */\nexport function draw(node, { delay = 0, speed, duration, easing = cubic_in_out } = {}) {\n\tlet len = node.getTotalLength();\n\tconst style = getComputedStyle(node);\n\tif (style.strokeLinecap !== 'butt') {\n\t\tlen += parseInt(style.strokeWidth);\n\t}\n\tif (duration === undefined) {\n\t\tif (speed === undefined) {\n\t\t\tduration = 800;\n\t\t} else {\n\t\t\tduration = len / speed;\n\t\t}\n\t} else if (typeof duration === 'function') {\n\t\tduration = duration(len);\n\t}\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_, u) => `\n\t\t\tstroke-dasharray: ${len};\n\t\t\tstroke-dashoffset: ${u * len};\n\t\t`\n\t};\n}\n\n/**\n * @template T\n * @template S\n * @param {T} tar\n * @param {S} src\n * @returns {T & S}\n */\nfunction assign(tar, src) {\n\t// @ts-ignore\n\tfor (const k in src) tar[k] = src[k];\n\treturn /** @type {T & S} */ (tar);\n}\n\n/**\n * The `crossfade` function creates a pair of [transitions](https://svelte.dev/docs/svelte/transition) called `send` and `receive`. When an element is 'sent', it looks for a corresponding element being 'received', and generates a transition that transforms the element to its counterpart's position and fades it out. When an element is 'received', the reverse happens. If there is no counterpart, the `fallback` transition is used.\n *\n * @param {CrossfadeParams & {\n * \tfallback?: (node: Element, params: CrossfadeParams, intro: boolean) => TransitionConfig;\n * }} params\n * @returns {[(node: any, params: CrossfadeParams & { key: any; }) => () => TransitionConfig, (node: any, params: CrossfadeParams & { key: any; }) => () => TransitionConfig]}\n */\nexport function crossfade({ fallback, ...defaults }) {\n\t/** @type {Map<any, Element>} */\n\tconst to_receive = new Map();\n\t/** @type {Map<any, Element>} */\n\tconst to_send = new Map();\n\n\t/**\n\t * @param {Element} from_node\n\t * @param {Element} node\n\t * @param {CrossfadeParams} params\n\t * @returns {TransitionConfig}\n\t */\n\tfunction crossfade(from_node, node, params) {\n\t\tconst {\n\t\t\tdelay = 0,\n\t\t\tduration = /** @param {number} d */ (d) => Math.sqrt(d) * 30,\n\t\t\teasing = cubic_out\n\t\t} = assign(assign({}, defaults), params);\n\t\tconst from = from_node.getBoundingClientRect();\n\t\tconst to = node.getBoundingClientRect();\n\t\tconst dx = from.left - to.left;\n\t\tconst dy = from.top - to.top;\n\t\tconst dw = from.width / to.width;\n\t\tconst dh = from.height / to.height;\n\t\tconst d = Math.sqrt(dx * dx + dy * dy);\n\t\tconst style = getComputedStyle(node);\n\t\tconst transform = style.transform === 'none' ? '' : style.transform;\n\t\tconst opacity = +style.opacity;\n\t\treturn {\n\t\t\tdelay,\n\t\t\tduration: typeof duration === 'function' ? duration(d) : duration,\n\t\t\teasing,\n\t\t\tcss: (t, u) => `\n\t\t\t   opacity: ${t * opacity};\n\t\t\t   transform-origin: top left;\n\t\t\t   transform: ${transform} translate(${u * dx}px,${u * dy}px) scale(${t + (1 - t) * dw}, ${\n\t\t\t\t\t\tt + (1 - t) * dh\n\t\t\t\t\t});\n\t\t   `\n\t\t};\n\t}\n\n\t/**\n\t * @param {Map<any, Element>} items\n\t * @param {Map<any, Element>} counterparts\n\t * @param {boolean} intro\n\t * @returns {(node: any, params: CrossfadeParams & { key: any; }) => () => TransitionConfig}\n\t */\n\tfunction transition(items, counterparts, intro) {\n\t\t// @ts-expect-error TODO improve typings (are the public types wrong?)\n\t\treturn (node, params) => {\n\t\t\titems.set(params.key, node);\n\t\t\treturn () => {\n\t\t\t\tif (counterparts.has(params.key)) {\n\t\t\t\t\tconst other_node = counterparts.get(params.key);\n\t\t\t\t\tcounterparts.delete(params.key);\n\t\t\t\t\treturn crossfade(/** @type {Element} */ (other_node), node, params);\n\t\t\t\t}\n\t\t\t\t// if the node is disappearing altogether\n\t\t\t\t// (i.e. wasn't claimed by the other list)\n\t\t\t\t// then we need to supply an outro\n\t\t\t\titems.delete(params.key);\n\t\t\t\treturn fallback && fallback(node, params, intro);\n\t\t\t};\n\t\t};\n\t}\n\treturn [transition(to_send, to_receive, false), transition(to_receive, to_send, true)];\n}\n"], "mappings": ";;;;;;;;AAMA,IAAM,SAAS,CAAC,MAAM;AAGtB,SAAS,UAAU,GAAG;AACrB,QAAM,IAAI,IAAI;AACd,SAAO,IAAI,IAAI,IAAI;AACpB;AAMA,SAAS,aAAa,GAAG;AACxB,SAAO,IAAI,MAAM,IAAM,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,IAAM,IAAI,GAAK,CAAG,IAAI;AACzE;AAKA,SAAS,eAAe,OAAO;AAC9B,QAAM,QAAQ,OAAO,UAAU,YAAY,MAAM,MAAM,4BAA4B;AACnF,SAAO,QAAQ,CAAC,WAAW,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI;AAAA;AAAA,IAAwB;AAAA,IAAQ;AAAA,EAAI;AAC/F;AASO,SAAS,KACf,MACA,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,cAAc,SAAS,GAAG,UAAU,EAAE,IAAI,CAAC,GAChF;AACD,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,IAAI,MAAM,WAAW,SAAS,KAAK,MAAM;AAC/C,QAAM,KAAK,kBAAkB,IAAI;AACjC,QAAM,CAAC,OAAO,IAAI,IAAI,eAAe,MAAM;AAC3C,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,IAAI,MAAM,YAAY,iBAAiB,KAAK,CAAC,aAAa,CAAC,SAAS,IAAI,KAAK,GAAG,IAAI;AAAA,EAC3F;AACD;AASO,SAAS,KAAK,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,OAAO,IAAI,CAAC,GAAG;AAC/E,QAAM,IAAI,CAAC,iBAAiB,IAAI,EAAE;AAClC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,MAAM,YAAY,IAAI,CAAC;AAAA,EAC9B;AACD;AASO,SAAS,IACf,MACA,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,WAAW,IAAI,GAAG,IAAI,GAAG,UAAU,EAAE,IAAI,CAAC,GAC/E;AACD,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,KAAK,kBAAkB,IAAI;AACjC,QAAM,CAAC,SAAS,MAAM,IAAI,eAAe,CAAC;AAC1C,QAAM,CAAC,SAAS,MAAM,IAAI,eAAe,CAAC;AAC1C,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AAAA,gBACD,SAAS,eAAe,IAAI,KAAK,OAAO,GAAG,MAAM,MAAM,IAAI,KAAK,OAAO,GAAG,MAAM;AAAA,cAClF,iBAAiB,KAAK,CAAC;AAAA,EACpC;AACD;AAEA,IAAI,gBAAgB;AASb,SAAS,MAAM,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,WAAW,OAAO,IAAI,IAAI,CAAC,GAAG;AAC/F,QAAM,QAAQ,iBAAiB,IAAI;AAEnC,MAAI,gBAAO,CAAC,iBAAiB,0BAA0B,KAAK,MAAM,OAAO,GAAG;AAC3E,oBAAgB;AAChB,YAAQ,QAAQ,EAAE,KAAK,MAAO,gBAAgB,KAAM;AACpD,IAAE,yBAAyB,MAAM,OAAO;AAAA,EACzC;AAEA,QAAM,UAAU,CAAC,MAAM;AACvB,QAAM,mBAAmB,SAAS,MAAM,WAAW;AACnD,QAAM,yBAAyB,WAAW,MAAM,gBAAgB,CAAC;AACjE,QAAM,uBAAuB,SAAS,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,QAAQ,OAAO;AAChF,QAAM,mCAAmC,qBAAqB;AAAA,IAC7D,CAAC;AAAA;AAAA,MAA0D,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA,EAC9F;AACA,QAAM,sBAAsB,WAAW,MAAM,UAAU,iCAAiC,CAAC,CAAC,EAAE,CAAC;AAC7F,QAAM,oBAAoB,WAAW,MAAM,UAAU,iCAAiC,CAAC,CAAC,EAAE,CAAC;AAC3F,QAAM,qBAAqB,WAAW,MAAM,SAAS,iCAAiC,CAAC,CAAC,EAAE,CAAC;AAC3F,QAAM,mBAAmB,WAAW,MAAM,SAAS,iCAAiC,CAAC,CAAC,EAAE,CAAC;AACzF,QAAM,2BAA2B;AAAA,IAChC,MAAM,SAAS,iCAAiC,CAAC,CAAC,OAAO;AAAA,EAC1D;AACA,QAAM,yBAAyB;AAAA,IAC9B,MAAM,SAAS,iCAAiC,CAAC,CAAC,OAAO;AAAA,EAC1D;AACA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,MACL,6BACY,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,OAAO,IACtC,gBAAgB,KAAK,IAAI,sBAAsB,cACvC,qBAAqB,CAAC,CAAC,KAAK,IAAI,mBAAmB,cACnD,qBAAqB,CAAC,CAAC,KAAK,IAAI,iBAAiB,aAClD,qBAAqB,CAAC,CAAC,KAAK,IAAI,kBAAkB,aAClD,qBAAqB,CAAC,CAAC,KAAK,IAAI,gBAAgB,aAChD,qBAAqB,CAAC,CAAC,WAAW,IAAI,wBAAwB,aAC9D,qBAAqB,CAAC,CAAC,WAAW,IAAI,sBAAsB,UAC/D,gBAAgB;AAAA,EACzB;AACD;AASO,SAAS,MACf,MACA,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,WAAW,QAAQ,GAAG,UAAU,EAAE,IAAI,CAAC,GAC5E;AACD,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,kBAAkB,IAAI;AACjC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,IAAI,MAAM;AAAA,gBACF,SAAS,UAAU,IAAI,KAAK,CAAC;AAAA,cAC/B,iBAAiB,KAAK,CAAC;AAAA;AAAA,EAEpC;AACD;AASO,SAAS,KAAK,MAAM,EAAE,QAAQ,GAAG,OAAO,UAAU,SAAS,aAAa,IAAI,CAAC,GAAG;AACtF,MAAI,MAAM,KAAK,eAAe;AAC9B,QAAM,QAAQ,iBAAiB,IAAI;AACnC,MAAI,MAAM,kBAAkB,QAAQ;AACnC,WAAO,SAAS,MAAM,WAAW;AAAA,EAClC;AACA,MAAI,aAAa,QAAW;AAC3B,QAAI,UAAU,QAAW;AACxB,iBAAW;AAAA,IACZ,OAAO;AACN,iBAAW,MAAM;AAAA,IAClB;AAAA,EACD,WAAW,OAAO,aAAa,YAAY;AAC1C,eAAW,SAAS,GAAG;AAAA,EACxB;AACA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AAAA,uBACM,GAAG;AAAA,wBACF,IAAI,GAAG;AAAA;AAAA,EAE9B;AACD;AASA,SAAS,OAAO,KAAK,KAAK;AAEzB,aAAW,KAAK,IAAK,KAAI,CAAC,IAAI,IAAI,CAAC;AACnC;AAAA;AAAA,IAA6B;AAAA;AAC9B;AAUO,SAAS,UAAU,EAAE,UAAU,GAAG,SAAS,GAAG;AAEpD,QAAM,aAAa,oBAAI,IAAI;AAE3B,QAAM,UAAU,oBAAI,IAAI;AAQxB,WAASA,WAAU,WAAW,MAAM,QAAQ;AAC3C,UAAM;AAAA,MACL,QAAQ;AAAA,MACR;AAAA;AAAA,QAAoC,CAACC,OAAM,KAAK,KAAKA,EAAC,IAAI;AAAA;AAAA,MAC1D,SAAS;AAAA,IACV,IAAI,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG,MAAM;AACvC,UAAM,OAAO,UAAU,sBAAsB;AAC7C,UAAM,KAAK,KAAK,sBAAsB;AACtC,UAAM,KAAK,KAAK,OAAO,GAAG;AAC1B,UAAM,KAAK,KAAK,MAAM,GAAG;AACzB,UAAM,KAAK,KAAK,QAAQ,GAAG;AAC3B,UAAM,KAAK,KAAK,SAAS,GAAG;AAC5B,UAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACrC,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,UAAM,UAAU,CAAC,MAAM;AACvB,WAAO;AAAA,MACN;AAAA,MACA,UAAU,OAAO,aAAa,aAAa,SAAS,CAAC,IAAI;AAAA,MACzD;AAAA,MACA,KAAK,CAAC,GAAG,MAAM;AAAA,iBACD,IAAI,OAAO;AAAA;AAAA,mBAET,SAAS,cAAc,IAAI,EAAE,MAAM,IAAI,EAAE,aAAa,KAAK,IAAI,KAAK,EAAE,KACnF,KAAK,IAAI,KAAK,EACf;AAAA;AAAA,IAEH;AAAA,EACD;AAQA,WAAS,WAAW,OAAO,cAAc,OAAO;AAE/C,WAAO,CAAC,MAAM,WAAW;AACxB,YAAM,IAAI,OAAO,KAAK,IAAI;AAC1B,aAAO,MAAM;AACZ,YAAI,aAAa,IAAI,OAAO,GAAG,GAAG;AACjC,gBAAM,aAAa,aAAa,IAAI,OAAO,GAAG;AAC9C,uBAAa,OAAO,OAAO,GAAG;AAC9B,iBAAOD;AAAA;AAAA,YAAkC;AAAA,YAAa;AAAA,YAAM;AAAA,UAAM;AAAA,QACnE;AAIA,cAAM,OAAO,OAAO,GAAG;AACvB,eAAO,YAAY,SAAS,MAAM,QAAQ,KAAK;AAAA,MAChD;AAAA,IACD;AAAA,EACD;AACA,SAAO,CAAC,WAAW,SAAS,YAAY,KAAK,GAAG,WAAW,YAAY,SAAS,IAAI,CAAC;AACtF;", "names": ["crossfade", "d"]}