<script>
  // Features array based on actual plan features
  const features = [
    {
      id: 'resume_scanner',
      name: 'Resume Scanner',
      description:
        '<PERSON><PERSON> and analyze your resume against job descriptions. Get detailed feedback and improvement suggestions to pass through ATS systems.',
      mockInterface: {
        type: 'ats_score',
        score: 85,
        metrics: [
          { name: 'Keywords', value: 92, color: 'green' },
          { name: 'Format', value: 78, color: 'yellow' },
          { name: 'Readability', value: 85, color: 'blue' },
        ],
      },
    },
    {
      id: 'one_click_apply',
      name: 'One-Click Apply',
      description:
        'Apply to jobs with a single click using your saved profile and resume. Streamline your job search process.',
      mockInterface: {
        type: 'apply_button',
        jobTitle: 'Senior Developer',
        company: 'TechCorp',
        applied: false,
      },
    },
    {
      id: 'ai_matching',
      name: 'AI Matching',
      description:
        'AI-powered job matching based on your skills and preferences. Find the perfect opportunities tailored to you.',
      mockInterface: {
        type: 'match_score',
        matches: [
          { company: 'Stripe', role: 'Frontend Engineer', match: 95 },
          { company: 'Airbnb', role: 'Full Stack Developer', match: 88 },
          { company: 'Uber', role: 'Software Engineer', match: 82 },
        ],
      },
    },
    {
      id: 'job_alerts',
      name: 'Smart Job Alerts',
      description:
        'Get notified about new job opportunities that match your criteria. Never miss the perfect opportunity.',
      mockInterface: {
        type: 'notifications',
        alerts: [
          { title: '3 new jobs match your criteria', time: '2 hours ago', unread: true },
          { title: 'Application deadline reminder', time: '1 day ago', unread: false },
        ],
      },
    },
  ];
</script>

<section id="features" class="px-4 py-16">
  <div class="mx-auto max-w-[90rem] space-y-8">
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 xl:grid-cols-3">
      {#each features as feature}
        <div class="border-border rounded-3xl border bg-white">
          <!-- Dynamic Mock Interface -->
          <div class="p-4">
            {#if feature.mockInterface.type === 'ats_score'}
              <div class="space-y-3">
                <!-- ATS Score -->
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">
                    {feature.mockInterface.score}/100
                  </div>
                  <div class="text-xs text-gray-500">ATS Score</div>
                </div>

                <!-- Progress bars -->
                <div class="space-y-2">
                  {#each feature.mockInterface.metrics as metric}
                    <div>
                      <div class="mb-1 flex justify-between text-xs">
                        <span>{metric.name}</span>
                        <span>{metric.value}%</span>
                      </div>
                      <div class="h-1.5 w-full rounded-full bg-gray-200">
                        <div
                          class="h-1.5 rounded-full bg-{metric.color}-500"
                          style="width: {metric.value}%">
                        </div>
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {:else if feature.mockInterface.type === 'application_list'}
              <div class="space-y-2">
                {#each feature.mockInterface.applications as app}
                  <div class="flex items-center justify-between rounded-lg bg-white p-2 shadow-sm">
                    <div class="flex items-center space-x-2">
                      <div
                        class="flex h-6 w-6 items-center justify-center rounded-full bg-{app.color}-500">
                        <span class="text-xs font-bold text-white">{app.company.charAt(0)}</span>
                      </div>
                      <div>
                        <div class="text-xs font-medium">{app.company}</div>
                        <div class="text-xs text-gray-500">{app.role}</div>
                      </div>
                    </div>
                    <span
                      class="rounded-full bg-{app.color}-100 px-2 py-0.5 text-xs font-medium text-{app.color}-800"
                      >{app.status}</span>
                  </div>
                {/each}
              </div>
            {:else if feature.mockInterface.type === 'apply_button'}
              <div class="space-y-3">
                <div class="rounded-lg bg-white p-3 shadow-sm">
                  <div class="text-sm font-medium">{feature.mockInterface.jobTitle}</div>
                  <div class="text-xs text-gray-500">{feature.mockInterface.company}</div>
                </div>
                <button
                  class="w-full rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
                  Apply Now
                </button>
              </div>
            {:else if feature.mockInterface.type === 'match_score'}
              <div class="space-y-2">
                {#each feature.mockInterface.matches as match}
                  <div class="flex items-center justify-between rounded-lg bg-white p-2 shadow-sm">
                    <div>
                      <div class="text-xs font-medium">{match.company}</div>
                      <div class="text-xs text-gray-500">{match.role}</div>
                    </div>
                    <div class="text-right">
                      <div class="text-sm font-bold text-green-600">{match.match}%</div>
                      <div class="text-xs text-gray-500">Match</div>
                    </div>
                  </div>
                {/each}
              </div>
            {:else if feature.mockInterface.type === 'resume_templates'}
              <div class="space-y-2">
                {#each feature.mockInterface.templates as template}
                  <div class="flex items-center justify-between rounded-lg bg-white p-2 shadow-sm">
                    <span class="text-xs font-medium">{template.name}</span>
                    <div
                      class="h-4 w-4 rounded-full {template.selected
                        ? 'bg-blue-600'
                        : 'bg-gray-300'}">
                    </div>
                  </div>
                {/each}
              </div>
            {:else if feature.mockInterface.type === 'notifications'}
              <div class="space-y-2">
                {#each feature.mockInterface.alerts as alert}
                  <div class="rounded-lg bg-white p-2 shadow-sm">
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <div
                          class="text-xs font-medium {alert.unread
                            ? 'text-gray-900'
                            : 'text-gray-600'}">
                          {alert.title}
                        </div>
                        <div class="text-xs text-gray-500">{alert.time}</div>
                      </div>
                      {#if alert.unread}
                        <div class="h-2 w-2 rounded-full bg-blue-600"></div>
                      {/if}
                    </div>
                  </div>
                {/each}
              </div>
            {/if}
          </div>
          <div class="space-y-6 px-[4rem] py-[3rem]">
            <!-- Header -->
            <div>
              <h3 class="!font-lighter text-3xl text-gray-900">{feature.name}</h3>
              <p class="mt-8 text-gray-600">{feature.description}</p>
            </div>
          </div>
        </div>
      {/each}
    </div>
  </div>
</section>
