import {
  DirectiveLocation,
  GraphQLBoolean,
  GraphQLDeprecatedDirective,
  GraphQLDirective,
  GraphQLEnumType,
  GraphQLError,
  GraphQLFloat,
  GraphQLID,
  GraphQLIncludeDirective,
  GraphQLInputObjectType,
  GraphQLInt,
  GraphQLInterfaceType,
  GraphQLList,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLScalarType,
  GraphQLSchema,
  GraphQLSkipDirective,
  GraphQLString,
  GraphQLUnionType,
  Kind,
  Source,
  TokenKind,
  buildASTSchema,
  buildSchema,
  defaultFieldResolver,
  getDirectiveValues,
  getNamedType,
  getNullableType,
  getOperationAST,
  init_graphql,
  isAbstractType,
  isDefinitionNode,
  isEnumType,
  isInputObjectType,
  isInterfaceType,
  isIntrospectionType,
  isLeafType,
  isListType,
  isNamedType,
  isNonNullType,
  isObjectType,
  isScalarType,
  isSchema,
  isSpecifiedDirective,
  isSpecifiedScalarType,
  isUnionType,
  parse,
  print,
  typeFromAST,
  valueFromAST,
  valueFromASTUntyped,
  versionInfo,
  visit
} from "./chunk-Y7PVUKRV.js";
import "./chunk-UQOTJTBP.js";

// node_modules/@graphql-tools/schema/esm/assertResolversPresent.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/helpers.js
init_graphql();
var asArray = (fns) => Array.isArray(fns) ? fns : fns ? [fns] : [];
function compareStrings(a, b) {
  if (String(a) < String(b)) {
    return -1;
  }
  if (String(a) > String(b)) {
    return 1;
  }
  return 0;
}
function nodeToString(a) {
  var _a, _b;
  let name;
  if ("alias" in a) {
    name = (_a = a.alias) == null ? void 0 : _a.value;
  }
  if (name == null && "name" in a) {
    name = (_b = a.name) == null ? void 0 : _b.value;
  }
  if (name == null) {
    name = a.kind;
  }
  return name;
}
function compareNodes(a, b, customFn) {
  const aStr = nodeToString(a);
  const bStr = nodeToString(b);
  if (typeof customFn === "function") {
    return customFn(aStr, bStr);
  }
  return compareStrings(aStr, bStr);
}
function isSome(input) {
  return input != null;
}

// node_modules/@graphql-tools/utils/esm/getDirectiveExtensions.js
init_graphql();

// node_modules/cross-inspect/esm/index.js
var MAX_RECURSIVE_DEPTH = 3;
function inspect(value) {
  return formatValue(value, []);
}
function formatValue(value, seenValues) {
  switch (typeof value) {
    case "string":
      return JSON.stringify(value);
    case "function":
      return value.name ? `[function ${value.name}]` : "[function]";
    case "object":
      return formatObjectValue(value, seenValues);
    default:
      return String(value);
  }
}
function formatError(value) {
  if (value.name = "GraphQLError") {
    return value.toString();
  }
  return `${value.name}: ${value.message};
 ${value.stack}`;
}
function formatObjectValue(value, previouslySeenValues) {
  if (value === null) {
    return "null";
  }
  if (value instanceof Error) {
    if (value.name === "AggregateError") {
      return formatError(value) + "\n" + formatArray(value.errors, previouslySeenValues);
    }
    return formatError(value);
  }
  if (previouslySeenValues.includes(value)) {
    return "[Circular]";
  }
  const seenValues = [...previouslySeenValues, value];
  if (isJSONable(value)) {
    const jsonValue = value.toJSON();
    if (jsonValue !== value) {
      return typeof jsonValue === "string" ? jsonValue : formatValue(jsonValue, seenValues);
    }
  } else if (Array.isArray(value)) {
    return formatArray(value, seenValues);
  }
  return formatObject(value, seenValues);
}
function isJSONable(value) {
  return typeof value.toJSON === "function";
}
function formatObject(object, seenValues) {
  const entries = Object.entries(object);
  if (entries.length === 0) {
    return "{}";
  }
  if (seenValues.length > MAX_RECURSIVE_DEPTH) {
    return "[" + getObjectTag(object) + "]";
  }
  const properties = entries.map(([key, value]) => key + ": " + formatValue(value, seenValues));
  return "{ " + properties.join(", ") + " }";
}
function formatArray(array, seenValues) {
  if (array.length === 0) {
    return "[]";
  }
  if (seenValues.length > MAX_RECURSIVE_DEPTH) {
    return "[Array]";
  }
  const len = array.length;
  const items = [];
  for (let i = 0; i < len; ++i) {
    items.push(formatValue(array[i], seenValues));
  }
  return "[" + items.join(", ") + "]";
}
function getObjectTag(object) {
  const tag = Object.prototype.toString.call(object).replace(/^\[object /, "").replace(/]$/, "");
  if (tag === "Object" && typeof object.constructor === "function") {
    const name = object.constructor.name;
    if (typeof name === "string" && name !== "") {
      return name;
    }
  }
  return tag;
}

// node_modules/@graphql-tools/utils/esm/getArgumentValues.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/errors.js
init_graphql();
var possibleGraphQLErrorProperties = [
  "message",
  "locations",
  "path",
  "nodes",
  "source",
  "positions",
  "originalError",
  "name",
  "stack",
  "extensions"
];
function isGraphQLErrorLike(error) {
  return error != null && typeof error === "object" && Object.keys(error).every((key) => possibleGraphQLErrorProperties.includes(key));
}
function createGraphQLError(message, options) {
  if ((options == null ? void 0 : options.originalError) && !(options.originalError instanceof Error) && isGraphQLErrorLike(options.originalError)) {
    options.originalError = createGraphQLError(options.originalError.message, options.originalError);
  }
  if (versionInfo.major >= 17) {
    return new GraphQLError(message, options);
  }
  return new GraphQLError(message, options == null ? void 0 : options.nodes, options == null ? void 0 : options.source, options == null ? void 0 : options.positions, options == null ? void 0 : options.path, options == null ? void 0 : options.originalError, options == null ? void 0 : options.extensions);
}

// node_modules/@whatwg-node/promise-helpers/esm/index.js
var kFakePromise = Symbol.for("@whatwg-node/promise-helpers/FakePromise");
function isPromise(value) {
  return (value == null ? void 0 : value.then) != null;
}
function isActualPromise(value) {
  const maybePromise = value;
  return maybePromise && maybePromise.then && maybePromise.catch && maybePromise.finally;
}
function fakePromise(value) {
  if (value && isActualPromise(value)) {
    return value;
  }
  if (isPromise(value)) {
    return {
      then: (resolve, reject) => fakePromise(value.then(resolve, reject)),
      catch: (reject) => fakePromise(value.then((res) => res, reject)),
      finally: (cb) => fakePromise(cb ? promiseLikeFinally(value, cb) : value),
      [Symbol.toStringTag]: "Promise"
    };
  }
  return {
    then(resolve) {
      if (resolve) {
        try {
          return fakePromise(resolve(value));
        } catch (err) {
          return fakeRejectPromise(err);
        }
      }
      return this;
    },
    catch() {
      return this;
    },
    finally(cb) {
      if (cb) {
        try {
          return fakePromise(cb()).then(() => value, () => value);
        } catch (err) {
          return fakeRejectPromise(err);
        }
      }
      return this;
    },
    [Symbol.toStringTag]: "Promise",
    __fakePromiseValue: value,
    [kFakePromise]: "resolved"
  };
}
function fakeRejectPromise(error) {
  return {
    then(_resolve, reject) {
      if (reject) {
        try {
          return fakePromise(reject(error));
        } catch (err) {
          return fakeRejectPromise(err);
        }
      }
      return this;
    },
    catch(reject) {
      if (reject) {
        try {
          return fakePromise(reject(error));
        } catch (err) {
          return fakeRejectPromise(err);
        }
      }
      return this;
    },
    finally(cb) {
      if (cb) {
        try {
          cb();
        } catch (err) {
          return fakeRejectPromise(err);
        }
      }
      return this;
    },
    __fakeRejectError: error,
    [Symbol.toStringTag]: "Promise",
    [kFakePromise]: "rejected"
  };
}
function promiseLikeFinally(value, onFinally) {
  if ("finally" in value) {
    return value.finally(onFinally);
  }
  return value.then((res) => {
    const finallyRes = onFinally();
    return isPromise(finallyRes) ? finallyRes.then(() => res) : res;
  }, (err) => {
    const finallyRes = onFinally();
    if (isPromise(finallyRes)) {
      return finallyRes.then(() => {
        throw err;
      });
    } else {
      throw err;
    }
  });
}

// node_modules/@graphql-tools/utils/esm/jsutils.js
function isIterableObject(value) {
  return value != null && typeof value === "object" && Symbol.iterator in value;
}
function isObjectLike(value) {
  return typeof value === "object" && value !== null;
}
function hasOwnProperty(obj, prop) {
  return Object.prototype.hasOwnProperty.call(obj, prop);
}

// node_modules/@graphql-tools/utils/esm/getArgumentValues.js
function getArgumentValues(def, node, variableValues = {}) {
  const coercedValues = {};
  const argumentNodes = node.arguments ?? [];
  const argNodeMap = argumentNodes.reduce((prev, arg) => ({
    ...prev,
    [arg.name.value]: arg
  }), {});
  for (const { name, type: argType, defaultValue } of def.args) {
    const argumentNode = argNodeMap[name];
    if (!argumentNode) {
      if (defaultValue !== void 0) {
        coercedValues[name] = defaultValue;
      } else if (isNonNullType(argType)) {
        throw createGraphQLError(`Argument "${name}" of required type "${inspect(argType)}" was not provided.`, {
          nodes: [node]
        });
      }
      continue;
    }
    const valueNode = argumentNode.value;
    let isNull = valueNode.kind === Kind.NULL;
    if (valueNode.kind === Kind.VARIABLE) {
      const variableName = valueNode.name.value;
      if (variableValues == null || !hasOwnProperty(variableValues, variableName)) {
        if (defaultValue !== void 0) {
          coercedValues[name] = defaultValue;
        } else if (isNonNullType(argType)) {
          throw createGraphQLError(`Argument "${name}" of required type "${inspect(argType)}" was provided the variable "$${variableName}" which was not provided a runtime value.`, {
            nodes: [valueNode]
          });
        }
        continue;
      }
      isNull = variableValues[variableName] == null;
    }
    if (isNull && isNonNullType(argType)) {
      throw createGraphQLError(`Argument "${name}" of non-null type "${inspect(argType)}" must not be null.`, {
        nodes: [valueNode]
      });
    }
    const coercedValue = valueFromAST(valueNode, argType, variableValues);
    if (coercedValue === void 0) {
      throw createGraphQLError(`Argument "${name}" has invalid value ${print(valueNode)}.`, {
        nodes: [valueNode]
      });
    }
    coercedValues[name] = coercedValue;
  }
  return coercedValues;
}

// node_modules/@graphql-tools/utils/esm/memoize.js
function memoize1(fn) {
  const memoize1cache = /* @__PURE__ */ new WeakMap();
  return function memoized(a1) {
    const cachedValue = memoize1cache.get(a1);
    if (cachedValue === void 0) {
      const newValue = fn(a1);
      memoize1cache.set(a1, newValue);
      return newValue;
    }
    return cachedValue;
  };
}
function memoize2(fn) {
  const memoize2cache = /* @__PURE__ */ new WeakMap();
  return function memoized(a1, a2) {
    let cache2 = memoize2cache.get(a1);
    if (!cache2) {
      cache2 = /* @__PURE__ */ new WeakMap();
      memoize2cache.set(a1, cache2);
      const newValue = fn(a1, a2);
      cache2.set(a2, newValue);
      return newValue;
    }
    const cachedValue = cache2.get(a2);
    if (cachedValue === void 0) {
      const newValue = fn(a1, a2);
      cache2.set(a2, newValue);
      return newValue;
    }
    return cachedValue;
  };
}
function memoize5(fn) {
  const memoize5Cache = /* @__PURE__ */ new WeakMap();
  return function memoized(a1, a2, a3, a4, a5) {
    let cache2 = memoize5Cache.get(a1);
    if (!cache2) {
      cache2 = /* @__PURE__ */ new WeakMap();
      memoize5Cache.set(a1, cache2);
      const cache32 = /* @__PURE__ */ new WeakMap();
      cache2.set(a2, cache32);
      const cache42 = /* @__PURE__ */ new WeakMap();
      cache32.set(a3, cache42);
      const cache52 = /* @__PURE__ */ new WeakMap();
      cache42.set(a4, cache52);
      const newValue = fn(a1, a2, a3, a4, a5);
      cache52.set(a5, newValue);
      return newValue;
    }
    let cache3 = cache2.get(a2);
    if (!cache3) {
      cache3 = /* @__PURE__ */ new WeakMap();
      cache2.set(a2, cache3);
      const cache42 = /* @__PURE__ */ new WeakMap();
      cache3.set(a3, cache42);
      const cache52 = /* @__PURE__ */ new WeakMap();
      cache42.set(a4, cache52);
      const newValue = fn(a1, a2, a3, a4, a5);
      cache52.set(a5, newValue);
      return newValue;
    }
    let cache4 = cache3.get(a3);
    if (!cache4) {
      cache4 = /* @__PURE__ */ new WeakMap();
      cache3.set(a3, cache4);
      const cache52 = /* @__PURE__ */ new WeakMap();
      cache4.set(a4, cache52);
      const newValue = fn(a1, a2, a3, a4, a5);
      cache52.set(a5, newValue);
      return newValue;
    }
    let cache5 = cache4.get(a4);
    if (!cache5) {
      cache5 = /* @__PURE__ */ new WeakMap();
      cache4.set(a4, cache5);
      const newValue = fn(a1, a2, a3, a4, a5);
      cache5.set(a5, newValue);
      return newValue;
    }
    const cachedValue = cache5.get(a5);
    if (cachedValue === void 0) {
      const newValue = fn(a1, a2, a3, a4, a5);
      cache5.set(a5, newValue);
      return newValue;
    }
    return cachedValue;
  };
}

// node_modules/@graphql-tools/utils/esm/getDirectiveExtensions.js
function getDirectiveExtensions(directableObj, schema, pathToDirectivesInExtensions = ["directives"]) {
  var _a;
  const directiveExtensions = {};
  if (directableObj.extensions) {
    let directivesInExtensions = directableObj.extensions;
    for (const pathSegment of pathToDirectivesInExtensions) {
      directivesInExtensions = directivesInExtensions == null ? void 0 : directivesInExtensions[pathSegment];
    }
    if (directivesInExtensions != null) {
      for (const directiveNameProp in directivesInExtensions) {
        const directiveObjs = directivesInExtensions[directiveNameProp];
        const directiveName = directiveNameProp;
        if (Array.isArray(directiveObjs)) {
          for (const directiveObj of directiveObjs) {
            let existingDirectiveExtensions = directiveExtensions[directiveName];
            if (!existingDirectiveExtensions) {
              existingDirectiveExtensions = [];
              directiveExtensions[directiveName] = existingDirectiveExtensions;
            }
            existingDirectiveExtensions.push(directiveObj);
          }
        } else {
          let existingDirectiveExtensions = directiveExtensions[directiveName];
          if (!existingDirectiveExtensions) {
            existingDirectiveExtensions = [];
            directiveExtensions[directiveName] = existingDirectiveExtensions;
          }
          existingDirectiveExtensions.push(directiveObjs);
        }
      }
    }
  }
  const memoizedStringify = memoize1((obj) => JSON.stringify(obj));
  const astNodes = [];
  if (directableObj.astNode) {
    astNodes.push(directableObj.astNode);
  }
  if (directableObj.extensionASTNodes) {
    astNodes.push(...directableObj.extensionASTNodes);
  }
  for (const astNode of astNodes) {
    if ((_a = astNode.directives) == null ? void 0 : _a.length) {
      for (const directive of astNode.directives) {
        const directiveName = directive.name.value;
        let existingDirectiveExtensions = directiveExtensions[directiveName];
        if (!existingDirectiveExtensions) {
          existingDirectiveExtensions = [];
          directiveExtensions[directiveName] = existingDirectiveExtensions;
        }
        const directiveInSchema = schema == null ? void 0 : schema.getDirective(directiveName);
        let value = {};
        if (directiveInSchema) {
          value = getArgumentValues(directiveInSchema, directive);
        }
        if (directive.arguments) {
          for (const argNode of directive.arguments) {
            const argName = argNode.name.value;
            if (value[argName] == null) {
              const argInDirective = directiveInSchema == null ? void 0 : directiveInSchema.args.find((arg) => arg.name === argName);
              if (argInDirective) {
                value[argName] = valueFromAST(argNode.value, argInDirective.type);
              }
            }
            if (value[argName] == null) {
              value[argName] = valueFromASTUntyped(argNode.value);
            }
          }
        }
        if (astNodes.length > 0 && existingDirectiveExtensions.length > 0) {
          const valStr = memoizedStringify(value);
          if (existingDirectiveExtensions.some((val) => memoizedStringify(val) === valStr)) {
            continue;
          }
        }
        existingDirectiveExtensions.push(value);
      }
    }
  }
  return directiveExtensions;
}

// node_modules/@graphql-tools/utils/esm/get-directives.js
function getDirectivesInExtensions(node, pathToDirectivesInExtensions = ["directives"]) {
  const directiveExtensions = getDirectiveExtensions(node, void 0, pathToDirectivesInExtensions);
  return Object.entries(directiveExtensions).map(([directiveName, directiveArgsArr]) => directiveArgsArr == null ? void 0 : directiveArgsArr.map((directiveArgs) => ({
    name: directiveName,
    args: directiveArgs
  }))).flat(Infinity).filter(Boolean);
}

// node_modules/@graphql-tools/utils/esm/get-fields-with-directives.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/get-arguments-with-directives.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/print-schema-with-directives.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/astFromType.js
init_graphql();
function astFromType(type) {
  if (isNonNullType(type)) {
    const innerType = astFromType(type.ofType);
    if (innerType.kind === Kind.NON_NULL_TYPE) {
      throw new Error(`Invalid type node ${inspect(type)}. Inner type of non-null type cannot be a non-null type.`);
    }
    return {
      kind: Kind.NON_NULL_TYPE,
      type: innerType
    };
  } else if (isListType(type)) {
    return {
      kind: Kind.LIST_TYPE,
      type: astFromType(type.ofType)
    };
  }
  return {
    kind: Kind.NAMED_TYPE,
    name: {
      kind: Kind.NAME,
      value: type.name
    }
  };
}

// node_modules/@graphql-tools/utils/esm/astFromValue.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/astFromValueUntyped.js
init_graphql();
function astFromValueUntyped(value) {
  if (value === null) {
    return { kind: Kind.NULL };
  }
  if (value === void 0) {
    return null;
  }
  if (Array.isArray(value)) {
    const valuesNodes = [];
    for (const item of value) {
      const itemNode = astFromValueUntyped(item);
      if (itemNode != null) {
        valuesNodes.push(itemNode);
      }
    }
    return { kind: Kind.LIST, values: valuesNodes };
  }
  if (typeof value === "object") {
    if (value == null ? void 0 : value.toJSON) {
      return astFromValueUntyped(value.toJSON());
    }
    const fieldNodes = [];
    for (const fieldName in value) {
      const fieldValue = value[fieldName];
      const ast = astFromValueUntyped(fieldValue);
      if (ast) {
        fieldNodes.push({
          kind: Kind.OBJECT_FIELD,
          name: { kind: Kind.NAME, value: fieldName },
          value: ast
        });
      }
    }
    return { kind: Kind.OBJECT, fields: fieldNodes };
  }
  if (typeof value === "boolean") {
    return { kind: Kind.BOOLEAN, value };
  }
  if (typeof value === "bigint") {
    return { kind: Kind.INT, value: String(value) };
  }
  if (typeof value === "number" && isFinite(value)) {
    const stringNum = String(value);
    return integerStringRegExp.test(stringNum) ? { kind: Kind.INT, value: stringNum } : { kind: Kind.FLOAT, value: stringNum };
  }
  if (typeof value === "string") {
    return { kind: Kind.STRING, value };
  }
  throw new TypeError(`Cannot convert value to AST: ${value}.`);
}
var integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;

// node_modules/@graphql-tools/utils/esm/astFromValue.js
function astFromValue(value, type) {
  if (isNonNullType(type)) {
    const astValue = astFromValue(value, type.ofType);
    if ((astValue == null ? void 0 : astValue.kind) === Kind.NULL) {
      return null;
    }
    return astValue;
  }
  if (value === null) {
    return { kind: Kind.NULL };
  }
  if (value === void 0) {
    return null;
  }
  if (isListType(type)) {
    const itemType = type.ofType;
    if (isIterableObject(value)) {
      const valuesNodes = [];
      for (const item of value) {
        const itemNode = astFromValue(item, itemType);
        if (itemNode != null) {
          valuesNodes.push(itemNode);
        }
      }
      return { kind: Kind.LIST, values: valuesNodes };
    }
    return astFromValue(value, itemType);
  }
  if (isInputObjectType(type)) {
    if (!isObjectLike(value)) {
      return null;
    }
    const fieldNodes = [];
    for (const field of Object.values(type.getFields())) {
      const fieldValue = astFromValue(value[field.name], field.type);
      if (fieldValue) {
        fieldNodes.push({
          kind: Kind.OBJECT_FIELD,
          name: { kind: Kind.NAME, value: field.name },
          value: fieldValue
        });
      }
    }
    return { kind: Kind.OBJECT, fields: fieldNodes };
  }
  if (isLeafType(type)) {
    const serialized = type.serialize(value);
    if (serialized == null) {
      return null;
    }
    if (isEnumType(type)) {
      return { kind: Kind.ENUM, value: serialized };
    }
    if (type.name === "ID" && typeof serialized === "string" && integerStringRegExp2.test(serialized)) {
      return { kind: Kind.INT, value: serialized };
    }
    return astFromValueUntyped(serialized);
  }
  console.assert(false, "Unexpected input type: " + inspect(type));
}
var integerStringRegExp2 = /^-?(?:0|[1-9][0-9]*)$/;

// node_modules/@graphql-tools/utils/esm/descriptionFromObject.js
init_graphql();
function getDescriptionNode(obj) {
  var _a;
  if ((_a = obj.astNode) == null ? void 0 : _a.description) {
    return {
      ...obj.astNode.description,
      block: true
    };
  }
  if (obj.description) {
    return {
      kind: Kind.STRING,
      value: obj.description,
      block: true
    };
  }
}

// node_modules/@graphql-tools/utils/esm/rootTypes.js
var getRootTypeNames = memoize1(function getRootTypeNames2(schema) {
  const rootTypes = getRootTypes(schema);
  return new Set([...rootTypes].map((type) => type.name));
});
var getRootTypes = memoize1(function getRootTypes2(schema) {
  const rootTypeMap = getRootTypeMap(schema);
  return new Set(rootTypeMap.values());
});
var getRootTypeMap = memoize1(function getRootTypeMap2(schema) {
  const rootTypeMap = /* @__PURE__ */ new Map();
  const queryType = schema.getQueryType();
  if (queryType) {
    rootTypeMap.set("query", queryType);
  }
  const mutationType = schema.getMutationType();
  if (mutationType) {
    rootTypeMap.set("mutation", mutationType);
  }
  const subscriptionType = schema.getSubscriptionType();
  if (subscriptionType) {
    rootTypeMap.set("subscription", subscriptionType);
  }
  return rootTypeMap;
});

// node_modules/@graphql-tools/utils/esm/print-schema-with-directives.js
function getDocumentNodeFromSchema(schema, options = {}) {
  const pathToDirectivesInExtensions = options.pathToDirectivesInExtensions;
  const typesMap = schema.getTypeMap();
  const schemaNode = astFromSchema(schema, pathToDirectivesInExtensions);
  const definitions = schemaNode != null ? [schemaNode] : [];
  const directives = schema.getDirectives();
  for (const directive of directives) {
    if (isSpecifiedDirective(directive)) {
      continue;
    }
    definitions.push(astFromDirective(directive, schema, pathToDirectivesInExtensions));
  }
  for (const typeName in typesMap) {
    const type = typesMap[typeName];
    const isPredefinedScalar = isSpecifiedScalarType(type);
    const isIntrospection = isIntrospectionType(type);
    if (isPredefinedScalar || isIntrospection) {
      continue;
    }
    if (isObjectType(type)) {
      definitions.push(astFromObjectType(type, schema, pathToDirectivesInExtensions));
    } else if (isInterfaceType(type)) {
      definitions.push(astFromInterfaceType(type, schema, pathToDirectivesInExtensions));
    } else if (isUnionType(type)) {
      definitions.push(astFromUnionType(type, schema, pathToDirectivesInExtensions));
    } else if (isInputObjectType(type)) {
      definitions.push(astFromInputObjectType(type, schema, pathToDirectivesInExtensions));
    } else if (isEnumType(type)) {
      definitions.push(astFromEnumType(type, schema, pathToDirectivesInExtensions));
    } else if (isScalarType(type)) {
      definitions.push(astFromScalarType(type, schema, pathToDirectivesInExtensions));
    } else {
      throw new Error(`Unknown type ${type}.`);
    }
  }
  return {
    kind: Kind.DOCUMENT,
    definitions
  };
}
function astFromSchema(schema, pathToDirectivesInExtensions) {
  const operationTypeMap = /* @__PURE__ */ new Map([
    ["query", void 0],
    ["mutation", void 0],
    ["subscription", void 0]
  ]);
  const nodes = [];
  if (schema.astNode != null) {
    nodes.push(schema.astNode);
  }
  if (schema.extensionASTNodes != null) {
    for (const extensionASTNode of schema.extensionASTNodes) {
      nodes.push(extensionASTNode);
    }
  }
  for (const node of nodes) {
    if (node.operationTypes) {
      for (const operationTypeDefinitionNode of node.operationTypes) {
        operationTypeMap.set(operationTypeDefinitionNode.operation, operationTypeDefinitionNode);
      }
    }
  }
  const rootTypeMap = getRootTypeMap(schema);
  for (const [operationTypeNode, operationTypeDefinitionNode] of operationTypeMap) {
    const rootType = rootTypeMap.get(operationTypeNode);
    if (rootType != null) {
      const rootTypeAST = astFromType(rootType);
      if (operationTypeDefinitionNode != null) {
        operationTypeDefinitionNode.type = rootTypeAST;
      } else {
        operationTypeMap.set(operationTypeNode, {
          kind: Kind.OPERATION_TYPE_DEFINITION,
          operation: operationTypeNode,
          type: rootTypeAST
        });
      }
    }
  }
  const operationTypes = [...operationTypeMap.values()].filter(isSome);
  const directives = getDirectiveNodes(schema, schema, pathToDirectivesInExtensions);
  if (!operationTypes.length && !directives.length) {
    return null;
  }
  const schemaNode = {
    kind: operationTypes != null ? Kind.SCHEMA_DEFINITION : Kind.SCHEMA_EXTENSION,
    operationTypes,
    // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
    directives
  };
  const descriptionNode = getDescriptionNode(schema);
  if (descriptionNode) {
    schemaNode.description = descriptionNode;
  }
  return schemaNode;
}
function astFromDirective(directive, schema, pathToDirectivesInExtensions) {
  var _a, _b;
  return {
    kind: Kind.DIRECTIVE_DEFINITION,
    description: getDescriptionNode(directive),
    name: {
      kind: Kind.NAME,
      value: directive.name
    },
    arguments: (_a = directive.args) == null ? void 0 : _a.map((arg) => astFromArg(arg, schema, pathToDirectivesInExtensions)),
    repeatable: directive.isRepeatable,
    locations: ((_b = directive.locations) == null ? void 0 : _b.map((location) => ({
      kind: Kind.NAME,
      value: location
    }))) || []
  };
}
function getDirectiveNodes(entity, schema, pathToDirectivesInExtensions) {
  var _a, _b;
  let directiveNodesBesidesDeprecatedAndSpecifiedBy = [];
  const directivesInExtensions = getDirectivesInExtensions(entity, pathToDirectivesInExtensions);
  let directives;
  if (directivesInExtensions != null) {
    directives = makeDirectiveNodes(schema, directivesInExtensions);
  }
  let deprecatedDirectiveNode = null;
  let specifiedByDirectiveNode = null;
  if (directives != null) {
    directiveNodesBesidesDeprecatedAndSpecifiedBy = directives.filter((directive) => directive.name.value !== "deprecated" && directive.name.value !== "specifiedBy");
    if (entity.deprecationReason != null) {
      deprecatedDirectiveNode = (_a = directives.filter((directive) => directive.name.value === "deprecated")) == null ? void 0 : _a[0];
    }
    if (entity.specifiedByUrl != null || entity.specifiedByURL != null) {
      specifiedByDirectiveNode = (_b = directives.filter((directive) => directive.name.value === "specifiedBy")) == null ? void 0 : _b[0];
    }
  }
  if (entity.deprecationReason != null && deprecatedDirectiveNode == null) {
    deprecatedDirectiveNode = makeDeprecatedDirective(entity.deprecationReason);
  }
  if (entity.specifiedByUrl != null || entity.specifiedByURL != null && specifiedByDirectiveNode == null) {
    const specifiedByValue = entity.specifiedByUrl || entity.specifiedByURL;
    const specifiedByArgs = {
      url: specifiedByValue
    };
    specifiedByDirectiveNode = makeDirectiveNode("specifiedBy", specifiedByArgs);
  }
  if (deprecatedDirectiveNode != null) {
    directiveNodesBesidesDeprecatedAndSpecifiedBy.push(deprecatedDirectiveNode);
  }
  if (specifiedByDirectiveNode != null) {
    directiveNodesBesidesDeprecatedAndSpecifiedBy.push(specifiedByDirectiveNode);
  }
  return directiveNodesBesidesDeprecatedAndSpecifiedBy;
}
function astFromArg(arg, schema, pathToDirectivesInExtensions) {
  return {
    kind: Kind.INPUT_VALUE_DEFINITION,
    description: getDescriptionNode(arg),
    name: {
      kind: Kind.NAME,
      value: arg.name
    },
    type: astFromType(arg.type),
    // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
    defaultValue: arg.defaultValue !== void 0 ? astFromValue(arg.defaultValue, arg.type) ?? void 0 : void 0,
    directives: getDirectiveNodes(arg, schema, pathToDirectivesInExtensions)
  };
}
function astFromObjectType(type, schema, pathToDirectivesInExtensions) {
  return {
    kind: Kind.OBJECT_TYPE_DEFINITION,
    description: getDescriptionNode(type),
    name: {
      kind: Kind.NAME,
      value: type.name
    },
    fields: Object.values(type.getFields()).map((field) => astFromField(field, schema, pathToDirectivesInExtensions)),
    interfaces: Object.values(type.getInterfaces()).map((iFace) => astFromType(iFace)),
    directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
  };
}
function astFromInterfaceType(type, schema, pathToDirectivesInExtensions) {
  const node = {
    kind: Kind.INTERFACE_TYPE_DEFINITION,
    description: getDescriptionNode(type),
    name: {
      kind: Kind.NAME,
      value: type.name
    },
    fields: Object.values(type.getFields()).map((field) => astFromField(field, schema, pathToDirectivesInExtensions)),
    directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
  };
  if ("getInterfaces" in type) {
    node.interfaces = Object.values(type.getInterfaces()).map((iFace) => astFromType(iFace));
  }
  return node;
}
function astFromUnionType(type, schema, pathToDirectivesInExtensions) {
  return {
    kind: Kind.UNION_TYPE_DEFINITION,
    description: getDescriptionNode(type),
    name: {
      kind: Kind.NAME,
      value: type.name
    },
    // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
    directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),
    types: type.getTypes().map((type2) => astFromType(type2))
  };
}
function astFromInputObjectType(type, schema, pathToDirectivesInExtensions) {
  return {
    kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,
    description: getDescriptionNode(type),
    name: {
      kind: Kind.NAME,
      value: type.name
    },
    fields: Object.values(type.getFields()).map((field) => astFromInputField(field, schema, pathToDirectivesInExtensions)),
    // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
    directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
  };
}
function astFromEnumType(type, schema, pathToDirectivesInExtensions) {
  return {
    kind: Kind.ENUM_TYPE_DEFINITION,
    description: getDescriptionNode(type),
    name: {
      kind: Kind.NAME,
      value: type.name
    },
    values: Object.values(type.getValues()).map((value) => astFromEnumValue(value, schema, pathToDirectivesInExtensions)),
    // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
    directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
  };
}
function astFromScalarType(type, schema, pathToDirectivesInExtensions) {
  const directivesInExtensions = getDirectivesInExtensions(type, pathToDirectivesInExtensions);
  const directives = makeDirectiveNodes(schema, directivesInExtensions);
  const specifiedByValue = type["specifiedByUrl"] || type["specifiedByURL"];
  if (specifiedByValue && !directives.some((directiveNode) => directiveNode.name.value === "specifiedBy")) {
    const specifiedByArgs = {
      url: specifiedByValue
    };
    directives.push(makeDirectiveNode("specifiedBy", specifiedByArgs));
  }
  return {
    kind: Kind.SCALAR_TYPE_DEFINITION,
    description: getDescriptionNode(type),
    name: {
      kind: Kind.NAME,
      value: type.name
    },
    // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
    directives
  };
}
function astFromField(field, schema, pathToDirectivesInExtensions) {
  return {
    kind: Kind.FIELD_DEFINITION,
    description: getDescriptionNode(field),
    name: {
      kind: Kind.NAME,
      value: field.name
    },
    arguments: field.args.map((arg) => astFromArg(arg, schema, pathToDirectivesInExtensions)),
    type: astFromType(field.type),
    // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
    directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions)
  };
}
function astFromInputField(field, schema, pathToDirectivesInExtensions) {
  return {
    kind: Kind.INPUT_VALUE_DEFINITION,
    description: getDescriptionNode(field),
    name: {
      kind: Kind.NAME,
      value: field.name
    },
    type: astFromType(field.type),
    // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
    directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),
    defaultValue: astFromValue(field.defaultValue, field.type) ?? void 0
  };
}
function astFromEnumValue(value, schema, pathToDirectivesInExtensions) {
  return {
    kind: Kind.ENUM_VALUE_DEFINITION,
    description: getDescriptionNode(value),
    name: {
      kind: Kind.NAME,
      value: value.name
    },
    directives: getDirectiveNodes(value, schema, pathToDirectivesInExtensions)
  };
}
function makeDeprecatedDirective(deprecationReason) {
  return makeDirectiveNode("deprecated", { reason: deprecationReason }, GraphQLDeprecatedDirective);
}
function makeDirectiveNode(name, args, directive) {
  const directiveArguments = [];
  for (const argName in args) {
    const argValue = args[argName];
    let value;
    if (directive != null) {
      const arg = directive.args.find((arg2) => arg2.name === argName);
      if (arg) {
        value = astFromValue(argValue, arg.type);
      }
    }
    if (value == null) {
      value = astFromValueUntyped(argValue);
    }
    if (value != null) {
      directiveArguments.push({
        kind: Kind.ARGUMENT,
        name: {
          kind: Kind.NAME,
          value: argName
        },
        value
      });
    }
  }
  return {
    kind: Kind.DIRECTIVE,
    name: {
      kind: Kind.NAME,
      value: name
    },
    arguments: directiveArguments
  };
}
function makeDirectiveNodes(schema, directiveValues) {
  const directiveNodes = [];
  for (const { name, args } of directiveValues) {
    const directive = schema == null ? void 0 : schema.getDirective(name);
    directiveNodes.push(makeDirectiveNode(name, args, directive));
  }
  return directiveNodes;
}

// node_modules/@graphql-tools/utils/esm/validate-documents.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/parse-graphql-json.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/parse-graphql-sdl.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/comments.js
init_graphql();
var MAX_LINE_LENGTH = 80;
var commentsRegistry = {};
function resetComments() {
  commentsRegistry = {};
}
function collectComment(node) {
  var _a;
  const entityName = (_a = node.name) == null ? void 0 : _a.value;
  if (entityName == null) {
    return;
  }
  pushComment(node, entityName);
  switch (node.kind) {
    case "EnumTypeDefinition":
      if (node.values) {
        for (const value of node.values) {
          pushComment(value, entityName, value.name.value);
        }
      }
      break;
    case "ObjectTypeDefinition":
    case "InputObjectTypeDefinition":
    case "InterfaceTypeDefinition":
      if (node.fields) {
        for (const field of node.fields) {
          pushComment(field, entityName, field.name.value);
          if (isFieldDefinitionNode(field) && field.arguments) {
            for (const arg of field.arguments) {
              pushComment(arg, entityName, field.name.value, arg.name.value);
            }
          }
        }
      }
      break;
  }
}
function pushComment(node, entity, field, argument) {
  const comment = getComment(node);
  if (typeof comment !== "string" || comment.length === 0) {
    return;
  }
  const keys = [entity];
  if (field) {
    keys.push(field);
    if (argument) {
      keys.push(argument);
    }
  }
  const path = keys.join(".");
  if (!commentsRegistry[path]) {
    commentsRegistry[path] = [];
  }
  commentsRegistry[path].push(comment);
}
function printComment(comment) {
  return "\n# " + comment.replace(/\n/g, "\n# ");
}
function join(maybeArray, separator) {
  return maybeArray ? maybeArray.filter((x) => x).join(separator || "") : "";
}
function hasMultilineItems(maybeArray) {
  return (maybeArray == null ? void 0 : maybeArray.some((str) => str.includes("\n"))) ?? false;
}
function addDescription(cb) {
  return (node, _key, _parent, path, ancestors) => {
    var _a;
    const keys = [];
    const parent = path.reduce((prev, key2) => {
      if (["fields", "arguments", "values"].includes(key2) && prev.name) {
        keys.push(prev.name.value);
      }
      return prev[key2];
    }, ancestors[0]);
    const key = [...keys, (_a = parent == null ? void 0 : parent.name) == null ? void 0 : _a.value].filter(Boolean).join(".");
    const items = [];
    if (node.kind.includes("Definition") && commentsRegistry[key]) {
      items.push(...commentsRegistry[key]);
    }
    return join([...items.map(printComment), node.description, cb(node, _key, _parent, path, ancestors)], "\n");
  };
}
function indent(maybeString) {
  return maybeString && `  ${maybeString.replace(/\n/g, "\n  ")}`;
}
function block(array) {
  return array && array.length !== 0 ? `{
${indent(join(array, "\n"))}
}` : "";
}
function wrap(start, maybeString, end) {
  return maybeString ? start + maybeString + (end || "") : "";
}
function printBlockString(value, isDescription = false) {
  const escaped = value.replace(/\\/g, "\\\\").replace(/"""/g, '\\"""');
  return (value[0] === " " || value[0] === "	") && value.indexOf("\n") === -1 ? `"""${escaped.replace(/"$/, '"\n')}"""` : `"""
${isDescription ? escaped : indent(escaped)}
"""`;
}
var printDocASTReducer = {
  Name: { leave: (node) => node.value },
  Variable: { leave: (node) => "$" + node.name },
  // Document
  Document: {
    leave: (node) => join(node.definitions, "\n\n")
  },
  OperationDefinition: {
    leave: (node) => {
      const varDefs = wrap("(", join(node.variableDefinitions, ", "), ")");
      const prefix = join([node.operation, join([node.name, varDefs]), join(node.directives, " ")], " ");
      return prefix + " " + node.selectionSet;
    }
  },
  VariableDefinition: {
    leave: ({ variable, type, defaultValue, directives }) => variable + ": " + type + wrap(" = ", defaultValue) + wrap(" ", join(directives, " "))
  },
  SelectionSet: { leave: ({ selections }) => block(selections) },
  Field: {
    leave({ alias, name, arguments: args, directives, selectionSet }) {
      const prefix = wrap("", alias, ": ") + name;
      let argsLine = prefix + wrap("(", join(args, ", "), ")");
      if (argsLine.length > MAX_LINE_LENGTH) {
        argsLine = prefix + wrap("(\n", indent(join(args, "\n")), "\n)");
      }
      return join([argsLine, join(directives, " "), selectionSet], " ");
    }
  },
  Argument: { leave: ({ name, value }) => name + ": " + value },
  // Fragments
  FragmentSpread: {
    leave: ({ name, directives }) => "..." + name + wrap(" ", join(directives, " "))
  },
  InlineFragment: {
    leave: ({ typeCondition, directives, selectionSet }) => join(["...", wrap("on ", typeCondition), join(directives, " "), selectionSet], " ")
  },
  FragmentDefinition: {
    leave: ({ name, typeCondition, variableDefinitions, directives, selectionSet }) => (
      // Note: fragment variable definitions are experimental and may be changed
      // or removed in the future.
      `fragment ${name}${wrap("(", join(variableDefinitions, ", "), ")")} on ${typeCondition} ${wrap("", join(directives, " "), " ")}` + selectionSet
    )
  },
  // Value
  IntValue: { leave: ({ value }) => value },
  FloatValue: { leave: ({ value }) => value },
  StringValue: {
    leave: ({ value, block: isBlockString }) => {
      if (isBlockString) {
        return printBlockString(value);
      }
      return JSON.stringify(value);
    }
  },
  BooleanValue: { leave: ({ value }) => value ? "true" : "false" },
  NullValue: { leave: () => "null" },
  EnumValue: { leave: ({ value }) => value },
  ListValue: { leave: ({ values }) => "[" + join(values, ", ") + "]" },
  ObjectValue: { leave: ({ fields }) => "{" + join(fields, ", ") + "}" },
  ObjectField: { leave: ({ name, value }) => name + ": " + value },
  // Directive
  Directive: {
    leave: ({ name, arguments: args }) => "@" + name + wrap("(", join(args, ", "), ")")
  },
  // Type
  NamedType: { leave: ({ name }) => name },
  ListType: { leave: ({ type }) => "[" + type + "]" },
  NonNullType: { leave: ({ type }) => type + "!" },
  // Type System Definitions
  SchemaDefinition: {
    leave: ({ directives, operationTypes }) => join(["schema", join(directives, " "), block(operationTypes)], " ")
  },
  OperationTypeDefinition: {
    leave: ({ operation, type }) => operation + ": " + type
  },
  ScalarTypeDefinition: {
    leave: ({ name, directives }) => join(["scalar", name, join(directives, " ")], " ")
  },
  ObjectTypeDefinition: {
    leave: ({ name, interfaces, directives, fields }) => join([
      "type",
      name,
      wrap("implements ", join(interfaces, " & ")),
      join(directives, " "),
      block(fields)
    ], " ")
  },
  FieldDefinition: {
    leave: ({ name, arguments: args, type, directives }) => name + (hasMultilineItems(args) ? wrap("(\n", indent(join(args, "\n")), "\n)") : wrap("(", join(args, ", "), ")")) + ": " + type + wrap(" ", join(directives, " "))
  },
  InputValueDefinition: {
    leave: ({ name, type, defaultValue, directives }) => join([name + ": " + type, wrap("= ", defaultValue), join(directives, " ")], " ")
  },
  InterfaceTypeDefinition: {
    leave: ({ name, interfaces, directives, fields }) => join([
      "interface",
      name,
      wrap("implements ", join(interfaces, " & ")),
      join(directives, " "),
      block(fields)
    ], " ")
  },
  UnionTypeDefinition: {
    leave: ({ name, directives, types }) => join(["union", name, join(directives, " "), wrap("= ", join(types, " | "))], " ")
  },
  EnumTypeDefinition: {
    leave: ({ name, directives, values }) => join(["enum", name, join(directives, " "), block(values)], " ")
  },
  EnumValueDefinition: {
    leave: ({ name, directives }) => join([name, join(directives, " ")], " ")
  },
  InputObjectTypeDefinition: {
    leave: ({ name, directives, fields }) => join(["input", name, join(directives, " "), block(fields)], " ")
  },
  DirectiveDefinition: {
    leave: ({ name, arguments: args, repeatable, locations }) => "directive @" + name + (hasMultilineItems(args) ? wrap("(\n", indent(join(args, "\n")), "\n)") : wrap("(", join(args, ", "), ")")) + (repeatable ? " repeatable" : "") + " on " + join(locations, " | ")
  },
  SchemaExtension: {
    leave: ({ directives, operationTypes }) => join(["extend schema", join(directives, " "), block(operationTypes)], " ")
  },
  ScalarTypeExtension: {
    leave: ({ name, directives }) => join(["extend scalar", name, join(directives, " ")], " ")
  },
  ObjectTypeExtension: {
    leave: ({ name, interfaces, directives, fields }) => join([
      "extend type",
      name,
      wrap("implements ", join(interfaces, " & ")),
      join(directives, " "),
      block(fields)
    ], " ")
  },
  InterfaceTypeExtension: {
    leave: ({ name, interfaces, directives, fields }) => join([
      "extend interface",
      name,
      wrap("implements ", join(interfaces, " & ")),
      join(directives, " "),
      block(fields)
    ], " ")
  },
  UnionTypeExtension: {
    leave: ({ name, directives, types }) => join(["extend union", name, join(directives, " "), wrap("= ", join(types, " | "))], " ")
  },
  EnumTypeExtension: {
    leave: ({ name, directives, values }) => join(["extend enum", name, join(directives, " "), block(values)], " ")
  },
  InputObjectTypeExtension: {
    leave: ({ name, directives, fields }) => join(["extend input", name, join(directives, " "), block(fields)], " ")
  }
};
var printDocASTReducerWithComments = Object.keys(printDocASTReducer).reduce((prev, key) => ({
  ...prev,
  [key]: {
    leave: addDescription(printDocASTReducer[key].leave)
  }
}), {});
function printWithComments(ast) {
  return visit(ast, printDocASTReducerWithComments);
}
function isFieldDefinitionNode(node) {
  return node.kind === "FieldDefinition";
}
function getComment(node) {
  const rawValue = getLeadingCommentBlock(node);
  if (rawValue !== void 0) {
    return dedentBlockStringValue(`
${rawValue}`);
  }
}
function getLeadingCommentBlock(node) {
  const loc = node.loc;
  if (!loc) {
    return;
  }
  const comments = [];
  let token = loc.startToken.prev;
  while (token != null && token.kind === TokenKind.COMMENT && token.next != null && token.prev != null && token.line + 1 === token.next.line && token.line !== token.prev.line) {
    const value = String(token.value);
    comments.push(value);
    token = token.prev;
  }
  return comments.length > 0 ? comments.reverse().join("\n") : void 0;
}
function dedentBlockStringValue(rawString) {
  const lines = rawString.split(/\r\n|[\n\r]/g);
  const commonIndent = getBlockStringIndentation(lines);
  if (commonIndent !== 0) {
    for (let i = 1; i < lines.length; i++) {
      lines[i] = lines[i].slice(commonIndent);
    }
  }
  while (lines.length > 0 && isBlank(lines[0])) {
    lines.shift();
  }
  while (lines.length > 0 && isBlank(lines[lines.length - 1])) {
    lines.pop();
  }
  return lines.join("\n");
}
function getBlockStringIndentation(lines) {
  let commonIndent = null;
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    const indent2 = leadingWhitespace(line);
    if (indent2 === line.length) {
      continue;
    }
    if (commonIndent === null || indent2 < commonIndent) {
      commonIndent = indent2;
      if (commonIndent === 0) {
        break;
      }
    }
  }
  return commonIndent === null ? 0 : commonIndent;
}
function leadingWhitespace(str) {
  let i = 0;
  while (i < str.length && (str[i] === " " || str[i] === "	")) {
    i++;
  }
  return i;
}
function isBlank(str) {
  return leadingWhitespace(str) === str.length;
}

// node_modules/@graphql-tools/utils/esm/build-operation-for-field.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/types.js
var DirectiveLocation2;
(function(DirectiveLocation3) {
  DirectiveLocation3["QUERY"] = "QUERY";
  DirectiveLocation3["MUTATION"] = "MUTATION";
  DirectiveLocation3["SUBSCRIPTION"] = "SUBSCRIPTION";
  DirectiveLocation3["FIELD"] = "FIELD";
  DirectiveLocation3["FRAGMENT_DEFINITION"] = "FRAGMENT_DEFINITION";
  DirectiveLocation3["FRAGMENT_SPREAD"] = "FRAGMENT_SPREAD";
  DirectiveLocation3["INLINE_FRAGMENT"] = "INLINE_FRAGMENT";
  DirectiveLocation3["VARIABLE_DEFINITION"] = "VARIABLE_DEFINITION";
  DirectiveLocation3["SCHEMA"] = "SCHEMA";
  DirectiveLocation3["SCALAR"] = "SCALAR";
  DirectiveLocation3["OBJECT"] = "OBJECT";
  DirectiveLocation3["FIELD_DEFINITION"] = "FIELD_DEFINITION";
  DirectiveLocation3["ARGUMENT_DEFINITION"] = "ARGUMENT_DEFINITION";
  DirectiveLocation3["INTERFACE"] = "INTERFACE";
  DirectiveLocation3["UNION"] = "UNION";
  DirectiveLocation3["ENUM"] = "ENUM";
  DirectiveLocation3["ENUM_VALUE"] = "ENUM_VALUE";
  DirectiveLocation3["INPUT_OBJECT"] = "INPUT_OBJECT";
  DirectiveLocation3["INPUT_FIELD_DEFINITION"] = "INPUT_FIELD_DEFINITION";
})(DirectiveLocation2 || (DirectiveLocation2 = {}));

// node_modules/@graphql-tools/utils/esm/filterSchema.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/Interfaces.js
var MapperKind;
(function(MapperKind2) {
  MapperKind2["TYPE"] = "MapperKind.TYPE";
  MapperKind2["SCALAR_TYPE"] = "MapperKind.SCALAR_TYPE";
  MapperKind2["ENUM_TYPE"] = "MapperKind.ENUM_TYPE";
  MapperKind2["COMPOSITE_TYPE"] = "MapperKind.COMPOSITE_TYPE";
  MapperKind2["OBJECT_TYPE"] = "MapperKind.OBJECT_TYPE";
  MapperKind2["INPUT_OBJECT_TYPE"] = "MapperKind.INPUT_OBJECT_TYPE";
  MapperKind2["ABSTRACT_TYPE"] = "MapperKind.ABSTRACT_TYPE";
  MapperKind2["UNION_TYPE"] = "MapperKind.UNION_TYPE";
  MapperKind2["INTERFACE_TYPE"] = "MapperKind.INTERFACE_TYPE";
  MapperKind2["ROOT_OBJECT"] = "MapperKind.ROOT_OBJECT";
  MapperKind2["QUERY"] = "MapperKind.QUERY";
  MapperKind2["MUTATION"] = "MapperKind.MUTATION";
  MapperKind2["SUBSCRIPTION"] = "MapperKind.SUBSCRIPTION";
  MapperKind2["DIRECTIVE"] = "MapperKind.DIRECTIVE";
  MapperKind2["FIELD"] = "MapperKind.FIELD";
  MapperKind2["COMPOSITE_FIELD"] = "MapperKind.COMPOSITE_FIELD";
  MapperKind2["OBJECT_FIELD"] = "MapperKind.OBJECT_FIELD";
  MapperKind2["ROOT_FIELD"] = "MapperKind.ROOT_FIELD";
  MapperKind2["QUERY_ROOT_FIELD"] = "MapperKind.QUERY_ROOT_FIELD";
  MapperKind2["MUTATION_ROOT_FIELD"] = "MapperKind.MUTATION_ROOT_FIELD";
  MapperKind2["SUBSCRIPTION_ROOT_FIELD"] = "MapperKind.SUBSCRIPTION_ROOT_FIELD";
  MapperKind2["INTERFACE_FIELD"] = "MapperKind.INTERFACE_FIELD";
  MapperKind2["INPUT_OBJECT_FIELD"] = "MapperKind.INPUT_OBJECT_FIELD";
  MapperKind2["ARGUMENT"] = "MapperKind.ARGUMENT";
  MapperKind2["ENUM_VALUE"] = "MapperKind.ENUM_VALUE";
})(MapperKind || (MapperKind = {}));

// node_modules/@graphql-tools/utils/esm/mapSchema.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/getObjectTypeFromTypeMap.js
init_graphql();
function getObjectTypeFromTypeMap(typeMap, type) {
  if (type) {
    const maybeObjectType = typeMap[type.name];
    if (isObjectType(maybeObjectType)) {
      return maybeObjectType;
    }
  }
}

// node_modules/@graphql-tools/utils/esm/rewire.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/stub.js
init_graphql();
function isNamedStub(type) {
  if ("getFields" in type) {
    const fields = type.getFields();
    for (const fieldName in fields) {
      const field = fields[fieldName];
      return field.name === "_fake";
    }
  }
  return false;
}
function getBuiltInForStub(type) {
  switch (type.name) {
    case GraphQLInt.name:
      return GraphQLInt;
    case GraphQLFloat.name:
      return GraphQLFloat;
    case GraphQLString.name:
      return GraphQLString;
    case GraphQLBoolean.name:
      return GraphQLBoolean;
    case GraphQLID.name:
      return GraphQLID;
    default:
      return type;
  }
}

// node_modules/@graphql-tools/utils/esm/rewire.js
function rewireTypes(originalTypeMap, directives) {
  const referenceTypeMap = /* @__PURE__ */ Object.create(null);
  for (const typeName in originalTypeMap) {
    referenceTypeMap[typeName] = originalTypeMap[typeName];
  }
  const newTypeMap = /* @__PURE__ */ Object.create(null);
  for (const typeName in referenceTypeMap) {
    const namedType = referenceTypeMap[typeName];
    if (namedType == null || typeName.startsWith("__")) {
      continue;
    }
    const newName = namedType.name;
    if (newName.startsWith("__")) {
      continue;
    }
    if (newTypeMap[newName] != null) {
      console.warn(`Duplicate schema type name ${newName} found; keeping the existing one found in the schema`);
      continue;
    }
    newTypeMap[newName] = namedType;
  }
  for (const typeName in newTypeMap) {
    newTypeMap[typeName] = rewireNamedType(newTypeMap[typeName]);
  }
  const newDirectives = directives.map((directive) => rewireDirective(directive));
  return {
    typeMap: newTypeMap,
    directives: newDirectives
  };
  function rewireDirective(directive) {
    if (isSpecifiedDirective(directive)) {
      return directive;
    }
    const directiveConfig = directive.toConfig();
    directiveConfig.args = rewireArgs(directiveConfig.args);
    return new GraphQLDirective(directiveConfig);
  }
  function rewireArgs(args) {
    const rewiredArgs = {};
    for (const argName in args) {
      const arg = args[argName];
      const rewiredArgType = rewireType(arg.type);
      if (rewiredArgType != null) {
        arg.type = rewiredArgType;
        rewiredArgs[argName] = arg;
      }
    }
    return rewiredArgs;
  }
  function rewireNamedType(type) {
    if (isObjectType(type)) {
      const config = type.toConfig();
      const newConfig = {
        ...config,
        fields: () => rewireFields(config.fields),
        interfaces: () => rewireNamedTypes(config.interfaces)
      };
      return new GraphQLObjectType(newConfig);
    } else if (isInterfaceType(type)) {
      const config = type.toConfig();
      const newConfig = {
        ...config,
        fields: () => rewireFields(config.fields)
      };
      if ("interfaces" in newConfig) {
        newConfig.interfaces = () => rewireNamedTypes(config.interfaces);
      }
      return new GraphQLInterfaceType(newConfig);
    } else if (isUnionType(type)) {
      const config = type.toConfig();
      const newConfig = {
        ...config,
        types: () => rewireNamedTypes(config.types)
      };
      return new GraphQLUnionType(newConfig);
    } else if (isInputObjectType(type)) {
      const config = type.toConfig();
      const newConfig = {
        ...config,
        fields: () => rewireInputFields(config.fields)
      };
      return new GraphQLInputObjectType(newConfig);
    } else if (isEnumType(type)) {
      const enumConfig = type.toConfig();
      return new GraphQLEnumType(enumConfig);
    } else if (isScalarType(type)) {
      if (isSpecifiedScalarType(type)) {
        return type;
      }
      const scalarConfig = type.toConfig();
      return new GraphQLScalarType(scalarConfig);
    }
    throw new Error(`Unexpected schema type: ${type}`);
  }
  function rewireFields(fields) {
    const rewiredFields = {};
    for (const fieldName in fields) {
      const field = fields[fieldName];
      const rewiredFieldType = rewireType(field.type);
      if (rewiredFieldType != null && field.args) {
        field.type = rewiredFieldType;
        field.args = rewireArgs(field.args);
        rewiredFields[fieldName] = field;
      }
    }
    return rewiredFields;
  }
  function rewireInputFields(fields) {
    const rewiredFields = {};
    for (const fieldName in fields) {
      const field = fields[fieldName];
      const rewiredFieldType = rewireType(field.type);
      if (rewiredFieldType != null) {
        field.type = rewiredFieldType;
        rewiredFields[fieldName] = field;
      }
    }
    return rewiredFields;
  }
  function rewireNamedTypes(namedTypes) {
    const rewiredTypes = [];
    for (const namedType of namedTypes) {
      const rewiredType = rewireType(namedType);
      if (rewiredType != null) {
        rewiredTypes.push(rewiredType);
      }
    }
    return rewiredTypes;
  }
  function rewireType(type) {
    if (isListType(type)) {
      const rewiredType = rewireType(type.ofType);
      return rewiredType != null ? new GraphQLList(rewiredType) : null;
    } else if (isNonNullType(type)) {
      const rewiredType = rewireType(type.ofType);
      return rewiredType != null ? new GraphQLNonNull(rewiredType) : null;
    } else if (isNamedType(type)) {
      let rewiredType = referenceTypeMap[type.name];
      if (rewiredType === void 0) {
        rewiredType = isNamedStub(type) ? getBuiltInForStub(type) : rewireNamedType(type);
        newTypeMap[rewiredType.name] = referenceTypeMap[type.name] = rewiredType;
      }
      return rewiredType != null ? newTypeMap[rewiredType.name] : null;
    }
    return null;
  }
}

// node_modules/@graphql-tools/utils/esm/transformInputValue.js
init_graphql();
function transformInputValue(type, value, inputLeafValueTransformer = null, inputObjectValueTransformer = null) {
  if (value == null) {
    return value;
  }
  const nullableType = getNullableType(type);
  if (isLeafType(nullableType)) {
    return inputLeafValueTransformer != null ? inputLeafValueTransformer(nullableType, value) : value;
  } else if (isListType(nullableType)) {
    return asArray(value).map((listMember) => transformInputValue(nullableType.ofType, listMember, inputLeafValueTransformer, inputObjectValueTransformer));
  } else if (isInputObjectType(nullableType)) {
    const fields = nullableType.getFields();
    const newValue = {};
    for (const key in value) {
      const field = fields[key];
      if (field != null) {
        newValue[key] = transformInputValue(field.type, value[key], inputLeafValueTransformer, inputObjectValueTransformer);
      }
    }
    return inputObjectValueTransformer != null ? inputObjectValueTransformer(nullableType, newValue) : newValue;
  }
}
function serializeInputValue(type, value) {
  return transformInputValue(type, value, (t, v) => {
    try {
      return t.serialize(v);
    } catch {
      return v;
    }
  });
}
function parseInputValue(type, value) {
  return transformInputValue(type, value, (t, v) => {
    try {
      return t.parseValue(v);
    } catch {
      return v;
    }
  });
}

// node_modules/@graphql-tools/utils/esm/mapSchema.js
function mapSchema(schema, schemaMapper = {}) {
  const newTypeMap = mapArguments(mapFields(mapTypes(mapDefaultValues(mapEnumValues(mapTypes(mapDefaultValues(schema.getTypeMap(), schema, serializeInputValue), schema, schemaMapper, (type) => isLeafType(type)), schema, schemaMapper), schema, parseInputValue), schema, schemaMapper, (type) => !isLeafType(type)), schema, schemaMapper), schema, schemaMapper);
  const originalDirectives = schema.getDirectives();
  const newDirectives = mapDirectives(originalDirectives, schema, schemaMapper);
  const { typeMap, directives } = rewireTypes(newTypeMap, newDirectives);
  return new GraphQLSchema({
    ...schema.toConfig(),
    query: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getQueryType())),
    mutation: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getMutationType())),
    subscription: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getSubscriptionType())),
    types: Object.values(typeMap),
    directives
  });
}
function mapTypes(originalTypeMap, schema, schemaMapper, testFn = () => true) {
  const newTypeMap = {};
  for (const typeName in originalTypeMap) {
    if (!typeName.startsWith("__")) {
      const originalType = originalTypeMap[typeName];
      if (originalType == null || !testFn(originalType)) {
        newTypeMap[typeName] = originalType;
        continue;
      }
      const typeMapper = getTypeMapper(schema, schemaMapper, typeName);
      if (typeMapper == null) {
        newTypeMap[typeName] = originalType;
        continue;
      }
      const maybeNewType = typeMapper(originalType, schema);
      if (maybeNewType === void 0) {
        newTypeMap[typeName] = originalType;
        continue;
      }
      newTypeMap[typeName] = maybeNewType;
    }
  }
  return newTypeMap;
}
function mapEnumValues(originalTypeMap, schema, schemaMapper) {
  const enumValueMapper = getEnumValueMapper(schemaMapper);
  if (!enumValueMapper) {
    return originalTypeMap;
  }
  return mapTypes(originalTypeMap, schema, {
    [MapperKind.ENUM_TYPE]: (type) => {
      const config = type.toConfig();
      const originalEnumValueConfigMap = config.values;
      const newEnumValueConfigMap = {};
      for (const externalValue in originalEnumValueConfigMap) {
        const originalEnumValueConfig = originalEnumValueConfigMap[externalValue];
        const mappedEnumValue = enumValueMapper(originalEnumValueConfig, type.name, schema, externalValue);
        if (mappedEnumValue === void 0) {
          newEnumValueConfigMap[externalValue] = originalEnumValueConfig;
        } else if (Array.isArray(mappedEnumValue)) {
          const [newExternalValue, newEnumValueConfig] = mappedEnumValue;
          newEnumValueConfigMap[newExternalValue] = newEnumValueConfig === void 0 ? originalEnumValueConfig : newEnumValueConfig;
        } else if (mappedEnumValue !== null) {
          newEnumValueConfigMap[externalValue] = mappedEnumValue;
        }
      }
      return correctASTNodes(new GraphQLEnumType({
        ...config,
        values: newEnumValueConfigMap
      }));
    }
  }, (type) => isEnumType(type));
}
function mapDefaultValues(originalTypeMap, schema, fn) {
  const newTypeMap = mapArguments(originalTypeMap, schema, {
    [MapperKind.ARGUMENT]: (argumentConfig) => {
      if (argumentConfig.defaultValue === void 0) {
        return argumentConfig;
      }
      const maybeNewType = getNewType(originalTypeMap, argumentConfig.type);
      if (maybeNewType != null) {
        return {
          ...argumentConfig,
          defaultValue: fn(maybeNewType, argumentConfig.defaultValue)
        };
      }
    }
  });
  return mapFields(newTypeMap, schema, {
    [MapperKind.INPUT_OBJECT_FIELD]: (inputFieldConfig) => {
      if (inputFieldConfig.defaultValue === void 0) {
        return inputFieldConfig;
      }
      const maybeNewType = getNewType(newTypeMap, inputFieldConfig.type);
      if (maybeNewType != null) {
        return {
          ...inputFieldConfig,
          defaultValue: fn(maybeNewType, inputFieldConfig.defaultValue)
        };
      }
    }
  });
}
function getNewType(newTypeMap, type) {
  if (isListType(type)) {
    const newType = getNewType(newTypeMap, type.ofType);
    return newType != null ? new GraphQLList(newType) : null;
  } else if (isNonNullType(type)) {
    const newType = getNewType(newTypeMap, type.ofType);
    return newType != null ? new GraphQLNonNull(newType) : null;
  } else if (isNamedType(type)) {
    const newType = newTypeMap[type.name];
    return newType != null ? newType : null;
  }
  return null;
}
function mapFields(originalTypeMap, schema, schemaMapper) {
  const newTypeMap = {};
  for (const typeName in originalTypeMap) {
    if (!typeName.startsWith("__")) {
      const originalType = originalTypeMap[typeName];
      if (!isObjectType(originalType) && !isInterfaceType(originalType) && !isInputObjectType(originalType)) {
        newTypeMap[typeName] = originalType;
        continue;
      }
      const fieldMapper = getFieldMapper(schema, schemaMapper, typeName);
      if (fieldMapper == null) {
        newTypeMap[typeName] = originalType;
        continue;
      }
      const config = originalType.toConfig();
      const originalFieldConfigMap = config.fields;
      const newFieldConfigMap = {};
      for (const fieldName in originalFieldConfigMap) {
        const originalFieldConfig = originalFieldConfigMap[fieldName];
        const mappedField = fieldMapper(originalFieldConfig, fieldName, typeName, schema);
        if (mappedField === void 0) {
          newFieldConfigMap[fieldName] = originalFieldConfig;
        } else if (Array.isArray(mappedField)) {
          const [newFieldName, newFieldConfig] = mappedField;
          if (newFieldConfig.astNode != null) {
            newFieldConfig.astNode = {
              ...newFieldConfig.astNode,
              name: {
                ...newFieldConfig.astNode.name,
                value: newFieldName
              }
            };
          }
          newFieldConfigMap[newFieldName] = newFieldConfig === void 0 ? originalFieldConfig : newFieldConfig;
        } else if (mappedField !== null) {
          newFieldConfigMap[fieldName] = mappedField;
        }
      }
      if (isObjectType(originalType)) {
        newTypeMap[typeName] = correctASTNodes(new GraphQLObjectType({
          ...config,
          fields: newFieldConfigMap
        }));
      } else if (isInterfaceType(originalType)) {
        newTypeMap[typeName] = correctASTNodes(new GraphQLInterfaceType({
          ...config,
          fields: newFieldConfigMap
        }));
      } else {
        newTypeMap[typeName] = correctASTNodes(new GraphQLInputObjectType({
          ...config,
          fields: newFieldConfigMap
        }));
      }
    }
  }
  return newTypeMap;
}
function mapArguments(originalTypeMap, schema, schemaMapper) {
  const newTypeMap = {};
  for (const typeName in originalTypeMap) {
    if (!typeName.startsWith("__")) {
      const originalType = originalTypeMap[typeName];
      if (!isObjectType(originalType) && !isInterfaceType(originalType)) {
        newTypeMap[typeName] = originalType;
        continue;
      }
      const argumentMapper = getArgumentMapper(schemaMapper);
      if (argumentMapper == null) {
        newTypeMap[typeName] = originalType;
        continue;
      }
      const config = originalType.toConfig();
      const originalFieldConfigMap = config.fields;
      const newFieldConfigMap = {};
      for (const fieldName in originalFieldConfigMap) {
        const originalFieldConfig = originalFieldConfigMap[fieldName];
        const originalArgumentConfigMap = originalFieldConfig.args;
        if (originalArgumentConfigMap == null) {
          newFieldConfigMap[fieldName] = originalFieldConfig;
          continue;
        }
        const argumentNames = Object.keys(originalArgumentConfigMap);
        if (!argumentNames.length) {
          newFieldConfigMap[fieldName] = originalFieldConfig;
          continue;
        }
        const newArgumentConfigMap = {};
        for (const argumentName of argumentNames) {
          const originalArgumentConfig = originalArgumentConfigMap[argumentName];
          const mappedArgument = argumentMapper(originalArgumentConfig, fieldName, typeName, schema);
          if (mappedArgument === void 0) {
            newArgumentConfigMap[argumentName] = originalArgumentConfig;
          } else if (Array.isArray(mappedArgument)) {
            const [newArgumentName, newArgumentConfig] = mappedArgument;
            newArgumentConfigMap[newArgumentName] = newArgumentConfig;
          } else if (mappedArgument !== null) {
            newArgumentConfigMap[argumentName] = mappedArgument;
          }
        }
        newFieldConfigMap[fieldName] = {
          ...originalFieldConfig,
          args: newArgumentConfigMap
        };
      }
      if (isObjectType(originalType)) {
        newTypeMap[typeName] = new GraphQLObjectType({
          ...config,
          fields: newFieldConfigMap
        });
      } else if (isInterfaceType(originalType)) {
        newTypeMap[typeName] = new GraphQLInterfaceType({
          ...config,
          fields: newFieldConfigMap
        });
      } else {
        newTypeMap[typeName] = new GraphQLInputObjectType({
          ...config,
          fields: newFieldConfigMap
        });
      }
    }
  }
  return newTypeMap;
}
function mapDirectives(originalDirectives, schema, schemaMapper) {
  const directiveMapper = getDirectiveMapper(schemaMapper);
  if (directiveMapper == null) {
    return originalDirectives.slice();
  }
  const newDirectives = [];
  for (const directive of originalDirectives) {
    const mappedDirective = directiveMapper(directive, schema);
    if (mappedDirective === void 0) {
      newDirectives.push(directive);
    } else if (mappedDirective !== null) {
      newDirectives.push(mappedDirective);
    }
  }
  return newDirectives;
}
function getTypeSpecifiers(schema, typeName) {
  var _a, _b, _c;
  const type = schema.getType(typeName);
  const specifiers = [MapperKind.TYPE];
  if (isObjectType(type)) {
    specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.OBJECT_TYPE);
    if (typeName === ((_a = schema.getQueryType()) == null ? void 0 : _a.name)) {
      specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.QUERY);
    } else if (typeName === ((_b = schema.getMutationType()) == null ? void 0 : _b.name)) {
      specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.MUTATION);
    } else if (typeName === ((_c = schema.getSubscriptionType()) == null ? void 0 : _c.name)) {
      specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.SUBSCRIPTION);
    }
  } else if (isInputObjectType(type)) {
    specifiers.push(MapperKind.INPUT_OBJECT_TYPE);
  } else if (isInterfaceType(type)) {
    specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.ABSTRACT_TYPE, MapperKind.INTERFACE_TYPE);
  } else if (isUnionType(type)) {
    specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.ABSTRACT_TYPE, MapperKind.UNION_TYPE);
  } else if (isEnumType(type)) {
    specifiers.push(MapperKind.ENUM_TYPE);
  } else if (isScalarType(type)) {
    specifiers.push(MapperKind.SCALAR_TYPE);
  }
  return specifiers;
}
function getTypeMapper(schema, schemaMapper, typeName) {
  const specifiers = getTypeSpecifiers(schema, typeName);
  let typeMapper;
  const stack = [...specifiers];
  while (!typeMapper && stack.length > 0) {
    const next = stack.pop();
    typeMapper = schemaMapper[next];
  }
  return typeMapper != null ? typeMapper : null;
}
function getFieldSpecifiers(schema, typeName) {
  var _a, _b, _c;
  const type = schema.getType(typeName);
  const specifiers = [MapperKind.FIELD];
  if (isObjectType(type)) {
    specifiers.push(MapperKind.COMPOSITE_FIELD, MapperKind.OBJECT_FIELD);
    if (typeName === ((_a = schema.getQueryType()) == null ? void 0 : _a.name)) {
      specifiers.push(MapperKind.ROOT_FIELD, MapperKind.QUERY_ROOT_FIELD);
    } else if (typeName === ((_b = schema.getMutationType()) == null ? void 0 : _b.name)) {
      specifiers.push(MapperKind.ROOT_FIELD, MapperKind.MUTATION_ROOT_FIELD);
    } else if (typeName === ((_c = schema.getSubscriptionType()) == null ? void 0 : _c.name)) {
      specifiers.push(MapperKind.ROOT_FIELD, MapperKind.SUBSCRIPTION_ROOT_FIELD);
    }
  } else if (isInterfaceType(type)) {
    specifiers.push(MapperKind.COMPOSITE_FIELD, MapperKind.INTERFACE_FIELD);
  } else if (isInputObjectType(type)) {
    specifiers.push(MapperKind.INPUT_OBJECT_FIELD);
  }
  return specifiers;
}
function getFieldMapper(schema, schemaMapper, typeName) {
  const specifiers = getFieldSpecifiers(schema, typeName);
  let fieldMapper;
  const stack = [...specifiers];
  while (!fieldMapper && stack.length > 0) {
    const next = stack.pop();
    fieldMapper = schemaMapper[next];
  }
  return fieldMapper ?? null;
}
function getArgumentMapper(schemaMapper) {
  const argumentMapper = schemaMapper[MapperKind.ARGUMENT];
  return argumentMapper != null ? argumentMapper : null;
}
function getDirectiveMapper(schemaMapper) {
  const directiveMapper = schemaMapper[MapperKind.DIRECTIVE];
  return directiveMapper != null ? directiveMapper : null;
}
function getEnumValueMapper(schemaMapper) {
  const enumValueMapper = schemaMapper[MapperKind.ENUM_VALUE];
  return enumValueMapper != null ? enumValueMapper : null;
}
function correctASTNodes(type) {
  if (isObjectType(type)) {
    const config = type.toConfig();
    if (config.astNode != null) {
      const fields = [];
      for (const fieldName in config.fields) {
        const fieldConfig = config.fields[fieldName];
        if (fieldConfig.astNode != null) {
          fields.push(fieldConfig.astNode);
        }
      }
      config.astNode = {
        ...config.astNode,
        kind: Kind.OBJECT_TYPE_DEFINITION,
        fields
      };
    }
    if (config.extensionASTNodes != null) {
      config.extensionASTNodes = config.extensionASTNodes.map((node) => ({
        ...node,
        kind: Kind.OBJECT_TYPE_EXTENSION,
        fields: void 0
      }));
    }
    return new GraphQLObjectType(config);
  } else if (isInterfaceType(type)) {
    const config = type.toConfig();
    if (config.astNode != null) {
      const fields = [];
      for (const fieldName in config.fields) {
        const fieldConfig = config.fields[fieldName];
        if (fieldConfig.astNode != null) {
          fields.push(fieldConfig.astNode);
        }
      }
      config.astNode = {
        ...config.astNode,
        kind: Kind.INTERFACE_TYPE_DEFINITION,
        fields
      };
    }
    if (config.extensionASTNodes != null) {
      config.extensionASTNodes = config.extensionASTNodes.map((node) => ({
        ...node,
        kind: Kind.INTERFACE_TYPE_EXTENSION,
        fields: void 0
      }));
    }
    return new GraphQLInterfaceType(config);
  } else if (isInputObjectType(type)) {
    const config = type.toConfig();
    if (config.astNode != null) {
      const fields = [];
      for (const fieldName in config.fields) {
        const fieldConfig = config.fields[fieldName];
        if (fieldConfig.astNode != null) {
          fields.push(fieldConfig.astNode);
        }
      }
      config.astNode = {
        ...config.astNode,
        kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,
        fields
      };
    }
    if (config.extensionASTNodes != null) {
      config.extensionASTNodes = config.extensionASTNodes.map((node) => ({
        ...node,
        kind: Kind.INPUT_OBJECT_TYPE_EXTENSION,
        fields: void 0
      }));
    }
    return new GraphQLInputObjectType(config);
  } else if (isEnumType(type)) {
    const config = type.toConfig();
    if (config.astNode != null) {
      const values = [];
      for (const enumKey in config.values) {
        const enumValueConfig = config.values[enumKey];
        if (enumValueConfig.astNode != null) {
          values.push(enumValueConfig.astNode);
        }
      }
      config.astNode = {
        ...config.astNode,
        values
      };
    }
    if (config.extensionASTNodes != null) {
      config.extensionASTNodes = config.extensionASTNodes.map((node) => ({
        ...node,
        values: void 0
      }));
    }
    return new GraphQLEnumType(config);
  } else {
    return type;
  }
}

// node_modules/@graphql-tools/utils/esm/heal.js
init_graphql();
function healSchema(schema) {
  healTypes(schema.getTypeMap(), schema.getDirectives());
  return schema;
}
function healTypes(originalTypeMap, directives) {
  const actualNamedTypeMap = /* @__PURE__ */ Object.create(null);
  for (const typeName in originalTypeMap) {
    const namedType = originalTypeMap[typeName];
    if (namedType == null || typeName.startsWith("__")) {
      continue;
    }
    const actualName = namedType.name;
    if (actualName.startsWith("__")) {
      continue;
    }
    if (actualNamedTypeMap[actualName] != null) {
      console.warn(`Duplicate schema type name ${actualName} found; keeping the existing one found in the schema`);
      continue;
    }
    actualNamedTypeMap[actualName] = namedType;
  }
  for (const typeName in actualNamedTypeMap) {
    const namedType = actualNamedTypeMap[typeName];
    originalTypeMap[typeName] = namedType;
  }
  for (const decl of directives) {
    decl.args = decl.args.filter((arg) => {
      arg.type = healType(arg.type);
      return arg.type !== null;
    });
  }
  for (const typeName in originalTypeMap) {
    const namedType = originalTypeMap[typeName];
    if (!typeName.startsWith("__") && typeName in actualNamedTypeMap) {
      if (namedType != null) {
        healNamedType(namedType);
      }
    }
  }
  for (const typeName in originalTypeMap) {
    if (!typeName.startsWith("__") && !(typeName in actualNamedTypeMap)) {
      delete originalTypeMap[typeName];
    }
  }
  function healNamedType(type) {
    if (isObjectType(type)) {
      healFields(type);
      healInterfaces(type);
      return;
    } else if (isInterfaceType(type)) {
      healFields(type);
      if ("getInterfaces" in type) {
        healInterfaces(type);
      }
      return;
    } else if (isUnionType(type)) {
      healUnderlyingTypes(type);
      return;
    } else if (isInputObjectType(type)) {
      healInputFields(type);
      return;
    } else if (isLeafType(type)) {
      return;
    }
    throw new Error(`Unexpected schema type: ${type}`);
  }
  function healFields(type) {
    const fieldMap = type.getFields();
    for (const [key, field] of Object.entries(fieldMap)) {
      field.args.map((arg) => {
        arg.type = healType(arg.type);
        return arg.type === null ? null : arg;
      }).filter(Boolean);
      field.type = healType(field.type);
      if (field.type === null) {
        delete fieldMap[key];
      }
    }
  }
  function healInterfaces(type) {
    if ("getInterfaces" in type) {
      const interfaces = type.getInterfaces();
      interfaces.push(...interfaces.splice(0).map((iface) => healType(iface)).filter(Boolean));
    }
  }
  function healInputFields(type) {
    const fieldMap = type.getFields();
    for (const [key, field] of Object.entries(fieldMap)) {
      field.type = healType(field.type);
      if (field.type === null) {
        delete fieldMap[key];
      }
    }
  }
  function healUnderlyingTypes(type) {
    const types = type.getTypes();
    types.push(...types.splice(0).map((t) => healType(t)).filter(Boolean));
  }
  function healType(type) {
    if (isListType(type)) {
      const healedType = healType(type.ofType);
      return healedType != null ? new GraphQLList(healedType) : null;
    } else if (isNonNullType(type)) {
      const healedType = healType(type.ofType);
      return healedType != null ? new GraphQLNonNull(healedType) : null;
    } else if (isNamedType(type)) {
      const officialType = originalTypeMap[type.name];
      if (officialType && type !== officialType) {
        return officialType;
      }
    }
    return type;
  }
}

// node_modules/@graphql-tools/utils/esm/getResolversFromSchema.js
init_graphql();
function getResolversFromSchema(schema, includeDefaultMergedResolver) {
  var _a, _b;
  const resolvers = /* @__PURE__ */ Object.create(null);
  const typeMap = schema.getTypeMap();
  for (const typeName in typeMap) {
    if (!typeName.startsWith("__")) {
      const type = typeMap[typeName];
      if (isScalarType(type)) {
        if (!isSpecifiedScalarType(type)) {
          const config = type.toConfig();
          delete config.astNode;
          resolvers[typeName] = new GraphQLScalarType(config);
        }
      } else if (isEnumType(type)) {
        resolvers[typeName] = {};
        const values = type.getValues();
        for (const value of values) {
          resolvers[typeName][value.name] = value.value;
        }
      } else if (isInterfaceType(type)) {
        if (type.resolveType != null) {
          resolvers[typeName] = {
            __resolveType: type.resolveType
          };
        }
      } else if (isUnionType(type)) {
        if (type.resolveType != null) {
          resolvers[typeName] = {
            __resolveType: type.resolveType
          };
        }
      } else if (isObjectType(type)) {
        resolvers[typeName] = {};
        if (type.isTypeOf != null) {
          resolvers[typeName].__isTypeOf = type.isTypeOf;
        }
        const fields = type.getFields();
        for (const fieldName in fields) {
          const field = fields[fieldName];
          if (field.subscribe != null) {
            resolvers[typeName][fieldName] = resolvers[typeName][fieldName] || {};
            resolvers[typeName][fieldName].subscribe = field.subscribe;
          }
          if (field.resolve != null && ((_a = field.resolve) == null ? void 0 : _a.name) !== "defaultFieldResolver") {
            switch ((_b = field.resolve) == null ? void 0 : _b.name) {
              case "defaultMergedResolver":
                if (!includeDefaultMergedResolver) {
                  continue;
                }
                break;
              case "defaultFieldResolver":
                continue;
            }
            resolvers[typeName][fieldName] = resolvers[typeName][fieldName] || {};
            resolvers[typeName][fieldName].resolve = field.resolve;
          }
        }
      }
    }
  }
  return resolvers;
}

// node_modules/@graphql-tools/utils/esm/forEachField.js
init_graphql();
function forEachField(schema, fn) {
  const typeMap = schema.getTypeMap();
  for (const typeName in typeMap) {
    const type = typeMap[typeName];
    if (!getNamedType(type).name.startsWith("__") && isObjectType(type)) {
      const fields = type.getFields();
      for (const fieldName in fields) {
        const field = fields[fieldName];
        fn(field, typeName, fieldName);
      }
    }
  }
}

// node_modules/@graphql-tools/utils/esm/forEachDefaultValue.js
init_graphql();
function forEachDefaultValue(schema, fn) {
  const typeMap = schema.getTypeMap();
  for (const typeName in typeMap) {
    const type = typeMap[typeName];
    if (!getNamedType(type).name.startsWith("__")) {
      if (isObjectType(type)) {
        const fields = type.getFields();
        for (const fieldName in fields) {
          const field = fields[fieldName];
          for (const arg of field.args) {
            arg.defaultValue = fn(arg.type, arg.defaultValue);
          }
        }
      } else if (isInputObjectType(type)) {
        const fields = type.getFields();
        for (const fieldName in fields) {
          const field = fields[fieldName];
          field.defaultValue = fn(field.type, field.defaultValue);
        }
      }
    }
  }
}

// node_modules/@graphql-tools/utils/esm/addTypes.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/prune.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/mergeDeep.js
function mergeDeep(sources, respectPrototype = false, respectArrays = false, respectArrayLength = false) {
  let expectedLength;
  let allArrays = true;
  const areArraysInTheSameLength = sources.every((source) => {
    if (Array.isArray(source)) {
      if (expectedLength === void 0) {
        expectedLength = source.length;
        return true;
      } else if (expectedLength === source.length) {
        return true;
      }
    } else {
      allArrays = false;
    }
    return false;
  });
  if (respectArrayLength && areArraysInTheSameLength) {
    return new Array(expectedLength).fill(null).map((_, index) => mergeDeep(sources.map((source) => source[index]), respectPrototype, respectArrays, respectArrayLength));
  }
  if (allArrays) {
    return sources.flat(1);
  }
  let output;
  let firstObjectSource;
  if (respectPrototype) {
    firstObjectSource = sources.find((source) => isObject(source));
    if (output == null) {
      output = {};
    }
    if (firstObjectSource) {
      Object.setPrototypeOf(output, Object.create(Object.getPrototypeOf(firstObjectSource)));
    }
  }
  for (const source of sources) {
    if (isObject(source)) {
      if (firstObjectSource) {
        const outputPrototype = Object.getPrototypeOf(output);
        const sourcePrototype = Object.getPrototypeOf(source);
        if (sourcePrototype) {
          for (const key of Object.getOwnPropertyNames(sourcePrototype)) {
            const descriptor = Object.getOwnPropertyDescriptor(sourcePrototype, key);
            if (isSome(descriptor)) {
              Object.defineProperty(outputPrototype, key, descriptor);
            }
          }
        }
      }
      for (const key in source) {
        if (output == null) {
          output = {};
        }
        if (key in output) {
          output[key] = mergeDeep([output[key], source[key]], respectPrototype, respectArrays, respectArrayLength);
        } else {
          output[key] = source[key];
        }
      }
    } else if (Array.isArray(source)) {
      if (!Array.isArray(output)) {
        output = source;
      } else {
        output = mergeDeep([output, source], respectPrototype, respectArrays, respectArrayLength);
      }
    } else {
      output = source;
    }
  }
  return output;
}
function isObject(item) {
  return item && typeof item === "object" && !Array.isArray(item);
}

// node_modules/@graphql-tools/utils/esm/selectionSets.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/fields.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/renameType.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/updateArgument.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/implementsAbstractType.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/visitResult.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/collectFields.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/AccumulatorMap.js
var AccumulatorMap = class extends Map {
  get [Symbol.toStringTag]() {
    return "AccumulatorMap";
  }
  add(key, item) {
    const group = this.get(key);
    if (group === void 0) {
      this.set(key, [item]);
    } else {
      group.push(item);
    }
  }
};

// node_modules/@graphql-tools/utils/esm/directives.js
init_graphql();
var GraphQLDeferDirective = new GraphQLDirective({
  name: "defer",
  description: "Directs the executor to defer this fragment when the `if` argument is true or undefined.",
  locations: [DirectiveLocation.FRAGMENT_SPREAD, DirectiveLocation.INLINE_FRAGMENT],
  args: {
    if: {
      type: new GraphQLNonNull(GraphQLBoolean),
      description: "Deferred when true or undefined.",
      defaultValue: true
    },
    label: {
      type: GraphQLString,
      description: "Unique name"
    }
  }
});
var GraphQLStreamDirective = new GraphQLDirective({
  name: "stream",
  description: "Directs the executor to stream plural fields when the `if` argument is true or undefined.",
  locations: [DirectiveLocation.FIELD],
  args: {
    if: {
      type: new GraphQLNonNull(GraphQLBoolean),
      description: "Stream when true or undefined.",
      defaultValue: true
    },
    label: {
      type: GraphQLString,
      description: "Unique name"
    },
    initialCount: {
      defaultValue: 0,
      type: GraphQLInt,
      description: "Number of items to return immediately"
    }
  }
});

// node_modules/@graphql-tools/utils/esm/collectFields.js
function collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, patches, visitedFragmentNames) {
  for (const selection of selectionSet.selections) {
    switch (selection.kind) {
      case Kind.FIELD: {
        if (!shouldIncludeNode(variableValues, selection)) {
          continue;
        }
        fields.add(getFieldEntryKey(selection), selection);
        break;
      }
      case Kind.INLINE_FRAGMENT: {
        if (!shouldIncludeNode(variableValues, selection) || !doesFragmentConditionMatch(schema, selection, runtimeType)) {
          continue;
        }
        const defer = getDeferValues(variableValues, selection);
        if (defer) {
          const patchFields = new AccumulatorMap();
          collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, patchFields, patches, visitedFragmentNames);
          patches.push({
            label: defer.label,
            fields: patchFields
          });
        } else {
          collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, fields, patches, visitedFragmentNames);
        }
        break;
      }
      case Kind.FRAGMENT_SPREAD: {
        const fragName = selection.name.value;
        if (!shouldIncludeNode(variableValues, selection)) {
          continue;
        }
        const defer = getDeferValues(variableValues, selection);
        if (visitedFragmentNames.has(fragName) && !defer) {
          continue;
        }
        const fragment = fragments[fragName];
        if (!fragment || !doesFragmentConditionMatch(schema, fragment, runtimeType)) {
          continue;
        }
        if (!defer) {
          visitedFragmentNames.add(fragName);
        }
        if (defer) {
          const patchFields = new AccumulatorMap();
          collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, patchFields, patches, visitedFragmentNames);
          patches.push({
            label: defer.label,
            fields: patchFields
          });
        } else {
          collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, fields, patches, visitedFragmentNames);
        }
        break;
      }
    }
  }
}
function shouldIncludeNode(variableValues, node) {
  const skip = getDirectiveValues(GraphQLSkipDirective, node, variableValues);
  if ((skip == null ? void 0 : skip["if"]) === true) {
    return false;
  }
  const include = getDirectiveValues(GraphQLIncludeDirective, node, variableValues);
  if ((include == null ? void 0 : include["if"]) === false) {
    return false;
  }
  return true;
}
function doesFragmentConditionMatch(schema, fragment, type) {
  const typeConditionNode = fragment.typeCondition;
  if (!typeConditionNode) {
    return true;
  }
  const conditionalType = typeFromAST(schema, typeConditionNode);
  if (conditionalType === type) {
    return true;
  }
  if (isAbstractType(conditionalType)) {
    const possibleTypes = schema.getPossibleTypes(conditionalType);
    return possibleTypes.includes(type);
  }
  return false;
}
function getFieldEntryKey(node) {
  return node.alias ? node.alias.value : node.name.value;
}
function getDeferValues(variableValues, node) {
  const defer = getDirectiveValues(GraphQLDeferDirective, node, variableValues);
  if (!defer) {
    return;
  }
  if (defer["if"] === false) {
    return;
  }
  return {
    label: typeof defer["label"] === "string" ? defer["label"] : void 0
  };
}
var collectSubFields = memoize5(function collectSubfields(schema, fragments, variableValues, returnType, fieldNodes) {
  const subFieldNodes = new AccumulatorMap();
  const visitedFragmentNames = /* @__PURE__ */ new Set();
  const subPatches = [];
  const subFieldsAndPatches = {
    fields: subFieldNodes,
    patches: subPatches
  };
  for (const node of fieldNodes) {
    if (node.selectionSet) {
      collectFieldsImpl(schema, fragments, variableValues, returnType, node.selectionSet, subFieldNodes, subPatches, visitedFragmentNames);
    }
  }
  return subFieldsAndPatches;
});

// node_modules/@graphql-tools/utils/esm/getOperationASTFromRequest.js
init_graphql();
function getOperationASTFromDocument(documentNode, operationName) {
  const doc = getOperationAST(documentNode, operationName);
  if (!doc) {
    throw new Error(`Cannot infer operation ${operationName || ""}`);
  }
  return doc;
}
var getOperationASTFromRequest = memoize1(function getOperationASTFromRequest2(request) {
  return getOperationASTFromDocument(request.document, request.operationName);
});

// node_modules/@graphql-tools/utils/esm/isDocumentNode.js
init_graphql();
function isDocumentNode(object) {
  return object && typeof object === "object" && "kind" in object && object.kind === Kind.DOCUMENT;
}

// node_modules/@graphql-tools/utils/esm/withCancel.js
var proxyMethodFactory = memoize2(function proxyMethodFactory2(target, targetMethod) {
  return function proxyMethod(...args) {
    return Reflect.apply(targetMethod, target, args);
  };
});

// node_modules/@graphql-tools/utils/esm/fixSchemaAst.js
init_graphql();

// node_modules/@graphql-tools/utils/esm/extractExtensionsFromSchema.js
function handleDirectiveExtensions(extensions, removeDirectives) {
  extensions = extensions || {};
  const { directives: existingDirectives, ...rest } = extensions;
  const finalExtensions = {
    ...rest
  };
  if (!removeDirectives) {
    if (existingDirectives != null) {
      const directives = {};
      for (const directiveName in existingDirectives) {
        directives[directiveName] = [...asArray(existingDirectives[directiveName])];
      }
      finalExtensions.directives = directives;
    }
  }
  return finalExtensions;
}
function extractExtensionsFromSchema(schema, removeDirectives = false) {
  const result = {
    schemaExtensions: handleDirectiveExtensions(schema.extensions, removeDirectives),
    types: {}
  };
  mapSchema(schema, {
    [MapperKind.OBJECT_TYPE]: (type) => {
      result.types[type.name] = {
        fields: {},
        type: "object",
        extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
      };
      return type;
    },
    [MapperKind.INTERFACE_TYPE]: (type) => {
      result.types[type.name] = {
        fields: {},
        type: "interface",
        extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
      };
      return type;
    },
    [MapperKind.FIELD]: (field, fieldName, typeName) => {
      result.types[typeName].fields[fieldName] = {
        arguments: {},
        extensions: handleDirectiveExtensions(field.extensions, removeDirectives)
      };
      const args = field.args;
      if (args != null) {
        for (const argName in args) {
          result.types[typeName].fields[fieldName].arguments[argName] = handleDirectiveExtensions(args[argName].extensions, removeDirectives);
        }
      }
      return field;
    },
    [MapperKind.ENUM_TYPE]: (type) => {
      result.types[type.name] = {
        values: {},
        type: "enum",
        extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
      };
      return type;
    },
    [MapperKind.ENUM_VALUE]: (value, typeName, _schema, valueName) => {
      result.types[typeName].values[valueName] = handleDirectiveExtensions(value.extensions, removeDirectives);
      return value;
    },
    [MapperKind.SCALAR_TYPE]: (type) => {
      result.types[type.name] = {
        type: "scalar",
        extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
      };
      return type;
    },
    [MapperKind.UNION_TYPE]: (type) => {
      result.types[type.name] = {
        type: "union",
        extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
      };
      return type;
    },
    [MapperKind.INPUT_OBJECT_TYPE]: (type) => {
      result.types[type.name] = {
        fields: {},
        type: "input",
        extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
      };
      return type;
    },
    [MapperKind.INPUT_OBJECT_FIELD]: (field, fieldName, typeName) => {
      result.types[typeName].fields[fieldName] = {
        extensions: handleDirectiveExtensions(field.extensions, removeDirectives)
      };
      return field;
    }
  });
  return result;
}

// node_modules/@graphql-tools/utils/esm/registerAbortSignalListener.js
var getListenersOfAbortSignal = memoize1(function getListenersOfAbortSignal2(signal) {
  const listeners = /* @__PURE__ */ new Set();
  signal.addEventListener("abort", (e) => {
    for (const listener of listeners) {
      listener(e);
    }
  }, { once: true });
  return listeners;
});
function registerAbortSignalListener(signal, listener) {
  if (signal.aborted) {
    listener();
    return;
  }
  getListenersOfAbortSignal(signal).add(listener);
}
var getAbortPromise = memoize1(function getAbortPromise2(signal) {
  if (signal.aborted) {
    return fakeRejectPromise(signal.reason);
  }
  return new Promise((_resolve, reject) => {
    if (signal.aborted) {
      reject(signal.reason);
      return;
    }
    registerAbortSignalListener(signal, () => {
      reject(signal.reason);
    });
  });
});

// node_modules/@graphql-tools/schema/esm/assertResolversPresent.js
function assertResolversPresent(schema, resolverValidationOptions = {}) {
  const { requireResolversForArgs, requireResolversForNonScalar, requireResolversForAllFields } = resolverValidationOptions;
  if (requireResolversForAllFields && (requireResolversForArgs || requireResolversForNonScalar)) {
    throw new TypeError("requireResolversForAllFields takes precedence over the more specific assertions. Please configure either requireResolversForAllFields or requireResolversForArgs / requireResolversForNonScalar, but not a combination of them.");
  }
  forEachField(schema, (field, typeName, fieldName) => {
    if (requireResolversForAllFields) {
      expectResolver("requireResolversForAllFields", requireResolversForAllFields, field, typeName, fieldName);
    }
    if (requireResolversForArgs && field.args.length > 0) {
      expectResolver("requireResolversForArgs", requireResolversForArgs, field, typeName, fieldName);
    }
    if (requireResolversForNonScalar !== "ignore" && !isScalarType(getNamedType(field.type))) {
      expectResolver("requireResolversForNonScalar", requireResolversForNonScalar, field, typeName, fieldName);
    }
  });
}
function expectResolver(validator, behavior, field, typeName, fieldName) {
  if (!field.resolve) {
    const message = `Resolver missing for "${typeName}.${fieldName}".
To disable this validator, use:
  resolverValidationOptions: {
    ${validator}: 'ignore'
  }`;
    if (behavior === "error") {
      throw new Error(message);
    }
    if (behavior === "warn") {
      console.warn(message);
    }
    return;
  }
  if (typeof field.resolve !== "function") {
    throw new Error(`Resolver "${typeName}.${fieldName}" must be a function`);
  }
}

// node_modules/@graphql-tools/schema/esm/chainResolvers.js
init_graphql();
function chainResolvers(resolvers) {
  return (root, args, ctx, info) => resolvers.reduce((prev, curResolver) => {
    if (curResolver != null) {
      return curResolver(prev, args, ctx, info);
    }
    return defaultFieldResolver(prev, args, ctx, info);
  }, root);
}

// node_modules/@graphql-tools/schema/esm/addResolversToSchema.js
init_graphql();

// node_modules/@graphql-tools/schema/esm/checkForResolveTypeResolver.js
function checkForResolveTypeResolver(schema, requireResolversForResolveType) {
  mapSchema(schema, {
    [MapperKind.ABSTRACT_TYPE]: (type) => {
      if (!type.resolveType) {
        const message = `Type "${type.name}" is missing a "__resolveType" resolver. Pass 'ignore' into "resolverValidationOptions.requireResolversForResolveType" to disable this error.`;
        if (requireResolversForResolveType === "error") {
          throw new Error(message);
        }
        if (requireResolversForResolveType === "warn") {
          console.warn(message);
        }
      }
      return void 0;
    }
  });
}

// node_modules/@graphql-tools/schema/esm/extendResolversFromInterfaces.js
function extendResolversFromInterfaces(schema, resolvers) {
  const extendedResolvers = {};
  const typeMap = schema.getTypeMap();
  for (const typeName in typeMap) {
    const type = typeMap[typeName];
    if ("getInterfaces" in type) {
      extendedResolvers[typeName] = {};
      for (const iFace of type.getInterfaces()) {
        if (resolvers[iFace.name]) {
          for (const fieldName in resolvers[iFace.name]) {
            if (fieldName === "__isTypeOf" || !fieldName.startsWith("__")) {
              extendedResolvers[typeName][fieldName] = resolvers[iFace.name][fieldName];
            }
          }
        }
      }
      const typeResolvers = resolvers[typeName];
      extendedResolvers[typeName] = {
        ...extendedResolvers[typeName],
        ...typeResolvers
      };
    } else {
      const typeResolvers = resolvers[typeName];
      if (typeResolvers != null) {
        extendedResolvers[typeName] = typeResolvers;
      }
    }
  }
  return extendedResolvers;
}

// node_modules/@graphql-tools/schema/esm/addResolversToSchema.js
function addResolversToSchema({ schema, resolvers: inputResolvers, defaultFieldResolver: defaultFieldResolver2, resolverValidationOptions = {}, inheritResolversFromInterfaces = false, updateResolversInPlace = false }) {
  const { requireResolversToMatchSchema = "error", requireResolversForResolveType } = resolverValidationOptions;
  const resolvers = inheritResolversFromInterfaces ? extendResolversFromInterfaces(schema, inputResolvers) : inputResolvers;
  for (const typeName in resolvers) {
    const resolverValue = resolvers[typeName];
    const resolverType = typeof resolverValue;
    if (resolverType !== "object") {
      throw new Error(`"${typeName}" defined in resolvers, but has invalid value "${resolverValue}". The resolver's value must be of type object.`);
    }
    const type = schema.getType(typeName);
    if (type == null) {
      const msg = `"${typeName}" defined in resolvers, but not in schema`;
      if (requireResolversToMatchSchema && requireResolversToMatchSchema !== "error") {
        if (requireResolversToMatchSchema === "warn") {
          console.warn(msg);
        }
        continue;
      }
      throw new Error(msg);
    } else if (isSpecifiedScalarType(type)) {
      for (const fieldName in resolverValue) {
        if (fieldName.startsWith("__")) {
          type[fieldName.substring(2)] = resolverValue[fieldName];
        } else {
          type[fieldName] = resolverValue[fieldName];
        }
      }
    } else if (isEnumType(type)) {
      const values = type.getValues();
      for (const fieldName in resolverValue) {
        if (!fieldName.startsWith("__") && !values.some((value) => value.name === fieldName) && requireResolversToMatchSchema && requireResolversToMatchSchema !== "ignore") {
          const msg = `${type.name}.${fieldName} was defined in resolvers, but not present within ${type.name}`;
          if (requireResolversToMatchSchema === "error") {
            throw new Error(msg);
          } else {
            console.warn(msg);
          }
        }
      }
    } else if (isUnionType(type)) {
      for (const fieldName in resolverValue) {
        if (!fieldName.startsWith("__") && requireResolversToMatchSchema && requireResolversToMatchSchema !== "ignore") {
          const msg = `${type.name}.${fieldName} was defined in resolvers, but ${type.name} is not an object or interface type`;
          if (requireResolversToMatchSchema === "error") {
            throw new Error(msg);
          } else {
            console.warn(msg);
          }
        }
      }
    } else if (isObjectType(type) || isInterfaceType(type)) {
      for (const fieldName in resolverValue) {
        if (!fieldName.startsWith("__")) {
          const fields = type.getFields();
          const field = fields[fieldName];
          if (field == null) {
            if (requireResolversToMatchSchema && requireResolversToMatchSchema !== "ignore") {
              const msg = `${typeName}.${fieldName} defined in resolvers, but not in schema`;
              if (requireResolversToMatchSchema === "error") {
                throw new Error(msg);
              } else {
                console.error(msg);
              }
            }
          } else {
            const fieldResolve = resolverValue[fieldName];
            if (typeof fieldResolve !== "function" && typeof fieldResolve !== "object") {
              throw new Error(`Resolver ${typeName}.${fieldName} must be object or function`);
            }
          }
        }
      }
    }
  }
  schema = updateResolversInPlace ? addResolversToExistingSchema(schema, resolvers, defaultFieldResolver2) : createNewSchemaWithResolvers(schema, resolvers, defaultFieldResolver2);
  if (requireResolversForResolveType && requireResolversForResolveType !== "ignore") {
    checkForResolveTypeResolver(schema, requireResolversForResolveType);
  }
  return schema;
}
function addResolversToExistingSchema(schema, resolvers, defaultFieldResolver2) {
  var _a, _b, _c, _d;
  const typeMap = schema.getTypeMap();
  for (const typeName in resolvers) {
    const type = schema.getType(typeName);
    const resolverValue = resolvers[typeName];
    if (isScalarType(type)) {
      for (const fieldName in resolverValue) {
        if (fieldName.startsWith("__")) {
          type[fieldName.substring(2)] = resolverValue[fieldName];
        } else if (fieldName === "astNode" && type.astNode != null) {
          type.astNode = {
            ...type.astNode,
            description: ((_a = resolverValue == null ? void 0 : resolverValue.astNode) == null ? void 0 : _a.description) ?? type.astNode.description,
            directives: (type.astNode.directives ?? []).concat(((_b = resolverValue == null ? void 0 : resolverValue.astNode) == null ? void 0 : _b.directives) ?? [])
          };
        } else if (fieldName === "extensionASTNodes" && type.extensionASTNodes != null) {
          type.extensionASTNodes = type.extensionASTNodes.concat((resolverValue == null ? void 0 : resolverValue.extensionASTNodes) ?? []);
        } else if (fieldName === "extensions" && type.extensions != null && resolverValue.extensions != null) {
          type.extensions = Object.assign(/* @__PURE__ */ Object.create(null), type.extensions, resolverValue.extensions);
        } else {
          type[fieldName] = resolverValue[fieldName];
        }
      }
    } else if (isEnumType(type)) {
      const config = type.toConfig();
      const enumValueConfigMap = config.values;
      for (const fieldName in resolverValue) {
        if (fieldName.startsWith("__")) {
          config[fieldName.substring(2)] = resolverValue[fieldName];
        } else if (fieldName === "astNode" && config.astNode != null) {
          config.astNode = {
            ...config.astNode,
            description: ((_c = resolverValue == null ? void 0 : resolverValue.astNode) == null ? void 0 : _c.description) ?? config.astNode.description,
            directives: (config.astNode.directives ?? []).concat(((_d = resolverValue == null ? void 0 : resolverValue.astNode) == null ? void 0 : _d.directives) ?? [])
          };
        } else if (fieldName === "extensionASTNodes" && config.extensionASTNodes != null) {
          config.extensionASTNodes = config.extensionASTNodes.concat((resolverValue == null ? void 0 : resolverValue.extensionASTNodes) ?? []);
        } else if (fieldName === "extensions" && type.extensions != null && resolverValue.extensions != null) {
          type.extensions = Object.assign(/* @__PURE__ */ Object.create(null), type.extensions, resolverValue.extensions);
        } else if (enumValueConfigMap[fieldName]) {
          enumValueConfigMap[fieldName].value = resolverValue[fieldName];
        }
      }
      typeMap[typeName] = new GraphQLEnumType(config);
    } else if (isUnionType(type)) {
      for (const fieldName in resolverValue) {
        if (fieldName.startsWith("__")) {
          type[fieldName.substring(2)] = resolverValue[fieldName];
        }
      }
    } else if (isObjectType(type) || isInterfaceType(type)) {
      for (const fieldName in resolverValue) {
        if (fieldName.startsWith("__")) {
          type[fieldName.substring(2)] = resolverValue[fieldName];
          continue;
        }
        const fields = type.getFields();
        const field = fields[fieldName];
        if (field != null) {
          const fieldResolve = resolverValue[fieldName];
          if (typeof fieldResolve === "function") {
            field.resolve = fieldResolve.bind(resolverValue);
          } else {
            setFieldProperties(field, fieldResolve);
          }
        }
      }
    }
  }
  forEachDefaultValue(schema, serializeInputValue);
  healSchema(schema);
  forEachDefaultValue(schema, parseInputValue);
  if (defaultFieldResolver2 != null) {
    forEachField(schema, (field) => {
      if (!field.resolve) {
        field.resolve = defaultFieldResolver2;
      }
    });
  }
  return schema;
}
function createNewSchemaWithResolvers(schema, resolvers, defaultFieldResolver2) {
  schema = mapSchema(schema, {
    [MapperKind.SCALAR_TYPE]: (type) => {
      var _a, _b;
      const config = type.toConfig();
      const resolverValue = resolvers[type.name];
      if (!isSpecifiedScalarType(type) && resolverValue != null) {
        for (const fieldName in resolverValue) {
          if (fieldName.startsWith("__")) {
            config[fieldName.substring(2)] = resolverValue[fieldName];
          } else if (fieldName === "astNode" && config.astNode != null) {
            config.astNode = {
              ...config.astNode,
              description: ((_a = resolverValue == null ? void 0 : resolverValue.astNode) == null ? void 0 : _a.description) ?? config.astNode.description,
              directives: (config.astNode.directives ?? []).concat(((_b = resolverValue == null ? void 0 : resolverValue.astNode) == null ? void 0 : _b.directives) ?? [])
            };
          } else if (fieldName === "extensionASTNodes" && config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.concat((resolverValue == null ? void 0 : resolverValue.extensionASTNodes) ?? []);
          } else if (fieldName === "extensions" && config.extensions != null && resolverValue.extensions != null) {
            config.extensions = Object.assign(/* @__PURE__ */ Object.create(null), type.extensions, resolverValue.extensions);
          } else {
            config[fieldName] = resolverValue[fieldName];
          }
        }
        return new GraphQLScalarType(config);
      }
    },
    [MapperKind.ENUM_TYPE]: (type) => {
      var _a, _b;
      const resolverValue = resolvers[type.name];
      const config = type.toConfig();
      const enumValueConfigMap = config.values;
      if (resolverValue != null) {
        for (const fieldName in resolverValue) {
          if (fieldName.startsWith("__")) {
            config[fieldName.substring(2)] = resolverValue[fieldName];
          } else if (fieldName === "astNode" && config.astNode != null) {
            config.astNode = {
              ...config.astNode,
              description: ((_a = resolverValue == null ? void 0 : resolverValue.astNode) == null ? void 0 : _a.description) ?? config.astNode.description,
              directives: (config.astNode.directives ?? []).concat(((_b = resolverValue == null ? void 0 : resolverValue.astNode) == null ? void 0 : _b.directives) ?? [])
            };
          } else if (fieldName === "extensionASTNodes" && config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.concat((resolverValue == null ? void 0 : resolverValue.extensionASTNodes) ?? []);
          } else if (fieldName === "extensions" && config.extensions != null && resolverValue.extensions != null) {
            config.extensions = Object.assign(/* @__PURE__ */ Object.create(null), type.extensions, resolverValue.extensions);
          } else if (enumValueConfigMap[fieldName]) {
            enumValueConfigMap[fieldName].value = resolverValue[fieldName];
          }
        }
        return new GraphQLEnumType(config);
      }
    },
    [MapperKind.UNION_TYPE]: (type) => {
      const resolverValue = resolvers[type.name];
      if (resolverValue != null) {
        const config = type.toConfig();
        if (resolverValue["__resolveType"]) {
          config.resolveType = resolverValue["__resolveType"];
        }
        return new GraphQLUnionType(config);
      }
    },
    [MapperKind.OBJECT_TYPE]: (type) => {
      const resolverValue = resolvers[type.name];
      if (resolverValue != null) {
        const config = type.toConfig();
        if (resolverValue["__isTypeOf"]) {
          config.isTypeOf = resolverValue["__isTypeOf"];
        }
        return new GraphQLObjectType(config);
      }
    },
    [MapperKind.INTERFACE_TYPE]: (type) => {
      const resolverValue = resolvers[type.name];
      if (resolverValue != null) {
        const config = type.toConfig();
        if (resolverValue["__resolveType"]) {
          config.resolveType = resolverValue["__resolveType"];
        }
        return new GraphQLInterfaceType(config);
      }
    },
    [MapperKind.COMPOSITE_FIELD]: (fieldConfig, fieldName, typeName) => {
      const resolverValue = resolvers[typeName];
      if (resolverValue != null) {
        const fieldResolve = resolverValue[fieldName];
        if (fieldResolve != null) {
          const newFieldConfig = { ...fieldConfig };
          if (typeof fieldResolve === "function") {
            newFieldConfig.resolve = fieldResolve.bind(resolverValue);
          } else {
            setFieldProperties(newFieldConfig, fieldResolve);
          }
          return newFieldConfig;
        }
      }
    }
  });
  if (defaultFieldResolver2 != null) {
    schema = mapSchema(schema, {
      [MapperKind.OBJECT_FIELD]: (fieldConfig) => ({
        ...fieldConfig,
        resolve: fieldConfig.resolve != null ? fieldConfig.resolve : defaultFieldResolver2
      })
    });
  }
  return schema;
}
function setFieldProperties(field, propertiesObj) {
  for (const propertyName in propertiesObj) {
    field[propertyName] = propertiesObj[propertyName];
  }
}

// node_modules/@graphql-tools/schema/esm/makeExecutableSchema.js
init_graphql();

// node_modules/@graphql-tools/merge/esm/merge-resolvers.js
function mergeResolvers(resolversDefinitions, options) {
  if (!resolversDefinitions || Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0) {
    return {};
  }
  if (!Array.isArray(resolversDefinitions)) {
    return resolversDefinitions;
  }
  if (resolversDefinitions.length === 1) {
    return resolversDefinitions[0] || {};
  }
  const resolvers = new Array();
  for (let resolversDefinition of resolversDefinitions) {
    if (Array.isArray(resolversDefinition)) {
      resolversDefinition = mergeResolvers(resolversDefinition);
    }
    if (typeof resolversDefinition === "object" && resolversDefinition) {
      resolvers.push(resolversDefinition);
    }
  }
  const result = mergeDeep(resolvers, true);
  if (options == null ? void 0 : options.exclusions) {
    for (const exclusion of options.exclusions) {
      const [typeName, fieldName] = exclusion.split(".");
      if (["__proto__", "constructor", "prototype"].includes(typeName) || ["__proto__", "constructor", "prototype"].includes(fieldName)) {
        continue;
      }
      if (!fieldName || fieldName === "*") {
        delete result[typeName];
      } else if (result[typeName]) {
        delete result[typeName][fieldName];
      }
    }
  }
  return result;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/arguments.js
function mergeArguments(args1, args2, config) {
  const result = deduplicateArguments([...args2, ...args1].filter(isSome), config);
  if (config && config.sort) {
    result.sort(compareNodes);
  }
  return result;
}
function deduplicateArguments(args, config) {
  return args.reduce((acc, current) => {
    const dupIndex = acc.findIndex((arg) => arg.name.value === current.name.value);
    if (dupIndex === -1) {
      return acc.concat([current]);
    } else if (!(config == null ? void 0 : config.reverseArguments)) {
      acc[dupIndex] = current;
    }
    return acc;
  }, []);
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js
function directiveAlreadyExists(directivesArr, otherDirective) {
  return !!directivesArr.find((directive) => directive.name.value === otherDirective.name.value);
}
function isRepeatableDirective(directive, directives) {
  var _a;
  return !!((_a = directives == null ? void 0 : directives[directive.name.value]) == null ? void 0 : _a.repeatable);
}
function nameAlreadyExists(name, namesArr) {
  return namesArr.some(({ value }) => value === name.value);
}
function mergeArguments2(a1, a2) {
  const result = [...a2];
  for (const argument of a1) {
    const existingIndex = result.findIndex((a) => a.name.value === argument.name.value);
    if (existingIndex > -1) {
      const existingArg = result[existingIndex];
      if (existingArg.value.kind === "ListValue") {
        const source = existingArg.value.values;
        const target = argument.value.values;
        existingArg.value.values = deduplicateLists(source, target, (targetVal, source2) => {
          const value = targetVal.value;
          return !value || !source2.some((sourceVal) => sourceVal.value === value);
        });
      } else {
        existingArg.value = argument.value;
      }
    } else {
      result.push(argument);
    }
  }
  return result;
}
function deduplicateDirectives(directives, definitions) {
  return directives.map((directive, i, all) => {
    const firstAt = all.findIndex((d) => d.name.value === directive.name.value);
    if (firstAt !== i && !isRepeatableDirective(directive, definitions)) {
      const dup = all[firstAt];
      directive.arguments = mergeArguments2(directive.arguments, dup.arguments);
      return null;
    }
    return directive;
  }).filter(isSome);
}
function mergeDirectives(d1 = [], d2 = [], config, directives) {
  const reverseOrder = config && config.reverseDirectives;
  const asNext = reverseOrder ? d1 : d2;
  const asFirst = reverseOrder ? d2 : d1;
  const result = deduplicateDirectives([...asNext], directives);
  for (const directive of asFirst) {
    if (directiveAlreadyExists(result, directive) && !isRepeatableDirective(directive, directives)) {
      const existingDirectiveIndex = result.findIndex((d) => d.name.value === directive.name.value);
      const existingDirective = result[existingDirectiveIndex];
      result[existingDirectiveIndex].arguments = mergeArguments2(directive.arguments || [], existingDirective.arguments || []);
    } else {
      result.push(directive);
    }
  }
  return result;
}
function mergeDirective(node, existingNode) {
  if (existingNode) {
    return {
      ...node,
      arguments: deduplicateLists(existingNode.arguments || [], node.arguments || [], (arg, existingArgs) => !nameAlreadyExists(arg.name, existingArgs.map((a) => a.name))),
      locations: [
        ...existingNode.locations,
        ...node.locations.filter((name) => !nameAlreadyExists(name, existingNode.locations))
      ]
    };
  }
  return node;
}
function deduplicateLists(source, target, filterFn) {
  return source.concat(target.filter((val) => filterFn(val, source)));
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum-values.js
function mergeEnumValues(first, second, config, directives) {
  if (config == null ? void 0 : config.consistentEnumMerge) {
    const reversed = [];
    if (first) {
      reversed.push(...first);
    }
    first = second;
    second = reversed;
  }
  const enumValueMap = /* @__PURE__ */ new Map();
  if (first) {
    for (const firstValue of first) {
      enumValueMap.set(firstValue.name.value, firstValue);
    }
  }
  if (second) {
    for (const secondValue of second) {
      const enumValue = secondValue.name.value;
      if (enumValueMap.has(enumValue)) {
        const firstValue = enumValueMap.get(enumValue);
        firstValue.description = secondValue.description || firstValue.description;
        firstValue.directives = mergeDirectives(secondValue.directives, firstValue.directives, directives);
      } else {
        enumValueMap.set(enumValue, secondValue);
      }
    }
  }
  const result = [...enumValueMap.values()];
  if (config && config.sort) {
    result.sort(compareNodes);
  }
  return result;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum.js
init_graphql();
function mergeEnum(e1, e2, config, directives) {
  if (e2) {
    return {
      name: e1.name,
      description: e1["description"] || e2["description"],
      kind: (config == null ? void 0 : config.convertExtensions) || e1.kind === "EnumTypeDefinition" || e2.kind === "EnumTypeDefinition" ? "EnumTypeDefinition" : "EnumTypeExtension",
      loc: e1.loc,
      directives: mergeDirectives(e1.directives, e2.directives, config, directives),
      values: mergeEnumValues(e1.values, e2.values, config)
    };
  }
  return (config == null ? void 0 : config.convertExtensions) ? {
    ...e1,
    kind: Kind.ENUM_TYPE_DEFINITION
  } : e1;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/utils.js
init_graphql();
function isStringTypes(types) {
  return typeof types === "string";
}
function isSourceTypes(types) {
  return types instanceof Source;
}
function extractType(type) {
  let visitedType = type;
  while (visitedType.kind === Kind.LIST_TYPE || visitedType.kind === "NonNullType") {
    visitedType = visitedType.type;
  }
  return visitedType;
}
function isWrappingTypeNode(type) {
  return type.kind !== Kind.NAMED_TYPE;
}
function isListTypeNode(type) {
  return type.kind === Kind.LIST_TYPE;
}
function isNonNullTypeNode(type) {
  return type.kind === Kind.NON_NULL_TYPE;
}
function printTypeNode(type) {
  if (isListTypeNode(type)) {
    return `[${printTypeNode(type.type)}]`;
  }
  if (isNonNullTypeNode(type)) {
    return `${printTypeNode(type.type)}!`;
  }
  return type.name.value;
}
var CompareVal;
(function(CompareVal2) {
  CompareVal2[CompareVal2["A_SMALLER_THAN_B"] = -1] = "A_SMALLER_THAN_B";
  CompareVal2[CompareVal2["A_EQUALS_B"] = 0] = "A_EQUALS_B";
  CompareVal2[CompareVal2["A_GREATER_THAN_B"] = 1] = "A_GREATER_THAN_B";
})(CompareVal || (CompareVal = {}));
function defaultStringComparator(a, b) {
  if (a == null && b == null) {
    return CompareVal.A_EQUALS_B;
  }
  if (a == null) {
    return CompareVal.A_SMALLER_THAN_B;
  }
  if (b == null) {
    return CompareVal.A_GREATER_THAN_B;
  }
  if (a < b)
    return CompareVal.A_SMALLER_THAN_B;
  if (a > b)
    return CompareVal.A_GREATER_THAN_B;
  return CompareVal.A_EQUALS_B;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js
function fieldAlreadyExists(fieldsArr, otherField) {
  const resultIndex = fieldsArr.findIndex((field) => field.name.value === otherField.name.value);
  return [resultIndex > -1 ? fieldsArr[resultIndex] : null, resultIndex];
}
function mergeFields(type, f1, f2, config, directives) {
  const result = [];
  if (f2 != null) {
    result.push(...f2);
  }
  if (f1 != null) {
    for (const field of f1) {
      const [existing, existingIndex] = fieldAlreadyExists(result, field);
      if (existing && !(config == null ? void 0 : config.ignoreFieldConflicts)) {
        const newField = (config == null ? void 0 : config.onFieldTypeConflict) && config.onFieldTypeConflict(existing, field, type, config == null ? void 0 : config.throwOnConflict) || preventConflicts(type, existing, field, config == null ? void 0 : config.throwOnConflict);
        newField.arguments = mergeArguments(field["arguments"] || [], existing["arguments"] || [], config);
        newField.directives = mergeDirectives(field.directives, existing.directives, config, directives);
        newField.description = field.description || existing.description;
        result[existingIndex] = newField;
      } else {
        result.push(field);
      }
    }
  }
  if (config && config.sort) {
    result.sort(compareNodes);
  }
  if (config && config.exclusions) {
    const exclusions = config.exclusions;
    return result.filter((field) => !exclusions.includes(`${type.name.value}.${field.name.value}`));
  }
  return result;
}
function preventConflicts(type, a, b, ignoreNullability = false) {
  const aType = printTypeNode(a.type);
  const bType = printTypeNode(b.type);
  if (aType !== bType) {
    const t1 = extractType(a.type);
    const t2 = extractType(b.type);
    if (t1.name.value !== t2.name.value) {
      throw new Error(`Field "${b.name.value}" already defined with a different type. Declared as "${t1.name.value}", but you tried to override with "${t2.name.value}"`);
    }
    if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {
      throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);
    }
  }
  if (isNonNullTypeNode(b.type) && !isNonNullTypeNode(a.type)) {
    a.type = b.type;
  }
  return a;
}
function safeChangeForFieldType(oldType, newType, ignoreNullability = false) {
  if (!isWrappingTypeNode(oldType) && !isWrappingTypeNode(newType)) {
    return oldType.toString() === newType.toString();
  }
  if (isNonNullTypeNode(newType)) {
    const ofType = isNonNullTypeNode(oldType) ? oldType.type : oldType;
    return safeChangeForFieldType(ofType, newType.type);
  }
  if (isNonNullTypeNode(oldType)) {
    return safeChangeForFieldType(newType, oldType, ignoreNullability);
  }
  if (isListTypeNode(oldType)) {
    return isListTypeNode(newType) && safeChangeForFieldType(oldType.type, newType.type) || isNonNullTypeNode(newType) && safeChangeForFieldType(oldType, newType["type"]);
  }
  return false;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/input-type.js
init_graphql();
function mergeInputType(node, existingNode, config, directives) {
  if (existingNode) {
    try {
      return {
        name: node.name,
        description: node["description"] || existingNode["description"],
        kind: (config == null ? void 0 : config.convertExtensions) || node.kind === "InputObjectTypeDefinition" || existingNode.kind === "InputObjectTypeDefinition" ? "InputObjectTypeDefinition" : "InputObjectTypeExtension",
        loc: node.loc,
        fields: mergeFields(node, node.fields, existingNode.fields, config),
        directives: mergeDirectives(node.directives, existingNode.directives, config, directives)
      };
    } catch (e) {
      throw new Error(`Unable to merge GraphQL input type "${node.name.value}": ${e.message}`);
    }
  }
  return (config == null ? void 0 : config.convertExtensions) ? {
    ...node,
    kind: Kind.INPUT_OBJECT_TYPE_DEFINITION
  } : node;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/interface.js
init_graphql();

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js
function alreadyExists(arr, other) {
  return !!arr.find((i) => i.name.value === other.name.value);
}
function mergeNamedTypeArray(first = [], second = [], config = {}) {
  const result = [...second, ...first.filter((d) => !alreadyExists(second, d))];
  if (config && config.sort) {
    result.sort(compareNodes);
  }
  return result;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/interface.js
function mergeInterface(node, existingNode, config, directives) {
  if (existingNode) {
    try {
      return {
        name: node.name,
        description: node["description"] || existingNode["description"],
        kind: (config == null ? void 0 : config.convertExtensions) || node.kind === "InterfaceTypeDefinition" || existingNode.kind === "InterfaceTypeDefinition" ? "InterfaceTypeDefinition" : "InterfaceTypeExtension",
        loc: node.loc,
        fields: mergeFields(node, node.fields, existingNode.fields, config, directives),
        directives: mergeDirectives(node.directives, existingNode.directives, config, directives),
        interfaces: node["interfaces"] ? mergeNamedTypeArray(node["interfaces"], existingNode["interfaces"], config) : void 0
      };
    } catch (e) {
      throw new Error(`Unable to merge GraphQL interface "${node.name.value}": ${e.message}`);
    }
  }
  return (config == null ? void 0 : config.convertExtensions) ? {
    ...node,
    kind: Kind.INTERFACE_TYPE_DEFINITION
  } : node;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js
init_graphql();

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/scalar.js
init_graphql();
function mergeScalar(node, existingNode, config, directives) {
  if (existingNode) {
    return {
      name: node.name,
      description: node["description"] || existingNode["description"],
      kind: (config == null ? void 0 : config.convertExtensions) || node.kind === "ScalarTypeDefinition" || existingNode.kind === "ScalarTypeDefinition" ? "ScalarTypeDefinition" : "ScalarTypeExtension",
      loc: node.loc,
      directives: mergeDirectives(node.directives, existingNode.directives, config, directives)
    };
  }
  return (config == null ? void 0 : config.convertExtensions) ? {
    ...node,
    kind: Kind.SCALAR_TYPE_DEFINITION
  } : node;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/schema-def.js
init_graphql();
var DEFAULT_OPERATION_TYPE_NAME_MAP = {
  query: "Query",
  mutation: "Mutation",
  subscription: "Subscription"
};
function mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {
  const finalOpNodeList = [];
  for (const opNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {
    const opNode = opNodeList.find((n) => n.operation === opNodeType) || existingOpNodeList.find((n) => n.operation === opNodeType);
    if (opNode) {
      finalOpNodeList.push(opNode);
    }
  }
  return finalOpNodeList;
}
function mergeSchemaDefs(node, existingNode, config, directives) {
  if (existingNode) {
    return {
      kind: node.kind === Kind.SCHEMA_DEFINITION || existingNode.kind === Kind.SCHEMA_DEFINITION ? Kind.SCHEMA_DEFINITION : Kind.SCHEMA_EXTENSION,
      description: node["description"] || existingNode["description"],
      directives: mergeDirectives(node.directives, existingNode.directives, config, directives),
      operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes)
    };
  }
  return (config == null ? void 0 : config.convertExtensions) ? {
    ...node,
    kind: Kind.SCHEMA_DEFINITION
  } : node;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/type.js
init_graphql();
function mergeType(node, existingNode, config, directives) {
  if (existingNode) {
    try {
      return {
        name: node.name,
        description: node["description"] || existingNode["description"],
        kind: (config == null ? void 0 : config.convertExtensions) || node.kind === "ObjectTypeDefinition" || existingNode.kind === "ObjectTypeDefinition" ? "ObjectTypeDefinition" : "ObjectTypeExtension",
        loc: node.loc,
        fields: mergeFields(node, node.fields, existingNode.fields, config, directives),
        directives: mergeDirectives(node.directives, existingNode.directives, config, directives),
        interfaces: mergeNamedTypeArray(node.interfaces, existingNode.interfaces, config)
      };
    } catch (e) {
      throw new Error(`Unable to merge GraphQL type "${node.name.value}": ${e.message}`);
    }
  }
  return (config == null ? void 0 : config.convertExtensions) ? {
    ...node,
    kind: Kind.OBJECT_TYPE_DEFINITION
  } : node;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/union.js
init_graphql();
function mergeUnion(first, second, config, directives) {
  if (second) {
    return {
      name: first.name,
      description: first["description"] || second["description"],
      // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
      directives: mergeDirectives(first.directives, second.directives, config, directives),
      kind: (config == null ? void 0 : config.convertExtensions) || first.kind === "UnionTypeDefinition" || second.kind === "UnionTypeDefinition" ? Kind.UNION_TYPE_DEFINITION : Kind.UNION_TYPE_EXTENSION,
      loc: first.loc,
      types: mergeNamedTypeArray(first.types, second.types, config)
    };
  }
  return (config == null ? void 0 : config.convertExtensions) ? {
    ...first,
    kind: Kind.UNION_TYPE_DEFINITION
  } : first;
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js
var schemaDefSymbol = "SCHEMA_DEF_SYMBOL";
function isNamedDefinitionNode(definitionNode) {
  return "name" in definitionNode;
}
function mergeGraphQLNodes(nodes, config, directives = {}) {
  var _a, _b, _c;
  const mergedResultMap = directives;
  for (const nodeDefinition of nodes) {
    if (isNamedDefinitionNode(nodeDefinition)) {
      const name = (_a = nodeDefinition.name) == null ? void 0 : _a.value;
      if (config == null ? void 0 : config.commentDescriptions) {
        collectComment(nodeDefinition);
      }
      if (name == null) {
        continue;
      }
      if (((_b = config == null ? void 0 : config.exclusions) == null ? void 0 : _b.includes(name + ".*")) || ((_c = config == null ? void 0 : config.exclusions) == null ? void 0 : _c.includes(name))) {
        delete mergedResultMap[name];
      } else {
        switch (nodeDefinition.kind) {
          case Kind.OBJECT_TYPE_DEFINITION:
          case Kind.OBJECT_TYPE_EXTENSION:
            mergedResultMap[name] = mergeType(nodeDefinition, mergedResultMap[name], config, directives);
            break;
          case Kind.ENUM_TYPE_DEFINITION:
          case Kind.ENUM_TYPE_EXTENSION:
            mergedResultMap[name] = mergeEnum(nodeDefinition, mergedResultMap[name], config, directives);
            break;
          case Kind.UNION_TYPE_DEFINITION:
          case Kind.UNION_TYPE_EXTENSION:
            mergedResultMap[name] = mergeUnion(nodeDefinition, mergedResultMap[name], config, directives);
            break;
          case Kind.SCALAR_TYPE_DEFINITION:
          case Kind.SCALAR_TYPE_EXTENSION:
            mergedResultMap[name] = mergeScalar(nodeDefinition, mergedResultMap[name], config, directives);
            break;
          case Kind.INPUT_OBJECT_TYPE_DEFINITION:
          case Kind.INPUT_OBJECT_TYPE_EXTENSION:
            mergedResultMap[name] = mergeInputType(nodeDefinition, mergedResultMap[name], config, directives);
            break;
          case Kind.INTERFACE_TYPE_DEFINITION:
          case Kind.INTERFACE_TYPE_EXTENSION:
            mergedResultMap[name] = mergeInterface(nodeDefinition, mergedResultMap[name], config, directives);
            break;
          case Kind.DIRECTIVE_DEFINITION:
            if (mergedResultMap[name]) {
              const isInheritedFromPrototype = name in {};
              if (isInheritedFromPrototype) {
                if (!isASTNode(mergedResultMap[name])) {
                  mergedResultMap[name] = void 0;
                }
              }
            }
            mergedResultMap[name] = mergeDirective(nodeDefinition, mergedResultMap[name]);
            break;
        }
      }
    } else if (nodeDefinition.kind === Kind.SCHEMA_DEFINITION || nodeDefinition.kind === Kind.SCHEMA_EXTENSION) {
      mergedResultMap[schemaDefSymbol] = mergeSchemaDefs(nodeDefinition, mergedResultMap[schemaDefSymbol], config);
    }
  }
  return mergedResultMap;
}
function isASTNode(node) {
  return node != null && typeof node === "object" && "kind" in node && typeof node.kind === "string";
}

// node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js
init_graphql();
function mergeTypeDefs(typeSource, config) {
  resetComments();
  const doc = {
    kind: Kind.DOCUMENT,
    definitions: mergeGraphQLTypes(typeSource, {
      useSchemaDefinition: true,
      forceSchemaDefinition: false,
      throwOnConflict: false,
      commentDescriptions: false,
      ...config
    })
  };
  let result;
  if (config == null ? void 0 : config.commentDescriptions) {
    result = printWithComments(doc);
  } else {
    result = doc;
  }
  resetComments();
  return result;
}
function visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = /* @__PURE__ */ new Set()) {
  if (typeSource && !visitedTypeSources.has(typeSource)) {
    visitedTypeSources.add(typeSource);
    if (typeof typeSource === "function") {
      visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources);
    } else if (Array.isArray(typeSource)) {
      for (const type of typeSource) {
        visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources);
      }
    } else if (isSchema(typeSource)) {
      const documentNode = getDocumentNodeFromSchema(typeSource, options);
      visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);
    } else if (isStringTypes(typeSource) || isSourceTypes(typeSource)) {
      const documentNode = parse(typeSource, options);
      visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);
    } else if (typeof typeSource === "object" && isDefinitionNode(typeSource)) {
      if (typeSource.kind === Kind.DIRECTIVE_DEFINITION) {
        allDirectives.push(typeSource);
      } else {
        allNodes.push(typeSource);
      }
    } else if (isDocumentNode(typeSource)) {
      visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources);
    } else {
      throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);
    }
  }
  return { allDirectives, allNodes };
}
function mergeGraphQLTypes(typeSource, config) {
  var _a, _b, _c;
  resetComments();
  const { allDirectives, allNodes } = visitTypeSources(typeSource, config);
  const mergedDirectives = mergeGraphQLNodes(allDirectives, config);
  const mergedNodes = mergeGraphQLNodes(allNodes, config, mergedDirectives);
  if (config == null ? void 0 : config.useSchemaDefinition) {
    const schemaDef = mergedNodes[schemaDefSymbol] || {
      kind: Kind.SCHEMA_DEFINITION,
      operationTypes: []
    };
    const operationTypes = schemaDef.operationTypes;
    for (const opTypeDefNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {
      const opTypeDefNode = operationTypes.find((operationType) => operationType.operation === opTypeDefNodeType);
      if (!opTypeDefNode) {
        const possibleRootTypeName = DEFAULT_OPERATION_TYPE_NAME_MAP[opTypeDefNodeType];
        const existingPossibleRootType = mergedNodes[possibleRootTypeName];
        if (existingPossibleRootType != null && existingPossibleRootType.name != null) {
          operationTypes.push({
            kind: Kind.OPERATION_TYPE_DEFINITION,
            type: {
              kind: Kind.NAMED_TYPE,
              name: existingPossibleRootType.name
            },
            operation: opTypeDefNodeType
          });
        }
      }
    }
    if (((_a = schemaDef == null ? void 0 : schemaDef.operationTypes) == null ? void 0 : _a.length) != null && schemaDef.operationTypes.length > 0) {
      mergedNodes[schemaDefSymbol] = schemaDef;
    }
  }
  if ((config == null ? void 0 : config.forceSchemaDefinition) && !((_c = (_b = mergedNodes[schemaDefSymbol]) == null ? void 0 : _b.operationTypes) == null ? void 0 : _c.length)) {
    mergedNodes[schemaDefSymbol] = {
      kind: Kind.SCHEMA_DEFINITION,
      operationTypes: [
        {
          kind: Kind.OPERATION_TYPE_DEFINITION,
          operation: "query",
          type: {
            kind: Kind.NAMED_TYPE,
            name: {
              kind: Kind.NAME,
              value: "Query"
            }
          }
        }
      ]
    };
  }
  const mergedNodeDefinitions = Object.values(mergedNodes);
  if (config == null ? void 0 : config.sort) {
    const sortFn = typeof config.sort === "function" ? config.sort : defaultStringComparator;
    mergedNodeDefinitions.sort((a, b) => {
      var _a2, _b2;
      return sortFn((_a2 = a.name) == null ? void 0 : _a2.value, (_b2 = b.name) == null ? void 0 : _b2.value);
    });
  }
  return mergedNodeDefinitions;
}

// node_modules/@graphql-tools/merge/esm/extensions.js
function applyExtensionObject(obj, extensions) {
  if (!obj || !extensions || extensions === obj.extensions) {
    return;
  }
  if (!obj.extensions) {
    obj.extensions = extensions;
    return;
  }
  obj.extensions = mergeDeep([obj.extensions, extensions], false, true);
}
function applyExtensions(schema, extensions) {
  applyExtensionObject(schema, extensions.schemaExtensions);
  for (const [typeName, data] of Object.entries(extensions.types || {})) {
    const type = schema.getType(typeName);
    if (type) {
      applyExtensionObject(type, data.extensions);
      if (data.type === "object" || data.type === "interface") {
        for (const [fieldName, fieldData] of Object.entries(data.fields)) {
          const field = type.getFields()[fieldName];
          if (field) {
            applyExtensionObject(field, fieldData.extensions);
            for (const [arg, argData] of Object.entries(fieldData.arguments)) {
              applyExtensionObject(field.args.find((a) => a.name === arg), argData);
            }
          }
        }
      } else if (data.type === "input") {
        for (const [fieldName, fieldData] of Object.entries(data.fields)) {
          const field = type.getFields()[fieldName];
          applyExtensionObject(field, fieldData.extensions);
        }
      } else if (data.type === "enum") {
        for (const [valueName, valueData] of Object.entries(data.values)) {
          const value = type.getValue(valueName);
          applyExtensionObject(value, valueData);
        }
      }
    }
  }
  return schema;
}

// node_modules/@graphql-tools/schema/esm/makeExecutableSchema.js
function makeExecutableSchema({ typeDefs, resolvers = {}, resolverValidationOptions = {}, inheritResolversFromInterfaces = false, updateResolversInPlace = false, schemaExtensions, defaultFieldResolver: defaultFieldResolver2, ...otherOptions }) {
  if (typeof resolverValidationOptions !== "object") {
    throw new Error("Expected `resolverValidationOptions` to be an object");
  }
  if (!typeDefs) {
    throw new Error("Must provide typeDefs");
  }
  let schema;
  if (isSchema(typeDefs)) {
    schema = typeDefs;
  } else if (otherOptions == null ? void 0 : otherOptions.commentDescriptions) {
    const mergedTypeDefs = mergeTypeDefs(typeDefs, {
      ...otherOptions,
      commentDescriptions: true
    });
    schema = buildSchema(mergedTypeDefs, otherOptions);
  } else {
    const mergedTypeDefs = mergeTypeDefs(typeDefs, otherOptions);
    schema = buildASTSchema(mergedTypeDefs, otherOptions);
  }
  schema = addResolversToSchema({
    schema,
    resolvers: mergeResolvers(resolvers),
    resolverValidationOptions,
    inheritResolversFromInterfaces,
    updateResolversInPlace,
    defaultFieldResolver: defaultFieldResolver2
  });
  if (Object.keys(resolverValidationOptions).length > 0) {
    assertResolversPresent(schema, resolverValidationOptions);
  }
  if (schemaExtensions) {
    for (const schemaExtension of asArray(schemaExtensions)) {
      applyExtensions(schema, schemaExtension);
    }
  }
  return schema;
}

// node_modules/@graphql-tools/schema/esm/merge-schemas.js
function mergeSchemas(config) {
  const extractedTypeDefs = [];
  const extractedResolvers = [];
  const extractedSchemaExtensions = [];
  if (config.schemas != null) {
    for (const schema of config.schemas) {
      extractedTypeDefs.push(getDocumentNodeFromSchema(schema));
      extractedResolvers.push(getResolversFromSchema(schema));
      extractedSchemaExtensions.push(extractExtensionsFromSchema(schema));
    }
  }
  if (config.typeDefs != null) {
    extractedTypeDefs.push(config.typeDefs);
  }
  if (config.resolvers != null) {
    const additionalResolvers = asArray(config.resolvers);
    extractedResolvers.push(...additionalResolvers);
  }
  if (config.schemaExtensions != null) {
    const additionalSchemaExtensions = asArray(config.schemaExtensions);
    extractedSchemaExtensions.push(...additionalSchemaExtensions);
  }
  return makeExecutableSchema({
    ...config,
    typeDefs: extractedTypeDefs,
    resolvers: extractedResolvers,
    schemaExtensions: extractedSchemaExtensions
  });
}
export {
  addResolversToSchema,
  assertResolversPresent,
  chainResolvers,
  checkForResolveTypeResolver,
  extendResolversFromInterfaces,
  extractExtensionsFromSchema,
  makeExecutableSchema,
  mergeSchemas
};
//# sourceMappingURL=@graphql-tools_schema.js.map
