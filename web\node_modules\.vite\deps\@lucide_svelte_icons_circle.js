import {
  Icon_default
} from "./chunk-CLQWOSOD.js";
import "./chunk-PCV3XR2B.js";
import {
  check_target,
  hmr,
  legacy_api,
  rest_props,
  snippet,
  spread_props,
  wrap_snippet
} from "./chunk-J67I5FE7.js";
import "./chunk-U7P2NEEE.js";
import {
  append,
  comment
} from "./chunk-4D5HC5JT.js";
import {
  FILENAME,
  HMR,
  first_child,
  noop,
  pop,
  push,
  set
} from "./chunk-2PJMTVJS.js";
import "./chunk-NXGWPDOQ.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-RVAV4ZRS.js";
import "./chunk-UQOTJTBP.js";

// node_modules/@lucide/svelte/dist/icons/circle.svelte
Circle[FILENAME] = "node_modules/@lucide/svelte/dist/icons/circle.svelte";
function Circle($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Circle);
  let props = rest_props($$props, ["$$slots", "$$events", "$$legacy"], "props");
  const iconNode = [
    [
      "circle",
      { "cx": "12", "cy": "12", "r": "10" }
    ]
  ];
  var fragment = comment();
  var node = first_child(fragment);
  Icon_default(node, spread_props({ name: "circle" }, () => props, {
    get iconNode() {
      return iconNode;
    },
    children: wrap_snippet(Circle, ($$anchor2, $$slotProps) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.children ?? noop);
      append($$anchor2, fragment_1);
    }),
    $$slots: { default: true }
  }));
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Circle = hmr(Circle, () => Circle[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Circle[HMR].source;
    set(Circle[HMR].source, module.default[HMR].original);
  });
}
var circle_default = Circle;
export {
  circle_default as default
};
/*! Bundled license information:

@lucide/svelte/dist/icons/circle.svelte:
  (**
   * @license @lucide/svelte v0.482.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   *)

@lucide/svelte/dist/icons/circle.js:
  (**
   * @license @lucide/svelte v0.482.0 - ISC
   *
   * ISC License
   * 
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   * 
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   * 
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   * 
   *)
*/
//# sourceMappingURL=@lucide_svelte_icons_circle.js.map
