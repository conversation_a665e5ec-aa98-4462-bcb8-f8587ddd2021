<script lang="ts">
  import { Switch as SwitchPrimitive } from 'bits-ui';
  import { cn, type WithoutChildrenOrChild } from '$lib/utils.js';

  let {
    ref = $bindable(null),
    class: className,
    checked = $bindable(false),
    ...restProps
  }: WithoutChildrenOrChild<SwitchPrimitive.RootProps> = $props();
</script>

<SwitchPrimitive.Root
  bind:ref
  bind:checked
  data-slot="switch"
  class={cn(
    'data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 shadow-xs dark:data-[state=unchecked]:bg-input w-8.5 peer inline-flex h-[1.4rem] shrink-0 cursor-pointer items-center rounded-full border border-transparent outline-none transition-all focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-green-500',
    className
  )}
  {...restProps}>
  <SwitchPrimitive.Thumb
    data-slot="switch-thumb"
    class={cn(
      ' bg-background dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0.5 dark:data-[state=unchecked]:bg-white'
    )} />
</SwitchPrimitive.Root>
