{"version": 3, "sources": ["../../sveltekit-superforms/dist/client/superForm.js", "../../sveltekit-superforms/dist/justClone.js", "../../sveltekit-superforms/dist/traversal.js", "../../sveltekit-superforms/dist/stringPath.js", "../../ts-deepmerge/esm/index.js", "../../sveltekit-superforms/dist/jsonSchema/schemaInfo.js", "../../sveltekit-superforms/dist/jsonSchema/schemaDefaults.js", "../../sveltekit-superforms/dist/errors.js", "../../sveltekit-superforms/dist/utils.js", "../../sveltekit-superforms/dist/client/flash.js", "../../sveltekit-superforms/dist/client/customValidity.js", "../../sveltekit-superforms/dist/client/elements.js", "../../sveltekit-superforms/dist/client/form.js", "../../sveltekit-superforms/dist/client/proxies.js", "../../sveltekit-superforms/dist/jsonSchema/schemaShape.js", "../../sveltekit-superforms/dist/defaults.js", "../../sveltekit-superforms/dist/actionResult.js", "../../sveltekit-superforms/dist/superValidate.js", "../../sveltekit-superforms/dist/formData.js"], "sourcesContent": ["import { derived, get, readonly, writable } from 'svelte/store';\nimport { navigating, page } from '$app/stores';\nimport { clone } from '../utils.js';\nimport { browser } from '$app/environment';\nimport { onDestroy, tick } from 'svelte';\nimport { comparePaths, pathExists, setPaths, traversePath, traversePaths } from '../traversal.js';\nimport { splitPath, mergePath } from '../stringPath.js';\nimport { beforeNavigate, goto, invalidateAll } from '$app/navigation';\nimport { SuperFormError, flattenErrors, mapErrors, updateErrors } from '../errors.js';\nimport { cancelFlash, shouldSyncFlash } from './flash.js';\nimport { applyAction, deserialize, enhance as kitEnhance } from '$app/forms';\nimport { setCustomValidityForm, updateCustomValidity } from './customValidity.js';\nimport { inputInfo } from './elements.js';\nimport { Form as HtmlForm, scrollToFirstError } from './form.js';\nimport { stringify } from 'devalue';\nimport { fieldProxy } from './proxies.js';\nimport { shapeFromObject } from '../jsonSchema/schemaShape.js';\nconst formIds = new WeakMap();\nconst initialForms = new WeakMap();\nconst defaultOnError = (event) => {\n    throw event.result.error;\n};\nconst defaultFormOptions = {\n    applyAction: true,\n    invalidateAll: true,\n    resetForm: true,\n    autoFocusOnError: 'detect',\n    scrollToError: 'smooth',\n    errorSelector: '[aria-invalid=\"true\"],[data-invalid]',\n    selectErrorText: false,\n    stickyNavbar: undefined,\n    taintedMessage: false,\n    onSubmit: undefined,\n    onResult: undefined,\n    onUpdate: undefined,\n    onUpdated: undefined,\n    onError: defaultOnError,\n    dataType: 'form',\n    validators: undefined,\n    customValidity: false,\n    clearOnSubmit: 'message',\n    delayMs: 500,\n    timeoutMs: 8000,\n    multipleSubmits: 'prevent',\n    SPA: undefined,\n    validationMethod: 'auto'\n};\nfunction multipleFormIdError(id) {\n    return (`Duplicate form id's found: \"${id}\". ` +\n        'Multiple forms will receive the same data. Use the id option to differentiate between them, ' +\n        'or if this is intended, set the warnings.duplicateId option to false in superForm to disable this warning. ' +\n        'More information: https://superforms.rocks/concepts/multiple-forms');\n}\n/////////////////////////////////////////////////////////////////////\n/**\n * V1 compatibilty. resetForm = false and taintedMessage = true\n */\nlet LEGACY_MODE = false;\ntry {\n    // @ts-expect-error Vite define check\n    if (SUPERFORMS_LEGACY)\n        LEGACY_MODE = true;\n}\ncatch {\n    // No legacy mode defined\n}\n/**\n * Storybook compatibility mode, basically disables the navigating store.\n */\nlet STORYBOOK_MODE = false;\ntry {\n    // @ts-expect-error Storybook check\n    if (globalThis.STORIES)\n        STORYBOOK_MODE = true;\n}\ncatch {\n    // No Storybook\n}\n/////////////////////////////////////////////////////////////////////\n/**\n * Initializes a SvelteKit form, for convenient handling of values, errors and sumbitting data.\n * @param {SuperValidated} form Usually data.form from PageData or defaults, but can also be an object with default values, but then constraints won't be available.\n * @param {FormOptions} formOptions Configuration for the form.\n * @returns {SuperForm} A SuperForm object that can be used in a Svelte component.\n * @DCI-context\n */\nexport function superForm(form, formOptions) {\n    // Used in reset\n    let initialForm;\n    let options = formOptions ?? {};\n    // To check if a full validator is used when switching options.validators dynamically\n    let initialValidator = undefined;\n    {\n        if (options.legacy ?? LEGACY_MODE) {\n            if (options.resetForm === undefined)\n                options.resetForm = false;\n            if (options.taintedMessage === undefined)\n                options.taintedMessage = true;\n        }\n        if (STORYBOOK_MODE) {\n            if (options.applyAction === undefined)\n                options.applyAction = false;\n        }\n        if (typeof options.SPA === 'string') {\n            // SPA action mode is \"passive\", no page updates are made.\n            if (options.invalidateAll === undefined)\n                options.invalidateAll = false;\n            if (options.applyAction === undefined)\n                options.applyAction = false;\n        }\n        initialValidator = options.validators;\n        options = {\n            ...defaultFormOptions,\n            ...options\n        };\n        if ((options.SPA === true || typeof options.SPA === 'object') &&\n            options.validators === undefined) {\n            console.warn('No validators set for superForm in SPA mode. ' +\n                'Add a validation adapter to the validators option, or set it to false to disable this warning.');\n        }\n        if (!form) {\n            throw new SuperFormError('No form data sent to superForm. ' +\n                \"Make sure the output from superValidate is used (usually data.form) and that it's not null or undefined. \" +\n                \"Alternatively, an object with default values for the form can also be used, but then constraints won't be available.\");\n        }\n        if (Context_isValidationObject(form) === false) {\n            form = {\n                id: options.id ?? Math.random().toString(36).slice(2, 10),\n                valid: false,\n                posted: false,\n                errors: {},\n                data: form,\n                shape: shapeFromObject(form)\n            };\n        }\n        form = form;\n        // Assign options.id to form, if it exists\n        const _initialFormId = (form.id = options.id ?? form.id);\n        const _currentPage = get(page) ?? (STORYBOOK_MODE ? {} : undefined);\n        // Check multiple id's\n        if (browser && options.warnings?.duplicateId !== false) {\n            if (!formIds.has(_currentPage)) {\n                formIds.set(_currentPage, new Set([_initialFormId]));\n            }\n            else {\n                const currentForms = formIds.get(_currentPage);\n                if (currentForms?.has(_initialFormId)) {\n                    console.warn(multipleFormIdError(_initialFormId));\n                }\n                else {\n                    currentForms?.add(_initialFormId);\n                }\n            }\n        }\n        /**\n         * Need to clone the form data, in case it's used to populate multiple forms\n         * and in components that are mounted and destroyed multiple times.\n         * This also means that it needs to be set here, before it's cloned further below.\n         */\n        if (!initialForms.has(form)) {\n            initialForms.set(form, form);\n        }\n        initialForm = initialForms.get(form);\n        // Detect if a form is posted without JavaScript.\n        if (!browser && _currentPage.form && typeof _currentPage.form === 'object') {\n            const postedData = _currentPage.form;\n            for (const postedForm of Context_findValidationForms(postedData).reverse()) {\n                if (postedForm.id == _initialFormId && !initialForms.has(postedForm)) {\n                    // Prevent multiple \"posting\" that can happen when components are recreated.\n                    initialForms.set(postedData, postedData);\n                    const pageDataForm = form;\n                    // Add the missing fields from the page data form\n                    form = postedForm;\n                    form.constraints = pageDataForm.constraints;\n                    form.shape = pageDataForm.shape;\n                    // Reset the form if option set and form is valid.\n                    if (form.valid &&\n                        options.resetForm &&\n                        (options.resetForm === true || options.resetForm())) {\n                        form = clone(pageDataForm);\n                        form.message = clone(postedForm.message);\n                    }\n                    break;\n                }\n            }\n        }\n        else {\n            form = clone(initialForm);\n        }\n        ///// From here, form is properly initialized /////\n        onDestroy(() => {\n            Unsubscriptions_unsubscribe();\n            NextChange_clear();\n            EnhancedForm_destroy();\n            for (const events of Object.values(formEvents)) {\n                events.length = 0;\n            }\n            formIds.get(_currentPage)?.delete(_initialFormId);\n        });\n        // Check for nested objects, throw if datatype isn't json\n        if (options.dataType !== 'json') {\n            const checkForNestedData = (key, value) => {\n                if (!value || typeof value !== 'object')\n                    return;\n                if (Array.isArray(value)) {\n                    if (value.length > 0)\n                        checkForNestedData(key, value[0]);\n                }\n                else if (!(value instanceof Date) &&\n                    !(value instanceof File) &&\n                    (!browser || !(value instanceof FileList))) {\n                    throw new SuperFormError(`Object found in form field \"${key}\". ` +\n                        `Set the dataType option to \"json\" and add use:enhance to use nested data structures. ` +\n                        `More information: https://superforms.rocks/concepts/nested-data`);\n                }\n            };\n            for (const [key, value] of Object.entries(form.data)) {\n                checkForNestedData(key, value);\n            }\n        }\n    }\n    ///// Roles ///////////////////////////////////////////////////////\n    //#region Data\n    /**\n     * Container for store data, subscribed to with Unsubscriptions\n     * to avoid \"get\" usage.\n     */\n    const __data = {\n        formId: form.id,\n        form: clone(form.data),\n        constraints: form.constraints ?? {},\n        posted: form.posted,\n        errors: clone(form.errors),\n        message: clone(form.message),\n        tainted: undefined,\n        valid: form.valid,\n        submitting: false,\n        shape: form.shape\n    };\n    const Data = __data;\n    //#endregion\n    //#region FormId\n    const FormId = writable(options.id ?? form.id);\n    //#endregion\n    //#region Context\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const Context = {};\n    function Context_findValidationForms(data) {\n        const forms = Object.values(data).filter((v) => Context_isValidationObject(v) !== false);\n        return forms;\n    }\n    /**\n     * Return false if object isn't a validation object, otherwise the form id,\n     * which can be an empty string, so always check with === false\n     */\n    function Context_isValidationObject(object) {\n        if (!object || typeof object !== 'object')\n            return false;\n        if (!('valid' in object && 'errors' in object && typeof object.valid === 'boolean')) {\n            return false;\n        }\n        return 'id' in object && typeof object.id === 'string' ? object.id : false;\n    }\n    //#endregion\n    //#region Form\n    // eslint-disable-next-line dci-lint/grouped-rolemethods\n    const _formData = writable(form.data);\n    const Form = {\n        subscribe: _formData.subscribe,\n        set: (value, options = {}) => {\n            // Need to clone the value, so it won't refer to $page for example.\n            const newData = clone(value);\n            Tainted_update(newData, options.taint ?? true);\n            return _formData.set(newData);\n        },\n        update: (updater, options = {}) => {\n            return _formData.update((value) => {\n                // No cloning here, since it's an update\n                const newData = updater(value);\n                Tainted_update(newData, options.taint ?? true);\n                return newData;\n            });\n        }\n    };\n    function Form_isSPA() {\n        return options.SPA === true || typeof options.SPA === 'object';\n    }\n    function Form_resultStatus(defaultStatus) {\n        if (defaultStatus > 400)\n            return defaultStatus;\n        return ((typeof options.SPA === 'boolean' || typeof options.SPA === 'string'\n            ? undefined\n            : options.SPA?.failStatus) || defaultStatus);\n    }\n    async function Form_validate(opts = {}) {\n        const dataToValidate = opts.formData ?? Data.form;\n        let errors = {};\n        let status;\n        const validator = opts.adapter ?? options.validators;\n        if (typeof validator == 'object') {\n            // Checking for full validation with the jsonSchema field (doesn't exist in client validators).\n            if (validator != initialValidator && !('jsonSchema' in validator)) {\n                throw new SuperFormError('Client validation adapter found in options.validators. ' +\n                    'A full adapter must be used when changing validators dynamically, for example \"zod\" instead of \"zodClient\".');\n            }\n            status = await /* @__PURE__ */ validator.validate(dataToValidate);\n            if (!status.success) {\n                errors = mapErrors(status.issues, validator.shape ?? Data.shape ?? {});\n            }\n            else if (opts.recheckValidData !== false) {\n                // need to make an additional validation, in case the data has been transformed\n                return Form_validate({ ...opts, recheckValidData: false });\n            }\n        }\n        else {\n            status = { success: true, data: {} };\n        }\n        const data = { ...Data.form, ...dataToValidate, ...(status.success ? status.data : {}) };\n        return {\n            valid: status.success,\n            posted: false,\n            errors,\n            data,\n            constraints: Data.constraints,\n            message: undefined,\n            id: Data.formId,\n            shape: Data.shape\n        };\n    }\n    function Form__changeEvent(event) {\n        if (!options.onChange || !event.paths.length || event.type == 'blur')\n            return;\n        let changeEvent;\n        const paths = event.paths.map(mergePath);\n        if (event.type &&\n            event.paths.length == 1 &&\n            event.formElement &&\n            event.target instanceof Element) {\n            changeEvent = {\n                path: paths[0],\n                paths,\n                formElement: event.formElement,\n                target: event.target,\n                set(path, value, options) {\n                    // Casting trick to make it think it's a SuperForm\n                    fieldProxy({ form: Form }, path, options).set(value);\n                },\n                get(path) {\n                    return get(fieldProxy(Form, path));\n                }\n            };\n        }\n        else {\n            changeEvent = {\n                paths,\n                target: undefined,\n                set(path, value, options) {\n                    // Casting trick to make it think it's a SuperForm\n                    fieldProxy({ form: Form }, path, options).set(value);\n                },\n                get(path) {\n                    return get(fieldProxy(Form, path));\n                }\n            };\n        }\n        options.onChange(changeEvent);\n    }\n    /**\n     * Make a client-side validation, updating the form data if successful.\n     * @param event A change event, from html input or programmatically\n     * @param force Is true if called from validateForm with update: true\n     * @param adapter ValidationAdapter, if called from validateForm with schema set\n     * @returns SuperValidated, or undefined if options prevented validation.\n     */\n    async function Form_clientValidation(event, force = false, adapter) {\n        if (event) {\n            if (options.validators == 'clear') {\n                Errors.update(($errors) => {\n                    setPaths($errors, event.paths, undefined);\n                    return $errors;\n                });\n            }\n            setTimeout(() => Form__changeEvent(event));\n        }\n        let skipValidation = false;\n        if (!force) {\n            if (options.validationMethod == 'onsubmit' || options.validationMethod == 'submit-only') {\n                skipValidation = true;\n            }\n            else if (options.validationMethod == 'onblur' && event?.type == 'input')\n                skipValidation = true;\n            else if (options.validationMethod == 'oninput' && event?.type == 'blur')\n                skipValidation = true;\n        }\n        if (skipValidation || !event || !options.validators || options.validators == 'clear') {\n            if (event?.paths) {\n                const formElement = event?.formElement ?? EnhancedForm_get();\n                if (formElement)\n                    Form__clearCustomValidity(formElement);\n            }\n            return;\n        }\n        const result = await Form_validate({ adapter });\n        // TODO: Add option for always setting result.data?\n        if (result.valid && (event.immediate || event.type != 'input')) {\n            Form.set(result.data, { taint: 'ignore' });\n        }\n        // Wait for tainted, so object errors can be displayed\n        await tick();\n        Form__displayNewErrors(result.errors, event, force);\n        return result;\n    }\n    function Form__clearCustomValidity(formElement) {\n        const validity = new Map();\n        if (options.customValidity && formElement) {\n            for (const el of formElement.querySelectorAll(`[name]`)) {\n                if (typeof el.name !== 'string' || !el.name.length)\n                    continue;\n                const message = 'validationMessage' in el ? String(el.validationMessage) : '';\n                validity.set(el.name, { el, message });\n                updateCustomValidity(el, undefined);\n            }\n        }\n        return validity;\n    }\n    async function Form__displayNewErrors(errors, event, force) {\n        const { type, immediate, multiple, paths } = event;\n        const previous = Data.errors;\n        const output = {};\n        let validity = new Map();\n        const formElement = event.formElement ?? EnhancedForm_get();\n        if (formElement)\n            validity = Form__clearCustomValidity(formElement);\n        traversePaths(errors, (error) => {\n            if (!Array.isArray(error.value))\n                return;\n            const currentPath = [...error.path];\n            if (currentPath[currentPath.length - 1] == '_errors') {\n                currentPath.pop();\n            }\n            const joinedPath = currentPath.join('.');\n            function addError() {\n                //console.log('Adding error', `[${error.path.join('.')}]`, error.value); //debug\n                setPaths(output, [error.path], error.value);\n                if (options.customValidity && isEventError && validity.has(joinedPath)) {\n                    const { el, message } = validity.get(joinedPath);\n                    if (message != error.value) {\n                        setTimeout(() => updateCustomValidity(el, error.value));\n                        // Only need one error to display\n                        validity.clear();\n                    }\n                }\n            }\n            if (force)\n                return addError();\n            const lastPath = error.path[error.path.length - 1];\n            const isObjectError = lastPath == '_errors';\n            const isEventError = error.value &&\n                paths.some((path) => {\n                    // If array/object, any part of the path can match. If not, exact match is required\n                    return isObjectError\n                        ? currentPath && path && currentPath.length > 0 && currentPath[0] == path[0]\n                        : joinedPath == path.join('.');\n                });\n            if (isEventError && options.validationMethod == 'oninput')\n                return addError();\n            // Immediate, non-multiple input should display the errors\n            if (immediate && !multiple && isEventError)\n                return addError();\n            // Special case for multiple, which should display errors on blur\n            // or if any error has existed previously. Tricky UX.\n            if (multiple) {\n                // For multi-select, if any error has existed, display all errors\n                const errorPath = pathExists(get(Errors), error.path.slice(0, -1));\n                if (errorPath?.value && typeof errorPath?.value == 'object') {\n                    for (const errors of Object.values(errorPath.value)) {\n                        if (Array.isArray(errors)) {\n                            return addError();\n                        }\n                    }\n                }\n            }\n            // If previous error exist, always display\n            const previousError = pathExists(previous, error.path);\n            if (previousError && previousError.key in previousError.parent) {\n                return addError();\n            }\n            if (isObjectError) {\n                // New object errors should be displayed on blur events,\n                // or the (parent) path is or has been tainted.\n                if (options.validationMethod == 'oninput' ||\n                    (type == 'blur' &&\n                        Tainted_hasBeenTainted(mergePath(error.path.slice(0, -1))))) {\n                    return addError();\n                }\n            }\n            else {\n                // Display text errors on blur, if the event matches the error path\n                // Also, display errors if the error is in an array an it has been tainted.\n                if (type == 'blur' &&\n                    isEventError\n                //|| (isErrorInArray &&\tTainted_hasBeenTainted(mergePath(error.path.slice(0, -1)) as FormPath<T>))\n                ) {\n                    return addError();\n                }\n            }\n        });\n        Errors.set(output);\n    }\n    function Form_set(data, options = {}) {\n        // Check if file fields should be kept, usually when the server returns them as undefined.\n        // in that case remove the undefined field from the new data.\n        if (options.keepFiles) {\n            traversePaths(Data.form, (info) => {\n                if ((!browser || !(info.parent instanceof FileList)) &&\n                    (info.value instanceof File || (browser && info.value instanceof FileList))) {\n                    const dataPath = pathExists(data, info.path);\n                    if (!dataPath || !(dataPath.key in dataPath.parent)) {\n                        setPaths(data, [info.path], info.value);\n                    }\n                }\n            });\n        }\n        return Form.set(data, options);\n    }\n    function Form_shouldReset(validForm, successActionResult) {\n        return (validForm &&\n            successActionResult &&\n            options.resetForm &&\n            (options.resetForm === true || options.resetForm()));\n    }\n    function Form_capture(removeFilesfromData = true) {\n        let data = Data.form;\n        let tainted = Data.tainted;\n        if (removeFilesfromData) {\n            const removed = removeFiles(Data.form);\n            data = removed.data;\n            const paths = removed.paths;\n            if (paths.length) {\n                tainted = clone(tainted) ?? {};\n                setPaths(tainted, paths, false);\n            }\n        }\n        return {\n            valid: Data.valid,\n            posted: Data.posted,\n            errors: Data.errors,\n            data,\n            constraints: Data.constraints,\n            message: Data.message,\n            id: Data.formId,\n            tainted,\n            shape: Data.shape\n        };\n    }\n    async function Form_updateFromValidation(form2, successResult) {\n        if (form2.valid && successResult && Form_shouldReset(form2.valid, successResult)) {\n            Form_reset({ message: form2.message, posted: true });\n        }\n        else {\n            rebind({\n                form: form2,\n                untaint: successResult,\n                keepFiles: true,\n                // Check if the form data should be used for updating, or if the invalidateAll load function should be used:\n                pessimisticUpdate: options.invalidateAll == 'force' || options.invalidateAll == 'pessimistic'\n            });\n        }\n        // onUpdated may check stores, so need to wait for them to update.\n        if (formEvents.onUpdated.length) {\n            await tick();\n        }\n        // But do not await on onUpdated itself, since we're already finished with the request\n        for (const event of formEvents.onUpdated) {\n            event({ form: form2 });\n        }\n    }\n    function Form_reset(opts = {}) {\n        if (opts.newState)\n            initialForm.data = { ...initialForm.data, ...opts.newState };\n        const resetData = clone(initialForm);\n        resetData.data = { ...resetData.data, ...opts.data };\n        if (opts.id !== undefined)\n            resetData.id = opts.id;\n        rebind({\n            form: resetData,\n            untaint: true,\n            message: opts.message,\n            keepFiles: false,\n            posted: opts.posted,\n            resetted: true\n        });\n    }\n    async function Form_updateFromActionResult(result) {\n        if (result.type == 'error') {\n            throw new SuperFormError(`ActionResult of type \"${result.type}\" cannot be passed to update function.`);\n        }\n        if (result.type == 'redirect') {\n            // All we need to do if redirected is to reset the form.\n            // No events should be triggered because technically we're somewhere else.\n            if (Form_shouldReset(true, true))\n                Form_reset({ posted: true });\n            return;\n        }\n        if (typeof result.data !== 'object') {\n            throw new SuperFormError('Non-object validation data returned from ActionResult.');\n        }\n        const forms = Context_findValidationForms(result.data);\n        if (!forms.length) {\n            throw new SuperFormError('No form data returned from ActionResult. Make sure you return { form } in the form actions.');\n        }\n        for (const newForm of forms) {\n            if (newForm.id !== Data.formId)\n                continue;\n            await Form_updateFromValidation(newForm, result.status >= 200 && result.status < 300);\n        }\n    }\n    //#endregion\n    const Message = writable(__data.message);\n    const Constraints = writable(__data.constraints);\n    const Posted = writable(__data.posted);\n    const Shape = writable(__data.shape);\n    //#region Errors\n    const _errors = writable(form.errors);\n    // eslint-disable-next-line dci-lint/grouped-rolemethods\n    const Errors = {\n        subscribe: _errors.subscribe,\n        set(value, options) {\n            return _errors.set(updateErrors(value, Data.errors, options?.force));\n        },\n        update(updater, options) {\n            return _errors.update((value) => {\n                return updateErrors(updater(value), Data.errors, options?.force);\n            });\n        },\n        /**\n         * To work with client-side validation, errors cannot be deleted but must\n         * be set to undefined, to know where they existed before (tainted+error check in oninput)\n         */\n        clear: () => Errors.set({})\n    };\n    //#endregion\n    //#region NextChange /////\n    let NextChange = null;\n    function NextChange_setHtmlEvent(event) {\n        // For File inputs, if only paths are available, use that instead of replacing\n        // (fileProxy updates causes this)\n        if (NextChange &&\n            event &&\n            Object.keys(event).length == 1 &&\n            event.paths?.length &&\n            NextChange.target &&\n            NextChange.target instanceof HTMLInputElement &&\n            NextChange.target.type.toLowerCase() == 'file') {\n            NextChange.paths = event.paths;\n        }\n        else {\n            NextChange = event;\n        }\n        // Wait for on:input to provide additional information\n        setTimeout(() => {\n            Form_clientValidation(NextChange);\n        }, 0);\n    }\n    function NextChange_additionalEventInformation(event, immediate, multiple, formElement, target) {\n        if (NextChange === null) {\n            NextChange = { paths: [] };\n        }\n        NextChange.type = event;\n        NextChange.immediate = immediate;\n        NextChange.multiple = multiple;\n        NextChange.formElement = formElement;\n        NextChange.target = target;\n    }\n    function NextChange_paths() {\n        return NextChange?.paths ?? [];\n    }\n    function NextChange_clear() {\n        NextChange = null;\n    }\n    //#endregion\n    //#region Tainted\n    const Tainted = {\n        defaultMessage: 'Leave page? Changes that you made may not be saved.',\n        state: writable(),\n        message: options.taintedMessage,\n        clean: clone(form.data), // Important to clone form.data, so it's not comparing the same object,\n        forceRedirection: false\n    };\n    function Tainted_isEnabled() {\n        return (options.taintedMessage && !Data.submitting && !Tainted.forceRedirection && Tainted_isTainted());\n    }\n    function Tainted_checkUnload(e) {\n        if (!Tainted_isEnabled())\n            return;\n        // Chrome requires returnValue to be set\n        e.preventDefault();\n        e.returnValue = '';\n        // Prompt the user\n        const { taintedMessage } = options;\n        const isTaintedFunction = typeof taintedMessage === 'function';\n        const confirmationMessage = isTaintedFunction || taintedMessage === true ? Tainted.defaultMessage : taintedMessage;\n        (e || window.event).returnValue = confirmationMessage || Tainted.defaultMessage;\n        return confirmationMessage;\n    }\n    async function Tainted_beforeNav(nav) {\n        if (!Tainted_isEnabled())\n            return;\n        const { taintedMessage } = options;\n        const isTaintedFunction = typeof taintedMessage === 'function';\n        // As beforeNavigate does not support Promise, we cancel the redirection until the promise resolve\n        // if it's a custom function\n        if (isTaintedFunction)\n            nav.cancel();\n        // Does not display any dialog on page refresh or closing tab, will use Tainted_checkUnload\n        if (nav.type === 'leave') {\n            return;\n        }\n        const message = isTaintedFunction || taintedMessage === true ? Tainted.defaultMessage : taintedMessage;\n        let shouldRedirect;\n        try {\n            // - rejected => shouldRedirect = false\n            // - resolved with false => shouldRedirect = false\n            // - resolved with true => shouldRedirect = true\n            shouldRedirect = isTaintedFunction\n                ? await taintedMessage()\n                : window.confirm(message || Tainted.defaultMessage);\n        }\n        catch {\n            shouldRedirect = false;\n        }\n        if (shouldRedirect && nav.to) {\n            try {\n                Tainted.forceRedirection = true;\n                await goto(nav.to.url, { ...nav.to.params });\n                return;\n            }\n            finally {\n                // Reset forceRedirection for multiple-tainted purpose\n                Tainted.forceRedirection = false;\n            }\n        }\n        else if (!shouldRedirect && !isTaintedFunction) {\n            nav.cancel();\n        }\n    }\n    function Tainted_enable() {\n        options.taintedMessage = Tainted.message;\n    }\n    function Tainted_currentState() {\n        return Tainted.state;\n    }\n    function Tainted_hasBeenTainted(path) {\n        if (!Data.tainted)\n            return false;\n        if (!path)\n            return !!Data.tainted;\n        const field = pathExists(Data.tainted, splitPath(path));\n        return !!field && field.key in field.parent;\n    }\n    function Tainted_isTainted(path) {\n        if (!arguments.length)\n            return Tainted__isObjectTainted(Data.tainted);\n        if (typeof path === 'boolean')\n            return path;\n        if (typeof path === 'object')\n            return Tainted__isObjectTainted(path);\n        if (!Data.tainted || path === undefined)\n            return false;\n        const field = pathExists(Data.tainted, splitPath(path));\n        return Tainted__isObjectTainted(field?.value);\n    }\n    function Tainted__isObjectTainted(obj) {\n        if (!obj)\n            return false;\n        if (typeof obj === 'object') {\n            for (const obj2 of Object.values(obj)) {\n                if (Tainted__isObjectTainted(obj2))\n                    return true;\n            }\n        }\n        return obj === true;\n    }\n    /**\n     * Updates the tainted state. Use most of the time, except when submitting.\n     */\n    function Tainted_update(newData, taintOptions) {\n        // Ignore is set when returning errors from the server\n        // so status messages and form-level errors won't be\n        // immediately cleared by client-side validation.\n        if (taintOptions == 'ignore')\n            return;\n        const paths = comparePaths(newData, Data.form);\n        //console.log('paths:', JSON.stringify(paths));\n        const newTainted = comparePaths(newData, Tainted.clean).map((path) => path.join());\n        //console.log('newTainted:', JSON.stringify(newTainted));\n        if (paths.length) {\n            if (taintOptions == 'untaint-all' || taintOptions == 'untaint-form') {\n                Tainted.state.set(undefined);\n            }\n            else {\n                Tainted.state.update((currentlyTainted) => {\n                    if (!currentlyTainted)\n                        currentlyTainted = {};\n                    setPaths(currentlyTainted, paths, (path, data) => {\n                        // If value goes back to the clean value, untaint the path\n                        if (!newTainted.includes(path.join()))\n                            return undefined;\n                        const currentValue = traversePath(newData, path);\n                        const cleanPath = traversePath(Tainted.clean, path);\n                        const identical = currentValue && cleanPath && currentValue.value === cleanPath.value;\n                        const output = identical\n                            ? undefined\n                            : taintOptions === true\n                                ? true\n                                : taintOptions === 'untaint'\n                                    ? undefined\n                                    : data.value;\n                        return output;\n                    });\n                    return currentlyTainted;\n                });\n            }\n            NextChange_setHtmlEvent({ paths });\n        }\n    }\n    /**\n     * Overwrites the current tainted state and setting a new clean state for the form data.\n     * @param tainted\n     * @param newClean\n     */\n    function Tainted_set(tainted, newClean) {\n        // TODO: Is it better to set tainted values to undefined instead of just overwriting?\n        Tainted.state.set(tainted);\n        if (newClean)\n            Tainted.clean = newClean;\n    }\n    //#endregion\n    //#region Timers\n    const Submitting = writable(false);\n    const Delayed = writable(false);\n    // eslint-disable-next-line dci-lint/grouped-rolemethods\n    const Timeout = writable(false);\n    //#endregion\n    //#region Unsubscriptions\n    /**\n     * Subscribe to certain stores and store the current value in Data, to avoid using get.\n     * Need to clone the form data, so it won't refer to the same object and prevent change detection\n     */\n    const Unsubscriptions = [\n        // eslint-disable-next-line dci-lint/private-role-access\n        Tainted.state.subscribe((tainted) => (__data.tainted = clone(tainted))),\n        // eslint-disable-next-line dci-lint/private-role-access\n        Form.subscribe((form) => (__data.form = clone(form))),\n        // eslint-disable-next-line dci-lint/private-role-access\n        Errors.subscribe((errors) => (__data.errors = clone(errors))),\n        FormId.subscribe((id) => (__data.formId = id)),\n        Constraints.subscribe((constraints) => (__data.constraints = constraints)),\n        Posted.subscribe((posted) => (__data.posted = posted)),\n        Message.subscribe((message) => (__data.message = message)),\n        Submitting.subscribe((submitting) => (__data.submitting = submitting)),\n        Shape.subscribe((shape) => (__data.shape = shape))\n    ];\n    function Unsubscriptions_add(func) {\n        Unsubscriptions.push(func);\n    }\n    function Unsubscriptions_unsubscribe() {\n        Unsubscriptions.forEach((unsub) => unsub());\n    }\n    //#endregion\n    //#region EnhancedForm\n    /**\n     * Used for SPA action mode and options.customValidity to display errors, even if programmatically set\n     */\n    let EnhancedForm;\n    function EnhancedForm_get() {\n        return EnhancedForm;\n    }\n    function EnhancedForm_createFromSPA(action) {\n        EnhancedForm = document.createElement('form');\n        EnhancedForm.method = 'POST';\n        EnhancedForm.action = action;\n        superFormEnhance(EnhancedForm);\n        document.body.appendChild(EnhancedForm);\n    }\n    function EnhancedForm_setAction(action) {\n        if (EnhancedForm)\n            EnhancedForm.action = action;\n    }\n    function EnhancedForm_destroy() {\n        if (EnhancedForm?.parentElement) {\n            EnhancedForm.remove();\n        }\n        EnhancedForm = undefined;\n    }\n    //#endregion\n    const AllErrors = derived(Errors, ($errors) => ($errors ? flattenErrors($errors) : []));\n    ///// End of Roles //////////////////////////////////////////////////////////\n    // Need to clear this and set it again when use:enhance has run, to avoid showing the\n    // tainted dialog when a form doesn't use it or the browser doesn't use JS.\n    options.taintedMessage = undefined;\n    // Role rebinding\n    function rebind(opts) {\n        //console.log('🚀 ~ file: superForm.ts:721 ~ rebind ~ form:', form.data); //debug\n        const form = opts.form;\n        const message = opts.message ?? form.message;\n        if (opts.untaint || opts.resetted) {\n            Tainted_set(typeof opts.untaint === 'boolean' ? undefined : opts.untaint, form.data);\n        }\n        // Form data is not tainted when rebinding.\n        // Prevents object errors from being revalidated after rebind.\n        // Check if form was invalidated (usually with options.invalidateAll) to prevent data from being\n        // overwritten by the load function data\n        if (!opts.pessimisticUpdate) {\n            Form_set(form.data, {\n                taint: 'ignore',\n                keepFiles: opts.keepFiles\n            });\n        }\n        Message.set(message);\n        if (opts.resetted)\n            Errors.update(() => ({}), { force: true });\n        else\n            Errors.set(form.errors);\n        FormId.set(form.id);\n        Posted.set(opts.posted ?? form.posted);\n        // Constraints and shape will only be set when they exist.\n        if (form.constraints)\n            Constraints.set(form.constraints);\n        if (form.shape)\n            Shape.set(form.shape);\n        // Only allowed non-subscribe __data access, here in rebind\n        __data.valid = form.valid;\n        if (options.flashMessage && shouldSyncFlash(options)) {\n            const flash = options.flashMessage.module.getFlash(page);\n            if (message && get(flash) === undefined) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                flash.set(message);\n            }\n        }\n    }\n    const formEvents = {\n        onSubmit: options.onSubmit ? [options.onSubmit] : [],\n        onResult: options.onResult ? [options.onResult] : [],\n        onUpdate: options.onUpdate ? [options.onUpdate] : [],\n        onUpdated: options.onUpdated ? [options.onUpdated] : [],\n        onError: options.onError ? [options.onError] : []\n    };\n    ///// Store subscriptions ///////////////////////////////////////////////////\n    if (browser) {\n        // Set up events for tainted check\n        window.addEventListener('beforeunload', Tainted_checkUnload);\n        onDestroy(() => {\n            window.removeEventListener('beforeunload', Tainted_checkUnload);\n        });\n        beforeNavigate(Tainted_beforeNav);\n        // Need to subscribe to catch page invalidation.\n        Unsubscriptions_add(page.subscribe(async (pageUpdate) => {\n            if (STORYBOOK_MODE && pageUpdate === undefined) {\n                pageUpdate = { status: 200 };\n            }\n            const successResult = pageUpdate.status >= 200 && pageUpdate.status < 300;\n            if (options.applyAction && pageUpdate.form && typeof pageUpdate.form === 'object') {\n                const actionData = pageUpdate.form;\n                // If actionData is an error, it's sent here from triggerOnError\n                if (actionData.type === 'error')\n                    return;\n                for (const newForm of Context_findValidationForms(actionData)) {\n                    const isInitial = initialForms.has(newForm);\n                    if (newForm.id !== Data.formId || isInitial) {\n                        continue;\n                    }\n                    // Prevent multiple \"posting\" that can happen when components are recreated.\n                    initialForms.set(newForm, newForm);\n                    await Form_updateFromValidation(newForm, successResult);\n                }\n            }\n            else if (options.applyAction !== 'never' &&\n                pageUpdate.data &&\n                typeof pageUpdate.data === 'object') {\n                // It's a page reload, redirect or error/failure,\n                // so don't trigger any events, just update the data.\n                for (const newForm of Context_findValidationForms(pageUpdate.data)) {\n                    const isInitial = initialForms.has(newForm);\n                    if (newForm.id !== Data.formId || isInitial) {\n                        continue;\n                    }\n                    if (options.invalidateAll === 'force' || options.invalidateAll === 'pessimistic') {\n                        initialForm.data = newForm.data;\n                    }\n                    const resetStatus = Form_shouldReset(newForm.valid, true);\n                    rebind({\n                        form: newForm,\n                        untaint: successResult,\n                        keepFiles: !resetStatus,\n                        resetted: resetStatus\n                    });\n                }\n            }\n        }));\n        if (typeof options.SPA === 'string') {\n            EnhancedForm_createFromSPA(options.SPA);\n        }\n    }\n    /**\n     * Custom use:enhance that enables all the client-side functionality.\n     * @param FormElement\n     * @param events\n     * @DCI-context\n     */\n    function superFormEnhance(FormElement, events) {\n        if (options.SPA !== undefined && FormElement.method == 'get')\n            FormElement.method = 'post';\n        if (typeof options.SPA === 'string') {\n            if (options.SPA.length && FormElement.action == document.location.href) {\n                FormElement.action = options.SPA;\n            }\n        }\n        else {\n            EnhancedForm = FormElement;\n        }\n        if (events) {\n            if (events.onError) {\n                if (options.onError === 'apply') {\n                    throw new SuperFormError('options.onError is set to \"apply\", cannot add any onError events.');\n                }\n                else if (events.onError === 'apply') {\n                    throw new SuperFormError('Cannot add \"apply\" as onError event in use:enhance.');\n                }\n                formEvents.onError.push(events.onError);\n            }\n            if (events.onResult)\n                formEvents.onResult.push(events.onResult);\n            if (events.onSubmit)\n                formEvents.onSubmit.push(events.onSubmit);\n            if (events.onUpdate)\n                formEvents.onUpdate.push(events.onUpdate);\n            if (events.onUpdated)\n                formEvents.onUpdated.push(events.onUpdated);\n        }\n        // Now we know that we are enhanced, we can enable the tainted form option\n        // for in-site navigation. Refresh and close tab is handled by window.beforeunload.\n        Tainted_enable();\n        let lastInputChange;\n        // TODO: Debounce option?\n        async function onInput(e) {\n            const info = inputInfo(e.target);\n            // Need to wait for immediate updates due to some timing issue\n            if (info.immediate && !info.file)\n                await new Promise((r) => setTimeout(r, 0));\n            lastInputChange = NextChange_paths();\n            NextChange_additionalEventInformation('input', info.immediate, info.multiple, FormElement, e.target ?? undefined);\n        }\n        async function onBlur(e) {\n            // Avoid triggering client-side validation while submitting\n            if (Data.submitting)\n                return;\n            if (!lastInputChange || NextChange_paths() != lastInputChange) {\n                return;\n            }\n            const info = inputInfo(e.target);\n            // Need to wait for immediate updates due to some timing issue\n            if (info.immediate && !info.file)\n                await new Promise((r) => setTimeout(r, 0));\n            Form_clientValidation({\n                paths: lastInputChange,\n                immediate: info.multiple,\n                multiple: info.multiple,\n                type: 'blur',\n                formElement: FormElement,\n                target: e.target ?? undefined\n            });\n            // Clear input change event, now that the field doesn't have focus anymore.\n            lastInputChange = undefined;\n        }\n        FormElement.addEventListener('focusout', onBlur);\n        FormElement.addEventListener('input', onInput);\n        onDestroy(() => {\n            FormElement.removeEventListener('focusout', onBlur);\n            FormElement.removeEventListener('input', onInput);\n        });\n        ///// SvelteKit enhance function //////////////////////////////////\n        const htmlForm = HtmlForm(FormElement, { submitting: Submitting, delayed: Delayed, timeout: Timeout }, options);\n        let currentRequest;\n        let customRequest = undefined;\n        const enhanced = kitEnhance(FormElement, async (submitParams) => {\n            let jsonData = undefined;\n            let validationAdapter = options.validators;\n            undefined;\n            const submit = {\n                ...submitParams,\n                jsonData(data) {\n                    if (options.dataType !== 'json') {\n                        throw new SuperFormError(\"options.dataType must be set to 'json' to use jsonData.\");\n                    }\n                    jsonData = data;\n                },\n                validators(adapter) {\n                    validationAdapter = adapter;\n                },\n                customRequest(request) {\n                    customRequest = request;\n                }\n            };\n            const _submitCancel = submit.cancel;\n            let cancelled = false;\n            function clientValidationResult(validation) {\n                const validationResult = { ...validation, posted: true };\n                const status = validationResult.valid ? 200 : Form_resultStatus(400);\n                const data = { form: validationResult };\n                const result = validationResult.valid\n                    ? { type: 'success', status, data }\n                    : { type: 'failure', status, data };\n                setTimeout(() => validationResponse({ result }), 0);\n            }\n            function clearOnSubmit() {\n                switch (options.clearOnSubmit) {\n                    case 'errors-and-message':\n                        Errors.clear();\n                        Message.set(undefined);\n                        break;\n                    case 'errors':\n                        Errors.clear();\n                        break;\n                    case 'message':\n                        Message.set(undefined);\n                        break;\n                }\n            }\n            async function triggerOnError(\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            result, status) {\n                // For v3, then return { form } as data in applyAction below:\n                //const form: SuperValidated<T, M, In> = Form_capture(false);\n                result.status = status;\n                // Check if the error message should be replaced\n                if (options.onError !== 'apply') {\n                    const event = { result, message: Message, form };\n                    for (const onErrorEvent of formEvents.onError) {\n                        if (onErrorEvent !== 'apply' &&\n                            (onErrorEvent != defaultOnError || !options.flashMessage?.onError)) {\n                            await onErrorEvent(event);\n                        }\n                    }\n                }\n                if (options.flashMessage && options.flashMessage.onError) {\n                    await options.flashMessage.onError({\n                        result,\n                        flashMessage: options.flashMessage.module.getFlash(page)\n                    });\n                }\n                if (options.applyAction) {\n                    if (options.onError == 'apply') {\n                        await applyAction(result);\n                    }\n                    else {\n                        // Transform to failure, to avoid data loss\n                        // Set the data to the error result, so it will be\n                        // picked up in page.subscribe in superForm.\n                        await applyAction({\n                            type: 'failure',\n                            status: Form_resultStatus(result.status),\n                            data: result\n                        });\n                    }\n                }\n            }\n            function cancel(opts = {\n                resetTimers: true\n            }) {\n                cancelled = true;\n                if (opts.resetTimers && htmlForm.isSubmitting()) {\n                    htmlForm.completed({ cancelled });\n                }\n                return _submitCancel();\n            }\n            submit.cancel = cancel;\n            if (htmlForm.isSubmitting() && options.multipleSubmits == 'prevent') {\n                cancel({ resetTimers: false });\n            }\n            else {\n                if (htmlForm.isSubmitting() && options.multipleSubmits == 'abort') {\n                    if (currentRequest)\n                        currentRequest.abort();\n                }\n                htmlForm.submitting();\n                currentRequest = submit.controller;\n                for (const event of formEvents.onSubmit) {\n                    try {\n                        await event(submit);\n                    }\n                    catch (error) {\n                        cancel();\n                        triggerOnError({ type: 'error', error }, 500);\n                    }\n                }\n            }\n            if (cancelled && options.flashMessage)\n                cancelFlash(options);\n            if (!cancelled) {\n                // Client validation\n                const noValidate = !Form_isSPA() &&\n                    (FormElement.noValidate ||\n                        ((submit.submitter instanceof HTMLButtonElement ||\n                            submit.submitter instanceof HTMLInputElement) &&\n                            submit.submitter.formNoValidate));\n                let validation = undefined;\n                const validateForm = async () => {\n                    return await Form_validate({ adapter: validationAdapter });\n                };\n                clearOnSubmit();\n                if (!noValidate) {\n                    validation = await validateForm();\n                    if (!validation.valid) {\n                        cancel({ resetTimers: false });\n                        clientValidationResult(validation);\n                    }\n                }\n                if (!cancelled) {\n                    if (options.flashMessage &&\n                        (options.clearOnSubmit == 'errors-and-message' || options.clearOnSubmit == 'message') &&\n                        shouldSyncFlash(options)) {\n                        options.flashMessage.module.getFlash(page).set(undefined);\n                    }\n                    // Deprecation fix\n                    const submitData = 'formData' in submit ? submit.formData : submit.data;\n                    // Prevent input/blur events to trigger client-side validation,\n                    // and accidentally removing errors set by setError\n                    lastInputChange = undefined;\n                    if (Form_isSPA()) {\n                        if (!validation)\n                            validation = await validateForm();\n                        cancel({ resetTimers: false });\n                        clientValidationResult(validation);\n                    }\n                    else if (options.dataType === 'json') {\n                        if (!validation)\n                            validation = await validateForm();\n                        const postData = clone(jsonData ?? validation.data);\n                        // Move files to form data, since they cannot be serialized.\n                        // Will be reassembled in superValidate.\n                        traversePaths(postData, (data) => {\n                            if (data.value instanceof File) {\n                                const key = '__superform_file_' + mergePath(data.path);\n                                submitData.append(key, data.value);\n                                return data.set(undefined);\n                            }\n                            else if (Array.isArray(data.value) &&\n                                data.value.length &&\n                                data.value.every((v) => v instanceof File)) {\n                                const key = '__superform_files_' + mergePath(data.path);\n                                for (const file of data.value) {\n                                    submitData.append(key, file);\n                                }\n                                return data.set(undefined);\n                            }\n                        });\n                        // Clear post data to reduce transfer size,\n                        // since $form should be serialized and sent as json.\n                        Object.keys(postData).forEach((key) => {\n                            // Files should be kept though, even if same key.\n                            if (typeof submitData.get(key) === 'string') {\n                                submitData.delete(key);\n                            }\n                        });\n                        const transport = options.transport\n                            ? Object.fromEntries(Object.entries(options.transport).map(([k, v]) => [k, v.encode]))\n                            : undefined;\n                        // Split the form data into chunks, in case it gets too large for proxy servers\n                        const chunks = chunkSubstr(stringify(postData, transport), options.jsonChunkSize ?? 500000);\n                        for (const chunk of chunks) {\n                            submitData.append('__superform_json', chunk);\n                        }\n                    }\n                    if (!submitData.has('__superform_id')) {\n                        // Add formId\n                        const id = Data.formId;\n                        if (id !== undefined)\n                            submitData.set('__superform_id', id);\n                    }\n                    if (typeof options.SPA === 'string') {\n                        EnhancedForm_setAction(options.SPA);\n                    }\n                }\n            }\n            ///// End of submit interaction ///////////////////////////////////////\n            // Thanks to https://stackoverflow.com/a/29202760/70894\n            function chunkSubstr(str, size) {\n                const numChunks = Math.ceil(str.length / size);\n                const chunks = new Array(numChunks);\n                for (let i = 0, o = 0; i < numChunks; ++i, o += size) {\n                    chunks[i] = str.substring(o, o + size);\n                }\n                return chunks;\n            }\n            // event can be a record if an external request was returning JSON,\n            // or if it failed parsing the expected JSON.\n            async function validationResponse(event) {\n                let cancelled = false;\n                currentRequest = null;\n                // Check if an error was thrown in hooks, in which case it has no type.\n                let result = 'type' in event.result && 'status' in event.result\n                    ? event.result\n                    : {\n                        type: 'error',\n                        status: Form_resultStatus(parseInt(String(event.result.status)) || 500),\n                        error: event.result.error instanceof Error ? event.result.error : event.result\n                    };\n                const cancel = () => (cancelled = true);\n                const data = {\n                    result,\n                    formEl: FormElement,\n                    formElement: FormElement,\n                    cancel\n                };\n                const unsubCheckforNav = STORYBOOK_MODE || !Form_isSPA()\n                    ? () => { }\n                    : navigating.subscribe(($nav) => {\n                        // Check for goto to a different route in the events\n                        if (!$nav || $nav.from?.route.id === $nav.to?.route.id)\n                            return;\n                        cancel();\n                    });\n                function setErrorResult(error, data, status) {\n                    data.result = {\n                        type: 'error',\n                        error,\n                        status: Form_resultStatus(status)\n                    };\n                }\n                for (const event of formEvents.onResult) {\n                    try {\n                        await event(data);\n                    }\n                    catch (error) {\n                        setErrorResult(error, data, Math.max(result.status ?? 500, 400));\n                    }\n                }\n                // In case it was modified in the event\n                result = data.result;\n                if (!cancelled) {\n                    if ((result.type === 'success' || result.type === 'failure') && result.data) {\n                        const forms = Context_findValidationForms(result.data);\n                        if (!forms.length) {\n                            throw new SuperFormError('No form data returned from ActionResult. Make sure you return { form } in the form actions.');\n                        }\n                        for (const newForm of forms) {\n                            if (newForm.id !== Data.formId)\n                                continue;\n                            const data = {\n                                form: newForm,\n                                formEl: FormElement,\n                                formElement: FormElement,\n                                cancel: () => (cancelled = true),\n                                result: result\n                            };\n                            for (const event of formEvents.onUpdate) {\n                                try {\n                                    await event(data);\n                                }\n                                catch (error) {\n                                    setErrorResult(error, data, Math.max(result.status ?? 500, 400));\n                                }\n                            }\n                            // In case it was modified in the event\n                            result = data.result;\n                            if (!cancelled) {\n                                if (options.customValidity) {\n                                    setCustomValidityForm(FormElement, data.form.errors);\n                                }\n                                // Special reset case for file inputs\n                                if (Form_shouldReset(data.form.valid, result.type == 'success')) {\n                                    data.formElement\n                                        .querySelectorAll('input[type=\"file\"]')\n                                        .forEach((e) => (e.value = ''));\n                                }\n                            }\n                        }\n                    }\n                    if (!cancelled) {\n                        if (result.type !== 'error') {\n                            if (result.type === 'success' && options.invalidateAll) {\n                                await invalidateAll();\n                            }\n                            if (options.applyAction) {\n                                // This will trigger the page subscription in superForm,\n                                // which will in turn call Data_update.\n                                await applyAction(result);\n                            }\n                            else {\n                                // Call Data_update directly to trigger events\n                                await Form_updateFromActionResult(result);\n                            }\n                        }\n                        else {\n                            await triggerOnError(result, Math.max(result.status ?? 500, 400));\n                        }\n                    }\n                }\n                if (cancelled && options.flashMessage) {\n                    cancelFlash(options);\n                }\n                // Redirect messages are handled in onDestroy and afterNavigate in client/form.ts.\n                if (cancelled || result.type != 'redirect') {\n                    htmlForm.completed({ cancelled });\n                }\n                else if (STORYBOOK_MODE) {\n                    htmlForm.completed({ cancelled, clearAll: true });\n                }\n                else {\n                    const unsub = navigating.subscribe(($nav) => {\n                        if ($nav)\n                            return;\n                        // Timeout required when applyAction is false\n                        setTimeout(() => {\n                            try {\n                                if (unsub)\n                                    unsub();\n                            }\n                            catch {\n                                // If component is already destroyed?\n                            }\n                        });\n                        if (htmlForm.isSubmitting()) {\n                            htmlForm.completed({ cancelled, clearAll: true });\n                        }\n                    });\n                }\n                unsubCheckforNav();\n            }\n            if (!cancelled && customRequest) {\n                _submitCancel();\n                const response = await customRequest(submitParams);\n                let result;\n                if (response instanceof Response) {\n                    result = deserialize(await response.text());\n                }\n                else if (response instanceof XMLHttpRequest) {\n                    result = deserialize(response.responseText);\n                }\n                else {\n                    result = response;\n                }\n                if (result.type === 'error')\n                    result.status = response.status;\n                validationResponse({ result });\n            }\n            return validationResponse;\n        });\n        return {\n            destroy: () => {\n                // Remove only events added in enhance\n                for (const [name, events] of Object.entries(formEvents)) {\n                    // @ts-expect-error formEvents and options have the same keys\n                    formEvents[name] = events.filter((e) => e === options[name]);\n                }\n                enhanced.destroy();\n            }\n        };\n    }\n    function removeFiles(formData) {\n        const paths = [];\n        traversePaths(formData, (data) => {\n            if (data.value instanceof File) {\n                paths.push(data.path);\n                return 'skip';\n            }\n            else if (Array.isArray(data.value) &&\n                data.value.length &&\n                data.value.every((d) => d instanceof File)) {\n                paths.push(data.path);\n                return 'skip';\n            }\n        });\n        if (!paths.length)\n            return { data: formData, paths };\n        const data = clone(formData);\n        setPaths(data, paths, (path) => pathExists(initialForm.data, path)?.value);\n        return { data, paths };\n    }\n    ///// Return the SuperForm object /////////////////////////////////\n    return {\n        form: Form,\n        formId: FormId,\n        errors: Errors,\n        message: Message,\n        constraints: Constraints,\n        tainted: Tainted_currentState(),\n        submitting: readonly(Submitting),\n        delayed: readonly(Delayed),\n        timeout: readonly(Timeout),\n        options: options,\n        capture: Form_capture,\n        restore: ((snapshot) => {\n            rebind({ form: snapshot, untaint: snapshot.tainted ?? true });\n        }),\n        async validate(path, opts = {}) {\n            if (!options.validators) {\n                throw new SuperFormError('options.validators must be set to use the validate method.');\n            }\n            if (opts.update === undefined)\n                opts.update = true;\n            if (opts.taint === undefined)\n                opts.taint = false;\n            if (typeof opts.errors == 'string')\n                opts.errors = [opts.errors];\n            let data;\n            const splittedPath = splitPath(path);\n            if ('value' in opts) {\n                if (opts.update === true || opts.update === 'value') {\n                    // eslint-disable-next-line dci-lint/private-role-access\n                    Form.update(($form) => {\n                        setPaths($form, [splittedPath], opts.value);\n                        return $form;\n                    }, { taint: opts.taint });\n                    data = Data.form;\n                }\n                else {\n                    data = clone(Data.form);\n                    setPaths(data, [splittedPath], opts.value);\n                }\n            }\n            else {\n                data = Data.form;\n            }\n            const result = await Form_validate({ formData: data });\n            const error = pathExists(result.errors, splittedPath);\n            // Replace with custom error, if it exist\n            if (error && error.value && opts.errors) {\n                error.value = opts.errors;\n            }\n            if (opts.update === true || opts.update == 'errors') {\n                Errors.update(($errors) => {\n                    setPaths($errors, [splittedPath], error?.value);\n                    return $errors;\n                });\n            }\n            return error?.value;\n        },\n        async validateForm(opts = {}) {\n            if (!options.validators && !opts.schema) {\n                throw new SuperFormError('options.validators or the schema option must be set to use the validateForm method.');\n            }\n            const result = opts.update\n                ? await Form_clientValidation({ paths: [] }, true, opts.schema)\n                : Form_validate({ adapter: opts.schema });\n            const enhancedForm = EnhancedForm_get();\n            if (opts.update && enhancedForm) {\n                // Focus on first error field\n                setTimeout(() => {\n                    if (!enhancedForm)\n                        return;\n                    scrollToFirstError(enhancedForm, {\n                        ...options,\n                        scrollToError: opts.focusOnError === false ? 'off' : options.scrollToError\n                    });\n                }, 1);\n            }\n            return result || Form_validate({ adapter: opts.schema });\n        },\n        allErrors: AllErrors,\n        posted: Posted,\n        reset(options) {\n            return Form_reset({\n                message: options?.keepMessage ? Data.message : undefined,\n                data: options?.data,\n                id: options?.id,\n                newState: options?.newState\n            });\n        },\n        submit(submitter) {\n            const form = EnhancedForm_get()\n                ? EnhancedForm_get()\n                : submitter && submitter instanceof HTMLElement\n                    ? submitter.closest('form')\n                    : undefined;\n            if (!form) {\n                throw new SuperFormError('use:enhance must be added to the form to use submit, or pass a HTMLElement inside the form (or the form itself) as an argument.');\n            }\n            if (!form.requestSubmit) {\n                return form.submit();\n            }\n            const isSubmitButton = submitter &&\n                ((submitter instanceof HTMLButtonElement && submitter.type == 'submit') ||\n                    (submitter instanceof HTMLInputElement && ['submit', 'image'].includes(submitter.type)));\n            form.requestSubmit(isSubmitButton ? submitter : undefined);\n        },\n        isTainted: Tainted_isTainted,\n        enhance: superFormEnhance\n    };\n}\n", "/*\n  Deep clones all properties except functions\n\n  var arr = [1, 2, 3];\n  var subObj = {aa: 1};\n  var obj = {a: 3, b: 5, c: arr, d: subObj};\n  var objClone = clone(obj);\n  arr.push(4);\n  subObj.bb = 2;\n  obj; // {a: 3, b: 5, c: [1, 2, 3, 4], d: {aa: 1}}\n  objClone; // {a: 3, b: 5, c: [1, 2, 3], d: {aa: 1, bb: 2}}\n*/\nexport function clone(obj) {\n    const type = {}.toString.call(obj).slice(8, -1);\n    if (type == 'Set') {\n        // @ts-expect-error Known type\n        return new Set([...obj].map((value) => clone(value)));\n    }\n    if (type == 'Map') {\n        // @ts-expect-error Known type\n        return new Map([...obj].map((kv) => [clone(kv[0]), clone(kv[1])]));\n    }\n    if (type == 'Date') {\n        // @ts-expect-error Known type\n        return new Date(obj.getTime());\n    }\n    if (type == 'RegExp') {\n        // @ts-expect-error Known type\n        return RegExp(obj.source, obj.flags);\n    }\n    if (type == 'Array' || type == 'Object') {\n        const result = type == 'Object' ? Object.create(Object.getPrototypeOf(obj)) : [];\n        for (const key in obj) {\n            result[key] = clone(obj[key]);\n        }\n        return result;\n    }\n    // primitives and non-supported objects (e.g. functions) land here\n    return obj;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction setPath(parent, key, value) {\n    parent[key] = value;\n    return 'skip';\n}\nfunction isInvalidPath(originalPath, pathData) {\n    return (pathData.value !== undefined &&\n        typeof pathData.value !== 'object' &&\n        pathData.path.length < originalPath.length);\n}\nexport function pathExists(obj, path, options = {}) {\n    if (!options.modifier) {\n        options.modifier = (pathData) => (isInvalidPath(path, pathData) ? undefined : pathData.value);\n    }\n    const exists = traversePath(obj, path, options.modifier);\n    if (!exists)\n        return undefined;\n    if (options.value === undefined)\n        return exists;\n    return options.value(exists.value) ? exists : undefined;\n}\nexport function traversePath(obj, realPath, modifier) {\n    if (!realPath.length)\n        return undefined;\n    const path = [realPath[0]];\n    let parent = obj;\n    while (parent && path.length < realPath.length) {\n        const key = path[path.length - 1];\n        const value = modifier\n            ? modifier({\n                parent,\n                key: String(key),\n                value: parent[key],\n                path: path.map((p) => String(p)),\n                isLeaf: false,\n                set: (v) => setPath(parent, key, v)\n            })\n            : parent[key];\n        if (value === undefined)\n            return undefined;\n        else\n            parent = value;\n        path.push(realPath[path.length]);\n    }\n    if (!parent)\n        return undefined;\n    const key = realPath[realPath.length - 1];\n    return {\n        parent,\n        key: String(key),\n        value: parent[key],\n        path: realPath.map((p) => String(p)),\n        isLeaf: true,\n        set: (v) => setPath(parent, key, v)\n    };\n}\nexport function traversePaths(parent, modifier, path = []) {\n    for (const key in parent) {\n        const value = parent[key];\n        const isLeaf = value === null || typeof value !== 'object';\n        const pathData = {\n            parent,\n            key,\n            value,\n            path: path.concat([key]), // path.map(String).concat([key])\n            isLeaf,\n            set: (v) => setPath(parent, key, v)\n        };\n        const status = modifier(pathData);\n        if (status === 'abort')\n            return status;\n        else if (status === 'skip')\n            continue;\n        else if (!isLeaf) {\n            const status = traversePaths(value, modifier, pathData.path);\n            if (status === 'abort')\n                return status;\n        }\n    }\n}\n// Thanks to https://stackoverflow.com/a/31129384/70894\nfunction eqSet(xs, ys) {\n    return xs === ys || (xs.size === ys.size && [...xs].every((x) => ys.has(x)));\n}\n/**\n * Compare two objects and return the differences as paths.\n */\nexport function comparePaths(newObj, oldObj) {\n    const diffPaths = new Map();\n    function builtInDiff(one, other) {\n        if (one instanceof Date && other instanceof Date && one.getTime() !== other.getTime())\n            return true;\n        if (one instanceof Set && other instanceof Set && !eqSet(one, other))\n            return true;\n        if (one instanceof File && other instanceof File && one !== other)\n            return true;\n        return false;\n    }\n    function isBuiltin(data) {\n        return data instanceof Date || data instanceof Set || data instanceof File;\n    }\n    function checkPath(data, compareTo) {\n        const otherData = compareTo ? traversePath(compareTo, data.path) : undefined;\n        //console.log('Compare', data.path, data.value, 'to', otherData?.path, otherData?.value);\n        function addDiff() {\n            //console.log('Diff', data.path);\n            diffPaths.set(data.path.join(' '), data.path);\n            return 'skip';\n        }\n        if (isBuiltin(data.value)) {\n            if (!isBuiltin(otherData?.value) || builtInDiff(data.value, otherData.value)) {\n                return addDiff();\n            }\n        }\n        if (data.isLeaf) {\n            if (!otherData || data.value !== otherData.value) {\n                addDiff();\n            }\n        }\n    }\n    traversePaths(newObj, (data) => checkPath(data, oldObj));\n    traversePaths(oldObj, (data) => checkPath(data, newObj));\n    // Need to sort the list so the shortest paths comes first\n    const output = Array.from(diffPaths.values());\n    output.sort((a, b) => a.length - b.length);\n    return output;\n}\nexport function setPaths(obj, paths, value) {\n    const isFunction = typeof value === 'function';\n    for (const path of paths) {\n        const leaf = traversePath(obj, path, ({ parent, key, value }) => {\n            if (value === undefined || typeof value !== 'object') {\n                // If a previous check tainted the node, but the search goes deeper,\n                // so it needs to be replaced with a (parent) node\n                parent[key] = {};\n            }\n            return parent[key];\n        });\n        if (leaf)\n            leaf.parent[leaf.key] = isFunction ? value(path, leaf) : value;\n    }\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nexport function splitPath(path) {\n    return path\n        .toString()\n        .split(/[[\\].]+/)\n        .filter((p) => p);\n}\nexport function mergePath(path) {\n    return path.reduce((acc, next) => {\n        const key = String(next);\n        if (typeof next === 'number' || /^\\d+$/.test(key))\n            acc += `[${key}]`;\n        else if (!acc)\n            acc += key;\n        else\n            acc += `.${key}`;\n        return acc;\n    }, '');\n}\n", "// istanbul ignore next\nconst isObject = (obj) => {\n    if (typeof obj === \"object\" && obj !== null) {\n        if (typeof Object.getPrototypeOf === \"function\") {\n            const prototype = Object.getPrototypeOf(obj);\n            return prototype === Object.prototype || prototype === null;\n        }\n        return Object.prototype.toString.call(obj) === \"[object Object]\";\n    }\n    return false;\n};\nexport const merge = (...objects) => objects.reduce((result, current) => {\n    if (current === undefined) {\n        return result;\n    }\n    if (Array.isArray(current)) {\n        throw new TypeError(\"Arguments provided to ts-deepmerge must be objects, not arrays.\");\n    }\n    Object.keys(current).forEach((key) => {\n        if ([\"__proto__\", \"constructor\", \"prototype\"].includes(key)) {\n            return;\n        }\n        if (Array.isArray(result[key]) && Array.isArray(current[key])) {\n            result[key] = merge.options.mergeArrays\n                ? merge.options.uniqueArrayItems\n                    ? Array.from(new Set(result[key].concat(current[key])))\n                    : [...result[key], ...current[key]]\n                : current[key];\n        }\n        else if (isObject(result[key]) && isObject(current[key])) {\n            result[key] = merge(result[key], current[key]);\n        }\n        else if (!isObject(result[key]) && isObject(current[key])) {\n            result[key] = merge(current[key], undefined);\n        }\n        else {\n            result[key] =\n                current[key] === undefined\n                    ? merge.options.allowUndefinedOverrides\n                        ? current[key]\n                        : result[key]\n                    : current[key];\n        }\n    });\n    return result;\n}, {});\nconst defaultOptions = {\n    allowUndefinedOverrides: true,\n    mergeArrays: true,\n    uniqueArrayItems: true,\n};\nmerge.options = defaultOptions;\nmerge.withOptions = (options, ...objects) => {\n    merge.options = Object.assign(Object.assign({}, defaultOptions), options);\n    const result = merge(...objects);\n    merge.options = defaultOptions;\n    return result;\n};\n", "import { assertSchema } from '../utils.js';\nimport { merge } from 'ts-deepmerge';\nconst conversionFormatTypes = ['unix-time', 'bigint', 'any', 'symbol', 'set', 'int64'];\n/**\n * Normalizes the different kind of schema variations (anyOf, union, const null, etc)\n * to figure out the field type, optional, nullable, etc.\n */\nexport function schemaInfo(schema, isOptional, path) {\n    assertSchema(schema, path);\n    const types = schemaTypes(schema, path);\n    const array = schema.items && types.includes('array')\n        ? (Array.isArray(schema.items) ? schema.items : [schema.items]).filter((s) => typeof s !== 'boolean')\n        : undefined;\n    const additionalProperties = schema.additionalProperties &&\n        typeof schema.additionalProperties === 'object' &&\n        types.includes('object')\n        ? Object.fromEntries(Object.entries(schema.additionalProperties).filter(([, value]) => typeof value !== 'boolean'))\n        : undefined;\n    const properties = schema.properties && types.includes('object')\n        ? Object.fromEntries(Object.entries(schema.properties).filter(([, value]) => typeof value !== 'boolean'))\n        : undefined;\n    const union = unionInfo(schema)?.filter((u) => u.type !== 'null' && u.const !== null);\n    const result = {\n        types: types.filter((s) => s !== 'null'),\n        isOptional,\n        isNullable: types.includes('null'),\n        schema,\n        union: union?.length ? union : undefined,\n        array,\n        properties,\n        additionalProperties,\n        required: schema.required\n    };\n    if (!schema.allOf || !schema.allOf.length) {\n        return result;\n    }\n    return {\n        ...merge.withOptions({ allowUndefinedOverrides: false }, result, ...schema.allOf.map((s) => schemaInfo(s, false, []))),\n        schema\n    };\n}\nfunction schemaTypes(schema, path) {\n    assertSchema(schema, path);\n    let types = schema.const === null ? ['null'] : [];\n    if (schema.type) {\n        types = Array.isArray(schema.type) ? schema.type : [schema.type];\n    }\n    if (schema.anyOf) {\n        types = schema.anyOf.flatMap((s) => schemaTypes(s, path));\n    }\n    if (types.includes('array') && schema.uniqueItems) {\n        const i = types.findIndex((t) => t != 'array');\n        types[i] = 'set';\n    }\n    else if (schema.format && conversionFormatTypes.includes(schema.format)) {\n        types.unshift(schema.format);\n        // Remove the integer type, as the schema format will be used\n        // instead in the following cases\n        if (schema.format == 'unix-time' || schema.format == 'int64') {\n            const i = types.findIndex((t) => t == 'integer');\n            types.splice(i, 1);\n        }\n    }\n    if (schema.const && schema.const !== null && typeof schema.const !== 'function') {\n        types.push(typeof schema.const);\n    }\n    return Array.from(new Set(types));\n}\nfunction unionInfo(schema) {\n    if (!schema.anyOf || !schema.anyOf.length)\n        return undefined;\n    return schema.anyOf.filter((s) => typeof s !== 'boolean');\n}\n", "import { SchemaError } from '../errors.js';\nimport { assertSchema } from '../utils.js';\nimport { merge } from 'ts-deepmerge';\nimport { schemaInfo } from './schemaInfo.js';\nexport function defaultValues(schema, isOptional = false, path = []) {\n    return _defaultValues(schema, isOptional, path);\n}\nfunction _defaultValues(schema, isOptional, path) {\n    if (!schema) {\n        throw new SchemaError('Schema was undefined', path);\n    }\n    const info = schemaInfo(schema, isOptional, path);\n    if (!info)\n        return undefined;\n    //if (schema.type == 'object') console.log('--- OBJECT ---');\n    //else console.dir({ path, schema, isOptional }, { depth: 10 });\n    let objectDefaults = undefined;\n    // Default takes (early) priority.\n    if ('default' in schema) {\n        // Test for object defaults.\n        // Cannot be returned directly, since undefined fields\n        // may have to be replaced with correct default values.\n        if (info.types.includes('object') &&\n            schema.default &&\n            typeof schema.default == 'object' &&\n            !Array.isArray(schema.default)) {\n            objectDefaults = schema.default;\n        }\n        else {\n            if (info.types.length > 1) {\n                if (info.types.includes('unix-time') &&\n                    (info.types.includes('integer') || info.types.includes('number')))\n                    throw new SchemaError('Cannot resolve a default value with a union that includes a date and a number/integer.', path);\n            }\n            const [type] = info.types;\n            return formatDefaultValue(type, schema.default);\n        }\n    }\n    let _multiType;\n    const isMultiTypeUnion = () => {\n        if (!info.union || info.union.length < 2)\n            return false;\n        if (info.union.some((i) => i.enum))\n            return true;\n        if (!_multiType) {\n            _multiType = new Set(info.types.map((i) => {\n                return ['integer', 'unix-time'].includes(i) ? 'number' : i;\n            }));\n        }\n        return _multiType.size > 1;\n    };\n    let output = undefined;\n    // Check unions first, so default values can take precedence over nullable and optional\n    if (!objectDefaults && info.union) {\n        const singleDefault = info.union.filter((s) => typeof s !== 'boolean' && s.default !== undefined);\n        if (singleDefault.length == 1) {\n            return _defaultValues(singleDefault[0], isOptional, path);\n        }\n        else if (singleDefault.length > 1) {\n            throw new SchemaError('Only one default value can exist in a union, or set a default value for the whole union.', path);\n        }\n        else {\n            // Null takes priority over undefined\n            if (info.isNullable)\n                return null;\n            if (info.isOptional)\n                return undefined;\n            if (isMultiTypeUnion()) {\n                throw new SchemaError('Multi-type unions must have a default value, or exactly one of the union types must have.', path);\n            }\n            // Objects must have default values to avoid setting undefined properties on nested data\n            if (info.union.length && info.types[0] == 'object') {\n                if (output === undefined)\n                    output = {};\n                output =\n                    info.union.length > 1\n                        ? merge.withOptions({ allowUndefinedOverrides: true }, ...info.union.map((s) => _defaultValues(s, isOptional, path)))\n                        : _defaultValues(info.union[0], isOptional, path);\n            }\n        }\n    }\n    if (!objectDefaults) {\n        // Null takes priority over undefined\n        if (info.isNullable)\n            return null;\n        if (info.isOptional)\n            return undefined;\n    }\n    // Objects\n    if (info.properties) {\n        for (const [key, objSchema] of Object.entries(info.properties)) {\n            assertSchema(objSchema, [...path, key]);\n            const def = objectDefaults && objectDefaults[key] !== undefined\n                ? objectDefaults[key]\n                : _defaultValues(objSchema, !info.required?.includes(key), [...path, key]);\n            //if (def !== undefined) output[key] = def;\n            if (output === undefined)\n                output = {};\n            output[key] = def;\n        }\n    }\n    else if (objectDefaults) {\n        return objectDefaults;\n    }\n    // TODO: [v3] Handle default values for array elements\n    // if (info.array && info.array.length) {\n    // \tconsole.log('===== Array default =====');\n    // \tconsole.dir(info.array, { depth: 10 }); //debug\n    // \t//if (info.array.length > 1) throw new SchemaError('Only one array type is supported.', path);\n    // \tconsole.dir(_defaultValues(info.array[0], info.isOptional, path), { depth: 10 }); //debug\n    // }\n    // Enums, return the first value so it can be a required field\n    if (schema.enum) {\n        return schema.enum[0];\n    }\n    // Basic type\n    if (isMultiTypeUnion()) {\n        throw new SchemaError('Default values cannot have more than one type.', path);\n    }\n    else if (info.types.length == 0) {\n        //console.warn('No type or format for property:', path); //debug\n        //console.dir(schema, { depth: 10 }); //debug\n        return undefined;\n    }\n    const [formatType] = info.types;\n    return output ?? defaultValue(formatType, schema.enum);\n}\nfunction formatDefaultValue(type, value) {\n    switch (type) {\n        case 'set':\n            return Array.isArray(value) ? new Set(value) : value;\n        case 'Date':\n        case 'date':\n        case 'unix-time':\n            if (typeof value === 'string' || typeof value === 'number')\n                return new Date(value);\n            break;\n        case 'bigint':\n            if (typeof value === 'string' || typeof value === 'number')\n                return BigInt(value);\n            break;\n        case 'symbol':\n            if (typeof value === 'string' || typeof value === 'number')\n                return Symbol(value);\n            break;\n    }\n    return value;\n}\nexport function defaultValue(type, enumType) {\n    switch (type) {\n        case 'string':\n            return enumType && enumType.length > 0 ? enumType[0] : '';\n        case 'number':\n        case 'integer':\n            return enumType && enumType.length > 0 ? enumType[0] : 0;\n        case 'boolean':\n            return false;\n        case 'array':\n            return [];\n        case 'object':\n            return {};\n        case 'null':\n            return null;\n        case 'Date':\n        case 'date':\n        case 'unix-time':\n            // Cannot add default for Date due to https://github.com/Rich-Harris/devalue/issues/51\n            return undefined;\n        case 'int64':\n        case 'bigint':\n            return BigInt(0);\n        case 'set':\n            return new Set();\n        case 'symbol':\n            return Symbol();\n        case 'undefined':\n        case 'any':\n            return undefined;\n        default:\n            throw new SchemaError('Schema type or format not supported, requires explicit default value: ' + type);\n    }\n}\n////////////////////////////////////////////////////////////////////////////\nexport function defaultTypes(schema, path = []) {\n    return _defaultTypes(schema, false, path);\n}\nfunction _defaultTypes(schema, isOptional, path) {\n    if (!schema) {\n        throw new SchemaError('Schema was undefined', path);\n    }\n    const info = schemaInfo(schema, isOptional, path);\n    const output = {\n        __types: info.types\n    };\n    //if (schema.type == 'object') console.log('--- OBJECT ---'); //debug\n    //else console.dir({ path, info }, { depth: 10 }); //debug\n    // schema.items cannot be an array according to\n    // https://www.learnjsonschema.com/2020-12/applicator/items/\n    if (info.schema.items &&\n        typeof info.schema.items == 'object' &&\n        !Array.isArray(info.schema.items)) {\n        output.__items = _defaultTypes(info.schema.items, info.isOptional, path);\n    }\n    if (info.properties) {\n        for (const [key, value] of Object.entries(info.properties)) {\n            assertSchema(value, [...path, key]);\n            output[key] = _defaultTypes(info.properties[key], !info.required?.includes(key), [\n                ...path,\n                key\n            ]);\n        }\n    }\n    // Check if a Record type is used for additionalProperties\n    if (info.additionalProperties && info.types.includes('object')) {\n        const additionalInfo = schemaInfo(info.additionalProperties, info.isOptional, path);\n        if (additionalInfo.properties && additionalInfo.types.includes('object')) {\n            for (const [key] of Object.entries(additionalInfo.properties)) {\n                output[key] = _defaultTypes(additionalInfo.properties[key], !additionalInfo.required?.includes(key), [...path, key]);\n            }\n        }\n    }\n    if (info.isNullable && !output.__types.includes('null')) {\n        output.__types.push('null');\n    }\n    if (info.isOptional && !output.__types.includes('undefined')) {\n        output.__types.push('undefined');\n    }\n    return output;\n}\n", "import { pathExists, setPaths, traversePath, traversePaths } from './traversal.js';\nimport { mergePath } from './stringPath.js';\nimport { defaultTypes, defaultValue } from './jsonSchema/schemaDefaults.js';\nimport { clone } from './utils.js';\nimport { merge } from 'ts-deepmerge';\nimport { schemaInfo } from './jsonSchema/schemaInfo.js';\nexport class SuperFormError extends Error {\n    constructor(message) {\n        super(message);\n        Object.setPrototypeOf(this, SuperFormError.prototype);\n    }\n}\nexport class SchemaError extends SuperFormError {\n    path;\n    constructor(message, path) {\n        super((path && path.length ? `[${Array.isArray(path) ? path.join('.') : path}] ` : '') + message);\n        this.path = Array.isArray(path) ? path.join('.') : path;\n        Object.setPrototypeOf(this, SchemaError.prototype);\n    }\n}\nexport function mapErrors(errors, shape) {\n    //console.log('===', errors.length, 'errors', shape);\n    const output = {};\n    function addFormLevelError(error) {\n        if (!('_errors' in output))\n            output._errors = [];\n        if (!Array.isArray(output._errors)) {\n            if (typeof output._errors === 'string')\n                output._errors = [output._errors];\n            else\n                throw new SuperFormError('Form-level error was not an array.');\n        }\n        output._errors.push(error.message);\n    }\n    for (const error of errors) {\n        // Form-level error\n        if (!error.path || (error.path.length == 1 && !error.path[0])) {\n            addFormLevelError(error);\n            continue;\n        }\n        // Path must filter away number indices, since the object shape doesn't contain these.\n        // Except the last, since otherwise any error in an array will count as an object error.\n        const isLastIndexNumeric = /^\\d$/.test(String(error.path[error.path.length - 1]));\n        const objectError = !isLastIndexNumeric &&\n            pathExists(shape, error.path.filter((p) => /\\D/.test(String(p))))?.value;\n        //console.log(error.path, error.message, objectError ? '[OBJ]' : '');\n        const leaf = traversePath(output, error.path, ({ value, parent, key }) => {\n            if (value === undefined)\n                parent[key] = {};\n            return parent[key];\n        });\n        if (!leaf) {\n            addFormLevelError(error);\n            continue;\n        }\n        const { parent, key } = leaf;\n        if (objectError) {\n            if (!(key in parent))\n                parent[key] = {};\n            if (!('_errors' in parent[key]))\n                parent[key]._errors = [error.message];\n            else\n                parent[key]._errors.push(error.message);\n        }\n        else {\n            if (!(key in parent))\n                parent[key] = [error.message];\n            else\n                parent[key].push(error.message);\n        }\n    }\n    return output;\n}\n/**\n * Filter errors based on validation method.\n * auto = Requires the existence of errors and tainted (field in store) to show\n * oninput = Set directly\n */\nexport function updateErrors(New, Previous, force) {\n    if (force)\n        return New;\n    // Set previous errors to undefined,\n    // which signifies that an error can be displayed there again.\n    traversePaths(Previous, (errors) => {\n        if (!Array.isArray(errors.value))\n            return;\n        errors.set(undefined);\n    });\n    traversePaths(New, (error) => {\n        if (!Array.isArray(error.value) && error.value !== undefined)\n            return;\n        setPaths(Previous, [error.path], error.value);\n    });\n    return Previous;\n}\nexport function flattenErrors(errors) {\n    return _flattenErrors(errors, []);\n}\nfunction _flattenErrors(errors, path) {\n    const entries = Object.entries(errors);\n    return entries\n        .filter(([, value]) => value !== undefined)\n        .flatMap(([key, messages]) => {\n        if (Array.isArray(messages) && messages.length > 0) {\n            const currPath = path.concat([key]);\n            return { path: mergePath(currPath), messages };\n        }\n        else {\n            return _flattenErrors(errors[key], path.concat([key]));\n        }\n    });\n}\n/**\n * Merge defaults with parsed data.\n */\nexport function mergeDefaults(parsedData, defaults) {\n    if (!parsedData)\n        return clone(defaults);\n    return merge.withOptions({ mergeArrays: false }, defaults, parsedData);\n}\n/**\n * Merge defaults with (important!) *already validated and merged data*.\n * @DCI-context\n */\nexport function replaceInvalidDefaults(Data, Defaults, _schema, Errors, preprocessed) {\n    const defaultType = _schema.additionalProperties && typeof _schema.additionalProperties == 'object'\n        ? { __types: schemaInfo(_schema.additionalProperties, false, []).types }\n        : undefined; // Will throw if a field does not exist\n    ///// Roles ///////////////////////////////////////////////////////\n    //#region Types\n    const Types = defaultTypes(_schema);\n    function Types_correctValue(dataValue, defValue, type) {\n        const types = type.__types;\n        if (!types.length || types.every((t) => t == 'undefined' || t == 'null' || t == 'any')) {\n            // No types counts as an \"any\" type\n            return dataValue;\n        }\n        else if (types.length == 1 && types[0] == 'array' && !type.__items) {\n            /*\n            No type info for array exists.\n            Keep the value even though it may not be the correct type, but validation\n            won't fail and the failed data is usually returned to the form without UX problems.\n            */\n            return dataValue;\n        }\n        const dateTypes = ['unix-time', 'Date', 'date'];\n        for (const schemaType of types) {\n            const defaultTypeValue = defaultValue(schemaType, undefined);\n            const sameType = typeof dataValue === typeof defaultTypeValue ||\n                (dateTypes.includes(schemaType) && dataValue instanceof Date);\n            const sameExistance = sameType && (dataValue === null) === (defaultTypeValue === null);\n            if (sameType && sameExistance) {\n                return dataValue;\n            }\n            else if (type.__items) {\n                // Parse array type\n                return Types_correctValue(dataValue, defValue, type.__items);\n            }\n        }\n        // null takes preference over undefined\n        if (defValue === undefined && types.includes('null')) {\n            return null;\n        }\n        return defValue;\n    }\n    //#endregion\n    //#region Data\n    function Data_traverse() {\n        traversePaths(Defaults, Defaults_traverseAndReplace);\n        Errors_traverseAndReplace();\n        return Data;\n    }\n    function Data_setValue(currentPath, newValue) {\n        setPaths(Data, [currentPath], newValue);\n    }\n    //#endregion\n    //#region Errors\n    function Errors_traverseAndReplace() {\n        for (const error of Errors) {\n            if (!error.path)\n                continue;\n            Defaults_traverseAndReplace({\n                path: error.path,\n                value: pathExists(Defaults, error.path)?.value\n            }, true);\n        }\n    }\n    //#endregion\n    //#region Defaults\n    function Defaults_traverseAndReplace(defaultPath, traversingErrors = false) {\n        const currentPath = defaultPath.path;\n        if (!currentPath || !currentPath[0])\n            return;\n        if (typeof currentPath[0] === 'string' && preprocessed?.includes(currentPath[0]))\n            return;\n        const dataPath = pathExists(Data, currentPath);\n        //let newValue = defValue;\n        if ((!dataPath && defaultPath.value !== undefined) ||\n            (dataPath && dataPath.value === undefined)) {\n            Data_setValue(currentPath, defaultPath.value);\n        }\n        else if (dataPath) {\n            const defValue = defaultPath.value;\n            const dataValue = dataPath.value;\n            // Check for same JS type with an existing default value.\n            if (defValue !== undefined &&\n                typeof dataValue === typeof defValue &&\n                (dataValue === null) === (defValue === null)) {\n                return;\n            }\n            const typePath = currentPath.filter((p) => /\\D/.test(String(p)));\n            const pathTypes = traversePath(Types, typePath, (path) => {\n                //console.log(path.path, path.value); //debug\n                return path.value && '__items' in path.value ? path.value.__items : path.value;\n            });\n            if (!pathTypes) {\n                // Return if checking for errors, as there may be deep errors that doesn't exist in the defaults.\n                if (traversingErrors)\n                    return;\n                throw new SchemaError('No types found for defaults', currentPath);\n            }\n            const fieldType = pathTypes.value ?? defaultType;\n            if (fieldType) {\n                Data_setValue(currentPath, Types_correctValue(dataValue, defValue, fieldType));\n            }\n        }\n    }\n    //#endregion\n    {\n        return Data_traverse();\n    }\n}\n", "import { clone as justClone } from './justClone.js';\nimport { SchemaError } from './errors.js';\nexport function clone(data) {\n    return data && typeof data === 'object' ? justClone(data) : data;\n}\nexport function assertSchema(schema, path) {\n    if (typeof schema === 'boolean') {\n        throw new SchemaError('Schema property cannot be defined as boolean.', path);\n    }\n}\n/**\n * Casts a Svelte store of a Record<string, unknown> type to a merged type of its unions.\n * @param store A Svelte store of a Record<string, unknown> type\n * @returns The same store but casted to a merged type of its unions.\n */\nexport function mergeFormUnion(store) {\n    return store;\n}\n", "import { browser } from '$app/environment';\nexport function cancelFlash(options) {\n    if (!options.flashMessage || !browser)\n        return;\n    if (!shouldSyncFlash(options))\n        return;\n    document.cookie = `flash=; Max-Age=0; Path=${options.flashMessage.cookiePath ?? '/'};`;\n}\nexport function shouldSyncFlash(options) {\n    if (!options.flashMessage || !browser)\n        return false;\n    return options.syncFlashMessage;\n}\n", "import { splitPath } from '../stringPath.js';\nimport { traversePath } from '../traversal.js';\nconst noCustomValidityDataAttribute = 'noCustomValidity';\nexport async function updateCustomValidity(validityEl, errors) {\n    // Always reset validity, in case it has been validated on the server.\n    if ('setCustomValidity' in validityEl) {\n        validityEl.setCustomValidity('');\n    }\n    if (noCustomValidityDataAttribute in validityEl.dataset)\n        return;\n    setCustomValidity(validityEl, errors);\n}\nexport function setCustomValidityForm(formElement, errors) {\n    for (const el of formElement.querySelectorAll('input,select,textarea,button')) {\n        if (('dataset' in el && noCustomValidityDataAttribute in el.dataset) || !el.name) {\n            continue;\n        }\n        const path = traversePath(errors, splitPath(el.name));\n        const error = path && typeof path.value === 'object' && '_errors' in path.value\n            ? path.value._errors\n            : path?.value;\n        setCustomValidity(el, error);\n        if (error)\n            return;\n    }\n}\nfunction setCustomValidity(el, errors) {\n    if (!('setCustomValidity' in el))\n        return;\n    const message = errors && errors.length ? errors.join('\\n') : '';\n    el.setCustomValidity(message);\n    if (message)\n        el.reportValidity();\n}\n", "// https://stackoverflow.com/a/7557433/70894\nexport const isElementInViewport = (el, topOffset = 0) => {\n    const rect = el.getBoundingClientRect();\n    return (rect.top >= topOffset &&\n        rect.left >= 0 &&\n        rect.bottom <=\n            (window.innerHeight || document.documentElement.clientHeight) /* or $(window).height() */ &&\n        rect.right <=\n            (window.innerWidth || document.documentElement.clientWidth) /* or $(window).width() */);\n};\n// https://stackoverflow.com/a/36499256/70894\nexport const scrollToAndCenter = (el, offset = 1.125, behavior = 'smooth') => {\n    const elementRect = el.getBoundingClientRect();\n    const absoluteElementTop = elementRect.top + window.pageYOffset;\n    const top = absoluteElementTop - window.innerHeight / (2 * offset);\n    window.scrollTo({ left: 0, top, behavior });\n};\nconst immediateInputTypes = ['checkbox', 'radio', 'range', 'file'];\n/**\n * Information about a HTML element, for determining when to display errors.\n */\nexport function inputInfo(el) {\n    const immediate = !!el &&\n        (el instanceof HTMLSelectElement ||\n            (el instanceof HTMLInputElement && immediateInputTypes.includes(el.type)));\n    const multiple = !!el && el instanceof HTMLSelectElement && el.multiple;\n    const file = !!el && el instanceof HTMLInputElement && el.type == 'file';\n    return { immediate, multiple, file };\n}\n", "import { isElementInViewport, scrollToAndCenter } from './elements.js';\nimport { onDestroy, tick } from 'svelte';\nvar FetchStatus;\n(function (FetchStatus) {\n    FetchStatus[FetchStatus[\"Idle\"] = 0] = \"Idle\";\n    FetchStatus[FetchStatus[\"Submitting\"] = 1] = \"Submitting\";\n    FetchStatus[FetchStatus[\"Delayed\"] = 2] = \"Delayed\";\n    FetchStatus[FetchStatus[\"Timeout\"] = 3] = \"Timeout\";\n})(FetchStatus || (FetchStatus = {}));\nconst activeTimers = new Set();\n//let _initialized = false;\n/**\n * @DCI-context\n */\nexport function Form(formElement, timers, options) {\n    let state = FetchStatus.Idle;\n    let delayedTimeout, timeoutTimeout;\n    //#region Timers\n    const Timers = activeTimers;\n    // https://www.nngroup.com/articles/response-times-3-important-limits/\n    function Timers_start() {\n        Timers_clear();\n        Timers_setState(state != FetchStatus.Delayed ? FetchStatus.Submitting : FetchStatus.Delayed);\n        delayedTimeout = window.setTimeout(() => {\n            if (delayedTimeout && state == FetchStatus.Submitting)\n                Timers_setState(FetchStatus.Delayed);\n        }, options.delayMs);\n        timeoutTimeout = window.setTimeout(() => {\n            if (timeoutTimeout && state == FetchStatus.Delayed)\n                Timers_setState(FetchStatus.Timeout);\n        }, options.timeoutMs);\n        Timers.add(Timers_clear);\n    }\n    /**\n     * Clear timers and set state to Idle.\n     */\n    function Timers_clear() {\n        clearTimeout(delayedTimeout);\n        clearTimeout(timeoutTimeout);\n        delayedTimeout = timeoutTimeout = 0;\n        Timers.delete(Timers_clear);\n        Timers_setState(FetchStatus.Idle);\n    }\n    function Timers_clearAll() {\n        Timers.forEach((t) => t());\n        Timers.clear();\n    }\n    function Timers_setState(s) {\n        state = s;\n        timers.submitting.set(state >= FetchStatus.Submitting);\n        timers.delayed.set(state >= FetchStatus.Delayed);\n        timers.timeout.set(state >= FetchStatus.Timeout);\n    }\n    //#endregion\n    //#region ErrorTextEvents\n    const ErrorTextEvents = formElement;\n    function ErrorTextEvents__selectText(e) {\n        const target = e.target;\n        if (options.selectErrorText)\n            target.select();\n    }\n    function ErrorTextEvents_addErrorTextListeners() {\n        if (!options.selectErrorText)\n            return;\n        ErrorTextEvents.querySelectorAll('input').forEach((el) => {\n            el.addEventListener('invalid', ErrorTextEvents__selectText);\n        });\n    }\n    function ErrorTextEvents_removeErrorTextListeners() {\n        if (!options.selectErrorText)\n            return;\n        ErrorTextEvents.querySelectorAll('input').forEach((el) => el.removeEventListener('invalid', ErrorTextEvents__selectText));\n    }\n    //#endregion\n    //#region Form\n    const Form = formElement;\n    //#endregion\n    {\n        ErrorTextEvents_addErrorTextListeners();\n        const completed = (opts) => {\n            if (!opts.clearAll)\n                Timers_clear();\n            else\n                Timers_clearAll();\n            if (!opts.cancelled)\n                setTimeout(() => scrollToFirstError(Form, options), 1);\n        };\n        onDestroy(() => {\n            ErrorTextEvents_removeErrorTextListeners();\n            completed({ cancelled: true });\n        });\n        return {\n            submitting() {\n                Timers_start();\n            },\n            completed,\n            scrollToFirstError() {\n                setTimeout(() => scrollToFirstError(Form, options), 1);\n            },\n            isSubmitting: () => state === FetchStatus.Submitting || state === FetchStatus.Delayed\n        };\n    }\n}\nexport const scrollToFirstError = async (Form, options) => {\n    if (options.scrollToError == 'off')\n        return;\n    const selector = options.errorSelector;\n    if (!selector)\n        return;\n    // Wait for form to update with errors\n    await tick();\n    // Scroll to first form message, if not visible\n    let el;\n    el = Form.querySelector(selector);\n    if (!el)\n        return;\n    // Find underlying element if it is a FormGroup element\n    el = el.querySelector(selector) ?? el;\n    const nav = options.stickyNavbar\n        ? document.querySelector(options.stickyNavbar)\n        : null;\n    if (typeof options.scrollToError != 'string') {\n        el.scrollIntoView(options.scrollToError);\n    }\n    else if (!isElementInViewport(el, nav?.offsetHeight ?? 0)) {\n        scrollToAndCenter(el, undefined, options.scrollToError);\n    }\n    function Form_shouldAutoFocus(userAgent) {\n        if (typeof options.autoFocusOnError === 'boolean')\n            return options.autoFocusOnError;\n        else\n            return !/iPhone|iPad|iPod|Android/i.test(userAgent);\n    }\n    // Don't focus on the element if on mobile, it will open the keyboard\n    // and probably hide the error message.\n    if (!Form_shouldAutoFocus(navigator.userAgent))\n        return;\n    let focusEl;\n    focusEl = el;\n    if (!['INPUT', 'SELECT', 'BUTTON', 'TEXTAREA'].includes(focusEl.tagName)) {\n        focusEl = focusEl.querySelector('input:not([type=\"hidden\"]):not(.flatpickr-input), select, textarea');\n    }\n    if (focusEl) {\n        try {\n            focusEl.focus({ preventScroll: true });\n            if (options.selectErrorText && focusEl.tagName == 'INPUT') {\n                focusEl.select();\n            }\n        }\n        catch (err) {\n            // Some hidden inputs like from flatpickr cannot be focused.\n        }\n    }\n};\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { derived, get, writable } from 'svelte/store';\nimport { SuperFormError } from '../errors.js';\nimport { pathExists, traversePath } from '../traversal.js';\nimport { splitPath } from '../stringPath.js';\nimport { browser } from '$app/environment';\nconst defaultOptions = {\n    trueStringValue: 'true',\n    dateFormat: 'iso',\n    step: 60\n};\n///// Proxy functions ///////////////////////////////////////////////\nexport function booleanProxy(form, path, options) {\n    return _stringProxy(form, path, 'boolean', {\n        ...defaultOptions,\n        ...options\n    });\n}\nexport function intProxy(form, path, options) {\n    return _stringProxy(form, path, 'int', {\n        ...defaultOptions,\n        ...options\n    });\n}\nexport function numberProxy(form, path, options) {\n    return _stringProxy(form, path, 'number', {\n        ...defaultOptions,\n        ...options\n    });\n}\nexport function dateProxy(form, path, options) {\n    return _stringProxy(form, path, 'date', {\n        ...defaultOptions,\n        dateFormat: options?.format ?? 'iso',\n        empty: options?.empty,\n        step: options?.step ?? 60\n    });\n}\nexport function stringProxy(form, path, options) {\n    return _stringProxy(form, path, 'string', {\n        ...defaultOptions,\n        ...options\n    });\n}\nexport function fileFieldProxy(form, path, options) {\n    const fileField = fileProxy(form, path, options);\n    const formField = formFieldProxy(form, path, options);\n    return { ...formField, value: fileField };\n}\nexport function fileProxy(form, path, options) {\n    const formFile = fieldProxy(form, path, options);\n    const fileProxy = writable(browser ? new DataTransfer().files : {});\n    let initialized = false;\n    let initialValue;\n    formFile.subscribe((file) => {\n        if (!browser)\n            return;\n        if (!initialized) {\n            initialValue = options?.empty ? (options.empty === 'undefined' ? undefined : null) : file;\n            initialized = true;\n        }\n        const dt = new DataTransfer();\n        if (file instanceof File)\n            dt.items.add(file);\n        fileProxy.set(dt.files);\n    });\n    const fileStore = {\n        subscribe(run) {\n            return fileProxy.subscribe(run);\n        },\n        set(file) {\n            if (!browser)\n                return;\n            if (!file) {\n                const dt = new DataTransfer();\n                fileProxy.set(dt.files);\n                formFile.set(file);\n            }\n            else if (file instanceof File) {\n                const dt = new DataTransfer();\n                dt.items.add(file);\n                fileProxy.set(dt.files);\n                formFile.set(file);\n            }\n            else if (file instanceof FileList) {\n                fileProxy.set(file);\n                if (file.length > 0)\n                    formFile.set(file.item(0));\n                else\n                    formFile.set(initialValue);\n            }\n        },\n        update() {\n            throw new SuperFormError('You cannot update a fileProxy, only set it.');\n        }\n    };\n    return fileStore;\n}\nexport function filesFieldProxy(form, path, options) {\n    const filesStore = filesProxy(form, path, options);\n    const arrayField = arrayProxy(form, path, options);\n    return { ...arrayField, values: filesStore };\n}\nexport function filesProxy(form, path, options) {\n    const formFiles = fieldProxy(form, path, options);\n    const filesProxy = writable(browser ? new DataTransfer().files : {});\n    formFiles.subscribe((files) => {\n        if (!browser)\n            return;\n        const dt = new DataTransfer();\n        if (Array.isArray(files)) {\n            if (files.length && files.every((f) => !f)) {\n                formFiles.set([]);\n                return;\n            }\n            files.filter((f) => f instanceof File).forEach((file) => dt.items.add(file));\n        }\n        filesProxy.set(dt.files);\n    });\n    const filesStore = {\n        subscribe(run) {\n            return filesProxy.subscribe(run);\n        },\n        set(files) {\n            if (!browser)\n                return;\n            if (!(files instanceof FileList)) {\n                const dt = new DataTransfer();\n                if (Array.isArray(files))\n                    files.forEach((file) => {\n                        if (file instanceof File)\n                            dt.items.add(file);\n                    });\n                filesProxy.set(dt.files);\n                formFiles.set(files);\n            }\n            else {\n                const output = [];\n                for (let i = 0; i < files.length; i++) {\n                    const file = files.item(i);\n                    if (file)\n                        output.push(file);\n                }\n                filesProxy.set(files);\n                formFiles.set(output);\n            }\n        },\n        update(updater) {\n            filesStore.set(updater(get(formFiles)));\n        }\n    };\n    return filesStore;\n}\n///// Implementation ////////////////////////////////////////////////\n/**\n * Creates a string store that will pass its value to a field in the form.\n * @param form The form\n * @param field Form field\n * @param type 'number' | 'int' | 'boolean'\n */\nfunction _stringProxy(form, path, type, options) {\n    function toValue(value) {\n        if (!value && options.empty !== undefined) {\n            return options.empty === 'null' ? null : options.empty === 'zero' ? 0 : undefined;\n        }\n        if (typeof value === 'number') {\n            value = value.toString();\n        }\n        if (typeof value !== 'string') {\n            // Can be undefined due to Proxy in Svelte 5\n            value = '';\n        }\n        const stringValue = value;\n        if (type == 'string')\n            return stringValue;\n        else if (type == 'boolean')\n            return !!stringValue;\n        else if (type == 'date') {\n            if (stringValue.indexOf('-') === -1) {\n                const utc = options.dateFormat.indexOf('utc') >= 0;\n                const date = utc ? UTCDate(new Date()) : localDate(new Date());\n                return new Date(date + 'T' + stringValue + (utc ? 'Z' : ''));\n            }\n            else\n                return new Date(stringValue);\n        }\n        const numberToConvert = options.delimiter\n            ? stringValue.replace(options.delimiter, '.')\n            : stringValue;\n        let num;\n        if (numberToConvert === '' && options.empty == 'zero')\n            num = 0;\n        else if (type == 'number')\n            num = parseFloat(numberToConvert);\n        else\n            num = parseInt(numberToConvert, 10);\n        return num;\n    }\n    const isSuper = isSuperForm(form, options);\n    const realProxy = isSuper\n        ? superFieldProxy(form, path, { taint: options.taint })\n        : fieldProxy(form, path);\n    let updatedValue = null;\n    let initialized = false;\n    const proxy = derived(realProxy, (value) => {\n        if (!initialized) {\n            initialized = true;\n            if (options.initiallyEmptyIfZero && !value)\n                return '';\n        }\n        // Prevent proxy updating itself\n        if (updatedValue !== null) {\n            const current = updatedValue;\n            updatedValue = null;\n            return current;\n        }\n        if (value === undefined || value === null)\n            return '';\n        if (type == 'string') {\n            return value;\n        }\n        else if (type == 'int' || type == 'number') {\n            if (value === '') {\n                // Special case for empty string values in number proxies\n                // Set the value to 0, to conform to the type.\n                realProxy.set(0, isSuper ? { taint: false } : undefined);\n            }\n            if (typeof value === 'number' && isNaN(value))\n                return '';\n            return String(value);\n        }\n        else if (type == 'date') {\n            const date = typeof value === 'string' || typeof value === 'number' ? new Date(value) : value;\n            if (isNaN(date))\n                return '';\n            switch (options.dateFormat) {\n                case 'iso':\n                    return date.toISOString();\n                case 'date':\n                    return date.toISOString().slice(0, 10);\n                case 'datetime':\n                    return date.toISOString().slice(0, options.step % 60 ? 19 : 16);\n                case 'time':\n                    return date.toISOString().slice(11, options.step % 60 ? 19 : 16);\n                case 'date-utc':\n                    return UTCDate(date);\n                case 'datetime-utc':\n                    return UTCDate(date) + 'T' + UTCTime(date, options.step);\n                case 'time-utc':\n                    return UTCTime(date, options.step);\n                case 'date-local':\n                    return localDate(date);\n                case 'datetime-local':\n                    return localDate(date) + 'T' + localTime(date, options.step);\n                case 'time-local':\n                    return localTime(date, options.step);\n            }\n        }\n        else {\n            // boolean\n            return value ? options.trueStringValue : '';\n        }\n    });\n    return {\n        subscribe: proxy.subscribe,\n        set(val) {\n            updatedValue = val;\n            const newValue = toValue(updatedValue);\n            realProxy.set(newValue);\n        },\n        update(updater) {\n            realProxy.update((f) => {\n                updatedValue = updater(String(f));\n                const newValue = toValue(updatedValue);\n                return newValue;\n            });\n        }\n    };\n}\nexport function arrayProxy(superForm, path, options) {\n    const formErrors = fieldProxy(superForm.errors, `${path}`);\n    const onlyFieldErrors = derived(formErrors, ($errors) => {\n        const output = [];\n        for (const key in $errors) {\n            if (key == '_errors')\n                continue;\n            output[key] = $errors[key];\n        }\n        return output;\n    });\n    function updateArrayErrors(errors, value) {\n        for (const key in errors) {\n            if (key == '_errors')\n                continue;\n            errors[key] = undefined;\n        }\n        if (value !== undefined) {\n            for (const key in value) {\n                errors[key] = value[key];\n            }\n        }\n        return errors;\n    }\n    const fieldErrors = {\n        subscribe: onlyFieldErrors.subscribe,\n        update(upd) {\n            formErrors.update(($errors) => \n            // @ts-expect-error Type is correct\n            updateArrayErrors($errors, upd($errors)));\n        },\n        set(value) {\n            // @ts-expect-error Type is correct\n            formErrors.update(($errors) => updateArrayErrors($errors, value));\n        }\n    };\n    const values = superFieldProxy(superForm, path, options);\n    // If array is shortened, delete all keys above length\n    // in errors, so they won't be kept if the array is lengthened again.\n    let lastLength = Array.isArray(get(values)) ? get(values).length : 0;\n    values.subscribe(($values) => {\n        const currentLength = Array.isArray($values) ? $values.length : 0;\n        if (currentLength < lastLength) {\n            superForm.errors.update(($errors) => {\n                const node = pathExists($errors, splitPath(path));\n                if (!node)\n                    return $errors;\n                for (const key in node.value) {\n                    if (Number(key) < currentLength)\n                        continue;\n                    delete node.value[key];\n                }\n                return $errors;\n            }, { force: true });\n        }\n        lastLength = currentLength;\n    });\n    return {\n        path,\n        values: values,\n        errors: fieldProxy(superForm.errors, `${path}._errors`),\n        valueErrors: fieldErrors\n    };\n}\nexport function formFieldProxy(superForm, path, options) {\n    const path2 = splitPath(path);\n    // Filter out array indices, the constraints structure doesn't contain these.\n    const constraintsPath = path2.filter((p) => /\\D/.test(String(p))).join('.');\n    const taintedProxy = derived(superForm.tainted, ($tainted) => {\n        if (!$tainted)\n            return $tainted;\n        const taintedPath = traversePath($tainted, path2);\n        return taintedPath ? taintedPath.value : undefined;\n    });\n    const tainted = {\n        subscribe: taintedProxy.subscribe,\n        update(upd) {\n            superForm.tainted.update(($tainted) => {\n                if (!$tainted)\n                    $tainted = {};\n                const output = traversePath($tainted, path2, (path) => {\n                    if (!path.value)\n                        path.parent[path.key] = {};\n                    return path.parent[path.key];\n                });\n                if (output)\n                    output.parent[output.key] = upd(output.value);\n                return $tainted;\n            });\n        },\n        set(value) {\n            superForm.tainted.update(($tainted) => {\n                if (!$tainted)\n                    $tainted = {};\n                const output = traversePath($tainted, path2, (path) => {\n                    if (!path.value)\n                        path.parent[path.key] = {};\n                    return path.parent[path.key];\n                });\n                if (output)\n                    output.parent[output.key] = value;\n                return $tainted;\n            });\n        }\n    };\n    return {\n        path,\n        value: superFieldProxy(superForm, path, options),\n        errors: fieldProxy(superForm.errors, path),\n        constraints: fieldProxy(superForm.constraints, constraintsPath),\n        tainted\n    };\n}\nfunction updateProxyField(obj, path, updater) {\n    const output = traversePath(obj, path, ({ parent, key, value }) => {\n        if (value === undefined)\n            parent[key] = /\\D/.test(key) ? {} : [];\n        return parent[key];\n    });\n    if (output) {\n        const newValue = updater(output.value);\n        output.parent[output.key] = newValue;\n    }\n    return obj;\n}\nfunction superFieldProxy(superForm, path, baseOptions) {\n    const form = superForm.form;\n    const path2 = splitPath(path);\n    const proxy = derived(form, ($form) => {\n        const data = traversePath($form, path2);\n        return data?.value;\n    });\n    return {\n        subscribe(...params) {\n            const unsub = proxy.subscribe(...params);\n            return () => unsub();\n        },\n        update(upd, options) {\n            form.update((data) => updateProxyField(data, path2, upd), options ?? baseOptions);\n        },\n        set(value, options) {\n            form.update((data) => updateProxyField(data, path2, () => value), options ?? baseOptions);\n        }\n    };\n}\nfunction isSuperForm(form, options) {\n    const isSuperForm = 'form' in form;\n    if (!isSuperForm && options?.taint !== undefined) {\n        throw new SuperFormError('If options.taint is set, the whole superForm object must be used as a proxy.');\n    }\n    return isSuperForm;\n}\nexport function fieldProxy(form, path, options) {\n    const path2 = splitPath(path);\n    if (isSuperForm(form, options)) {\n        return superFieldProxy(form, path, options);\n    }\n    const proxy = derived(form, ($form) => {\n        const data = traversePath($form, path2);\n        return data?.value;\n    });\n    return {\n        subscribe(...params) {\n            const unsub = proxy.subscribe(...params);\n            return () => unsub();\n        },\n        update(upd) {\n            form.update((data) => updateProxyField(data, path2, upd));\n        },\n        set(value) {\n            form.update((data) => updateProxyField(data, path2, () => value));\n        }\n    };\n}\nfunction localDate(date) {\n    return (date.getFullYear() +\n        '-' +\n        String(date.getMonth() + 1).padStart(2, '0') +\n        '-' +\n        String(date.getDate()).padStart(2, '0'));\n}\nfunction localTime(date, step) {\n    return (String(date.getHours()).padStart(2, '0') +\n        ':' +\n        String(date.getMinutes()).padStart(2, '0') +\n        (step % 60 ? ':' + String(date.getSeconds()).padStart(2, '0') : ''));\n}\nfunction UTCDate(date) {\n    return (date.getUTCFullYear() +\n        '-' +\n        String(date.getUTCMonth() + 1).padStart(2, '0') +\n        '-' +\n        String(date.getUTCDate()).padStart(2, '0'));\n}\nfunction UTCTime(date, step) {\n    return (String(date.getUTCHours()).padStart(2, '0') +\n        ':' +\n        String(date.getUTCMinutes()).padStart(2, '0') +\n        (step % 60 ? ':' + String(date.getUTCSeconds()).padStart(2, '0') : ''));\n}\n/*\nfunction dateToUTC(date: Date) {\n  return new Date(\n    date.getUTCFullYear(),\n    date.getUTCMonth(),\n    date.getUTCDate(),\n    date.getUTCHours(),\n    date.getUTCMinutes(),\n    date.getUTCSeconds()\n  );\n}\n*/\n", "import { SchemaError } from '../errors.js';\nimport { schemaInfo } from './schemaInfo.js';\nimport { assertSchema } from '../utils.js';\nexport function schemaShape(schema, path = []) {\n    const output = _schemaShape(schema, path);\n    if (!output)\n        throw new SchemaError('No shape could be created for schema.', path);\n    return output;\n}\nfunction _schemaShape(schema, path) {\n    assertSchema(schema, path);\n    const info = schemaInfo(schema, false, path);\n    if (info.array || info.union) {\n        const arr = info.array || [];\n        const union = info.union || [];\n        return arr.concat(union).reduce((shape, next) => {\n            const nextShape = _schemaShape(next, path);\n            if (nextShape)\n                shape = { ...(shape ?? {}), ...nextShape };\n            return shape;\n        }, arr.length ? {} : undefined);\n    }\n    if (info.properties) {\n        const output = {};\n        for (const [key, prop] of Object.entries(info.properties)) {\n            const shape = _schemaShape(prop, [...path, key]);\n            if (shape)\n                output[key] = shape;\n        }\n        return output;\n    }\n    return info.types.includes('array') || info.types.includes('object') ? {} : undefined;\n}\nexport function shapeFromObject(obj) {\n    let output = {};\n    const isArray = Array.isArray(obj);\n    for (const [key, value] of Object.entries(obj)) {\n        if (!value || typeof value !== 'object')\n            continue;\n        if (isArray)\n            output = { ...output, ...shapeFromObject(value) };\n        else\n            output[key] = shapeFromObject(value);\n    }\n    return output;\n}\n", "export function defaults(data, adapter, options) {\n    if (data && 'superFormValidationLibrary' in data) {\n        options = adapter;\n        adapter = data;\n        data = null;\n    }\n    const validator = adapter;\n    const optionDefaults = options?.defaults ?? validator.defaults;\n    return {\n        id: options?.id ?? validator.id ?? '',\n        valid: false,\n        posted: false,\n        errors: {},\n        data: { ...optionDefaults, ...data },\n        constraints: validator.constraints,\n        shape: validator.shape\n    };\n}\nexport function defaultValues(adapter) {\n    return adapter.defaults;\n}\n", "import { json } from '@sveltejs/kit';\nimport { stringify } from 'devalue';\nexport function actionResult(type, data, options) {\n    function cookieData() {\n        if (typeof options === 'number' || !options?.message)\n            return '';\n        const extra = [\n            `Path=${options?.cookieOptions?.path || '/'}`,\n            `Max-Age=${options?.cookieOptions?.maxAge || 120}`,\n            `SameSite=${options?.cookieOptions?.sameSite ?? 'Strict'}`\n        ];\n        if (options?.cookieOptions?.secure) {\n            extra.push(`Secure`);\n        }\n        return `flash=${encodeURIComponent(JSON.stringify(options.message))}; ` + extra.join('; ');\n    }\n    const status = options && typeof options !== 'number' ? options.status : options;\n    const result = (struct) => {\n        return json({ type, ...struct }, {\n            status: struct.status,\n            headers: typeof options === 'object' && options.message\n                ? {\n                    'Set-Cookie': cookieData()\n                }\n                : undefined\n        });\n    };\n    if (type == 'error') {\n        return result({\n            status: status || 500,\n            error: typeof data === 'string' ? { message: data } : data\n        });\n    }\n    else if (type == 'redirect') {\n        return result({\n            status: status || 303,\n            location: data\n        });\n    }\n    else if (type == 'failure') {\n        return result({\n            status: status || 400,\n            data: stringify(data)\n        });\n    }\n    else {\n        return result({ status: status || 200, data: stringify(data) });\n    }\n}\n", "import { traversePath } from './traversal.js';\nimport { fail as kitFail } from '@sveltejs/kit';\nimport {} from './adapters/adapters.js';\nimport { parseRequest } from './formData.js';\nimport { splitPath } from './stringPath.js';\nimport { mapErrors, mergeDefaults, replaceInvalidDefaults } from './errors.js';\n/**\n * Validates a schema for data validation and usage in superForm.\n * @param data Data corresponding to a schema, or RequestEvent/FormData/URL. If falsy, the schema's default values will be used.\n * @param schema The schema to validate against.\n */\nexport async function superValidate(data, adapter, options) {\n    if (data && 'superFormValidationLibrary' in data) {\n        options = adapter;\n        adapter = data;\n        data = undefined;\n    }\n    const validator = adapter;\n    const defaults = options?.defaults ?? validator.defaults;\n    const jsonSchema = validator.jsonSchema;\n    const parsed = await parseRequest(data, jsonSchema, options);\n    const addErrors = options?.errors ?? (options?.strict ? true : !!parsed.data);\n    // Merge with defaults in non-strict mode.\n    const parsedData = options?.strict ? (parsed.data ?? {}) : mergeDefaults(parsed.data, defaults);\n    let status;\n    if (!!parsed.data || addErrors) {\n        status = await /* @__PURE__ */ validator.validate(parsedData);\n    }\n    else {\n        status = { success: false, issues: [] };\n    }\n    const valid = status.success;\n    const errors = valid || !addErrors ? {} : mapErrors(status.issues, validator.shape);\n    // Final data should always have defaults, to ensure type safety\n    //const dataWithDefaults = { ...defaults, ...(valid ? status.data : parsedData) };\n    const dataWithDefaults = valid\n        ? status.data\n        : replaceInvalidDefaults(options?.strict ? mergeDefaults(parsedData, defaults) : parsedData, defaults, jsonSchema, status.issues, options?.preprocessed);\n    let outputData;\n    if (jsonSchema.additionalProperties === false) {\n        // Strip keys not belonging to schema\n        outputData = {};\n        for (const key of Object.keys(jsonSchema.properties ?? {})) {\n            if (key in dataWithDefaults)\n                outputData[key] = dataWithDefaults[key];\n        }\n    }\n    else {\n        outputData = dataWithDefaults;\n    }\n    const output = {\n        id: parsed.id ?? options?.id ?? validator.id,\n        valid,\n        posted: parsed.posted,\n        errors: errors,\n        data: outputData\n    };\n    if (!parsed.posted) {\n        output.constraints = validator.constraints;\n        if (Object.keys(validator.shape).length) {\n            output.shape = validator.shape;\n        }\n    }\n    return output;\n}\n/////////////////////////////////////////////////////////////////////\n/**\n * Sends a message with a form, with an optional HTTP status code that will set\n * form.valid to false if status >= 400. A status lower than 400 cannot be sent.\n */\nexport function message(form, message, options) {\n    if (options?.status && options.status >= 400) {\n        form.valid = false;\n    }\n    form.message = message;\n    const remove = options?.removeFiles !== false;\n    const output = remove ? withFiles({ form }) : { form };\n    return form.valid ? output : kitFail(options?.status ?? 400, output);\n}\nexport const setMessage = message;\nexport function setError(form, path, error, options) {\n    // Unify signatures\n    if (error == undefined || (typeof error !== 'string' && !Array.isArray(error))) {\n        options = error;\n        error = path;\n        path = '';\n    }\n    if (options === undefined)\n        options = {};\n    const errArr = Array.isArray(error) ? error : [error];\n    if (!form.errors)\n        form.errors = {};\n    if (path === null || path === '') {\n        if (!form.errors._errors)\n            form.errors._errors = [];\n        form.errors._errors = options.overwrite ? errArr : form.errors._errors.concat(errArr);\n    }\n    else {\n        const realPath = splitPath(path);\n        const leaf = traversePath(form.errors, realPath, ({ parent, key, value }) => {\n            if (value === undefined)\n                parent[key] = {};\n            return parent[key];\n        });\n        if (leaf) {\n            leaf.parent[leaf.key] =\n                Array.isArray(leaf.value) && !options.overwrite ? leaf.value.concat(errArr) : errArr;\n        }\n    }\n    form.valid = false;\n    const output = options.removeFiles === false ? { form } : withFiles({ form });\n    return kitFail(options.status ?? 400, output);\n}\nexport function withFiles(obj) {\n    if (typeof obj !== 'object')\n        return obj;\n    for (const key in obj) {\n        const value = obj[key];\n        if (value instanceof File)\n            delete obj[key];\n        else if (value && typeof value === 'object')\n            withFiles(value);\n    }\n    return obj;\n}\nexport const removeFiles = withFiles;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function fail(status, data) {\n    function checkForm(data) {\n        return !!data && typeof data === 'object' && 'valid' in data && 'data' in data && 'id' in data;\n    }\n    function checkObj(data) {\n        if (data && typeof data === 'object') {\n            for (const key in data) {\n                const v = data[key];\n                if (checkForm(v)) {\n                    v.valid = false;\n                    removeFiles(v);\n                }\n                else if (v && typeof v === 'object') {\n                    checkObj(v);\n                }\n            }\n        }\n        return data;\n    }\n    return kitFail(status, checkObj(data));\n}\n", "import { SuperFormError, SchemaError } from './errors.js';\nimport { parse } from 'devalue';\nimport { schemaInfo } from './jsonSchema/schemaInfo.js';\nimport { defaultValues } from './jsonSchema/schemaDefaults.js';\nimport { setPaths } from './traversal.js';\nimport { splitPath } from './stringPath.js';\nimport { assertSchema } from './utils.js';\n/**\n * V1 compatibilty. resetForm = false and taintedMessage = true\n */\nlet legacyMode = false;\ntry {\n    // @ts-expect-error Vite define check\n    if (SUPERFORMS_LEGACY)\n        legacyMode = true;\n}\ncatch {\n    // No legacy mode defined\n}\nconst unionError = 'FormData parsing failed: Unions are only supported when the dataType option for superForm is set to \"json\".';\nexport async function parseRequest(data, schemaData, options) {\n    let parsed;\n    if (data instanceof FormData) {\n        parsed = parseFormData(data, schemaData, options);\n    }\n    else if (data instanceof URL || data instanceof URLSearchParams) {\n        parsed = parseSearchParams(data, schemaData, options);\n    }\n    else if (data instanceof Request) {\n        parsed = await tryParseFormData(data, schemaData, options);\n    }\n    else if (\n    // RequestEvent\n    data &&\n        typeof data === 'object' &&\n        'request' in data &&\n        data.request instanceof Request) {\n        parsed = await tryParseFormData(data.request, schemaData, options);\n    }\n    else {\n        parsed = {\n            id: undefined,\n            data: data,\n            posted: false\n        };\n    }\n    return parsed;\n}\nasync function tryParseFormData(request, schemaData, options) {\n    let formData = undefined;\n    try {\n        formData = await request.formData();\n    }\n    catch (e) {\n        if (e instanceof TypeError && e.message.includes('already been consumed')) {\n            // Pass through the \"body already consumed\" error, which applies to\n            // POST requests when event/request is used after formData has been fetched.\n            throw e;\n        }\n        // No data found, return an empty form\n        return { id: undefined, data: undefined, posted: false };\n    }\n    return parseFormData(formData, schemaData, options);\n}\nexport function parseSearchParams(data, schemaData, options) {\n    if (data instanceof URL)\n        data = data.searchParams;\n    const convert = new FormData();\n    for (const [key, value] of data.entries()) {\n        convert.append(key, value);\n    }\n    const output = parseFormData(convert, schemaData, options);\n    // Set posted to false since it's a URL\n    output.posted = false;\n    return output;\n}\nexport function parseFormData(formData, schemaData, options) {\n    function tryParseSuperJson() {\n        if (formData.has('__superform_json')) {\n            try {\n                const transport = options && options.transport\n                    ? Object.fromEntries(Object.entries(options.transport).map(([k, v]) => [k, v.decode]))\n                    : undefined;\n                const output = parse(formData.getAll('__superform_json').join('') ?? '', transport);\n                if (typeof output === 'object') {\n                    // Restore uploaded files and add to data\n                    const filePaths = Array.from(formData.keys());\n                    for (const path of filePaths.filter((path) => path.startsWith('__superform_file_'))) {\n                        const realPath = splitPath(path.substring(17));\n                        setPaths(output, [realPath], formData.get(path));\n                    }\n                    for (const path of filePaths.filter((path) => path.startsWith('__superform_files_'))) {\n                        const realPath = splitPath(path.substring(18));\n                        const allFiles = formData.getAll(path);\n                        setPaths(output, [realPath], Array.from(allFiles));\n                    }\n                    return output;\n                }\n            }\n            catch {\n                //\n            }\n        }\n        return null;\n    }\n    const data = tryParseSuperJson();\n    const id = formData.get('__superform_id')?.toString();\n    return data\n        ? { id, data, posted: true }\n        : {\n            id,\n            data: _parseFormData(formData, schemaData, options),\n            posted: true\n        };\n}\nfunction _parseFormData(formData, schema, options) {\n    const output = {};\n    let schemaKeys;\n    if (options?.strict) {\n        schemaKeys = new Set([...formData.keys()].filter((key) => !key.startsWith('__superform_')));\n    }\n    else {\n        let unionKeys = [];\n        // Special fix for union schemas, then the keys must be gathered from the objects in the union\n        if (schema.anyOf) {\n            const info = schemaInfo(schema, false, []);\n            if (info.union?.some((s) => s.type !== 'object')) {\n                throw new SchemaError('All form types must be an object if schema is a union.');\n            }\n            unionKeys = info.union?.flatMap((s) => Object.keys(s.properties ?? {})) ?? [];\n        }\n        schemaKeys = new Set([\n            ...unionKeys,\n            ...Object.keys(schema.properties ?? {}),\n            ...(schema.additionalProperties ? formData.keys() : [])\n        ].filter((key) => !key.startsWith('__superform_')));\n    }\n    function parseSingleEntry(key, entry, info) {\n        if (options?.preprocessed && options.preprocessed.includes(key)) {\n            return entry;\n        }\n        if (entry && typeof entry !== 'string') {\n            const allowFiles = legacyMode ? options?.allowFiles === true : options?.allowFiles !== false;\n            return !allowFiles ? undefined : entry.size ? entry : info.isNullable ? null : undefined;\n        }\n        if (info.types.length > 1) {\n            throw new SchemaError(unionError, key);\n        }\n        const [type] = info.types;\n        return parseFormDataEntry(key, entry, type ?? 'any', info);\n    }\n    const defaultPropertyType = typeof schema.additionalProperties == 'object'\n        ? schema.additionalProperties\n        : { type: 'string' };\n    for (const key of schemaKeys) {\n        const property = schema.properties\n            ? schema.properties[key]\n            : defaultPropertyType;\n        assertSchema(property, key);\n        const info = schemaInfo(property ?? defaultPropertyType, !schema.required?.includes(key), [\n            key\n        ]);\n        if (!info)\n            continue;\n        if (!info.types.includes('boolean') && !schema.additionalProperties && !formData.has(key)) {\n            continue;\n        }\n        const entries = formData.getAll(key);\n        if (info.union && info.union.length > 1) {\n            throw new SchemaError(unionError, key);\n        }\n        if (info.types.includes('array') || info.types.includes('set')) {\n            // If no items, it could be a union containing the info\n            const items = property.items ?? (info.union?.length == 1 ? info.union[0] : undefined);\n            if (!items || typeof items == 'boolean' || (Array.isArray(items) && items.length != 1)) {\n                throw new SchemaError('Arrays must have a single \"items\" property that defines its type.', key);\n            }\n            const arrayType = Array.isArray(items) ? items[0] : items;\n            assertSchema(arrayType, key);\n            const arrayInfo = schemaInfo(arrayType, info.isOptional, [key]);\n            if (!arrayInfo)\n                continue;\n            // Check for empty files being posted (and filtered)\n            const isFileArray = entries.length && entries.some((e) => e && typeof e !== 'string');\n            const arrayData = entries.map((e) => parseSingleEntry(key, e, arrayInfo));\n            if (isFileArray && arrayData.every((file) => !file))\n                arrayData.length = 0;\n            output[key] = info.types.includes('set') ? new Set(arrayData) : arrayData;\n        }\n        else {\n            output[key] = parseSingleEntry(key, entries[entries.length - 1], info);\n        }\n    }\n    return output;\n}\nfunction parseFormDataEntry(key, value, type, info) {\n    //console.log(`Parsing FormData ${key} (${type}): \"${value}\"`, info); //debug\n    if (!value) {\n        //console.log(`No FormData for \"${key}\" (${type}).`, info); //debug\n        // Special case for booleans with default value true\n        if (type == 'boolean' && info.isOptional && info.schema.default === true) {\n            return false;\n        }\n        const defaultValue = defaultValues(info.schema, info.isOptional, [key]);\n        // Special case for empty posted enums, then the empty value should be returned,\n        // otherwise even a required field will get a default value, resulting in that\n        // posting missing enum values must use strict mode.\n        if (info.schema.enum && defaultValue !== null && defaultValue !== undefined) {\n            return value;\n        }\n        if (defaultValue !== undefined)\n            return defaultValue;\n        if (info.isNullable)\n            return null;\n        if (info.isOptional)\n            return undefined;\n    }\n    function typeError() {\n        throw new SchemaError(type[0].toUpperCase() +\n            type.slice(1) +\n            ` type found. ` +\n            `Set the dataType option to \"json\" and add use:enhance on the client to use nested data structures. ` +\n            `More information: https://superforms.rocks/concepts/nested-data`, key);\n    }\n    switch (type) {\n        case 'string':\n        case 'any':\n            return value;\n        case 'integer':\n            return parseInt(value ?? '', 10);\n        case 'number':\n            return parseFloat(value ?? '');\n        case 'boolean':\n            return Boolean(value == 'false' ? '' : value).valueOf();\n        case 'unix-time': {\n            // Must return undefined for invalid dates due to https://github.com/Rich-Harris/devalue/issues/51\n            const date = new Date(value ?? '');\n            return !isNaN(date) ? date : undefined;\n        }\n        case 'int64':\n        case 'bigint':\n            return BigInt(value ?? '.');\n        case 'symbol':\n            return Symbol(String(value));\n        case 'set':\n        case 'array':\n        case 'object':\n            return typeError();\n        default:\n            throw new SuperFormError('Unsupported schema type for FormData: ' + type);\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAS,YAAY,YAAY;;;ACW1B,SAAS,MAAM,KAAK;AACvB,QAAM,OAAO,CAAC,EAAE,SAAS,KAAK,GAAG,EAAE,MAAM,GAAG,EAAE;AAC9C,MAAI,QAAQ,OAAO;AAEf,WAAO,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC,CAAC;AAAA,EACxD;AACA,MAAI,QAAQ,OAAO;AAEf,WAAO,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,EACrE;AACA,MAAI,QAAQ,QAAQ;AAEhB,WAAO,IAAI,KAAK,IAAI,QAAQ,CAAC;AAAA,EACjC;AACA,MAAI,QAAQ,UAAU;AAElB,WAAO,OAAO,IAAI,QAAQ,IAAI,KAAK;AAAA,EACvC;AACA,MAAI,QAAQ,WAAW,QAAQ,UAAU;AACrC,UAAM,SAAS,QAAQ,WAAW,OAAO,OAAO,OAAO,eAAe,GAAG,CAAC,IAAI,CAAC;AAC/E,eAAW,OAAO,KAAK;AACnB,aAAO,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AAEA,SAAO;AACX;;;ACtCA,SAAS,QAAQ,QAAQ,KAAK,OAAO;AACjC,SAAO,GAAG,IAAI;AACd,SAAO;AACX;AACA,SAAS,cAAc,cAAc,UAAU;AAC3C,SAAQ,SAAS,UAAU,UACvB,OAAO,SAAS,UAAU,YAC1B,SAAS,KAAK,SAAS,aAAa;AAC5C;AACO,SAAS,WAAW,KAAK,MAAM,UAAU,CAAC,GAAG;AAChD,MAAI,CAAC,QAAQ,UAAU;AACnB,YAAQ,WAAW,CAAC,aAAc,cAAc,MAAM,QAAQ,IAAI,SAAY,SAAS;AAAA,EAC3F;AACA,QAAM,SAAS,aAAa,KAAK,MAAM,QAAQ,QAAQ;AACvD,MAAI,CAAC;AACD,WAAO;AACX,MAAI,QAAQ,UAAU;AAClB,WAAO;AACX,SAAO,QAAQ,MAAM,OAAO,KAAK,IAAI,SAAS;AAClD;AACO,SAAS,aAAa,KAAK,UAAU,UAAU;AAClD,MAAI,CAAC,SAAS;AACV,WAAO;AACX,QAAM,OAAO,CAAC,SAAS,CAAC,CAAC;AACzB,MAAI,SAAS;AACb,SAAO,UAAU,KAAK,SAAS,SAAS,QAAQ;AAC5C,UAAMA,OAAM,KAAK,KAAK,SAAS,CAAC;AAChC,UAAM,QAAQ,WACR,SAAS;AAAA,MACP;AAAA,MACA,KAAK,OAAOA,IAAG;AAAA,MACf,OAAO,OAAOA,IAAG;AAAA,MACjB,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,MAC/B,QAAQ;AAAA,MACR,KAAK,CAAC,MAAM,QAAQ,QAAQA,MAAK,CAAC;AAAA,IACtC,CAAC,IACC,OAAOA,IAAG;AAChB,QAAI,UAAU;AACV,aAAO;AAAA;AAEP,eAAS;AACb,SAAK,KAAK,SAAS,KAAK,MAAM,CAAC;AAAA,EACnC;AACA,MAAI,CAAC;AACD,WAAO;AACX,QAAM,MAAM,SAAS,SAAS,SAAS,CAAC;AACxC,SAAO;AAAA,IACH;AAAA,IACA,KAAK,OAAO,GAAG;AAAA,IACf,OAAO,OAAO,GAAG;AAAA,IACjB,MAAM,SAAS,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,IACnC,QAAQ;AAAA,IACR,KAAK,CAAC,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAAA,EACtC;AACJ;AACO,SAAS,cAAc,QAAQ,UAAU,OAAO,CAAC,GAAG;AACvD,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,OAAO,GAAG;AACxB,UAAM,SAAS,UAAU,QAAQ,OAAO,UAAU;AAClD,UAAM,WAAW;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,OAAO,CAAC,GAAG,CAAC;AAAA;AAAA,MACvB;AAAA,MACA,KAAK,CAAC,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAAA,IACtC;AACA,UAAM,SAAS,SAAS,QAAQ;AAChC,QAAI,WAAW;AACX,aAAO;AAAA,aACF,WAAW;AAChB;AAAA,aACK,CAAC,QAAQ;AACd,YAAMC,UAAS,cAAc,OAAO,UAAU,SAAS,IAAI;AAC3D,UAAIA,YAAW;AACX,eAAOA;AAAA,IACf;AAAA,EACJ;AACJ;AAEA,SAAS,MAAM,IAAI,IAAI;AACnB,SAAO,OAAO,MAAO,GAAG,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;AAC9E;AAIO,SAAS,aAAa,QAAQ,QAAQ;AACzC,QAAM,YAAY,oBAAI,IAAI;AAC1B,WAAS,YAAY,KAAK,OAAO;AAC7B,QAAI,eAAe,QAAQ,iBAAiB,QAAQ,IAAI,QAAQ,MAAM,MAAM,QAAQ;AAChF,aAAO;AACX,QAAI,eAAe,OAAO,iBAAiB,OAAO,CAAC,MAAM,KAAK,KAAK;AAC/D,aAAO;AACX,QAAI,eAAe,QAAQ,iBAAiB,QAAQ,QAAQ;AACxD,aAAO;AACX,WAAO;AAAA,EACX;AACA,WAAS,UAAU,MAAM;AACrB,WAAO,gBAAgB,QAAQ,gBAAgB,OAAO,gBAAgB;AAAA,EAC1E;AACA,WAAS,UAAU,MAAM,WAAW;AAChC,UAAM,YAAY,YAAY,aAAa,WAAW,KAAK,IAAI,IAAI;AAEnE,aAAS,UAAU;AAEf,gBAAU,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,KAAK,IAAI;AAC5C,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK,KAAK,GAAG;AACvB,UAAI,CAAC,UAAU,uCAAW,KAAK,KAAK,YAAY,KAAK,OAAO,UAAU,KAAK,GAAG;AAC1E,eAAO,QAAQ;AAAA,MACnB;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ;AACb,UAAI,CAAC,aAAa,KAAK,UAAU,UAAU,OAAO;AAC9C,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ;AACA,gBAAc,QAAQ,CAAC,SAAS,UAAU,MAAM,MAAM,CAAC;AACvD,gBAAc,QAAQ,CAAC,SAAS,UAAU,MAAM,MAAM,CAAC;AAEvD,QAAM,SAAS,MAAM,KAAK,UAAU,OAAO,CAAC;AAC5C,SAAO,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AACzC,SAAO;AACX;AACO,SAAS,SAAS,KAAK,OAAO,OAAO;AACxC,QAAM,aAAa,OAAO,UAAU;AACpC,aAAW,QAAQ,OAAO;AACtB,UAAM,OAAO,aAAa,KAAK,MAAM,CAAC,EAAE,QAAQ,KAAK,OAAAC,OAAM,MAAM;AAC7D,UAAIA,WAAU,UAAa,OAAOA,WAAU,UAAU;AAGlD,eAAO,GAAG,IAAI,CAAC;AAAA,MACnB;AACA,aAAO,OAAO,GAAG;AAAA,IACrB,CAAC;AACD,QAAI;AACA,WAAK,OAAO,KAAK,GAAG,IAAI,aAAa,MAAM,MAAM,IAAI,IAAI;AAAA,EACjE;AACJ;;;AC5IO,SAAS,UAAU,MAAM;AAC5B,SAAO,KACF,SAAS,EACT,MAAM,SAAS,EACf,OAAO,CAAC,MAAM,CAAC;AACxB;AACO,SAAS,UAAU,MAAM;AAC5B,SAAO,KAAK,OAAO,CAAC,KAAK,SAAS;AAC9B,UAAM,MAAM,OAAO,IAAI;AACvB,QAAI,OAAO,SAAS,YAAY,QAAQ,KAAK,GAAG;AAC5C,aAAO,IAAI,GAAG;AAAA,aACT,CAAC;AACN,aAAO;AAAA;AAEP,aAAO,IAAI,GAAG;AAClB,WAAO;AAAA,EACX,GAAG,EAAE;AACT;;;ACjBA,IAAM,WAAW,CAAC,QAAQ;AACtB,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,QAAI,OAAO,OAAO,mBAAmB,YAAY;AAC7C,YAAM,YAAY,OAAO,eAAe,GAAG;AAC3C,aAAO,cAAc,OAAO,aAAa,cAAc;AAAA,IAC3D;AACA,WAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,EACnD;AACA,SAAO;AACX;AACO,IAAM,QAAQ,IAAI,YAAY,QAAQ,OAAO,CAAC,QAAQ,YAAY;AACrE,MAAI,YAAY,QAAW;AACvB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,OAAO,GAAG;AACxB,UAAM,IAAI,UAAU,iEAAiE;AAAA,EACzF;AACA,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AAClC,QAAI,CAAC,aAAa,eAAe,WAAW,EAAE,SAAS,GAAG,GAAG;AACzD;AAAA,IACJ;AACA,QAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,KAAK,MAAM,QAAQ,QAAQ,GAAG,CAAC,GAAG;AAC3D,aAAO,GAAG,IAAI,MAAM,QAAQ,cACtB,MAAM,QAAQ,mBACV,MAAM,KAAK,IAAI,IAAI,OAAO,GAAG,EAAE,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,IACpD,CAAC,GAAG,OAAO,GAAG,GAAG,GAAG,QAAQ,GAAG,CAAC,IACpC,QAAQ,GAAG;AAAA,IACrB,WACS,SAAS,OAAO,GAAG,CAAC,KAAK,SAAS,QAAQ,GAAG,CAAC,GAAG;AACtD,aAAO,GAAG,IAAI,MAAM,OAAO,GAAG,GAAG,QAAQ,GAAG,CAAC;AAAA,IACjD,WACS,CAAC,SAAS,OAAO,GAAG,CAAC,KAAK,SAAS,QAAQ,GAAG,CAAC,GAAG;AACvD,aAAO,GAAG,IAAI,MAAM,QAAQ,GAAG,GAAG,MAAS;AAAA,IAC/C,OACK;AACD,aAAO,GAAG,IACN,QAAQ,GAAG,MAAM,SACX,MAAM,QAAQ,0BACV,QAAQ,GAAG,IACX,OAAO,GAAG,IACd,QAAQ,GAAG;AAAA,IACzB;AAAA,EACJ,CAAC;AACD,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,iBAAiB;AAAA,EACnB,yBAAyB;AAAA,EACzB,aAAa;AAAA,EACb,kBAAkB;AACtB;AACA,MAAM,UAAU;AAChB,MAAM,cAAc,CAAC,YAAY,YAAY;AACzC,QAAM,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG,OAAO;AACxE,QAAM,SAAS,MAAM,GAAG,OAAO;AAC/B,QAAM,UAAU;AAChB,SAAO;AACX;;;ACvDA,IAAM,wBAAwB,CAAC,aAAa,UAAU,OAAO,UAAU,OAAO,OAAO;AAK9E,SAAS,WAAW,QAAQ,YAAY,MAAM;AAPrD;AAQI,eAAa,QAAQ,IAAI;AACzB,QAAM,QAAQ,YAAY,QAAQ,IAAI;AACtC,QAAM,QAAQ,OAAO,SAAS,MAAM,SAAS,OAAO,KAC7C,MAAM,QAAQ,OAAO,KAAK,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,GAAG,OAAO,CAAC,MAAM,OAAO,MAAM,SAAS,IAClG;AACN,QAAM,uBAAuB,OAAO,wBAChC,OAAO,OAAO,yBAAyB,YACvC,MAAM,SAAS,QAAQ,IACrB,OAAO,YAAY,OAAO,QAAQ,OAAO,oBAAoB,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,OAAO,UAAU,SAAS,CAAC,IAChH;AACN,QAAM,aAAa,OAAO,cAAc,MAAM,SAAS,QAAQ,IACzD,OAAO,YAAY,OAAO,QAAQ,OAAO,UAAU,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,OAAO,UAAU,SAAS,CAAC,IACtG;AACN,QAAM,SAAQ,eAAU,MAAM,MAAhB,mBAAmB,OAAO,CAAC,MAAM,EAAE,SAAS,UAAU,EAAE,UAAU;AAChF,QAAM,SAAS;AAAA,IACX,OAAO,MAAM,OAAO,CAAC,MAAM,MAAM,MAAM;AAAA,IACvC;AAAA,IACA,YAAY,MAAM,SAAS,MAAM;AAAA,IACjC;AAAA,IACA,QAAO,+BAAO,UAAS,QAAQ;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,OAAO;AAAA,EACrB;AACA,MAAI,CAAC,OAAO,SAAS,CAAC,OAAO,MAAM,QAAQ;AACvC,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,GAAG,MAAM,YAAY,EAAE,yBAAyB,MAAM,GAAG,QAAQ,GAAG,OAAO,MAAM,IAAI,CAAC,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,IACrH;AAAA,EACJ;AACJ;AACA,SAAS,YAAY,QAAQ,MAAM;AAC/B,eAAa,QAAQ,IAAI;AACzB,MAAI,QAAQ,OAAO,UAAU,OAAO,CAAC,MAAM,IAAI,CAAC;AAChD,MAAI,OAAO,MAAM;AACb,YAAQ,MAAM,QAAQ,OAAO,IAAI,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI;AAAA,EACnE;AACA,MAAI,OAAO,OAAO;AACd,YAAQ,OAAO,MAAM,QAAQ,CAAC,MAAM,YAAY,GAAG,IAAI,CAAC;AAAA,EAC5D;AACA,MAAI,MAAM,SAAS,OAAO,KAAK,OAAO,aAAa;AAC/C,UAAM,IAAI,MAAM,UAAU,CAAC,MAAM,KAAK,OAAO;AAC7C,UAAM,CAAC,IAAI;AAAA,EACf,WACS,OAAO,UAAU,sBAAsB,SAAS,OAAO,MAAM,GAAG;AACrE,UAAM,QAAQ,OAAO,MAAM;AAG3B,QAAI,OAAO,UAAU,eAAe,OAAO,UAAU,SAAS;AAC1D,YAAM,IAAI,MAAM,UAAU,CAAC,MAAM,KAAK,SAAS;AAC/C,YAAM,OAAO,GAAG,CAAC;AAAA,IACrB;AAAA,EACJ;AACA,MAAI,OAAO,SAAS,OAAO,UAAU,QAAQ,OAAO,OAAO,UAAU,YAAY;AAC7E,UAAM,KAAK,OAAO,OAAO,KAAK;AAAA,EAClC;AACA,SAAO,MAAM,KAAK,IAAI,IAAI,KAAK,CAAC;AACpC;AACA,SAAS,UAAU,QAAQ;AACvB,MAAI,CAAC,OAAO,SAAS,CAAC,OAAO,MAAM;AAC/B,WAAO;AACX,SAAO,OAAO,MAAM,OAAO,CAAC,MAAM,OAAO,MAAM,SAAS;AAC5D;;;ACpEO,SAAS,cAAc,QAAQ,aAAa,OAAO,OAAO,CAAC,GAAG;AACjE,SAAO,eAAe,QAAQ,YAAY,IAAI;AAClD;AACA,SAAS,eAAe,QAAQ,YAAY,MAAM;AAPlD;AAQI,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,YAAY,wBAAwB,IAAI;AAAA,EACtD;AACA,QAAM,OAAO,WAAW,QAAQ,YAAY,IAAI;AAChD,MAAI,CAAC;AACD,WAAO;AAGX,MAAI,iBAAiB;AAErB,MAAI,aAAa,QAAQ;AAIrB,QAAI,KAAK,MAAM,SAAS,QAAQ,KAC5B,OAAO,WACP,OAAO,OAAO,WAAW,YACzB,CAAC,MAAM,QAAQ,OAAO,OAAO,GAAG;AAChC,uBAAiB,OAAO;AAAA,IAC5B,OACK;AACD,UAAI,KAAK,MAAM,SAAS,GAAG;AACvB,YAAI,KAAK,MAAM,SAAS,WAAW,MAC9B,KAAK,MAAM,SAAS,SAAS,KAAK,KAAK,MAAM,SAAS,QAAQ;AAC/D,gBAAM,IAAI,YAAY,0FAA0F,IAAI;AAAA,MAC5H;AACA,YAAM,CAAC,IAAI,IAAI,KAAK;AACpB,aAAO,mBAAmB,MAAM,OAAO,OAAO;AAAA,IAClD;AAAA,EACJ;AACA,MAAI;AACJ,QAAM,mBAAmB,MAAM;AAC3B,QAAI,CAAC,KAAK,SAAS,KAAK,MAAM,SAAS;AACnC,aAAO;AACX,QAAI,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,IAAI;AAC7B,aAAO;AACX,QAAI,CAAC,YAAY;AACb,mBAAa,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,MAAM;AACvC,eAAO,CAAC,WAAW,WAAW,EAAE,SAAS,CAAC,IAAI,WAAW;AAAA,MAC7D,CAAC,CAAC;AAAA,IACN;AACA,WAAO,WAAW,OAAO;AAAA,EAC7B;AACA,MAAI,SAAS;AAEb,MAAI,CAAC,kBAAkB,KAAK,OAAO;AAC/B,UAAM,gBAAgB,KAAK,MAAM,OAAO,CAAC,MAAM,OAAO,MAAM,aAAa,EAAE,YAAY,MAAS;AAChG,QAAI,cAAc,UAAU,GAAG;AAC3B,aAAO,eAAe,cAAc,CAAC,GAAG,YAAY,IAAI;AAAA,IAC5D,WACS,cAAc,SAAS,GAAG;AAC/B,YAAM,IAAI,YAAY,4FAA4F,IAAI;AAAA,IAC1H,OACK;AAED,UAAI,KAAK;AACL,eAAO;AACX,UAAI,KAAK;AACL,eAAO;AACX,UAAI,iBAAiB,GAAG;AACpB,cAAM,IAAI,YAAY,6FAA6F,IAAI;AAAA,MAC3H;AAEA,UAAI,KAAK,MAAM,UAAU,KAAK,MAAM,CAAC,KAAK,UAAU;AAChD,YAAI,WAAW;AACX,mBAAS,CAAC;AACd,iBACI,KAAK,MAAM,SAAS,IACd,MAAM,YAAY,EAAE,yBAAyB,KAAK,GAAG,GAAG,KAAK,MAAM,IAAI,CAAC,MAAM,eAAe,GAAG,YAAY,IAAI,CAAC,CAAC,IAClH,eAAe,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI;AAAA,MAC5D;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,CAAC,gBAAgB;AAEjB,QAAI,KAAK;AACL,aAAO;AACX,QAAI,KAAK;AACL,aAAO;AAAA,EACf;AAEA,MAAI,KAAK,YAAY;AACjB,eAAW,CAAC,KAAK,SAAS,KAAK,OAAO,QAAQ,KAAK,UAAU,GAAG;AAC5D,mBAAa,WAAW,CAAC,GAAG,MAAM,GAAG,CAAC;AACtC,YAAM,MAAM,kBAAkB,eAAe,GAAG,MAAM,SAChD,eAAe,GAAG,IAClB,eAAe,WAAW,GAAC,UAAK,aAAL,mBAAe,SAAS,OAAM,CAAC,GAAG,MAAM,GAAG,CAAC;AAE7E,UAAI,WAAW;AACX,iBAAS,CAAC;AACd,aAAO,GAAG,IAAI;AAAA,IAClB;AAAA,EACJ,WACS,gBAAgB;AACrB,WAAO;AAAA,EACX;AASA,MAAI,OAAO,MAAM;AACb,WAAO,OAAO,KAAK,CAAC;AAAA,EACxB;AAEA,MAAI,iBAAiB,GAAG;AACpB,UAAM,IAAI,YAAY,kDAAkD,IAAI;AAAA,EAChF,WACS,KAAK,MAAM,UAAU,GAAG;AAG7B,WAAO;AAAA,EACX;AACA,QAAM,CAAC,UAAU,IAAI,KAAK;AAC1B,SAAO,UAAU,aAAa,YAAY,OAAO,IAAI;AACzD;AACA,SAAS,mBAAmB,MAAM,OAAO;AACrC,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,MAAM,QAAQ,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,IACnD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAC9C,eAAO,IAAI,KAAK,KAAK;AACzB;AAAA,IACJ,KAAK;AACD,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAC9C,eAAO,OAAO,KAAK;AACvB;AAAA,IACJ,KAAK;AACD,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAC9C,eAAO,OAAO,KAAK;AACvB;AAAA,EACR;AACA,SAAO;AACX;AACO,SAAS,aAAa,MAAM,UAAU;AACzC,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,YAAY,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,IAC3D,KAAK;AAAA,IACL,KAAK;AACD,aAAO,YAAY,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,IAC3D,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,CAAC;AAAA,IACZ,KAAK;AACD,aAAO,CAAC;AAAA,IACZ,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAED,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO,OAAO,CAAC;AAAA,IACnB,KAAK;AACD,aAAO,oBAAI,IAAI;AAAA,IACnB,KAAK;AACD,aAAO,OAAO;AAAA,IAClB,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,YAAY,2EAA2E,IAAI;AAAA,EAC7G;AACJ;AAEO,SAAS,aAAa,QAAQ,OAAO,CAAC,GAAG;AAC5C,SAAO,cAAc,QAAQ,OAAO,IAAI;AAC5C;AACA,SAAS,cAAc,QAAQ,YAAY,MAAM;AA1LjD;AA2LI,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,YAAY,wBAAwB,IAAI;AAAA,EACtD;AACA,QAAM,OAAO,WAAW,QAAQ,YAAY,IAAI;AAChD,QAAM,SAAS;AAAA,IACX,SAAS,KAAK;AAAA,EAClB;AAKA,MAAI,KAAK,OAAO,SACZ,OAAO,KAAK,OAAO,SAAS,YAC5B,CAAC,MAAM,QAAQ,KAAK,OAAO,KAAK,GAAG;AACnC,WAAO,UAAU,cAAc,KAAK,OAAO,OAAO,KAAK,YAAY,IAAI;AAAA,EAC3E;AACA,MAAI,KAAK,YAAY;AACjB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,UAAU,GAAG;AACxD,mBAAa,OAAO,CAAC,GAAG,MAAM,GAAG,CAAC;AAClC,aAAO,GAAG,IAAI,cAAc,KAAK,WAAW,GAAG,GAAG,GAAC,UAAK,aAAL,mBAAe,SAAS,OAAM;AAAA,QAC7E,GAAG;AAAA,QACH;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,MAAI,KAAK,wBAAwB,KAAK,MAAM,SAAS,QAAQ,GAAG;AAC5D,UAAM,iBAAiB,WAAW,KAAK,sBAAsB,KAAK,YAAY,IAAI;AAClF,QAAI,eAAe,cAAc,eAAe,MAAM,SAAS,QAAQ,GAAG;AACtE,iBAAW,CAAC,GAAG,KAAK,OAAO,QAAQ,eAAe,UAAU,GAAG;AAC3D,eAAO,GAAG,IAAI,cAAc,eAAe,WAAW,GAAG,GAAG,GAAC,oBAAe,aAAf,mBAAyB,SAAS,OAAM,CAAC,GAAG,MAAM,GAAG,CAAC;AAAA,MACvH;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,KAAK,cAAc,CAAC,OAAO,QAAQ,SAAS,MAAM,GAAG;AACrD,WAAO,QAAQ,KAAK,MAAM;AAAA,EAC9B;AACA,MAAI,KAAK,cAAc,CAAC,OAAO,QAAQ,SAAS,WAAW,GAAG;AAC1D,WAAO,QAAQ,KAAK,WAAW;AAAA,EACnC;AACA,SAAO;AACX;;;AC9NO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;AAAA,EACtC,YAAYC,UAAS;AACjB,UAAMA,QAAO;AACb,WAAO,eAAe,MAAM,gBAAe,SAAS;AAAA,EACxD;AACJ;AACO,IAAM,cAAN,MAAM,qBAAoB,eAAe;AAAA,EAE5C,YAAYA,UAAS,MAAM;AACvB,WAAO,QAAQ,KAAK,SAAS,IAAI,MAAM,QAAQ,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,OAAO,MAAMA,QAAO;AAFpG;AAGI,SAAK,OAAO,MAAM,QAAQ,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;AACnD,WAAO,eAAe,MAAM,aAAY,SAAS;AAAA,EACrD;AACJ;AACO,SAAS,UAAU,QAAQ,OAAO;AApBzC;AAsBI,QAAM,SAAS,CAAC;AAChB,WAAS,kBAAkB,OAAO;AAC9B,QAAI,EAAE,aAAa;AACf,aAAO,UAAU,CAAC;AACtB,QAAI,CAAC,MAAM,QAAQ,OAAO,OAAO,GAAG;AAChC,UAAI,OAAO,OAAO,YAAY;AAC1B,eAAO,UAAU,CAAC,OAAO,OAAO;AAAA;AAEhC,cAAM,IAAI,eAAe,oCAAoC;AAAA,IACrE;AACA,WAAO,QAAQ,KAAK,MAAM,OAAO;AAAA,EACrC;AACA,aAAW,SAAS,QAAQ;AAExB,QAAI,CAAC,MAAM,QAAS,MAAM,KAAK,UAAU,KAAK,CAAC,MAAM,KAAK,CAAC,GAAI;AAC3D,wBAAkB,KAAK;AACvB;AAAA,IACJ;AAGA,UAAM,qBAAqB,OAAO,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;AAChF,UAAM,cAAc,CAAC,wBACjB,gBAAW,OAAO,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,MAAhE,mBAAmE;AAEvE,UAAM,OAAO,aAAa,QAAQ,MAAM,MAAM,CAAC,EAAE,OAAO,QAAAC,SAAQ,KAAAC,KAAI,MAAM;AACtE,UAAI,UAAU;AACV,QAAAD,QAAOC,IAAG,IAAI,CAAC;AACnB,aAAOD,QAAOC,IAAG;AAAA,IACrB,CAAC;AACD,QAAI,CAAC,MAAM;AACP,wBAAkB,KAAK;AACvB;AAAA,IACJ;AACA,UAAM,EAAE,QAAQ,IAAI,IAAI;AACxB,QAAI,aAAa;AACb,UAAI,EAAE,OAAO;AACT,eAAO,GAAG,IAAI,CAAC;AACnB,UAAI,EAAE,aAAa,OAAO,GAAG;AACzB,eAAO,GAAG,EAAE,UAAU,CAAC,MAAM,OAAO;AAAA;AAEpC,eAAO,GAAG,EAAE,QAAQ,KAAK,MAAM,OAAO;AAAA,IAC9C,OACK;AACD,UAAI,EAAE,OAAO;AACT,eAAO,GAAG,IAAI,CAAC,MAAM,OAAO;AAAA;AAE5B,eAAO,GAAG,EAAE,KAAK,MAAM,OAAO;AAAA,IACtC;AAAA,EACJ;AACA,SAAO;AACX;AAMO,SAAS,aAAa,KAAK,UAAU,OAAO;AAC/C,MAAI;AACA,WAAO;AAGX,gBAAc,UAAU,CAAC,WAAW;AAChC,QAAI,CAAC,MAAM,QAAQ,OAAO,KAAK;AAC3B;AACJ,WAAO,IAAI,MAAS;AAAA,EACxB,CAAC;AACD,gBAAc,KAAK,CAAC,UAAU;AAC1B,QAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,MAAM,UAAU;AAC/C;AACJ,aAAS,UAAU,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK;AAAA,EAChD,CAAC;AACD,SAAO;AACX;AACO,SAAS,cAAc,QAAQ;AAClC,SAAO,eAAe,QAAQ,CAAC,CAAC;AACpC;AACA,SAAS,eAAe,QAAQ,MAAM;AAClC,QAAM,UAAU,OAAO,QAAQ,MAAM;AACrC,SAAO,QACF,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,UAAU,MAAS,EACzC,QAAQ,CAAC,CAAC,KAAK,QAAQ,MAAM;AAC9B,QAAI,MAAM,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG;AAChD,YAAM,WAAW,KAAK,OAAO,CAAC,GAAG,CAAC;AAClC,aAAO,EAAE,MAAM,UAAU,QAAQ,GAAG,SAAS;AAAA,IACjD,OACK;AACD,aAAO,eAAe,OAAO,GAAG,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;AAAA,IACzD;AAAA,EACJ,CAAC;AACL;AAIO,SAAS,cAAc,YAAYC,WAAU;AAChD,MAAI,CAAC;AACD,WAAOC,OAAMD,SAAQ;AACzB,SAAO,MAAM,YAAY,EAAE,aAAa,MAAM,GAAGA,WAAU,UAAU;AACzE;AAKO,SAAS,uBAAuB,MAAM,UAAU,SAAS,QAAQ,cAAc;AAClF,QAAM,cAAc,QAAQ,wBAAwB,OAAO,QAAQ,wBAAwB,WACrF,EAAE,SAAS,WAAW,QAAQ,sBAAsB,OAAO,CAAC,CAAC,EAAE,MAAM,IACrE;AAGN,QAAM,QAAQ,aAAa,OAAO;AAClC,WAAS,mBAAmB,WAAW,UAAU,MAAM;AACnD,UAAM,QAAQ,KAAK;AACnB,QAAI,CAAC,MAAM,UAAU,MAAM,MAAM,CAAC,MAAM,KAAK,eAAe,KAAK,UAAU,KAAK,KAAK,GAAG;AAEpF,aAAO;AAAA,IACX,WACS,MAAM,UAAU,KAAK,MAAM,CAAC,KAAK,WAAW,CAAC,KAAK,SAAS;AAMhE,aAAO;AAAA,IACX;AACA,UAAM,YAAY,CAAC,aAAa,QAAQ,MAAM;AAC9C,eAAW,cAAc,OAAO;AAC5B,YAAM,mBAAmB,aAAa,YAAY,MAAS;AAC3D,YAAM,WAAW,OAAO,cAAc,OAAO,oBACxC,UAAU,SAAS,UAAU,KAAK,qBAAqB;AAC5D,YAAM,gBAAgB,YAAa,cAAc,UAAW,qBAAqB;AACjF,UAAI,YAAY,eAAe;AAC3B,eAAO;AAAA,MACX,WACS,KAAK,SAAS;AAEnB,eAAO,mBAAmB,WAAW,UAAU,KAAK,OAAO;AAAA,MAC/D;AAAA,IACJ;AAEA,QAAI,aAAa,UAAa,MAAM,SAAS,MAAM,GAAG;AAClD,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAGA,WAAS,gBAAgB;AACrB,kBAAc,UAAU,2BAA2B;AACnD,8BAA0B;AAC1B,WAAO;AAAA,EACX;AACA,WAAS,cAAc,aAAa,UAAU;AAC1C,aAAS,MAAM,CAAC,WAAW,GAAG,QAAQ;AAAA,EAC1C;AAGA,WAAS,4BAA4B;AAjLzC;AAkLQ,eAAW,SAAS,QAAQ;AACxB,UAAI,CAAC,MAAM;AACP;AACJ,kCAA4B;AAAA,QACxB,MAAM,MAAM;AAAA,QACZ,QAAO,gBAAW,UAAU,MAAM,IAAI,MAA/B,mBAAkC;AAAA,MAC7C,GAAG,IAAI;AAAA,IACX;AAAA,EACJ;AAGA,WAAS,4BAA4B,aAAa,mBAAmB,OAAO;AACxE,UAAM,cAAc,YAAY;AAChC,QAAI,CAAC,eAAe,CAAC,YAAY,CAAC;AAC9B;AACJ,QAAI,OAAO,YAAY,CAAC,MAAM,aAAY,6CAAc,SAAS,YAAY,CAAC;AAC1E;AACJ,UAAM,WAAW,WAAW,MAAM,WAAW;AAE7C,QAAK,CAAC,YAAY,YAAY,UAAU,UACnC,YAAY,SAAS,UAAU,QAAY;AAC5C,oBAAc,aAAa,YAAY,KAAK;AAAA,IAChD,WACS,UAAU;AACf,YAAM,WAAW,YAAY;AAC7B,YAAM,YAAY,SAAS;AAE3B,UAAI,aAAa,UACb,OAAO,cAAc,OAAO,YAC3B,cAAc,UAAW,aAAa,OAAO;AAC9C;AAAA,MACJ;AACA,YAAM,WAAW,YAAY,OAAO,CAAC,MAAM,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC;AAC/D,YAAM,YAAY,aAAa,OAAO,UAAU,CAAC,SAAS;AAEtD,eAAO,KAAK,SAAS,aAAa,KAAK,QAAQ,KAAK,MAAM,UAAU,KAAK;AAAA,MAC7E,CAAC;AACD,UAAI,CAAC,WAAW;AAEZ,YAAI;AACA;AACJ,cAAM,IAAI,YAAY,+BAA+B,WAAW;AAAA,MACpE;AACA,YAAM,YAAY,UAAU,SAAS;AACrC,UAAI,WAAW;AACX,sBAAc,aAAa,mBAAmB,WAAW,UAAU,SAAS,CAAC;AAAA,MACjF;AAAA,IACJ;AAAA,EACJ;AAEA;AACI,WAAO,cAAc;AAAA,EACzB;AACJ;;;ACrOO,SAASE,OAAM,MAAM;AACxB,SAAO,QAAQ,OAAO,SAAS,WAAW,MAAU,IAAI,IAAI;AAChE;AACO,SAAS,aAAa,QAAQ,MAAM;AACvC,MAAI,OAAO,WAAW,WAAW;AAC7B,UAAM,IAAI,YAAY,iDAAiD,IAAI;AAAA,EAC/E;AACJ;;;ARNA,SAAS,WAAAC,gBAAe;AAIxB,SAAS,gBAAgB,MAAM,qBAAqB;;;ASPpD,SAAS,eAAe;AACjB,SAAS,YAAY,SAAS;AACjC,MAAI,CAAC,QAAQ,gBAAgB,CAAC;AAC1B;AACJ,MAAI,CAAC,gBAAgB,OAAO;AACxB;AACJ,WAAS,SAAS,2BAA2B,QAAQ,aAAa,cAAc,GAAG;AACvF;AACO,SAAS,gBAAgB,SAAS;AACrC,MAAI,CAAC,QAAQ,gBAAgB,CAAC;AAC1B,WAAO;AACX,SAAO,QAAQ;AACnB;;;ATFA,SAAS,aAAa,aAAa,WAAW,kBAAkB;;;AURhE,IAAM,gCAAgC;AACtC,eAAsB,qBAAqB,YAAY,QAAQ;AAE3D,MAAI,uBAAuB,YAAY;AACnC,eAAW,kBAAkB,EAAE;AAAA,EACnC;AACA,MAAI,iCAAiC,WAAW;AAC5C;AACJ,oBAAkB,YAAY,MAAM;AACxC;AACO,SAAS,sBAAsB,aAAa,QAAQ;AACvD,aAAW,MAAM,YAAY,iBAAiB,8BAA8B,GAAG;AAC3E,QAAK,aAAa,MAAM,iCAAiC,GAAG,WAAY,CAAC,GAAG,MAAM;AAC9E;AAAA,IACJ;AACA,UAAM,OAAO,aAAa,QAAQ,UAAU,GAAG,IAAI,CAAC;AACpD,UAAM,QAAQ,QAAQ,OAAO,KAAK,UAAU,YAAY,aAAa,KAAK,QACpE,KAAK,MAAM,UACX,6BAAM;AACZ,sBAAkB,IAAI,KAAK;AAC3B,QAAI;AACA;AAAA,EACR;AACJ;AACA,SAAS,kBAAkB,IAAI,QAAQ;AACnC,MAAI,EAAE,uBAAuB;AACzB;AACJ,QAAMC,WAAU,UAAU,OAAO,SAAS,OAAO,KAAK,IAAI,IAAI;AAC9D,KAAG,kBAAkBA,QAAO;AAC5B,MAAIA;AACA,OAAG,eAAe;AAC1B;;;AChCO,IAAM,sBAAsB,CAAC,IAAI,YAAY,MAAM;AACtD,QAAM,OAAO,GAAG,sBAAsB;AACtC,SAAQ,KAAK,OAAO,aAChB,KAAK,QAAQ,KACb,KAAK,WACA,OAAO,eAAe,SAAS,gBAAgB,iBACpD,KAAK,UACA,OAAO,cAAc,SAAS,gBAAgB;AAC3D;AAEO,IAAM,oBAAoB,CAAC,IAAI,SAAS,OAAO,WAAW,aAAa;AAC1E,QAAM,cAAc,GAAG,sBAAsB;AAC7C,QAAM,qBAAqB,YAAY,MAAM,OAAO;AACpD,QAAM,MAAM,qBAAqB,OAAO,eAAe,IAAI;AAC3D,SAAO,SAAS,EAAE,MAAM,GAAG,KAAK,SAAS,CAAC;AAC9C;AACA,IAAM,sBAAsB,CAAC,YAAY,SAAS,SAAS,MAAM;AAI1D,SAAS,UAAU,IAAI;AAC1B,QAAM,YAAY,CAAC,CAAC,OACf,cAAc,qBACV,cAAc,oBAAoB,oBAAoB,SAAS,GAAG,IAAI;AAC/E,QAAM,WAAW,CAAC,CAAC,MAAM,cAAc,qBAAqB,GAAG;AAC/D,QAAM,OAAO,CAAC,CAAC,MAAM,cAAc,oBAAoB,GAAG,QAAQ;AAClE,SAAO,EAAE,WAAW,UAAU,KAAK;AACvC;;;AC1BA,IAAI;AAAA,CACH,SAAUC,cAAa;AACpB,EAAAA,aAAYA,aAAY,MAAM,IAAI,CAAC,IAAI;AACvC,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,EAAAA,aAAYA,aAAY,SAAS,IAAI,CAAC,IAAI;AAC1C,EAAAA,aAAYA,aAAY,SAAS,IAAI,CAAC,IAAI;AAC9C,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAM,eAAe,oBAAI,IAAI;AAKtB,SAAS,KAAK,aAAa,QAAQ,SAAS;AAC/C,MAAI,QAAQ,YAAY;AACxB,MAAI,gBAAgB;AAEpB,QAAM,SAAS;AAEf,WAAS,eAAe;AACpB,iBAAa;AACb,oBAAgB,SAAS,YAAY,UAAU,YAAY,aAAa,YAAY,OAAO;AAC3F,qBAAiB,OAAO,WAAW,MAAM;AACrC,UAAI,kBAAkB,SAAS,YAAY;AACvC,wBAAgB,YAAY,OAAO;AAAA,IAC3C,GAAG,QAAQ,OAAO;AAClB,qBAAiB,OAAO,WAAW,MAAM;AACrC,UAAI,kBAAkB,SAAS,YAAY;AACvC,wBAAgB,YAAY,OAAO;AAAA,IAC3C,GAAG,QAAQ,SAAS;AACpB,WAAO,IAAI,YAAY;AAAA,EAC3B;AAIA,WAAS,eAAe;AACpB,iBAAa,cAAc;AAC3B,iBAAa,cAAc;AAC3B,qBAAiB,iBAAiB;AAClC,WAAO,OAAO,YAAY;AAC1B,oBAAgB,YAAY,IAAI;AAAA,EACpC;AACA,WAAS,kBAAkB;AACvB,WAAO,QAAQ,CAAC,MAAM,EAAE,CAAC;AACzB,WAAO,MAAM;AAAA,EACjB;AACA,WAAS,gBAAgB,GAAG;AACxB,YAAQ;AACR,WAAO,WAAW,IAAI,SAAS,YAAY,UAAU;AACrD,WAAO,QAAQ,IAAI,SAAS,YAAY,OAAO;AAC/C,WAAO,QAAQ,IAAI,SAAS,YAAY,OAAO;AAAA,EACnD;AAGA,QAAM,kBAAkB;AACxB,WAAS,4BAA4B,GAAG;AACpC,UAAM,SAAS,EAAE;AACjB,QAAI,QAAQ;AACR,aAAO,OAAO;AAAA,EACtB;AACA,WAAS,wCAAwC;AAC7C,QAAI,CAAC,QAAQ;AACT;AACJ,oBAAgB,iBAAiB,OAAO,EAAE,QAAQ,CAAC,OAAO;AACtD,SAAG,iBAAiB,WAAW,2BAA2B;AAAA,IAC9D,CAAC;AAAA,EACL;AACA,WAAS,2CAA2C;AAChD,QAAI,CAAC,QAAQ;AACT;AACJ,oBAAgB,iBAAiB,OAAO,EAAE,QAAQ,CAAC,OAAO,GAAG,oBAAoB,WAAW,2BAA2B,CAAC;AAAA,EAC5H;AAGA,QAAMC,QAAO;AAEb;AACI,0CAAsC;AACtC,UAAM,YAAY,CAAC,SAAS;AACxB,UAAI,CAAC,KAAK;AACN,qBAAa;AAAA;AAEb,wBAAgB;AACpB,UAAI,CAAC,KAAK;AACN,mBAAW,MAAM,mBAAmBA,OAAM,OAAO,GAAG,CAAC;AAAA,IAC7D;AACA,cAAU,MAAM;AACZ,+CAAyC;AACzC,gBAAU,EAAE,WAAW,KAAK,CAAC;AAAA,IACjC,CAAC;AACD,WAAO;AAAA,MACH,aAAa;AACT,qBAAa;AAAA,MACjB;AAAA,MACA;AAAA,MACA,qBAAqB;AACjB,mBAAW,MAAM,mBAAmBA,OAAM,OAAO,GAAG,CAAC;AAAA,MACzD;AAAA,MACA,cAAc,MAAM,UAAU,YAAY,cAAc,UAAU,YAAY;AAAA,IAClF;AAAA,EACJ;AACJ;AACO,IAAM,qBAAqB,OAAOA,OAAM,YAAY;AACvD,MAAI,QAAQ,iBAAiB;AACzB;AACJ,QAAM,WAAW,QAAQ;AACzB,MAAI,CAAC;AACD;AAEJ,QAAM,KAAK;AAEX,MAAI;AACJ,OAAKA,MAAK,cAAc,QAAQ;AAChC,MAAI,CAAC;AACD;AAEJ,OAAK,GAAG,cAAc,QAAQ,KAAK;AACnC,QAAM,MAAM,QAAQ,eACd,SAAS,cAAc,QAAQ,YAAY,IAC3C;AACN,MAAI,OAAO,QAAQ,iBAAiB,UAAU;AAC1C,OAAG,eAAe,QAAQ,aAAa;AAAA,EAC3C,WACS,CAAC,oBAAoB,KAAI,2BAAK,iBAAgB,CAAC,GAAG;AACvD,sBAAkB,IAAI,QAAW,QAAQ,aAAa;AAAA,EAC1D;AACA,WAAS,qBAAqB,WAAW;AACrC,QAAI,OAAO,QAAQ,qBAAqB;AACpC,aAAO,QAAQ;AAAA;AAEf,aAAO,CAAC,4BAA4B,KAAK,SAAS;AAAA,EAC1D;AAGA,MAAI,CAAC,qBAAqB,UAAU,SAAS;AACzC;AACJ,MAAI;AACJ,YAAU;AACV,MAAI,CAAC,CAAC,SAAS,UAAU,UAAU,UAAU,EAAE,SAAS,QAAQ,OAAO,GAAG;AACtE,cAAU,QAAQ,cAAc,oEAAoE;AAAA,EACxG;AACA,MAAI,SAAS;AACT,QAAI;AACA,cAAQ,MAAM,EAAE,eAAe,KAAK,CAAC;AACrC,UAAI,QAAQ,mBAAmB,QAAQ,WAAW,SAAS;AACvD,gBAAQ,OAAO;AAAA,MACnB;AAAA,IACJ,SACO,KAAK;AAAA,IAEZ;AAAA,EACJ;AACJ;;;ACpJA,SAAS,WAAAC,gBAAe;AACxB,IAAMC,kBAAiB;AAAA,EACnB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,MAAM;AACV;AAEO,SAAS,aAAa,MAAM,MAAM,SAAS;AAC9C,SAAO,aAAa,MAAM,MAAM,WAAW;AAAA,IACvC,GAAGA;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL;AACO,SAAS,SAAS,MAAM,MAAM,SAAS;AAC1C,SAAO,aAAa,MAAM,MAAM,OAAO;AAAA,IACnC,GAAGA;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL;AACO,SAAS,YAAY,MAAM,MAAM,SAAS;AAC7C,SAAO,aAAa,MAAM,MAAM,UAAU;AAAA,IACtC,GAAGA;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL;AACO,SAAS,UAAU,MAAM,MAAM,SAAS;AAC3C,SAAO,aAAa,MAAM,MAAM,QAAQ;AAAA,IACpC,GAAGA;AAAA,IACH,aAAY,mCAAS,WAAU;AAAA,IAC/B,OAAO,mCAAS;AAAA,IAChB,OAAM,mCAAS,SAAQ;AAAA,EAC3B,CAAC;AACL;AACO,SAAS,YAAY,MAAM,MAAM,SAAS;AAC7C,SAAO,aAAa,MAAM,MAAM,UAAU;AAAA,IACtC,GAAGA;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL;AACO,SAAS,eAAe,MAAM,MAAM,SAAS;AAChD,QAAM,YAAY,UAAU,MAAM,MAAM,OAAO;AAC/C,QAAM,YAAY,eAAe,MAAM,MAAM,OAAO;AACpD,SAAO,EAAE,GAAG,WAAW,OAAO,UAAU;AAC5C;AACO,SAAS,UAAU,MAAM,MAAM,SAAS;AAC3C,QAAM,WAAW,WAAW,MAAM,MAAM,OAAO;AAC/C,QAAMC,aAAY,SAASF,WAAU,IAAI,aAAa,EAAE,QAAQ,CAAC,CAAC;AAClE,MAAI,cAAc;AAClB,MAAI;AACJ,WAAS,UAAU,CAAC,SAAS;AACzB,QAAI,CAACA;AACD;AACJ,QAAI,CAAC,aAAa;AACd,sBAAe,mCAAS,SAAS,QAAQ,UAAU,cAAc,SAAY,OAAQ;AACrF,oBAAc;AAAA,IAClB;AACA,UAAM,KAAK,IAAI,aAAa;AAC5B,QAAI,gBAAgB;AAChB,SAAG,MAAM,IAAI,IAAI;AACrB,IAAAE,WAAU,IAAI,GAAG,KAAK;AAAA,EAC1B,CAAC;AACD,QAAM,YAAY;AAAA,IACd,UAAU,KAAK;AACX,aAAOA,WAAU,UAAU,GAAG;AAAA,IAClC;AAAA,IACA,IAAI,MAAM;AACN,UAAI,CAACF;AACD;AACJ,UAAI,CAAC,MAAM;AACP,cAAM,KAAK,IAAI,aAAa;AAC5B,QAAAE,WAAU,IAAI,GAAG,KAAK;AACtB,iBAAS,IAAI,IAAI;AAAA,MACrB,WACS,gBAAgB,MAAM;AAC3B,cAAM,KAAK,IAAI,aAAa;AAC5B,WAAG,MAAM,IAAI,IAAI;AACjB,QAAAA,WAAU,IAAI,GAAG,KAAK;AACtB,iBAAS,IAAI,IAAI;AAAA,MACrB,WACS,gBAAgB,UAAU;AAC/B,QAAAA,WAAU,IAAI,IAAI;AAClB,YAAI,KAAK,SAAS;AACd,mBAAS,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA;AAEzB,mBAAS,IAAI,YAAY;AAAA,MACjC;AAAA,IACJ;AAAA,IACA,SAAS;AACL,YAAM,IAAI,eAAe,6CAA6C;AAAA,IAC1E;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,gBAAgB,MAAM,MAAM,SAAS;AACjD,QAAM,aAAa,WAAW,MAAM,MAAM,OAAO;AACjD,QAAM,aAAa,WAAW,MAAM,MAAM,OAAO;AACjD,SAAO,EAAE,GAAG,YAAY,QAAQ,WAAW;AAC/C;AACO,SAAS,WAAW,MAAM,MAAM,SAAS;AAC5C,QAAM,YAAY,WAAW,MAAM,MAAM,OAAO;AAChD,QAAMC,cAAa,SAASH,WAAU,IAAI,aAAa,EAAE,QAAQ,CAAC,CAAC;AACnE,YAAU,UAAU,CAAC,UAAU;AAC3B,QAAI,CAACA;AACD;AACJ,UAAM,KAAK,IAAI,aAAa;AAC5B,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,UAAI,MAAM,UAAU,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG;AACxC,kBAAU,IAAI,CAAC,CAAC;AAChB;AAAA,MACJ;AACA,YAAM,OAAO,CAAC,MAAM,aAAa,IAAI,EAAE,QAAQ,CAAC,SAAS,GAAG,MAAM,IAAI,IAAI,CAAC;AAAA,IAC/E;AACA,IAAAG,YAAW,IAAI,GAAG,KAAK;AAAA,EAC3B,CAAC;AACD,QAAM,aAAa;AAAA,IACf,UAAU,KAAK;AACX,aAAOA,YAAW,UAAU,GAAG;AAAA,IACnC;AAAA,IACA,IAAI,OAAO;AACP,UAAI,CAACH;AACD;AACJ,UAAI,EAAE,iBAAiB,WAAW;AAC9B,cAAM,KAAK,IAAI,aAAa;AAC5B,YAAI,MAAM,QAAQ,KAAK;AACnB,gBAAM,QAAQ,CAAC,SAAS;AACpB,gBAAI,gBAAgB;AAChB,iBAAG,MAAM,IAAI,IAAI;AAAA,UACzB,CAAC;AACL,QAAAG,YAAW,IAAI,GAAG,KAAK;AACvB,kBAAU,IAAI,KAAK;AAAA,MACvB,OACK;AACD,cAAM,SAAS,CAAC;AAChB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,gBAAM,OAAO,MAAM,KAAK,CAAC;AACzB,cAAI;AACA,mBAAO,KAAK,IAAI;AAAA,QACxB;AACA,QAAAA,YAAW,IAAI,KAAK;AACpB,kBAAU,IAAI,MAAM;AAAA,MACxB;AAAA,IACJ;AAAA,IACA,OAAO,SAAS;AACZ,iBAAW,IAAI,QAAQ,IAAI,SAAS,CAAC,CAAC;AAAA,IAC1C;AAAA,EACJ;AACA,SAAO;AACX;AAQA,SAAS,aAAa,MAAM,MAAM,MAAM,SAAS;AAC7C,WAAS,QAAQ,OAAO;AACpB,QAAI,CAAC,SAAS,QAAQ,UAAU,QAAW;AACvC,aAAO,QAAQ,UAAU,SAAS,OAAO,QAAQ,UAAU,SAAS,IAAI;AAAA,IAC5E;AACA,QAAI,OAAO,UAAU,UAAU;AAC3B,cAAQ,MAAM,SAAS;AAAA,IAC3B;AACA,QAAI,OAAO,UAAU,UAAU;AAE3B,cAAQ;AAAA,IACZ;AACA,UAAM,cAAc;AACpB,QAAI,QAAQ;AACR,aAAO;AAAA,aACF,QAAQ;AACb,aAAO,CAAC,CAAC;AAAA,aACJ,QAAQ,QAAQ;AACrB,UAAI,YAAY,QAAQ,GAAG,MAAM,IAAI;AACjC,cAAM,MAAM,QAAQ,WAAW,QAAQ,KAAK,KAAK;AACjD,cAAM,OAAO,MAAM,QAAQ,oBAAI,KAAK,CAAC,IAAI,UAAU,oBAAI,KAAK,CAAC;AAC7D,eAAO,oBAAI,KAAK,OAAO,MAAM,eAAe,MAAM,MAAM,GAAG;AAAA,MAC/D;AAEI,eAAO,IAAI,KAAK,WAAW;AAAA,IACnC;AACA,UAAM,kBAAkB,QAAQ,YAC1B,YAAY,QAAQ,QAAQ,WAAW,GAAG,IAC1C;AACN,QAAI;AACJ,QAAI,oBAAoB,MAAM,QAAQ,SAAS;AAC3C,YAAM;AAAA,aACD,QAAQ;AACb,YAAM,WAAW,eAAe;AAAA;AAEhC,YAAM,SAAS,iBAAiB,EAAE;AACtC,WAAO;AAAA,EACX;AACA,QAAM,UAAU,YAAY,MAAM,OAAO;AACzC,QAAM,YAAY,UACZ,gBAAgB,MAAM,MAAM,EAAE,OAAO,QAAQ,MAAM,CAAC,IACpD,WAAW,MAAM,IAAI;AAC3B,MAAI,eAAe;AACnB,MAAI,cAAc;AAClB,QAAM,QAAQ,QAAQ,WAAW,CAAC,UAAU;AACxC,QAAI,CAAC,aAAa;AACd,oBAAc;AACd,UAAI,QAAQ,wBAAwB,CAAC;AACjC,eAAO;AAAA,IACf;AAEA,QAAI,iBAAiB,MAAM;AACvB,YAAM,UAAU;AAChB,qBAAe;AACf,aAAO;AAAA,IACX;AACA,QAAI,UAAU,UAAa,UAAU;AACjC,aAAO;AACX,QAAI,QAAQ,UAAU;AAClB,aAAO;AAAA,IACX,WACS,QAAQ,SAAS,QAAQ,UAAU;AACxC,UAAI,UAAU,IAAI;AAGd,kBAAU,IAAI,GAAG,UAAU,EAAE,OAAO,MAAM,IAAI,MAAS;AAAA,MAC3D;AACA,UAAI,OAAO,UAAU,YAAY,MAAM,KAAK;AACxC,eAAO;AACX,aAAO,OAAO,KAAK;AAAA,IACvB,WACS,QAAQ,QAAQ;AACrB,YAAM,OAAO,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW,IAAI,KAAK,KAAK,IAAI;AACxF,UAAI,MAAM,IAAI;AACV,eAAO;AACX,cAAQ,QAAQ,YAAY;AAAA,QACxB,KAAK;AACD,iBAAO,KAAK,YAAY;AAAA,QAC5B,KAAK;AACD,iBAAO,KAAK,YAAY,EAAE,MAAM,GAAG,EAAE;AAAA,QACzC,KAAK;AACD,iBAAO,KAAK,YAAY,EAAE,MAAM,GAAG,QAAQ,OAAO,KAAK,KAAK,EAAE;AAAA,QAClE,KAAK;AACD,iBAAO,KAAK,YAAY,EAAE,MAAM,IAAI,QAAQ,OAAO,KAAK,KAAK,EAAE;AAAA,QACnE,KAAK;AACD,iBAAO,QAAQ,IAAI;AAAA,QACvB,KAAK;AACD,iBAAO,QAAQ,IAAI,IAAI,MAAM,QAAQ,MAAM,QAAQ,IAAI;AAAA,QAC3D,KAAK;AACD,iBAAO,QAAQ,MAAM,QAAQ,IAAI;AAAA,QACrC,KAAK;AACD,iBAAO,UAAU,IAAI;AAAA,QACzB,KAAK;AACD,iBAAO,UAAU,IAAI,IAAI,MAAM,UAAU,MAAM,QAAQ,IAAI;AAAA,QAC/D,KAAK;AACD,iBAAO,UAAU,MAAM,QAAQ,IAAI;AAAA,MAC3C;AAAA,IACJ,OACK;AAED,aAAO,QAAQ,QAAQ,kBAAkB;AAAA,IAC7C;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH,WAAW,MAAM;AAAA,IACjB,IAAI,KAAK;AACL,qBAAe;AACf,YAAM,WAAW,QAAQ,YAAY;AACrC,gBAAU,IAAI,QAAQ;AAAA,IAC1B;AAAA,IACA,OAAO,SAAS;AACZ,gBAAU,OAAO,CAAC,MAAM;AACpB,uBAAe,QAAQ,OAAO,CAAC,CAAC;AAChC,cAAM,WAAW,QAAQ,YAAY;AACrC,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AACO,SAAS,WAAWC,YAAW,MAAM,SAAS;AACjD,QAAM,aAAa,WAAWA,WAAU,QAAQ,GAAG,IAAI,EAAE;AACzD,QAAM,kBAAkB,QAAQ,YAAY,CAAC,YAAY;AACrD,UAAM,SAAS,CAAC;AAChB,eAAW,OAAO,SAAS;AACvB,UAAI,OAAO;AACP;AACJ,aAAO,GAAG,IAAI,QAAQ,GAAG;AAAA,IAC7B;AACA,WAAO;AAAA,EACX,CAAC;AACD,WAAS,kBAAkB,QAAQ,OAAO;AACtC,eAAW,OAAO,QAAQ;AACtB,UAAI,OAAO;AACP;AACJ,aAAO,GAAG,IAAI;AAAA,IAClB;AACA,QAAI,UAAU,QAAW;AACrB,iBAAW,OAAO,OAAO;AACrB,eAAO,GAAG,IAAI,MAAM,GAAG;AAAA,MAC3B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,cAAc;AAAA,IAChB,WAAW,gBAAgB;AAAA,IAC3B,OAAO,KAAK;AACR,iBAAW,OAAO,CAAC;AAAA;AAAA,QAEnB,kBAAkB,SAAS,IAAI,OAAO,CAAC;AAAA,OAAC;AAAA,IAC5C;AAAA,IACA,IAAI,OAAO;AAEP,iBAAW,OAAO,CAAC,YAAY,kBAAkB,SAAS,KAAK,CAAC;AAAA,IACpE;AAAA,EACJ;AACA,QAAM,SAAS,gBAAgBA,YAAW,MAAM,OAAO;AAGvD,MAAI,aAAa,MAAM,QAAQ,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,EAAE,SAAS;AACnE,SAAO,UAAU,CAAC,YAAY;AAC1B,UAAM,gBAAgB,MAAM,QAAQ,OAAO,IAAI,QAAQ,SAAS;AAChE,QAAI,gBAAgB,YAAY;AAC5B,MAAAA,WAAU,OAAO,OAAO,CAAC,YAAY;AACjC,cAAM,OAAO,WAAW,SAAS,UAAU,IAAI,CAAC;AAChD,YAAI,CAAC;AACD,iBAAO;AACX,mBAAW,OAAO,KAAK,OAAO;AAC1B,cAAI,OAAO,GAAG,IAAI;AACd;AACJ,iBAAO,KAAK,MAAM,GAAG;AAAA,QACzB;AACA,eAAO;AAAA,MACX,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,IACtB;AACA,iBAAa;AAAA,EACjB,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,QAAQ,WAAWA,WAAU,QAAQ,GAAG,IAAI,UAAU;AAAA,IACtD,aAAa;AAAA,EACjB;AACJ;AACO,SAAS,eAAeA,YAAW,MAAM,SAAS;AACrD,QAAM,QAAQ,UAAU,IAAI;AAE5B,QAAM,kBAAkB,MAAM,OAAO,CAAC,MAAM,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG;AAC1E,QAAM,eAAe,QAAQA,WAAU,SAAS,CAAC,aAAa;AAC1D,QAAI,CAAC;AACD,aAAO;AACX,UAAM,cAAc,aAAa,UAAU,KAAK;AAChD,WAAO,cAAc,YAAY,QAAQ;AAAA,EAC7C,CAAC;AACD,QAAM,UAAU;AAAA,IACZ,WAAW,aAAa;AAAA,IACxB,OAAO,KAAK;AACR,MAAAA,WAAU,QAAQ,OAAO,CAAC,aAAa;AACnC,YAAI,CAAC;AACD,qBAAW,CAAC;AAChB,cAAM,SAAS,aAAa,UAAU,OAAO,CAACC,UAAS;AACnD,cAAI,CAACA,MAAK;AACN,YAAAA,MAAK,OAAOA,MAAK,GAAG,IAAI,CAAC;AAC7B,iBAAOA,MAAK,OAAOA,MAAK,GAAG;AAAA,QAC/B,CAAC;AACD,YAAI;AACA,iBAAO,OAAO,OAAO,GAAG,IAAI,IAAI,OAAO,KAAK;AAChD,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,IACA,IAAI,OAAO;AACP,MAAAD,WAAU,QAAQ,OAAO,CAAC,aAAa;AACnC,YAAI,CAAC;AACD,qBAAW,CAAC;AAChB,cAAM,SAAS,aAAa,UAAU,OAAO,CAACC,UAAS;AACnD,cAAI,CAACA,MAAK;AACN,YAAAA,MAAK,OAAOA,MAAK,GAAG,IAAI,CAAC;AAC7B,iBAAOA,MAAK,OAAOA,MAAK,GAAG;AAAA,QAC/B,CAAC;AACD,YAAI;AACA,iBAAO,OAAO,OAAO,GAAG,IAAI;AAChC,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA,OAAO,gBAAgBD,YAAW,MAAM,OAAO;AAAA,IAC/C,QAAQ,WAAWA,WAAU,QAAQ,IAAI;AAAA,IACzC,aAAa,WAAWA,WAAU,aAAa,eAAe;AAAA,IAC9D;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,KAAK,MAAM,SAAS;AAC1C,QAAM,SAAS,aAAa,KAAK,MAAM,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM;AAC/D,QAAI,UAAU;AACV,aAAO,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;AACzC,WAAO,OAAO,GAAG;AAAA,EACrB,CAAC;AACD,MAAI,QAAQ;AACR,UAAM,WAAW,QAAQ,OAAO,KAAK;AACrC,WAAO,OAAO,OAAO,GAAG,IAAI;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,gBAAgBA,YAAW,MAAM,aAAa;AACnD,QAAM,OAAOA,WAAU;AACvB,QAAM,QAAQ,UAAU,IAAI;AAC5B,QAAM,QAAQ,QAAQ,MAAM,CAAC,UAAU;AACnC,UAAM,OAAO,aAAa,OAAO,KAAK;AACtC,WAAO,6BAAM;AAAA,EACjB,CAAC;AACD,SAAO;AAAA,IACH,aAAa,QAAQ;AACjB,YAAM,QAAQ,MAAM,UAAU,GAAG,MAAM;AACvC,aAAO,MAAM,MAAM;AAAA,IACvB;AAAA,IACA,OAAO,KAAK,SAAS;AACjB,WAAK,OAAO,CAAC,SAAS,iBAAiB,MAAM,OAAO,GAAG,GAAG,WAAW,WAAW;AAAA,IACpF;AAAA,IACA,IAAI,OAAO,SAAS;AAChB,WAAK,OAAO,CAAC,SAAS,iBAAiB,MAAM,OAAO,MAAM,KAAK,GAAG,WAAW,WAAW;AAAA,IAC5F;AAAA,EACJ;AACJ;AACA,SAAS,YAAY,MAAM,SAAS;AAChC,QAAME,eAAc,UAAU;AAC9B,MAAI,CAACA,iBAAe,mCAAS,WAAU,QAAW;AAC9C,UAAM,IAAI,eAAe,8EAA8E;AAAA,EAC3G;AACA,SAAOA;AACX;AACO,SAAS,WAAW,MAAM,MAAM,SAAS;AAC5C,QAAM,QAAQ,UAAU,IAAI;AAC5B,MAAI,YAAY,MAAM,OAAO,GAAG;AAC5B,WAAO,gBAAgB,MAAM,MAAM,OAAO;AAAA,EAC9C;AACA,QAAM,QAAQ,QAAQ,MAAM,CAAC,UAAU;AACnC,UAAM,OAAO,aAAa,OAAO,KAAK;AACtC,WAAO,6BAAM;AAAA,EACjB,CAAC;AACD,SAAO;AAAA,IACH,aAAa,QAAQ;AACjB,YAAM,QAAQ,MAAM,UAAU,GAAG,MAAM;AACvC,aAAO,MAAM,MAAM;AAAA,IACvB;AAAA,IACA,OAAO,KAAK;AACR,WAAK,OAAO,CAAC,SAAS,iBAAiB,MAAM,OAAO,GAAG,CAAC;AAAA,IAC5D;AAAA,IACA,IAAI,OAAO;AACP,WAAK,OAAO,CAAC,SAAS,iBAAiB,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,IACpE;AAAA,EACJ;AACJ;AACA,SAAS,UAAU,MAAM;AACrB,SAAQ,KAAK,YAAY,IACrB,MACA,OAAO,KAAK,SAAS,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,IAC3C,MACA,OAAO,KAAK,QAAQ,CAAC,EAAE,SAAS,GAAG,GAAG;AAC9C;AACA,SAAS,UAAU,MAAM,MAAM;AAC3B,SAAQ,OAAO,KAAK,SAAS,CAAC,EAAE,SAAS,GAAG,GAAG,IAC3C,MACA,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG,KACxC,OAAO,KAAK,MAAM,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI;AACxE;AACA,SAAS,QAAQ,MAAM;AACnB,SAAQ,KAAK,eAAe,IACxB,MACA,OAAO,KAAK,YAAY,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,IAC9C,MACA,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG;AACjD;AACA,SAAS,QAAQ,MAAM,MAAM;AACzB,SAAQ,OAAO,KAAK,YAAY,CAAC,EAAE,SAAS,GAAG,GAAG,IAC9C,MACA,OAAO,KAAK,cAAc,CAAC,EAAE,SAAS,GAAG,GAAG,KAC3C,OAAO,KAAK,MAAM,OAAO,KAAK,cAAc,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI;AAC3E;;;AC3dO,SAAS,YAAY,QAAQ,OAAO,CAAC,GAAG;AAC3C,QAAM,SAAS,aAAa,QAAQ,IAAI;AACxC,MAAI,CAAC;AACD,UAAM,IAAI,YAAY,yCAAyC,IAAI;AACvE,SAAO;AACX;AACA,SAAS,aAAa,QAAQ,MAAM;AAChC,eAAa,QAAQ,IAAI;AACzB,QAAM,OAAO,WAAW,QAAQ,OAAO,IAAI;AAC3C,MAAI,KAAK,SAAS,KAAK,OAAO;AAC1B,UAAM,MAAM,KAAK,SAAS,CAAC;AAC3B,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,WAAO,IAAI,OAAO,KAAK,EAAE,OAAO,CAAC,OAAO,SAAS;AAC7C,YAAM,YAAY,aAAa,MAAM,IAAI;AACzC,UAAI;AACA,gBAAQ,EAAE,GAAI,SAAS,CAAC,GAAI,GAAG,UAAU;AAC7C,aAAO;AAAA,IACX,GAAG,IAAI,SAAS,CAAC,IAAI,MAAS;AAAA,EAClC;AACA,MAAI,KAAK,YAAY;AACjB,UAAM,SAAS,CAAC;AAChB,eAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,UAAU,GAAG;AACvD,YAAM,QAAQ,aAAa,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC;AAC/C,UAAI;AACA,eAAO,GAAG,IAAI;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACA,SAAO,KAAK,MAAM,SAAS,OAAO,KAAK,KAAK,MAAM,SAAS,QAAQ,IAAI,CAAC,IAAI;AAChF;AACO,SAAS,gBAAgB,KAAK;AACjC,MAAI,SAAS,CAAC;AACd,QAAM,UAAU,MAAM,QAAQ,GAAG;AACjC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC5C,QAAI,CAAC,SAAS,OAAO,UAAU;AAC3B;AACJ,QAAI;AACA,eAAS,EAAE,GAAG,QAAQ,GAAG,gBAAgB,KAAK,EAAE;AAAA;AAEhD,aAAO,GAAG,IAAI,gBAAgB,KAAK;AAAA,EAC3C;AACA,SAAO;AACX;;;Ad5BA,IAAM,UAAU,oBAAI,QAAQ;AAC5B,IAAM,eAAe,oBAAI,QAAQ;AACjC,IAAM,iBAAiB,CAAC,UAAU;AAC9B,QAAM,MAAM,OAAO;AACvB;AACA,IAAM,qBAAqB;AAAA,EACvB,aAAa;AAAA,EACb,eAAe;AAAA,EACf,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,KAAK;AAAA,EACL,kBAAkB;AACtB;AACA,SAAS,oBAAoB,IAAI;AAC7B,SAAQ,+BAA+B,EAAE;AAI7C;AAKA,IAAI,cAAc;AAClB,IAAI;AAEA,MAAI;AACA,kBAAc;AACtB,QACM;AAEN;AAIA,IAAI,iBAAiB;AACrB,IAAI;AAEA,MAAI,WAAW;AACX,qBAAiB;AACzB,QACM;AAEN;AASO,SAAS,UAAU,MAAM,aAAa;AAtF7C;AAwFI,MAAI;AACJ,MAAI,UAAU,eAAe,CAAC;AAE9B,MAAI,mBAAmB;AACvB;AACI,QAAI,QAAQ,UAAU,aAAa;AAC/B,UAAI,QAAQ,cAAc;AACtB,gBAAQ,YAAY;AACxB,UAAI,QAAQ,mBAAmB;AAC3B,gBAAQ,iBAAiB;AAAA,IACjC;AACA,QAAI,gBAAgB;AAChB,UAAI,QAAQ,gBAAgB;AACxB,gBAAQ,cAAc;AAAA,IAC9B;AACA,QAAI,OAAO,QAAQ,QAAQ,UAAU;AAEjC,UAAI,QAAQ,kBAAkB;AAC1B,gBAAQ,gBAAgB;AAC5B,UAAI,QAAQ,gBAAgB;AACxB,gBAAQ,cAAc;AAAA,IAC9B;AACA,uBAAmB,QAAQ;AAC3B,cAAU;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AACA,SAAK,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,aAChD,QAAQ,eAAe,QAAW;AAClC,cAAQ,KAAK,6IACuF;AAAA,IACxG;AACA,QAAI,CAAC,MAAM;AACP,YAAM,IAAI,eAAe,+PAEiG;AAAA,IAC9H;AACA,QAAI,2BAA2B,IAAI,MAAM,OAAO;AAC5C,aAAO;AAAA,QACH,IAAI,QAAQ,MAAM,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,EAAE;AAAA,QACxD,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ,CAAC;AAAA,QACT,MAAM;AAAA,QACN,OAAO,gBAAgB,IAAI;AAAA,MAC/B;AAAA,IACJ;AACA,WAAO;AAEP,UAAM,iBAAkB,KAAK,KAAK,QAAQ,MAAM,KAAK;AACrD,UAAM,eAAe,IAAI,IAAI,MAAM,iBAAiB,CAAC,IAAI;AAEzD,QAAIC,cAAW,aAAQ,aAAR,mBAAkB,iBAAgB,OAAO;AACpD,UAAI,CAAC,QAAQ,IAAI,YAAY,GAAG;AAC5B,gBAAQ,IAAI,cAAc,oBAAI,IAAI,CAAC,cAAc,CAAC,CAAC;AAAA,MACvD,OACK;AACD,cAAM,eAAe,QAAQ,IAAI,YAAY;AAC7C,YAAI,6CAAc,IAAI,iBAAiB;AACnC,kBAAQ,KAAK,oBAAoB,cAAc,CAAC;AAAA,QACpD,OACK;AACD,uDAAc,IAAI;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AAMA,QAAI,CAAC,aAAa,IAAI,IAAI,GAAG;AACzB,mBAAa,IAAI,MAAM,IAAI;AAAA,IAC/B;AACA,kBAAc,aAAa,IAAI,IAAI;AAEnC,QAAI,CAACA,YAAW,aAAa,QAAQ,OAAO,aAAa,SAAS,UAAU;AACxE,YAAM,aAAa,aAAa;AAChC,iBAAW,cAAc,4BAA4B,UAAU,EAAE,QAAQ,GAAG;AACxE,YAAI,WAAW,MAAM,kBAAkB,CAAC,aAAa,IAAI,UAAU,GAAG;AAElE,uBAAa,IAAI,YAAY,UAAU;AACvC,gBAAM,eAAe;AAErB,iBAAO;AACP,eAAK,cAAc,aAAa;AAChC,eAAK,QAAQ,aAAa;AAE1B,cAAI,KAAK,SACL,QAAQ,cACP,QAAQ,cAAc,QAAQ,QAAQ,UAAU,IAAI;AACrD,mBAAOC,OAAM,YAAY;AACzB,iBAAK,UAAUA,OAAM,WAAW,OAAO;AAAA,UAC3C;AACA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,OACK;AACD,aAAOA,OAAM,WAAW;AAAA,IAC5B;AAEA,cAAU,MAAM;AA9LxB,UAAAC;AA+LY,kCAA4B;AAC5B,uBAAiB;AACjB,2BAAqB;AACrB,iBAAW,UAAU,OAAO,OAAO,UAAU,GAAG;AAC5C,eAAO,SAAS;AAAA,MACpB;AACA,OAAAA,MAAA,QAAQ,IAAI,YAAY,MAAxB,gBAAAA,IAA2B,OAAO;AAAA,IACtC,CAAC;AAED,QAAI,QAAQ,aAAa,QAAQ;AAC7B,YAAM,qBAAqB,CAAC,KAAK,UAAU;AACvC,YAAI,CAAC,SAAS,OAAO,UAAU;AAC3B;AACJ,YAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,cAAI,MAAM,SAAS;AACf,+BAAmB,KAAK,MAAM,CAAC,CAAC;AAAA,QACxC,WACS,EAAE,iBAAiB,SACxB,EAAE,iBAAiB,UAClB,CAACF,YAAW,EAAE,iBAAiB,YAAY;AAC5C,gBAAM,IAAI,eAAe,+BAA+B,GAAG,yJAEU;AAAA,QACzE;AAAA,MACJ;AACA,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,IAAI,GAAG;AAClD,2BAAmB,KAAK,KAAK;AAAA,MACjC;AAAA,IACJ;AAAA,EACJ;AAOA,QAAM,SAAS;AAAA,IACX,QAAQ,KAAK;AAAA,IACb,MAAMC,OAAM,KAAK,IAAI;AAAA,IACrB,aAAa,KAAK,eAAe,CAAC;AAAA,IAClC,QAAQ,KAAK;AAAA,IACb,QAAQA,OAAM,KAAK,MAAM;AAAA,IACzB,SAASA,OAAM,KAAK,OAAO;AAAA,IAC3B,SAAS;AAAA,IACT,OAAO,KAAK;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO,KAAK;AAAA,EAChB;AACA,QAAM,OAAO;AAGb,QAAM,SAAS,SAAS,QAAQ,MAAM,KAAK,EAAE;AAI7C,QAAM,UAAU,CAAC;AACjB,WAAS,4BAA4B,MAAM;AACvC,UAAM,QAAQ,OAAO,OAAO,IAAI,EAAE,OAAO,CAAC,MAAM,2BAA2B,CAAC,MAAM,KAAK;AACvF,WAAO;AAAA,EACX;AAKA,WAAS,2BAA2B,QAAQ;AACxC,QAAI,CAAC,UAAU,OAAO,WAAW;AAC7B,aAAO;AACX,QAAI,EAAE,WAAW,UAAU,YAAY,UAAU,OAAO,OAAO,UAAU,YAAY;AACjF,aAAO;AAAA,IACX;AACA,WAAO,QAAQ,UAAU,OAAO,OAAO,OAAO,WAAW,OAAO,KAAK;AAAA,EACzE;AAIA,QAAM,YAAY,SAAS,KAAK,IAAI;AACpC,QAAME,QAAO;AAAA,IACT,WAAW,UAAU;AAAA,IACrB,KAAK,CAAC,OAAOC,WAAU,CAAC,MAAM;AAE1B,YAAM,UAAUH,OAAM,KAAK;AAC3B,qBAAe,SAASG,SAAQ,SAAS,IAAI;AAC7C,aAAO,UAAU,IAAI,OAAO;AAAA,IAChC;AAAA,IACA,QAAQ,CAAC,SAASA,WAAU,CAAC,MAAM;AAC/B,aAAO,UAAU,OAAO,CAAC,UAAU;AAE/B,cAAM,UAAU,QAAQ,KAAK;AAC7B,uBAAe,SAASA,SAAQ,SAAS,IAAI;AAC7C,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ;AACA,WAAS,aAAa;AAClB,WAAO,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;AAAA,EAC1D;AACA,WAAS,kBAAkB,eAAe;AA/R9C,QAAAF;AAgSQ,QAAI,gBAAgB;AAChB,aAAO;AACX,YAAS,OAAO,QAAQ,QAAQ,aAAa,OAAO,QAAQ,QAAQ,WAC9D,UACAA,MAAA,QAAQ,QAAR,gBAAAA,IAAa,eAAe;AAAA,EACtC;AACA,iBAAe,cAAc,OAAO,CAAC,GAAG;AACpC,UAAM,iBAAiB,KAAK,YAAY,KAAK;AAC7C,QAAI,SAAS,CAAC;AACd,QAAI;AACJ,UAAM,YAAY,KAAK,WAAW,QAAQ;AAC1C,QAAI,OAAO,aAAa,UAAU;AAE9B,UAAI,aAAa,oBAAoB,EAAE,gBAAgB,YAAY;AAC/D,cAAM,IAAI,eAAe,oKACwF;AAAA,MACrH;AACA,eAAS,MAAsB,UAAU,SAAS,cAAc;AAChE,UAAI,CAAC,OAAO,SAAS;AACjB,iBAAS,UAAU,OAAO,QAAQ,UAAU,SAAS,KAAK,SAAS,CAAC,CAAC;AAAA,MACzE,WACS,KAAK,qBAAqB,OAAO;AAEtC,eAAO,cAAc,EAAE,GAAG,MAAM,kBAAkB,MAAM,CAAC;AAAA,MAC7D;AAAA,IACJ,OACK;AACD,eAAS,EAAE,SAAS,MAAM,MAAM,CAAC,EAAE;AAAA,IACvC;AACA,UAAM,OAAO,EAAE,GAAG,KAAK,MAAM,GAAG,gBAAgB,GAAI,OAAO,UAAU,OAAO,OAAO,CAAC,EAAG;AACvF,WAAO;AAAA,MACH,OAAO,OAAO;AAAA,MACd,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,aAAa,KAAK;AAAA,MAClB,SAAS;AAAA,MACT,IAAI,KAAK;AAAA,MACT,OAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AACA,WAAS,kBAAkB,OAAO;AAC9B,QAAI,CAAC,QAAQ,YAAY,CAAC,MAAM,MAAM,UAAU,MAAM,QAAQ;AAC1D;AACJ,QAAI;AACJ,UAAM,QAAQ,MAAM,MAAM,IAAI,SAAS;AACvC,QAAI,MAAM,QACN,MAAM,MAAM,UAAU,KACtB,MAAM,eACN,MAAM,kBAAkB,SAAS;AACjC,oBAAc;AAAA,QACV,MAAM,MAAM,CAAC;AAAA,QACb;AAAA,QACA,aAAa,MAAM;AAAA,QACnB,QAAQ,MAAM;AAAA,QACd,IAAI,MAAM,OAAOE,UAAS;AAEtB,qBAAW,EAAE,MAAMD,MAAK,GAAG,MAAMC,QAAO,EAAE,IAAI,KAAK;AAAA,QACvD;AAAA,QACA,IAAI,MAAM;AACN,iBAAO,IAAI,WAAWD,OAAM,IAAI,CAAC;AAAA,QACrC;AAAA,MACJ;AAAA,IACJ,OACK;AACD,oBAAc;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,QACR,IAAI,MAAM,OAAOC,UAAS;AAEtB,qBAAW,EAAE,MAAMD,MAAK,GAAG,MAAMC,QAAO,EAAE,IAAI,KAAK;AAAA,QACvD;AAAA,QACA,IAAI,MAAM;AACN,iBAAO,IAAI,WAAWD,OAAM,IAAI,CAAC;AAAA,QACrC;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,SAAS,WAAW;AAAA,EAChC;AAQA,iBAAe,sBAAsB,OAAO,QAAQ,OAAO,SAAS;AAChE,QAAI,OAAO;AACP,UAAI,QAAQ,cAAc,SAAS;AAC/B,eAAO,OAAO,CAAC,YAAY;AACvB,mBAAS,SAAS,MAAM,OAAO,MAAS;AACxC,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AACA,iBAAW,MAAM,kBAAkB,KAAK,CAAC;AAAA,IAC7C;AACA,QAAI,iBAAiB;AACrB,QAAI,CAAC,OAAO;AACR,UAAI,QAAQ,oBAAoB,cAAc,QAAQ,oBAAoB,eAAe;AACrF,yBAAiB;AAAA,MACrB,WACS,QAAQ,oBAAoB,aAAY,+BAAO,SAAQ;AAC5D,yBAAiB;AAAA,eACZ,QAAQ,oBAAoB,cAAa,+BAAO,SAAQ;AAC7D,yBAAiB;AAAA,IACzB;AACA,QAAI,kBAAkB,CAAC,SAAS,CAAC,QAAQ,cAAc,QAAQ,cAAc,SAAS;AAClF,UAAI,+BAAO,OAAO;AACd,cAAM,eAAc,+BAAO,gBAAe,iBAAiB;AAC3D,YAAI;AACA,oCAA0B,WAAW;AAAA,MAC7C;AACA;AAAA,IACJ;AACA,UAAM,SAAS,MAAM,cAAc,EAAE,QAAQ,CAAC;AAE9C,QAAI,OAAO,UAAU,MAAM,aAAa,MAAM,QAAQ,UAAU;AAC5D,MAAAA,MAAK,IAAI,OAAO,MAAM,EAAE,OAAO,SAAS,CAAC;AAAA,IAC7C;AAEA,UAAM,KAAK;AACX,2BAAuB,OAAO,QAAQ,OAAO,KAAK;AAClD,WAAO;AAAA,EACX;AACA,WAAS,0BAA0B,aAAa;AAC5C,UAAM,WAAW,oBAAI,IAAI;AACzB,QAAI,QAAQ,kBAAkB,aAAa;AACvC,iBAAW,MAAM,YAAY,iBAAiB,QAAQ,GAAG;AACrD,YAAI,OAAO,GAAG,SAAS,YAAY,CAAC,GAAG,KAAK;AACxC;AACJ,cAAME,WAAU,uBAAuB,KAAK,OAAO,GAAG,iBAAiB,IAAI;AAC3E,iBAAS,IAAI,GAAG,MAAM,EAAE,IAAI,SAAAA,SAAQ,CAAC;AACrC,6BAAqB,IAAI,MAAS;AAAA,MACtC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,iBAAe,uBAAuB,QAAQ,OAAO,OAAO;AACxD,UAAM,EAAE,MAAM,WAAW,UAAU,MAAM,IAAI;AAC7C,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,CAAC;AAChB,QAAI,WAAW,oBAAI,IAAI;AACvB,UAAM,cAAc,MAAM,eAAe,iBAAiB;AAC1D,QAAI;AACA,iBAAW,0BAA0B,WAAW;AACpD,kBAAc,QAAQ,CAAC,UAAU;AAC7B,UAAI,CAAC,MAAM,QAAQ,MAAM,KAAK;AAC1B;AACJ,YAAM,cAAc,CAAC,GAAG,MAAM,IAAI;AAClC,UAAI,YAAY,YAAY,SAAS,CAAC,KAAK,WAAW;AAClD,oBAAY,IAAI;AAAA,MACpB;AACA,YAAM,aAAa,YAAY,KAAK,GAAG;AACvC,eAAS,WAAW;AAEhB,iBAAS,QAAQ,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK;AAC1C,YAAI,QAAQ,kBAAkB,gBAAgB,SAAS,IAAI,UAAU,GAAG;AACpE,gBAAM,EAAE,IAAI,SAAAA,SAAQ,IAAI,SAAS,IAAI,UAAU;AAC/C,cAAIA,YAAW,MAAM,OAAO;AACxB,uBAAW,MAAM,qBAAqB,IAAI,MAAM,KAAK,CAAC;AAEtD,qBAAS,MAAM;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ;AACA,UAAI;AACA,eAAO,SAAS;AACpB,YAAM,WAAW,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC;AACjD,YAAM,gBAAgB,YAAY;AAClC,YAAM,eAAe,MAAM,SACvB,MAAM,KAAK,CAAC,SAAS;AAEjB,eAAO,gBACD,eAAe,QAAQ,YAAY,SAAS,KAAK,YAAY,CAAC,KAAK,KAAK,CAAC,IACzE,cAAc,KAAK,KAAK,GAAG;AAAA,MACrC,CAAC;AACL,UAAI,gBAAgB,QAAQ,oBAAoB;AAC5C,eAAO,SAAS;AAEpB,UAAI,aAAa,CAAC,YAAY;AAC1B,eAAO,SAAS;AAGpB,UAAI,UAAU;AAEV,cAAM,YAAY,WAAW,IAAI,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC;AACjE,aAAI,uCAAW,UAAS,QAAO,uCAAW,UAAS,UAAU;AACzD,qBAAWC,WAAU,OAAO,OAAO,UAAU,KAAK,GAAG;AACjD,gBAAI,MAAM,QAAQA,OAAM,GAAG;AACvB,qBAAO,SAAS;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,YAAM,gBAAgB,WAAW,UAAU,MAAM,IAAI;AACrD,UAAI,iBAAiB,cAAc,OAAO,cAAc,QAAQ;AAC5D,eAAO,SAAS;AAAA,MACpB;AACA,UAAI,eAAe;AAGf,YAAI,QAAQ,oBAAoB,aAC3B,QAAQ,UACL,uBAAuB,UAAU,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,GAAI;AACjE,iBAAO,SAAS;AAAA,QACpB;AAAA,MACJ,OACK;AAGD,YAAI,QAAQ,UACR,cAEF;AACE,iBAAO,SAAS;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO,IAAI,MAAM;AAAA,EACrB;AACA,WAAS,SAAS,MAAMF,WAAU,CAAC,GAAG;AAGlC,QAAIA,SAAQ,WAAW;AACnB,oBAAc,KAAK,MAAM,CAAC,SAAS;AAC/B,aAAK,CAACJ,YAAW,EAAE,KAAK,kBAAkB,eACrC,KAAK,iBAAiB,QAASA,YAAW,KAAK,iBAAiB,WAAY;AAC7E,gBAAM,WAAW,WAAW,MAAM,KAAK,IAAI;AAC3C,cAAI,CAAC,YAAY,EAAE,SAAS,OAAO,SAAS,SAAS;AACjD,qBAAS,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,KAAK;AAAA,UAC1C;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAOG,MAAK,IAAI,MAAMC,QAAO;AAAA,EACjC;AACA,WAAS,iBAAiB,WAAW,qBAAqB;AACtD,WAAQ,aACJ,uBACA,QAAQ,cACP,QAAQ,cAAc,QAAQ,QAAQ,UAAU;AAAA,EACzD;AACA,WAAS,aAAa,sBAAsB,MAAM;AAC9C,QAAI,OAAO,KAAK;AAChB,QAAI,UAAU,KAAK;AACnB,QAAI,qBAAqB;AACrB,YAAM,UAAUG,aAAY,KAAK,IAAI;AACrC,aAAO,QAAQ;AACf,YAAM,QAAQ,QAAQ;AACtB,UAAI,MAAM,QAAQ;AACd,kBAAUN,OAAM,OAAO,KAAK,CAAC;AAC7B,iBAAS,SAAS,OAAO,KAAK;AAAA,MAClC;AAAA,IACJ;AACA,WAAO;AAAA,MACH,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb;AAAA,MACA,aAAa,KAAK;AAAA,MAClB,SAAS,KAAK;AAAA,MACd,IAAI,KAAK;AAAA,MACT;AAAA,MACA,OAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AACA,iBAAe,0BAA0B,OAAO,eAAe;AAC3D,QAAI,MAAM,SAAS,iBAAiB,iBAAiB,MAAM,OAAO,aAAa,GAAG;AAC9E,iBAAW,EAAE,SAAS,MAAM,SAAS,QAAQ,KAAK,CAAC;AAAA,IACvD,OACK;AACD,aAAO;AAAA,QACH,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW;AAAA;AAAA,QAEX,mBAAmB,QAAQ,iBAAiB,WAAW,QAAQ,iBAAiB;AAAA,MACpF,CAAC;AAAA,IACL;AAEA,QAAI,WAAW,UAAU,QAAQ;AAC7B,YAAM,KAAK;AAAA,IACf;AAEA,eAAW,SAAS,WAAW,WAAW;AACtC,YAAM,EAAE,MAAM,MAAM,CAAC;AAAA,IACzB;AAAA,EACJ;AACA,WAAS,WAAW,OAAO,CAAC,GAAG;AAC3B,QAAI,KAAK;AACL,kBAAY,OAAO,EAAE,GAAG,YAAY,MAAM,GAAG,KAAK,SAAS;AAC/D,UAAM,YAAYA,OAAM,WAAW;AACnC,cAAU,OAAO,EAAE,GAAG,UAAU,MAAM,GAAG,KAAK,KAAK;AACnD,QAAI,KAAK,OAAO;AACZ,gBAAU,KAAK,KAAK;AACxB,WAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS,KAAK;AAAA,MACd,WAAW;AAAA,MACX,QAAQ,KAAK;AAAA,MACb,UAAU;AAAA,IACd,CAAC;AAAA,EACL;AACA,iBAAe,4BAA4B,QAAQ;AAC/C,QAAI,OAAO,QAAQ,SAAS;AACxB,YAAM,IAAI,eAAe,yBAAyB,OAAO,IAAI,wCAAwC;AAAA,IACzG;AACA,QAAI,OAAO,QAAQ,YAAY;AAG3B,UAAI,iBAAiB,MAAM,IAAI;AAC3B,mBAAW,EAAE,QAAQ,KAAK,CAAC;AAC/B;AAAA,IACJ;AACA,QAAI,OAAO,OAAO,SAAS,UAAU;AACjC,YAAM,IAAI,eAAe,wDAAwD;AAAA,IACrF;AACA,UAAM,QAAQ,4BAA4B,OAAO,IAAI;AACrD,QAAI,CAAC,MAAM,QAAQ;AACf,YAAM,IAAI,eAAe,6FAA6F;AAAA,IAC1H;AACA,eAAW,WAAW,OAAO;AACzB,UAAI,QAAQ,OAAO,KAAK;AACpB;AACJ,YAAM,0BAA0B,SAAS,OAAO,UAAU,OAAO,OAAO,SAAS,GAAG;AAAA,IACxF;AAAA,EACJ;AAEA,QAAM,UAAU,SAAS,OAAO,OAAO;AACvC,QAAM,cAAc,SAAS,OAAO,WAAW;AAC/C,QAAM,SAAS,SAAS,OAAO,MAAM;AACrC,QAAM,QAAQ,SAAS,OAAO,KAAK;AAEnC,QAAM,UAAU,SAAS,KAAK,MAAM;AAEpC,QAAM,SAAS;AAAA,IACX,WAAW,QAAQ;AAAA,IACnB,IAAI,OAAOG,UAAS;AAChB,aAAO,QAAQ,IAAI,aAAa,OAAO,KAAK,QAAQA,YAAA,gBAAAA,SAAS,KAAK,CAAC;AAAA,IACvE;AAAA,IACA,OAAO,SAASA,UAAS;AACrB,aAAO,QAAQ,OAAO,CAAC,UAAU;AAC7B,eAAO,aAAa,QAAQ,KAAK,GAAG,KAAK,QAAQA,YAAA,gBAAAA,SAAS,KAAK;AAAA,MACnE,CAAC;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,EAC9B;AAGA,MAAI,aAAa;AACjB,WAAS,wBAAwB,OAAO;AApoB5C,QAAAF;AAuoBQ,QAAI,cACA,SACA,OAAO,KAAK,KAAK,EAAE,UAAU,OAC7BA,MAAA,MAAM,UAAN,gBAAAA,IAAa,WACb,WAAW,UACX,WAAW,kBAAkB,oBAC7B,WAAW,OAAO,KAAK,YAAY,KAAK,QAAQ;AAChD,iBAAW,QAAQ,MAAM;AAAA,IAC7B,OACK;AACD,mBAAa;AAAA,IACjB;AAEA,eAAW,MAAM;AACb,4BAAsB,UAAU;AAAA,IACpC,GAAG,CAAC;AAAA,EACR;AACA,WAAS,sCAAsC,OAAO,WAAW,UAAU,aAAa,QAAQ;AAC5F,QAAI,eAAe,MAAM;AACrB,mBAAa,EAAE,OAAO,CAAC,EAAE;AAAA,IAC7B;AACA,eAAW,OAAO;AAClB,eAAW,YAAY;AACvB,eAAW,WAAW;AACtB,eAAW,cAAc;AACzB,eAAW,SAAS;AAAA,EACxB;AACA,WAAS,mBAAmB;AACxB,YAAO,yCAAY,UAAS,CAAC;AAAA,EACjC;AACA,WAAS,mBAAmB;AACxB,iBAAa;AAAA,EACjB;AAGA,QAAM,UAAU;AAAA,IACZ,gBAAgB;AAAA,IAChB,OAAO,SAAS;AAAA,IAChB,SAAS,QAAQ;AAAA,IACjB,OAAOD,OAAM,KAAK,IAAI;AAAA;AAAA,IACtB,kBAAkB;AAAA,EACtB;AACA,WAAS,oBAAoB;AACzB,WAAQ,QAAQ,kBAAkB,CAAC,KAAK,cAAc,CAAC,QAAQ,oBAAoB,kBAAkB;AAAA,EACzG;AACA,WAAS,oBAAoB,GAAG;AAC5B,QAAI,CAAC,kBAAkB;AACnB;AAEJ,MAAE,eAAe;AACjB,MAAE,cAAc;AAEhB,UAAM,EAAE,eAAe,IAAI;AAC3B,UAAM,oBAAoB,OAAO,mBAAmB;AACpD,UAAM,sBAAsB,qBAAqB,mBAAmB,OAAO,QAAQ,iBAAiB;AACpG,KAAC,KAAK,OAAO,OAAO,cAAc,uBAAuB,QAAQ;AACjE,WAAO;AAAA,EACX;AACA,iBAAe,kBAAkB,KAAK;AAClC,QAAI,CAAC,kBAAkB;AACnB;AACJ,UAAM,EAAE,eAAe,IAAI;AAC3B,UAAM,oBAAoB,OAAO,mBAAmB;AAGpD,QAAI;AACA,UAAI,OAAO;AAEf,QAAI,IAAI,SAAS,SAAS;AACtB;AAAA,IACJ;AACA,UAAMI,WAAU,qBAAqB,mBAAmB,OAAO,QAAQ,iBAAiB;AACxF,QAAI;AACJ,QAAI;AAIA,uBAAiB,oBACX,MAAM,eAAe,IACrB,OAAO,QAAQA,YAAW,QAAQ,cAAc;AAAA,IAC1D,QACM;AACF,uBAAiB;AAAA,IACrB;AACA,QAAI,kBAAkB,IAAI,IAAI;AAC1B,UAAI;AACA,gBAAQ,mBAAmB;AAC3B,cAAM,KAAK,IAAI,GAAG,KAAK,EAAE,GAAG,IAAI,GAAG,OAAO,CAAC;AAC3C;AAAA,MACJ,UACA;AAEI,gBAAQ,mBAAmB;AAAA,MAC/B;AAAA,IACJ,WACS,CAAC,kBAAkB,CAAC,mBAAmB;AAC5C,UAAI,OAAO;AAAA,IACf;AAAA,EACJ;AACA,WAAS,iBAAiB;AACtB,YAAQ,iBAAiB,QAAQ;AAAA,EACrC;AACA,WAAS,uBAAuB;AAC5B,WAAO,QAAQ;AAAA,EACnB;AACA,WAAS,uBAAuB,MAAM;AAClC,QAAI,CAAC,KAAK;AACN,aAAO;AACX,QAAI,CAAC;AACD,aAAO,CAAC,CAAC,KAAK;AAClB,UAAM,QAAQ,WAAW,KAAK,SAAS,UAAU,IAAI,CAAC;AACtD,WAAO,CAAC,CAAC,SAAS,MAAM,OAAO,MAAM;AAAA,EACzC;AACA,WAAS,kBAAkB,MAAM;AAC7B,QAAI,CAAC,UAAU;AACX,aAAO,yBAAyB,KAAK,OAAO;AAChD,QAAI,OAAO,SAAS;AAChB,aAAO;AACX,QAAI,OAAO,SAAS;AAChB,aAAO,yBAAyB,IAAI;AACxC,QAAI,CAAC,KAAK,WAAW,SAAS;AAC1B,aAAO;AACX,UAAM,QAAQ,WAAW,KAAK,SAAS,UAAU,IAAI,CAAC;AACtD,WAAO,yBAAyB,+BAAO,KAAK;AAAA,EAChD;AACA,WAAS,yBAAyB,KAAK;AACnC,QAAI,CAAC;AACD,aAAO;AACX,QAAI,OAAO,QAAQ,UAAU;AACzB,iBAAW,QAAQ,OAAO,OAAO,GAAG,GAAG;AACnC,YAAI,yBAAyB,IAAI;AAC7B,iBAAO;AAAA,MACf;AAAA,IACJ;AACA,WAAO,QAAQ;AAAA,EACnB;AAIA,WAAS,eAAe,SAAS,cAAc;AAI3C,QAAI,gBAAgB;AAChB;AACJ,UAAM,QAAQ,aAAa,SAAS,KAAK,IAAI;AAE7C,UAAM,aAAa,aAAa,SAAS,QAAQ,KAAK,EAAE,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC;AAEjF,QAAI,MAAM,QAAQ;AACd,UAAI,gBAAgB,iBAAiB,gBAAgB,gBAAgB;AACjE,gBAAQ,MAAM,IAAI,MAAS;AAAA,MAC/B,OACK;AACD,gBAAQ,MAAM,OAAO,CAAC,qBAAqB;AACvC,cAAI,CAAC;AACD,+BAAmB,CAAC;AACxB,mBAAS,kBAAkB,OAAO,CAAC,MAAM,SAAS;AAE9C,gBAAI,CAAC,WAAW,SAAS,KAAK,KAAK,CAAC;AAChC,qBAAO;AACX,kBAAM,eAAe,aAAa,SAAS,IAAI;AAC/C,kBAAM,YAAY,aAAa,QAAQ,OAAO,IAAI;AAClD,kBAAM,YAAY,gBAAgB,aAAa,aAAa,UAAU,UAAU;AAChF,kBAAM,SAAS,YACT,SACA,iBAAiB,OACb,OACA,iBAAiB,YACb,SACA,KAAK;AACnB,mBAAO;AAAA,UACX,CAAC;AACD,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AACA,8BAAwB,EAAE,MAAM,CAAC;AAAA,IACrC;AAAA,EACJ;AAMA,WAAS,YAAY,SAAS,UAAU;AAEpC,YAAQ,MAAM,IAAI,OAAO;AACzB,QAAI;AACA,cAAQ,QAAQ;AAAA,EACxB;AAGA,QAAM,aAAa,SAAS,KAAK;AACjC,QAAM,UAAU,SAAS,KAAK;AAE9B,QAAM,UAAU,SAAS,KAAK;AAO9B,QAAM,kBAAkB;AAAA;AAAA,IAEpB,QAAQ,MAAM,UAAU,CAAC,YAAa,OAAO,UAAUJ,OAAM,OAAO,CAAE;AAAA;AAAA,IAEtEE,MAAK,UAAU,CAACK,UAAU,OAAO,OAAOP,OAAMO,KAAI,CAAE;AAAA;AAAA,IAEpD,OAAO,UAAU,CAAC,WAAY,OAAO,SAASP,OAAM,MAAM,CAAE;AAAA,IAC5D,OAAO,UAAU,CAAC,OAAQ,OAAO,SAAS,EAAG;AAAA,IAC7C,YAAY,UAAU,CAACQ,iBAAiB,OAAO,cAAcA,YAAY;AAAA,IACzE,OAAO,UAAU,CAAC,WAAY,OAAO,SAAS,MAAO;AAAA,IACrD,QAAQ,UAAU,CAACJ,aAAa,OAAO,UAAUA,QAAQ;AAAA,IACzD,WAAW,UAAU,CAAC,eAAgB,OAAO,aAAa,UAAW;AAAA,IACrE,MAAM,UAAU,CAAC,UAAW,OAAO,QAAQ,KAAM;AAAA,EACrD;AACA,WAAS,oBAAoB,MAAM;AAC/B,oBAAgB,KAAK,IAAI;AAAA,EAC7B;AACA,WAAS,8BAA8B;AACnC,oBAAgB,QAAQ,CAAC,UAAU,MAAM,CAAC;AAAA,EAC9C;AAMA,MAAI;AACJ,WAAS,mBAAmB;AACxB,WAAO;AAAA,EACX;AACA,WAAS,2BAA2B,QAAQ;AACxC,mBAAe,SAAS,cAAc,MAAM;AAC5C,iBAAa,SAAS;AACtB,iBAAa,SAAS;AACtB,qBAAiB,YAAY;AAC7B,aAAS,KAAK,YAAY,YAAY;AAAA,EAC1C;AACA,WAAS,uBAAuB,QAAQ;AACpC,QAAI;AACA,mBAAa,SAAS;AAAA,EAC9B;AACA,WAAS,uBAAuB;AAC5B,QAAI,6CAAc,eAAe;AAC7B,mBAAa,OAAO;AAAA,IACxB;AACA,mBAAe;AAAA,EACnB;AAEA,QAAM,YAAY,QAAQ,QAAQ,CAAC,YAAa,UAAU,cAAc,OAAO,IAAI,CAAC,CAAE;AAItF,UAAQ,iBAAiB;AAEzB,WAAS,OAAO,MAAM;AAElB,UAAMG,QAAO,KAAK;AAClB,UAAMH,WAAU,KAAK,WAAWG,MAAK;AACrC,QAAI,KAAK,WAAW,KAAK,UAAU;AAC/B,kBAAY,OAAO,KAAK,YAAY,YAAY,SAAY,KAAK,SAASA,MAAK,IAAI;AAAA,IACvF;AAKA,QAAI,CAAC,KAAK,mBAAmB;AACzB,eAASA,MAAK,MAAM;AAAA,QAChB,OAAO;AAAA,QACP,WAAW,KAAK;AAAA,MACpB,CAAC;AAAA,IACL;AACA,YAAQ,IAAIH,QAAO;AACnB,QAAI,KAAK;AACL,aAAO,OAAO,OAAO,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC;AAAA;AAEzC,aAAO,IAAIG,MAAK,MAAM;AAC1B,WAAO,IAAIA,MAAK,EAAE;AAClB,WAAO,IAAI,KAAK,UAAUA,MAAK,MAAM;AAErC,QAAIA,MAAK;AACL,kBAAY,IAAIA,MAAK,WAAW;AACpC,QAAIA,MAAK;AACL,YAAM,IAAIA,MAAK,KAAK;AAExB,WAAO,QAAQA,MAAK;AACpB,QAAI,QAAQ,gBAAgB,gBAAgB,OAAO,GAAG;AAClD,YAAM,QAAQ,QAAQ,aAAa,OAAO,SAAS,IAAI;AACvD,UAAIH,YAAW,IAAI,KAAK,MAAM,QAAW;AAErC,cAAM,IAAIA,QAAO;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,aAAa;AAAA,IACf,UAAU,QAAQ,WAAW,CAAC,QAAQ,QAAQ,IAAI,CAAC;AAAA,IACnD,UAAU,QAAQ,WAAW,CAAC,QAAQ,QAAQ,IAAI,CAAC;AAAA,IACnD,UAAU,QAAQ,WAAW,CAAC,QAAQ,QAAQ,IAAI,CAAC;AAAA,IACnD,WAAW,QAAQ,YAAY,CAAC,QAAQ,SAAS,IAAI,CAAC;AAAA,IACtD,SAAS,QAAQ,UAAU,CAAC,QAAQ,OAAO,IAAI,CAAC;AAAA,EACpD;AAEA,MAAIL,UAAS;AAET,WAAO,iBAAiB,gBAAgB,mBAAmB;AAC3D,cAAU,MAAM;AACZ,aAAO,oBAAoB,gBAAgB,mBAAmB;AAAA,IAClE,CAAC;AACD,mBAAe,iBAAiB;AAEhC,wBAAoB,KAAK,UAAU,OAAO,eAAe;AACrD,UAAI,kBAAkB,eAAe,QAAW;AAC5C,qBAAa,EAAE,QAAQ,IAAI;AAAA,MAC/B;AACA,YAAM,gBAAgB,WAAW,UAAU,OAAO,WAAW,SAAS;AACtE,UAAI,QAAQ,eAAe,WAAW,QAAQ,OAAO,WAAW,SAAS,UAAU;AAC/E,cAAM,aAAa,WAAW;AAE9B,YAAI,WAAW,SAAS;AACpB;AACJ,mBAAW,WAAW,4BAA4B,UAAU,GAAG;AAC3D,gBAAM,YAAY,aAAa,IAAI,OAAO;AAC1C,cAAI,QAAQ,OAAO,KAAK,UAAU,WAAW;AACzC;AAAA,UACJ;AAEA,uBAAa,IAAI,SAAS,OAAO;AACjC,gBAAM,0BAA0B,SAAS,aAAa;AAAA,QAC1D;AAAA,MACJ,WACS,QAAQ,gBAAgB,WAC7B,WAAW,QACX,OAAO,WAAW,SAAS,UAAU;AAGrC,mBAAW,WAAW,4BAA4B,WAAW,IAAI,GAAG;AAChE,gBAAM,YAAY,aAAa,IAAI,OAAO;AAC1C,cAAI,QAAQ,OAAO,KAAK,UAAU,WAAW;AACzC;AAAA,UACJ;AACA,cAAI,QAAQ,kBAAkB,WAAW,QAAQ,kBAAkB,eAAe;AAC9E,wBAAY,OAAO,QAAQ;AAAA,UAC/B;AACA,gBAAM,cAAc,iBAAiB,QAAQ,OAAO,IAAI;AACxD,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS;AAAA,YACT,WAAW,CAAC;AAAA,YACZ,UAAU;AAAA,UACd,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC;AACF,QAAI,OAAO,QAAQ,QAAQ,UAAU;AACjC,iCAA2B,QAAQ,GAAG;AAAA,IAC1C;AAAA,EACJ;AAOA,WAAS,iBAAiB,aAAa,QAAQ;AAC3C,QAAI,QAAQ,QAAQ,UAAa,YAAY,UAAU;AACnD,kBAAY,SAAS;AACzB,QAAI,OAAO,QAAQ,QAAQ,UAAU;AACjC,UAAI,QAAQ,IAAI,UAAU,YAAY,UAAU,SAAS,SAAS,MAAM;AACpE,oBAAY,SAAS,QAAQ;AAAA,MACjC;AAAA,IACJ,OACK;AACD,qBAAe;AAAA,IACnB;AACA,QAAI,QAAQ;AACR,UAAI,OAAO,SAAS;AAChB,YAAI,QAAQ,YAAY,SAAS;AAC7B,gBAAM,IAAI,eAAe,mEAAmE;AAAA,QAChG,WACS,OAAO,YAAY,SAAS;AACjC,gBAAM,IAAI,eAAe,qDAAqD;AAAA,QAClF;AACA,mBAAW,QAAQ,KAAK,OAAO,OAAO;AAAA,MAC1C;AACA,UAAI,OAAO;AACP,mBAAW,SAAS,KAAK,OAAO,QAAQ;AAC5C,UAAI,OAAO;AACP,mBAAW,SAAS,KAAK,OAAO,QAAQ;AAC5C,UAAI,OAAO;AACP,mBAAW,SAAS,KAAK,OAAO,QAAQ;AAC5C,UAAI,OAAO;AACP,mBAAW,UAAU,KAAK,OAAO,SAAS;AAAA,IAClD;AAGA,mBAAe;AACf,QAAI;AAEJ,mBAAe,QAAQ,GAAG;AACtB,YAAM,OAAO,UAAU,EAAE,MAAM;AAE/B,UAAI,KAAK,aAAa,CAAC,KAAK;AACxB,cAAM,IAAI,QAAQ,CAAC,MAAM,WAAW,GAAG,CAAC,CAAC;AAC7C,wBAAkB,iBAAiB;AACnC,4CAAsC,SAAS,KAAK,WAAW,KAAK,UAAU,aAAa,EAAE,UAAU,MAAS;AAAA,IACpH;AACA,mBAAe,OAAO,GAAG;AAErB,UAAI,KAAK;AACL;AACJ,UAAI,CAAC,mBAAmB,iBAAiB,KAAK,iBAAiB;AAC3D;AAAA,MACJ;AACA,YAAM,OAAO,UAAU,EAAE,MAAM;AAE/B,UAAI,KAAK,aAAa,CAAC,KAAK;AACxB,cAAM,IAAI,QAAQ,CAAC,MAAM,WAAW,GAAG,CAAC,CAAC;AAC7C,4BAAsB;AAAA,QAClB,OAAO;AAAA,QACP,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,QACf,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ,EAAE,UAAU;AAAA,MACxB,CAAC;AAED,wBAAkB;AAAA,IACtB;AACA,gBAAY,iBAAiB,YAAY,MAAM;AAC/C,gBAAY,iBAAiB,SAAS,OAAO;AAC7C,cAAU,MAAM;AACZ,kBAAY,oBAAoB,YAAY,MAAM;AAClD,kBAAY,oBAAoB,SAAS,OAAO;AAAA,IACpD,CAAC;AAED,UAAM,WAAW,KAAS,aAAa,EAAE,YAAY,YAAY,SAAS,SAAS,SAAS,QAAQ,GAAG,OAAO;AAC9G,QAAI;AACJ,QAAI,gBAAgB;AACpB,UAAM,WAAW,WAAW,aAAa,OAAO,iBAAiB;AAC7D,UAAI,WAAW;AACf,UAAI,oBAAoB,QAAQ;AAEhC,YAAM,SAAS;AAAA,QACX,GAAG;AAAA,QACH,SAAS,MAAM;AACX,cAAI,QAAQ,aAAa,QAAQ;AAC7B,kBAAM,IAAI,eAAe,yDAAyD;AAAA,UACtF;AACA,qBAAW;AAAA,QACf;AAAA,QACA,WAAW,SAAS;AAChB,8BAAoB;AAAA,QACxB;AAAA,QACA,cAAc,SAAS;AACnB,0BAAgB;AAAA,QACpB;AAAA,MACJ;AACA,YAAM,gBAAgB,OAAO;AAC7B,UAAI,YAAY;AAChB,eAAS,uBAAuB,YAAY;AACxC,cAAM,mBAAmB,EAAE,GAAG,YAAY,QAAQ,KAAK;AACvD,cAAM,SAAS,iBAAiB,QAAQ,MAAM,kBAAkB,GAAG;AACnE,cAAM,OAAO,EAAE,MAAM,iBAAiB;AACtC,cAAM,SAAS,iBAAiB,QAC1B,EAAE,MAAM,WAAW,QAAQ,KAAK,IAChC,EAAE,MAAM,WAAW,QAAQ,KAAK;AACtC,mBAAW,MAAM,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC;AAAA,MACtD;AACA,eAAS,gBAAgB;AACrB,gBAAQ,QAAQ,eAAe;AAAA,UAC3B,KAAK;AACD,mBAAO,MAAM;AACb,oBAAQ,IAAI,MAAS;AACrB;AAAA,UACJ,KAAK;AACD,mBAAO,MAAM;AACb;AAAA,UACJ,KAAK;AACD,oBAAQ,IAAI,MAAS;AACrB;AAAA,QACR;AAAA,MACJ;AACA,qBAAe,eAEf,QAAQ,QAAQ;AA3mC5B,YAAAE;AA8mCgB,eAAO,SAAS;AAEhB,YAAI,QAAQ,YAAY,SAAS;AAC7B,gBAAM,QAAQ,EAAE,QAAQ,SAAS,SAAS,KAAK;AAC/C,qBAAW,gBAAgB,WAAW,SAAS;AAC3C,gBAAI,iBAAiB,YAChB,gBAAgB,kBAAkB,GAACA,MAAA,QAAQ,iBAAR,gBAAAA,IAAsB,WAAU;AACpE,oBAAM,aAAa,KAAK;AAAA,YAC5B;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,QAAQ,gBAAgB,QAAQ,aAAa,SAAS;AACtD,gBAAM,QAAQ,aAAa,QAAQ;AAAA,YAC/B;AAAA,YACA,cAAc,QAAQ,aAAa,OAAO,SAAS,IAAI;AAAA,UAC3D,CAAC;AAAA,QACL;AACA,YAAI,QAAQ,aAAa;AACrB,cAAI,QAAQ,WAAW,SAAS;AAC5B,kBAAM,YAAY,MAAM;AAAA,UAC5B,OACK;AAID,kBAAM,YAAY;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,kBAAkB,OAAO,MAAM;AAAA,cACvC,MAAM;AAAA,YACV,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,OAAO,OAAO;AAAA,QACnB,aAAa;AAAA,MACjB,GAAG;AACC,oBAAY;AACZ,YAAI,KAAK,eAAe,SAAS,aAAa,GAAG;AAC7C,mBAAS,UAAU,EAAE,UAAU,CAAC;AAAA,QACpC;AACA,eAAO,cAAc;AAAA,MACzB;AACA,aAAO,SAAS;AAChB,UAAI,SAAS,aAAa,KAAK,QAAQ,mBAAmB,WAAW;AACjE,eAAO,EAAE,aAAa,MAAM,CAAC;AAAA,MACjC,OACK;AACD,YAAI,SAAS,aAAa,KAAK,QAAQ,mBAAmB,SAAS;AAC/D,cAAI;AACA,2BAAe,MAAM;AAAA,QAC7B;AACA,iBAAS,WAAW;AACpB,yBAAiB,OAAO;AACxB,mBAAW,SAAS,WAAW,UAAU;AACrC,cAAI;AACA,kBAAM,MAAM,MAAM;AAAA,UACtB,SACO,OAAO;AACV,mBAAO;AACP,2BAAe,EAAE,MAAM,SAAS,MAAM,GAAG,GAAG;AAAA,UAChD;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,aAAa,QAAQ;AACrB,oBAAY,OAAO;AACvB,UAAI,CAAC,WAAW;AAEZ,cAAM,aAAa,CAAC,WAAW,MAC1B,YAAY,eACP,OAAO,qBAAqB,qBAC1B,OAAO,qBAAqB,qBAC5B,OAAO,UAAU;AAC7B,YAAI,aAAa;AACjB,cAAM,eAAe,YAAY;AAC7B,iBAAO,MAAM,cAAc,EAAE,SAAS,kBAAkB,CAAC;AAAA,QAC7D;AACA,sBAAc;AACd,YAAI,CAAC,YAAY;AACb,uBAAa,MAAM,aAAa;AAChC,cAAI,CAAC,WAAW,OAAO;AACnB,mBAAO,EAAE,aAAa,MAAM,CAAC;AAC7B,mCAAuB,UAAU;AAAA,UACrC;AAAA,QACJ;AACA,YAAI,CAAC,WAAW;AACZ,cAAI,QAAQ,iBACP,QAAQ,iBAAiB,wBAAwB,QAAQ,iBAAiB,cAC3E,gBAAgB,OAAO,GAAG;AAC1B,oBAAQ,aAAa,OAAO,SAAS,IAAI,EAAE,IAAI,MAAS;AAAA,UAC5D;AAEA,gBAAM,aAAa,cAAc,SAAS,OAAO,WAAW,OAAO;AAGnE,4BAAkB;AAClB,cAAI,WAAW,GAAG;AACd,gBAAI,CAAC;AACD,2BAAa,MAAM,aAAa;AACpC,mBAAO,EAAE,aAAa,MAAM,CAAC;AAC7B,mCAAuB,UAAU;AAAA,UACrC,WACS,QAAQ,aAAa,QAAQ;AAClC,gBAAI,CAAC;AACD,2BAAa,MAAM,aAAa;AACpC,kBAAM,WAAWD,OAAM,YAAY,WAAW,IAAI;AAGlD,0BAAc,UAAU,CAAC,SAAS;AAC9B,kBAAI,KAAK,iBAAiB,MAAM;AAC5B,sBAAM,MAAM,sBAAsB,UAAU,KAAK,IAAI;AACrD,2BAAW,OAAO,KAAK,KAAK,KAAK;AACjC,uBAAO,KAAK,IAAI,MAAS;AAAA,cAC7B,WACS,MAAM,QAAQ,KAAK,KAAK,KAC7B,KAAK,MAAM,UACX,KAAK,MAAM,MAAM,CAAC,MAAM,aAAa,IAAI,GAAG;AAC5C,sBAAM,MAAM,uBAAuB,UAAU,KAAK,IAAI;AACtD,2BAAW,QAAQ,KAAK,OAAO;AAC3B,6BAAW,OAAO,KAAK,IAAI;AAAA,gBAC/B;AACA,uBAAO,KAAK,IAAI,MAAS;AAAA,cAC7B;AAAA,YACJ,CAAC;AAGD,mBAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AAEnC,kBAAI,OAAO,WAAW,IAAI,GAAG,MAAM,UAAU;AACzC,2BAAW,OAAO,GAAG;AAAA,cACzB;AAAA,YACJ,CAAC;AACD,kBAAM,YAAY,QAAQ,YACpB,OAAO,YAAY,OAAO,QAAQ,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,IACnF;AAEN,kBAAM,SAAS,YAAY,UAAU,UAAU,SAAS,GAAG,QAAQ,iBAAiB,GAAM;AAC1F,uBAAW,SAAS,QAAQ;AACxB,yBAAW,OAAO,oBAAoB,KAAK;AAAA,YAC/C;AAAA,UACJ;AACA,cAAI,CAAC,WAAW,IAAI,gBAAgB,GAAG;AAEnC,kBAAM,KAAK,KAAK;AAChB,gBAAI,OAAO;AACP,yBAAW,IAAI,kBAAkB,EAAE;AAAA,UAC3C;AACA,cAAI,OAAO,QAAQ,QAAQ,UAAU;AACjC,mCAAuB,QAAQ,GAAG;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAGA,eAAS,YAAY,KAAK,MAAM;AAC5B,cAAM,YAAY,KAAK,KAAK,IAAI,SAAS,IAAI;AAC7C,cAAM,SAAS,IAAI,MAAM,SAAS;AAClC,iBAAS,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG,KAAK,MAAM;AAClD,iBAAO,CAAC,IAAI,IAAI,UAAU,GAAG,IAAI,IAAI;AAAA,QACzC;AACA,eAAO;AAAA,MACX;AAGA,qBAAe,mBAAmB,OAAO;AACrC,YAAIS,aAAY;AAChB,yBAAiB;AAEjB,YAAI,SAAS,UAAU,MAAM,UAAU,YAAY,MAAM,SACnD,MAAM,SACN;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,kBAAkB,SAAS,OAAO,MAAM,OAAO,MAAM,CAAC,KAAK,GAAG;AAAA,UACtE,OAAO,MAAM,OAAO,iBAAiB,QAAQ,MAAM,OAAO,QAAQ,MAAM;AAAA,QAC5E;AACJ,cAAMC,UAAS,MAAOD,aAAY;AAClC,cAAM,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,QAAAC;AAAA,QACJ;AACA,cAAM,mBAAmB,kBAAkB,CAAC,WAAW,IACjD,MAAM;AAAA,QAAE,IACR,WAAW,UAAU,CAAC,SAAS;AAryCrD,cAAAT,KAAA;AAuyCwB,cAAI,CAAC,UAAQA,MAAA,KAAK,SAAL,gBAAAA,IAAW,MAAM,UAAO,UAAK,OAAL,mBAAS,MAAM;AAChD;AACJ,UAAAS,QAAO;AAAA,QACX,CAAC;AACL,iBAAS,eAAe,OAAOC,OAAM,QAAQ;AACzC,UAAAA,MAAK,SAAS;AAAA,YACV,MAAM;AAAA,YACN;AAAA,YACA,QAAQ,kBAAkB,MAAM;AAAA,UACpC;AAAA,QACJ;AACA,mBAAWC,UAAS,WAAW,UAAU;AACrC,cAAI;AACA,kBAAMA,OAAM,IAAI;AAAA,UACpB,SACO,OAAO;AACV,2BAAe,OAAO,MAAM,KAAK,IAAI,OAAO,UAAU,KAAK,GAAG,CAAC;AAAA,UACnE;AAAA,QACJ;AAEA,iBAAS,KAAK;AACd,YAAI,CAACH,YAAW;AACZ,eAAK,OAAO,SAAS,aAAa,OAAO,SAAS,cAAc,OAAO,MAAM;AACzE,kBAAM,QAAQ,4BAA4B,OAAO,IAAI;AACrD,gBAAI,CAAC,MAAM,QAAQ;AACf,oBAAM,IAAI,eAAe,6FAA6F;AAAA,YAC1H;AACA,uBAAW,WAAW,OAAO;AACzB,kBAAI,QAAQ,OAAO,KAAK;AACpB;AACJ,oBAAME,QAAO;AAAA,gBACT,MAAM;AAAA,gBACN,QAAQ;AAAA,gBACR,aAAa;AAAA,gBACb,QAAQ,MAAOF,aAAY;AAAA,gBAC3B;AAAA,cACJ;AACA,yBAAWG,UAAS,WAAW,UAAU;AACrC,oBAAI;AACA,wBAAMA,OAAMD,KAAI;AAAA,gBACpB,SACO,OAAO;AACV,iCAAe,OAAOA,OAAM,KAAK,IAAI,OAAO,UAAU,KAAK,GAAG,CAAC;AAAA,gBACnE;AAAA,cACJ;AAEA,uBAASA,MAAK;AACd,kBAAI,CAACF,YAAW;AACZ,oBAAI,QAAQ,gBAAgB;AACxB,wCAAsB,aAAaE,MAAK,KAAK,MAAM;AAAA,gBACvD;AAEA,oBAAI,iBAAiBA,MAAK,KAAK,OAAO,OAAO,QAAQ,SAAS,GAAG;AAC7D,kBAAAA,MAAK,YACA,iBAAiB,oBAAoB,EACrC,QAAQ,CAAC,MAAO,EAAE,QAAQ,EAAG;AAAA,gBACtC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,CAACF,YAAW;AACZ,gBAAI,OAAO,SAAS,SAAS;AACzB,kBAAI,OAAO,SAAS,aAAa,QAAQ,eAAe;AACpD,sBAAM,cAAc;AAAA,cACxB;AACA,kBAAI,QAAQ,aAAa;AAGrB,sBAAM,YAAY,MAAM;AAAA,cAC5B,OACK;AAED,sBAAM,4BAA4B,MAAM;AAAA,cAC5C;AAAA,YACJ,OACK;AACD,oBAAM,eAAe,QAAQ,KAAK,IAAI,OAAO,UAAU,KAAK,GAAG,CAAC;AAAA,YACpE;AAAA,UACJ;AAAA,QACJ;AACA,YAAIA,cAAa,QAAQ,cAAc;AACnC,sBAAY,OAAO;AAAA,QACvB;AAEA,YAAIA,cAAa,OAAO,QAAQ,YAAY;AACxC,mBAAS,UAAU,EAAE,WAAAA,WAAU,CAAC;AAAA,QACpC,WACS,gBAAgB;AACrB,mBAAS,UAAU,EAAE,WAAAA,YAAW,UAAU,KAAK,CAAC;AAAA,QACpD,OACK;AACD,gBAAM,QAAQ,WAAW,UAAU,CAAC,SAAS;AACzC,gBAAI;AACA;AAEJ,uBAAW,MAAM;AACb,kBAAI;AACA,oBAAI;AACA,wBAAM;AAAA,cACd,QACM;AAAA,cAEN;AAAA,YACJ,CAAC;AACD,gBAAI,SAAS,aAAa,GAAG;AACzB,uBAAS,UAAU,EAAE,WAAAA,YAAW,UAAU,KAAK,CAAC;AAAA,YACpD;AAAA,UACJ,CAAC;AAAA,QACL;AACA,yBAAiB;AAAA,MACrB;AACA,UAAI,CAAC,aAAa,eAAe;AAC7B,sBAAc;AACd,cAAM,WAAW,MAAM,cAAc,YAAY;AACjD,YAAI;AACJ,YAAI,oBAAoB,UAAU;AAC9B,mBAAS,YAAY,MAAM,SAAS,KAAK,CAAC;AAAA,QAC9C,WACS,oBAAoB,gBAAgB;AACzC,mBAAS,YAAY,SAAS,YAAY;AAAA,QAC9C,OACK;AACD,mBAAS;AAAA,QACb;AACA,YAAI,OAAO,SAAS;AAChB,iBAAO,SAAS,SAAS;AAC7B,2BAAmB,EAAE,OAAO,CAAC;AAAA,MACjC;AACA,aAAO;AAAA,IACX,CAAC;AACD,WAAO;AAAA,MACH,SAAS,MAAM;AAEX,mBAAW,CAAC,MAAMI,OAAM,KAAK,OAAO,QAAQ,UAAU,GAAG;AAErD,qBAAW,IAAI,IAAIA,QAAO,OAAO,CAAC,MAAM,MAAM,QAAQ,IAAI,CAAC;AAAA,QAC/D;AACA,iBAAS,QAAQ;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,WAASP,aAAY,UAAU;AAC3B,UAAM,QAAQ,CAAC;AACf,kBAAc,UAAU,CAACK,UAAS;AAC9B,UAAIA,MAAK,iBAAiB,MAAM;AAC5B,cAAM,KAAKA,MAAK,IAAI;AACpB,eAAO;AAAA,MACX,WACS,MAAM,QAAQA,MAAK,KAAK,KAC7BA,MAAK,MAAM,UACXA,MAAK,MAAM,MAAM,CAAC,MAAM,aAAa,IAAI,GAAG;AAC5C,cAAM,KAAKA,MAAK,IAAI;AACpB,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AACD,QAAI,CAAC,MAAM;AACP,aAAO,EAAE,MAAM,UAAU,MAAM;AACnC,UAAM,OAAOX,OAAM,QAAQ;AAC3B,aAAS,MAAM,OAAO,CAAC,SAAM;AAr8CrC,UAAAC;AAq8CwC,cAAAA,MAAA,WAAW,YAAY,MAAM,IAAI,MAAjC,gBAAAA,IAAoC;AAAA,KAAK;AACzE,WAAO,EAAE,MAAM,MAAM;AAAA,EACzB;AAEA,SAAO;AAAA,IACH,MAAMC;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,SAAS,qBAAqB;AAAA,IAC9B,YAAY,SAAS,UAAU;AAAA,IAC/B,SAAS,SAAS,OAAO;AAAA,IACzB,SAAS,SAAS,OAAO;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,IACT,SAAU,CAAC,aAAa;AACpB,aAAO,EAAE,MAAM,UAAU,SAAS,SAAS,WAAW,KAAK,CAAC;AAAA,IAChE;AAAA,IACA,MAAM,SAAS,MAAM,OAAO,CAAC,GAAG;AAC5B,UAAI,CAAC,QAAQ,YAAY;AACrB,cAAM,IAAI,eAAe,4DAA4D;AAAA,MACzF;AACA,UAAI,KAAK,WAAW;AAChB,aAAK,SAAS;AAClB,UAAI,KAAK,UAAU;AACf,aAAK,QAAQ;AACjB,UAAI,OAAO,KAAK,UAAU;AACtB,aAAK,SAAS,CAAC,KAAK,MAAM;AAC9B,UAAI;AACJ,YAAM,eAAe,UAAU,IAAI;AACnC,UAAI,WAAW,MAAM;AACjB,YAAI,KAAK,WAAW,QAAQ,KAAK,WAAW,SAAS;AAEjD,UAAAA,MAAK,OAAO,CAAC,UAAU;AACnB,qBAAS,OAAO,CAAC,YAAY,GAAG,KAAK,KAAK;AAC1C,mBAAO;AAAA,UACX,GAAG,EAAE,OAAO,KAAK,MAAM,CAAC;AACxB,iBAAO,KAAK;AAAA,QAChB,OACK;AACD,iBAAOF,OAAM,KAAK,IAAI;AACtB,mBAAS,MAAM,CAAC,YAAY,GAAG,KAAK,KAAK;AAAA,QAC7C;AAAA,MACJ,OACK;AACD,eAAO,KAAK;AAAA,MAChB;AACA,YAAM,SAAS,MAAM,cAAc,EAAE,UAAU,KAAK,CAAC;AACrD,YAAM,QAAQ,WAAW,OAAO,QAAQ,YAAY;AAEpD,UAAI,SAAS,MAAM,SAAS,KAAK,QAAQ;AACrC,cAAM,QAAQ,KAAK;AAAA,MACvB;AACA,UAAI,KAAK,WAAW,QAAQ,KAAK,UAAU,UAAU;AACjD,eAAO,OAAO,CAAC,YAAY;AACvB,mBAAS,SAAS,CAAC,YAAY,GAAG,+BAAO,KAAK;AAC9C,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AACA,aAAO,+BAAO;AAAA,IAClB;AAAA,IACA,MAAM,aAAa,OAAO,CAAC,GAAG;AAC1B,UAAI,CAAC,QAAQ,cAAc,CAAC,KAAK,QAAQ;AACrC,cAAM,IAAI,eAAe,qFAAqF;AAAA,MAClH;AACA,YAAM,SAAS,KAAK,SACd,MAAM,sBAAsB,EAAE,OAAO,CAAC,EAAE,GAAG,MAAM,KAAK,MAAM,IAC5D,cAAc,EAAE,SAAS,KAAK,OAAO,CAAC;AAC5C,YAAM,eAAe,iBAAiB;AACtC,UAAI,KAAK,UAAU,cAAc;AAE7B,mBAAW,MAAM;AACb,cAAI,CAAC;AACD;AACJ,6BAAmB,cAAc;AAAA,YAC7B,GAAG;AAAA,YACH,eAAe,KAAK,iBAAiB,QAAQ,QAAQ,QAAQ;AAAA,UACjE,CAAC;AAAA,QACL,GAAG,CAAC;AAAA,MACR;AACA,aAAO,UAAU,cAAc,EAAE,SAAS,KAAK,OAAO,CAAC;AAAA,IAC3D;AAAA,IACA,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,MAAMG,UAAS;AACX,aAAO,WAAW;AAAA,QACd,UAASA,YAAA,gBAAAA,SAAS,eAAc,KAAK,UAAU;AAAA,QAC/C,MAAMA,YAAA,gBAAAA,SAAS;AAAA,QACf,IAAIA,YAAA,gBAAAA,SAAS;AAAA,QACb,UAAUA,YAAA,gBAAAA,SAAS;AAAA,MACvB,CAAC;AAAA,IACL;AAAA,IACA,OAAO,WAAW;AACd,YAAMI,QAAO,iBAAiB,IACxB,iBAAiB,IACjB,aAAa,qBAAqB,cAC9B,UAAU,QAAQ,MAAM,IACxB;AACV,UAAI,CAACA,OAAM;AACP,cAAM,IAAI,eAAe,iIAAiI;AAAA,MAC9J;AACA,UAAI,CAACA,MAAK,eAAe;AACrB,eAAOA,MAAK,OAAO;AAAA,MACvB;AACA,YAAM,iBAAiB,cACjB,qBAAqB,qBAAqB,UAAU,QAAQ,YACzD,qBAAqB,oBAAoB,CAAC,UAAU,OAAO,EAAE,SAAS,UAAU,IAAI;AAC7F,MAAAA,MAAK,cAAc,iBAAiB,YAAY,MAAS;AAAA,IAC7D;AAAA,IACA,WAAW;AAAA,IACX,SAAS;AAAA,EACb;AACJ;;;AetjDO,SAAS,SAAS,MAAM,SAAS,SAAS;AAC7C,MAAI,QAAQ,gCAAgC,MAAM;AAC9C,cAAU;AACV,cAAU;AACV,WAAO;AAAA,EACX;AACA,QAAM,YAAY;AAClB,QAAM,kBAAiB,mCAAS,aAAY,UAAU;AACtD,SAAO;AAAA,IACH,KAAI,mCAAS,OAAM,UAAU,MAAM;AAAA,IACnC,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ,CAAC;AAAA,IACT,MAAM,EAAE,GAAG,gBAAgB,GAAG,KAAK;AAAA,IACnC,aAAa,UAAU;AAAA,IACvB,OAAO,UAAU;AAAA,EACrB;AACJ;AACO,SAASO,eAAc,SAAS;AACnC,SAAO,QAAQ;AACnB;;;ACpBA,SAAS,YAAY;AAEd,SAAS,aAAa,MAAM,MAAM,SAAS;AAC9C,WAAS,aAAa;AAH1B;AAIQ,QAAI,OAAO,YAAY,YAAY,EAAC,mCAAS;AACzC,aAAO;AACX,UAAM,QAAQ;AAAA,MACV,UAAQ,wCAAS,kBAAT,mBAAwB,SAAQ,GAAG;AAAA,MAC3C,aAAW,wCAAS,kBAAT,mBAAwB,WAAU,GAAG;AAAA,MAChD,cAAY,wCAAS,kBAAT,mBAAwB,aAAY,QAAQ;AAAA,IAC5D;AACA,SAAI,wCAAS,kBAAT,mBAAwB,QAAQ;AAChC,YAAM,KAAK,QAAQ;AAAA,IACvB;AACA,WAAO,SAAS,mBAAmB,KAAK,UAAU,QAAQ,OAAO,CAAC,CAAC,OAAO,MAAM,KAAK,IAAI;AAAA,EAC7F;AACA,QAAM,SAAS,WAAW,OAAO,YAAY,WAAW,QAAQ,SAAS;AACzE,QAAM,SAAS,CAAC,WAAW;AACvB,WAAO,KAAK,EAAE,MAAM,GAAG,OAAO,GAAG;AAAA,MAC7B,QAAQ,OAAO;AAAA,MACf,SAAS,OAAO,YAAY,YAAY,QAAQ,UAC1C;AAAA,QACE,cAAc,WAAW;AAAA,MAC7B,IACE;AAAA,IACV,CAAC;AAAA,EACL;AACA,MAAI,QAAQ,SAAS;AACjB,WAAO,OAAO;AAAA,MACV,QAAQ,UAAU;AAAA,MAClB,OAAO,OAAO,SAAS,WAAW,EAAE,SAAS,KAAK,IAAI;AAAA,IAC1D,CAAC;AAAA,EACL,WACS,QAAQ,YAAY;AACzB,WAAO,OAAO;AAAA,MACV,QAAQ,UAAU;AAAA,MAClB,UAAU;AAAA,IACd,CAAC;AAAA,EACL,WACS,QAAQ,WAAW;AACxB,WAAO,OAAO;AAAA,MACV,QAAQ,UAAU;AAAA,MAClB,MAAM,UAAU,IAAI;AAAA,IACxB,CAAC;AAAA,EACL,OACK;AACD,WAAO,OAAO,EAAE,QAAQ,UAAU,KAAK,MAAM,UAAU,IAAI,EAAE,CAAC;AAAA,EAClE;AACJ;;;AC/CA,SAAS,QAAQ,eAAe;;;ACShC,IAAI,aAAa;AACjB,IAAI;AAEA,MAAI;AACA,iBAAa;AACrB,QACM;AAEN;AACA,IAAM,aAAa;AACnB,eAAsB,aAAa,MAAM,YAAY,SAAS;AAC1D,MAAI;AACJ,MAAI,gBAAgB,UAAU;AAC1B,aAAS,cAAc,MAAM,YAAY,OAAO;AAAA,EACpD,WACS,gBAAgB,OAAO,gBAAgB,iBAAiB;AAC7D,aAAS,kBAAkB,MAAM,YAAY,OAAO;AAAA,EACxD,WACS,gBAAgB,SAAS;AAC9B,aAAS,MAAM,iBAAiB,MAAM,YAAY,OAAO;AAAA,EAC7D;AAAA;AAAA,IAGA,QACI,OAAO,SAAS,YAChB,aAAa,QACb,KAAK,mBAAmB;AAAA,IAAS;AACjC,aAAS,MAAM,iBAAiB,KAAK,SAAS,YAAY,OAAO;AAAA,EACrE,OACK;AACD,aAAS;AAAA,MACL,IAAI;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,IACZ;AAAA,EACJ;AACA,SAAO;AACX;AACA,eAAe,iBAAiB,SAAS,YAAY,SAAS;AAC1D,MAAI,WAAW;AACf,MAAI;AACA,eAAW,MAAM,QAAQ,SAAS;AAAA,EACtC,SACO,GAAG;AACN,QAAI,aAAa,aAAa,EAAE,QAAQ,SAAS,uBAAuB,GAAG;AAGvE,YAAM;AAAA,IACV;AAEA,WAAO,EAAE,IAAI,QAAW,MAAM,QAAW,QAAQ,MAAM;AAAA,EAC3D;AACA,SAAO,cAAc,UAAU,YAAY,OAAO;AACtD;AACO,SAAS,kBAAkB,MAAM,YAAY,SAAS;AACzD,MAAI,gBAAgB;AAChB,WAAO,KAAK;AAChB,QAAM,UAAU,IAAI,SAAS;AAC7B,aAAW,CAAC,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,OAAO,KAAK,KAAK;AAAA,EAC7B;AACA,QAAM,SAAS,cAAc,SAAS,YAAY,OAAO;AAEzD,SAAO,SAAS;AAChB,SAAO;AACX;AACO,SAAS,cAAc,UAAU,YAAY,SAAS;AA5E7D;AA6EI,WAAS,oBAAoB;AACzB,QAAI,SAAS,IAAI,kBAAkB,GAAG;AAClC,UAAI;AACA,cAAM,YAAY,WAAW,QAAQ,YAC/B,OAAO,YAAY,OAAO,QAAQ,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,IACnF;AACN,cAAM,SAAS,MAAM,SAAS,OAAO,kBAAkB,EAAE,KAAK,EAAE,KAAK,IAAI,SAAS;AAClF,YAAI,OAAO,WAAW,UAAU;AAE5B,gBAAM,YAAY,MAAM,KAAK,SAAS,KAAK,CAAC;AAC5C,qBAAW,QAAQ,UAAU,OAAO,CAACC,UAASA,MAAK,WAAW,mBAAmB,CAAC,GAAG;AACjF,kBAAM,WAAW,UAAU,KAAK,UAAU,EAAE,CAAC;AAC7C,qBAAS,QAAQ,CAAC,QAAQ,GAAG,SAAS,IAAI,IAAI,CAAC;AAAA,UACnD;AACA,qBAAW,QAAQ,UAAU,OAAO,CAACA,UAASA,MAAK,WAAW,oBAAoB,CAAC,GAAG;AAClF,kBAAM,WAAW,UAAU,KAAK,UAAU,EAAE,CAAC;AAC7C,kBAAM,WAAW,SAAS,OAAO,IAAI;AACrC,qBAAS,QAAQ,CAAC,QAAQ,GAAG,MAAM,KAAK,QAAQ,CAAC;AAAA,UACrD;AACA,iBAAO;AAAA,QACX;AAAA,MACJ,QACM;AAAA,MAEN;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,OAAO,kBAAkB;AAC/B,QAAM,MAAK,cAAS,IAAI,gBAAgB,MAA7B,mBAAgC;AAC3C,SAAO,OACD,EAAE,IAAI,MAAM,QAAQ,KAAK,IACzB;AAAA,IACE;AAAA,IACA,MAAM,eAAe,UAAU,YAAY,OAAO;AAAA,IAClD,QAAQ;AAAA,EACZ;AACR;AACA,SAAS,eAAe,UAAU,QAAQ,SAAS;AAnHnD;AAoHI,QAAM,SAAS,CAAC;AAChB,MAAI;AACJ,MAAI,mCAAS,QAAQ;AACjB,iBAAa,IAAI,IAAI,CAAC,GAAG,SAAS,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,WAAW,cAAc,CAAC,CAAC;AAAA,EAC9F,OACK;AACD,QAAI,YAAY,CAAC;AAEjB,QAAI,OAAO,OAAO;AACd,YAAM,OAAO,WAAW,QAAQ,OAAO,CAAC,CAAC;AACzC,WAAI,UAAK,UAAL,mBAAY,KAAK,CAAC,MAAM,EAAE,SAAS,WAAW;AAC9C,cAAM,IAAI,YAAY,wDAAwD;AAAA,MAClF;AACA,oBAAY,UAAK,UAAL,mBAAY,QAAQ,CAAC,MAAM,OAAO,KAAK,EAAE,cAAc,CAAC,CAAC,OAAM,CAAC;AAAA,IAChF;AACA,iBAAa,IAAI,IAAI;AAAA,MACjB,GAAG;AAAA,MACH,GAAG,OAAO,KAAK,OAAO,cAAc,CAAC,CAAC;AAAA,MACtC,GAAI,OAAO,uBAAuB,SAAS,KAAK,IAAI,CAAC;AAAA,IACzD,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,WAAW,cAAc,CAAC,CAAC;AAAA,EACtD;AACA,WAAS,iBAAiB,KAAK,OAAO,MAAM;AACxC,SAAI,mCAAS,iBAAgB,QAAQ,aAAa,SAAS,GAAG,GAAG;AAC7D,aAAO;AAAA,IACX;AACA,QAAI,SAAS,OAAO,UAAU,UAAU;AACpC,YAAM,aAAa,cAAa,mCAAS,gBAAe,QAAO,mCAAS,gBAAe;AACvF,aAAO,CAAC,aAAa,SAAY,MAAM,OAAO,QAAQ,KAAK,aAAa,OAAO;AAAA,IACnF;AACA,QAAI,KAAK,MAAM,SAAS,GAAG;AACvB,YAAM,IAAI,YAAY,YAAY,GAAG;AAAA,IACzC;AACA,UAAM,CAAC,IAAI,IAAI,KAAK;AACpB,WAAO,mBAAmB,KAAK,OAAO,QAAQ,OAAO,IAAI;AAAA,EAC7D;AACA,QAAM,sBAAsB,OAAO,OAAO,wBAAwB,WAC5D,OAAO,uBACP,EAAE,MAAM,SAAS;AACvB,aAAW,OAAO,YAAY;AAC1B,UAAM,WAAW,OAAO,aAClB,OAAO,WAAW,GAAG,IACrB;AACN,iBAAa,UAAU,GAAG;AAC1B,UAAM,OAAO,WAAW,YAAY,qBAAqB,GAAC,YAAO,aAAP,mBAAiB,SAAS,OAAM;AAAA,MACtF;AAAA,IACJ,CAAC;AACD,QAAI,CAAC;AACD;AACJ,QAAI,CAAC,KAAK,MAAM,SAAS,SAAS,KAAK,CAAC,OAAO,wBAAwB,CAAC,SAAS,IAAI,GAAG,GAAG;AACvF;AAAA,IACJ;AACA,UAAM,UAAU,SAAS,OAAO,GAAG;AACnC,QAAI,KAAK,SAAS,KAAK,MAAM,SAAS,GAAG;AACrC,YAAM,IAAI,YAAY,YAAY,GAAG;AAAA,IACzC;AACA,QAAI,KAAK,MAAM,SAAS,OAAO,KAAK,KAAK,MAAM,SAAS,KAAK,GAAG;AAE5D,YAAM,QAAQ,SAAS,YAAU,UAAK,UAAL,mBAAY,WAAU,IAAI,KAAK,MAAM,CAAC,IAAI;AAC3E,UAAI,CAAC,SAAS,OAAO,SAAS,aAAc,MAAM,QAAQ,KAAK,KAAK,MAAM,UAAU,GAAI;AACpF,cAAM,IAAI,YAAY,qEAAqE,GAAG;AAAA,MAClG;AACA,YAAM,YAAY,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AACpD,mBAAa,WAAW,GAAG;AAC3B,YAAM,YAAY,WAAW,WAAW,KAAK,YAAY,CAAC,GAAG,CAAC;AAC9D,UAAI,CAAC;AACD;AAEJ,YAAM,cAAc,QAAQ,UAAU,QAAQ,KAAK,CAAC,MAAM,KAAK,OAAO,MAAM,QAAQ;AACpF,YAAM,YAAY,QAAQ,IAAI,CAAC,MAAM,iBAAiB,KAAK,GAAG,SAAS,CAAC;AACxE,UAAI,eAAe,UAAU,MAAM,CAAC,SAAS,CAAC,IAAI;AAC9C,kBAAU,SAAS;AACvB,aAAO,GAAG,IAAI,KAAK,MAAM,SAAS,KAAK,IAAI,IAAI,IAAI,SAAS,IAAI;AAAA,IACpE,OACK;AACD,aAAO,GAAG,IAAI,iBAAiB,KAAK,QAAQ,QAAQ,SAAS,CAAC,GAAG,IAAI;AAAA,IACzE;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,KAAK,OAAO,MAAM,MAAM;AAEhD,MAAI,CAAC,OAAO;AAGR,QAAI,QAAQ,aAAa,KAAK,cAAc,KAAK,OAAO,YAAY,MAAM;AACtE,aAAO;AAAA,IACX;AACA,UAAMC,gBAAe,cAAc,KAAK,QAAQ,KAAK,YAAY,CAAC,GAAG,CAAC;AAItE,QAAI,KAAK,OAAO,QAAQA,kBAAiB,QAAQA,kBAAiB,QAAW;AACzE,aAAO;AAAA,IACX;AACA,QAAIA,kBAAiB;AACjB,aAAOA;AACX,QAAI,KAAK;AACL,aAAO;AACX,QAAI,KAAK;AACL,aAAO;AAAA,EACf;AACA,WAAS,YAAY;AACjB,UAAM,IAAI,YAAY,KAAK,CAAC,EAAE,YAAY,IACtC,KAAK,MAAM,CAAC,IACZ,mLAEmE,GAAG;AAAA,EAC9E;AACA,UAAQ,MAAM;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,SAAS,SAAS,IAAI,EAAE;AAAA,IACnC,KAAK;AACD,aAAO,WAAW,SAAS,EAAE;AAAA,IACjC,KAAK;AACD,aAAO,QAAQ,SAAS,UAAU,KAAK,KAAK,EAAE,QAAQ;AAAA,IAC1D,KAAK,aAAa;AAEd,YAAM,OAAO,IAAI,KAAK,SAAS,EAAE;AACjC,aAAO,CAAC,MAAM,IAAI,IAAI,OAAO;AAAA,IACjC;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AACD,aAAO,OAAO,SAAS,GAAG;AAAA,IAC9B,KAAK;AACD,aAAO,OAAO,OAAO,KAAK,CAAC;AAAA,IAC/B,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,UAAU;AAAA,IACrB;AACI,YAAM,IAAI,eAAe,2CAA2C,IAAI;AAAA,EAChF;AACJ;;;ADhPA,eAAsB,cAAc,MAAM,SAAS,SAAS;AACxD,MAAI,QAAQ,gCAAgC,MAAM;AAC9C,cAAU;AACV,cAAU;AACV,WAAO;AAAA,EACX;AACA,QAAM,YAAY;AAClB,QAAMC,aAAW,mCAAS,aAAY,UAAU;AAChD,QAAM,aAAa,UAAU;AAC7B,QAAM,SAAS,MAAM,aAAa,MAAM,YAAY,OAAO;AAC3D,QAAM,aAAY,mCAAS,aAAW,mCAAS,UAAS,OAAO,CAAC,CAAC,OAAO;AAExE,QAAM,cAAa,mCAAS,UAAU,OAAO,QAAQ,CAAC,IAAK,cAAc,OAAO,MAAMA,SAAQ;AAC9F,MAAI;AACJ,MAAI,CAAC,CAAC,OAAO,QAAQ,WAAW;AAC5B,aAAS,MAAsB,UAAU,SAAS,UAAU;AAAA,EAChE,OACK;AACD,aAAS,EAAE,SAAS,OAAO,QAAQ,CAAC,EAAE;AAAA,EAC1C;AACA,QAAM,QAAQ,OAAO;AACrB,QAAM,SAAS,SAAS,CAAC,YAAY,CAAC,IAAI,UAAU,OAAO,QAAQ,UAAU,KAAK;AAGlF,QAAM,mBAAmB,QACnB,OAAO,OACP,wBAAuB,mCAAS,UAAS,cAAc,YAAYA,SAAQ,IAAI,YAAYA,WAAU,YAAY,OAAO,QAAQ,mCAAS,YAAY;AAC3J,MAAI;AACJ,MAAI,WAAW,yBAAyB,OAAO;AAE3C,iBAAa,CAAC;AACd,eAAW,OAAO,OAAO,KAAK,WAAW,cAAc,CAAC,CAAC,GAAG;AACxD,UAAI,OAAO;AACP,mBAAW,GAAG,IAAI,iBAAiB,GAAG;AAAA,IAC9C;AAAA,EACJ,OACK;AACD,iBAAa;AAAA,EACjB;AACA,QAAM,SAAS;AAAA,IACX,IAAI,OAAO,OAAM,mCAAS,OAAM,UAAU;AAAA,IAC1C;AAAA,IACA,QAAQ,OAAO;AAAA,IACf;AAAA,IACA,MAAM;AAAA,EACV;AACA,MAAI,CAAC,OAAO,QAAQ;AAChB,WAAO,cAAc,UAAU;AAC/B,QAAI,OAAO,KAAK,UAAU,KAAK,EAAE,QAAQ;AACrC,aAAO,QAAQ,UAAU;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO;AACX;AAMO,SAAS,QAAQ,MAAMC,UAAS,SAAS;AAC5C,OAAI,mCAAS,WAAU,QAAQ,UAAU,KAAK;AAC1C,SAAK,QAAQ;AAAA,EACjB;AACA,OAAK,UAAUA;AACf,QAAM,UAAS,mCAAS,iBAAgB;AACxC,QAAM,SAAS,SAAS,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK;AACrD,SAAO,KAAK,QAAQ,SAAS,SAAQ,mCAAS,WAAU,KAAK,MAAM;AACvE;AACO,IAAM,aAAa;AACnB,SAAS,SAAS,MAAM,MAAM,OAAO,SAAS;AAEjD,MAAI,SAAS,UAAc,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAI;AAC5E,cAAU;AACV,YAAQ;AACR,WAAO;AAAA,EACX;AACA,MAAI,YAAY;AACZ,cAAU,CAAC;AACf,QAAM,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACpD,MAAI,CAAC,KAAK;AACN,SAAK,SAAS,CAAC;AACnB,MAAI,SAAS,QAAQ,SAAS,IAAI;AAC9B,QAAI,CAAC,KAAK,OAAO;AACb,WAAK,OAAO,UAAU,CAAC;AAC3B,SAAK,OAAO,UAAU,QAAQ,YAAY,SAAS,KAAK,OAAO,QAAQ,OAAO,MAAM;AAAA,EACxF,OACK;AACD,UAAM,WAAW,UAAU,IAAI;AAC/B,UAAM,OAAO,aAAa,KAAK,QAAQ,UAAU,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM;AACzE,UAAI,UAAU;AACV,eAAO,GAAG,IAAI,CAAC;AACnB,aAAO,OAAO,GAAG;AAAA,IACrB,CAAC;AACD,QAAI,MAAM;AACN,WAAK,OAAO,KAAK,GAAG,IAChB,MAAM,QAAQ,KAAK,KAAK,KAAK,CAAC,QAAQ,YAAY,KAAK,MAAM,OAAO,MAAM,IAAI;AAAA,IACtF;AAAA,EACJ;AACA,OAAK,QAAQ;AACb,QAAM,SAAS,QAAQ,gBAAgB,QAAQ,EAAE,KAAK,IAAI,UAAU,EAAE,KAAK,CAAC;AAC5E,SAAO,QAAQ,QAAQ,UAAU,KAAK,MAAM;AAChD;AACO,SAAS,UAAU,KAAK;AAC3B,MAAI,OAAO,QAAQ;AACf,WAAO;AACX,aAAW,OAAO,KAAK;AACnB,UAAM,QAAQ,IAAI,GAAG;AACrB,QAAI,iBAAiB;AACjB,aAAO,IAAI,GAAG;AAAA,aACT,SAAS,OAAO,UAAU;AAC/B,gBAAU,KAAK;AAAA,EACvB;AACA,SAAO;AACX;AACO,IAAM,cAAc;AAEpB,SAAS,KAAK,QAAQ,MAAM;AAC/B,WAAS,UAAUC,OAAM;AACrB,WAAO,CAAC,CAACA,SAAQ,OAAOA,UAAS,YAAY,WAAWA,SAAQ,UAAUA,SAAQ,QAAQA;AAAA,EAC9F;AACA,WAAS,SAASA,OAAM;AACpB,QAAIA,SAAQ,OAAOA,UAAS,UAAU;AAClC,iBAAW,OAAOA,OAAM;AACpB,cAAM,IAAIA,MAAK,GAAG;AAClB,YAAI,UAAU,CAAC,GAAG;AACd,YAAE,QAAQ;AACV,sBAAY,CAAC;AAAA,QACjB,WACS,KAAK,OAAO,MAAM,UAAU;AACjC,mBAAS,CAAC;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AACA,WAAOA;AAAA,EACX;AACA,SAAO,QAAQ,QAAQ,SAAS,IAAI,CAAC;AACzC;", "names": ["key", "status", "value", "message", "parent", "key", "defaults", "clone", "clone", "browser", "message", "FetchStatus", "Form", "browser", "defaultOptions", "fileProxy", "filesProxy", "superForm", "path", "isSuperForm", "browser", "clone", "_a", "Form", "options", "message", "errors", "removeFiles", "form", "constraints", "cancelled", "cancel", "data", "event", "events", "defaultValues", "path", "defaultValue", "defaults", "message", "data"]}