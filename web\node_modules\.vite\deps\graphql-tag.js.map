{"version": 3, "sources": ["../../graphql-tag/src/index.ts", "../../graphql-tag/main.js"], "sourcesContent": ["import { parse } from 'graphql';\n\nimport {\n  DocumentNode,\n  DefinitionNode,\n  Location,\n} from 'graphql/language/ast';\n\n// A map docString -> graphql document\nconst docCache = new Map<string, DocumentNode>();\n\n// A map fragmentName -> [normalized source]\nconst fragmentSourceMap = new Map<string, Set<string>>();\n\nlet printFragmentWarnings = true;\nlet experimentalFragmentVariables = false;\n\n// Strip insignificant whitespace\n// Note that this could do a lot more, such as reorder fields etc.\nfunction normalize(string: string) {\n  return string.replace(/[\\s,]+/g, ' ').trim();\n}\n\nfunction cacheKeyFromLoc(loc: Location) {\n  return normalize(loc.source.body.substring(loc.start, loc.end));\n}\n\n// Take a unstripped parsed document (query/mutation or even fragment), and\n// check all fragment definitions, checking for name->source uniqueness.\n// We also want to make sure only unique fragments exist in the document.\nfunction processFragments(ast: DocumentNode) {\n  const seenKeys = new Set<string>();\n  const definitions: DefinitionNode[] = [];\n\n  ast.definitions.forEach(fragmentDefinition => {\n    if (fragmentDefinition.kind === 'FragmentDefinition') {\n      var fragmentName = fragmentDefinition.name.value;\n      var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc!);\n\n      // We know something about this fragment\n      let sourceKeySet = fragmentSourceMap.get(fragmentName)!;\n      if (sourceKeySet && !sourceKeySet.has(sourceKey)) {\n        // this is a problem because the app developer is trying to register another fragment with\n        // the same name as one previously registered. So, we tell them about it.\n        if (printFragmentWarnings) {\n          console.warn(\"Warning: fragment with name \" + fragmentName + \" already exists.\\n\"\n            + \"graphql-tag enforces all fragment names across your application to be unique; read more about\\n\"\n            + \"this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names\");\n        }\n      } else if (!sourceKeySet) {\n        fragmentSourceMap.set(fragmentName, sourceKeySet = new Set);\n      }\n\n      sourceKeySet.add(sourceKey);\n\n      if (!seenKeys.has(sourceKey)) {\n        seenKeys.add(sourceKey);\n        definitions.push(fragmentDefinition);\n      }\n    } else {\n      definitions.push(fragmentDefinition);\n    }\n  });\n\n  return {\n    ...ast,\n    definitions,\n  };\n}\n\nfunction stripLoc(doc: DocumentNode) {\n  const workSet = new Set<Record<string, any>>(doc.definitions);\n\n  workSet.forEach(node => {\n    if (node.loc) delete node.loc;\n    Object.keys(node).forEach(key => {\n      const value = node[key];\n      if (value && typeof value === 'object') {\n        workSet.add(value);\n      }\n    });\n  });\n\n  const loc = doc.loc as Record<string, any>;\n  if (loc) {\n    delete loc.startToken;\n    delete loc.endToken;\n  }\n\n  return doc;\n}\n\nfunction parseDocument(source: string) {\n  var cacheKey = normalize(source);\n  if (!docCache.has(cacheKey)) {\n    const parsed = parse(source, {\n      experimentalFragmentVariables,\n      allowLegacyFragmentVariables: experimentalFragmentVariables,\n    } as any);\n    if (!parsed || parsed.kind !== 'Document') {\n      throw new Error('Not a valid GraphQL document.');\n    }\n    docCache.set(\n      cacheKey,\n      // check that all \"new\" fragments inside the documents are consistent with\n      // existing fragments of the same name\n      stripLoc(processFragments(parsed)),\n    );\n  }\n  return docCache.get(cacheKey)!;\n}\n\n// XXX This should eventually disallow arbitrary string interpolation, like Relay does\nexport function gql(\n  literals: string | readonly string[],\n  ...args: any[]\n) {\n\n  if (typeof literals === 'string') {\n    literals = [literals];\n  }\n\n  let result = literals[0];\n\n  args.forEach((arg, i) => {\n    if (arg && arg.kind === 'Document') {\n      result += arg.loc.source.body;\n    } else {\n      result += arg;\n    }\n    result += literals[i + 1];\n  });\n\n  return parseDocument(result);\n}\n\nexport function resetCaches() {\n  docCache.clear();\n  fragmentSourceMap.clear();\n}\n\nexport function disableFragmentWarnings() {\n  printFragmentWarnings = false;\n}\n\nexport function enableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = true;\n}\n\nexport function disableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = false;\n}\n\nconst extras = {\n  gql,\n  resetCaches,\n  disableFragmentWarnings,\n  enableExperimentalFragmentVariables,\n  disableExperimentalFragmentVariables,\n};\n\nexport namespace gql {\n  export const {\n    gql,\n    resetCaches,\n    disableFragmentWarnings,\n    enableExperimentalFragmentVariables,\n    disableExperimentalFragmentVariables,\n  } = extras;\n}\n\ngql.default = gql;\n\nexport default gql;\n", "// For backwards compatibility, make sure require(\"graphql-tag\") returns\n// the gql function, rather than an exports object.\nmodule.exports = require('./lib/graphql-tag.umd.js').gql;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AASA,UAAM,WAAW,oBAAI,IAAG;AAGxB,UAAM,oBAAoB,oBAAI,IAAG;AAEjC,UAAI,wBAAwB;AAC5B,UAAI,gCAAgC;AAIpC,eAAS,UAAU,QAAc;AAC/B,eAAO,OAAO,QAAQ,WAAW,GAAG,EAAE,KAAI;MAC5C;AAEA,eAAS,gBAAgB,KAAa;AACpC,eAAO,UAAU,IAAI,OAAO,KAAK,UAAU,IAAI,OAAO,IAAI,GAAG,CAAC;MAChE;AAKA,eAAS,iBAAiB,KAAiB;AACzC,YAAM,WAAW,oBAAI,IAAG;AACxB,YAAM,cAAgC,CAAA;AAEtC,YAAI,YAAY,QAAQ,SAAA,oBAAkB;AACxC,cAAI,mBAAmB,SAAS,sBAAsB;AACpD,gBAAI,eAAe,mBAAmB,KAAK;AAC3C,gBAAI,YAAY,gBAAgB,mBAAmB,GAAI;AAGvD,gBAAI,eAAe,kBAAkB,IAAI,YAAY;AACrD,gBAAI,gBAAgB,CAAC,aAAa,IAAI,SAAS,GAAG;AAGhD,kBAAI,uBAAuB;AACzB,wBAAQ,KAAK,iCAAiC,eAAe,+LAEqB;;uBAE3E,CAAC,cAAc;AACxB,gCAAkB,IAAI,cAAc,eAAe,oBAAI,KAAG;;AAG5D,yBAAa,IAAI,SAAS;AAE1B,gBAAI,CAAC,SAAS,IAAI,SAAS,GAAG;AAC5B,uBAAS,IAAI,SAAS;AACtB,0BAAY,KAAK,kBAAkB;;iBAEhC;AACL,wBAAY,KAAK,kBAAkB;;SAEtC;AAED,eAAA,MAAA,SAAA,MAAA,SAAA,CAAA,GACK,GAAG,GAAA,EACN,YAAW,CAAA;MAEf;AAEA,eAAS,SAAS,KAAiB;AACjC,YAAM,UAAU,IAAI,IAAyB,IAAI,WAAW;AAE5D,gBAAQ,QAAQ,SAAA,MAAI;AAClB,cAAI,KAAK;AAAK,mBAAO,KAAK;AAC1B,iBAAO,KAAK,IAAI,EAAE,QAAQ,SAAA,KAAG;AAC3B,gBAAM,QAAQ,KAAK,GAAG;AACtB,gBAAI,SAAS,OAAO,UAAU,UAAU;AACtC,sBAAQ,IAAI,KAAK;;WAEpB;SACF;AAED,YAAM,MAAM,IAAI;AAChB,YAAI,KAAK;AACP,iBAAO,IAAI;AACX,iBAAO,IAAI;;AAGb,eAAO;MACT;AAEA,eAAS,cAAc,QAAc;AACnC,YAAI,WAAW,UAAU,MAAM;AAC/B,YAAI,CAAC,SAAS,IAAI,QAAQ,GAAG;AAC3B,cAAM,SAASA,QAAAA,MAAM,QAAQ;YAC3B;YACA,8BAA8B;WACxB;AACR,cAAI,CAAC,UAAU,OAAO,SAAS,YAAY;AACzC,kBAAM,IAAI,MAAM,+BAA+B;;AAEjD,mBAAS,IACP,UAGA,SAAS,iBAAiB,MAAM,CAAC,CAAC;;AAGtC,eAAO,SAAS,IAAI,QAAQ;MAC9B;eAGgB,IACd,UAAoC;AACpC,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAGA,YAAI,OAAO,aAAa,UAAU;AAChC,qBAAW,CAAC,QAAQ;;AAGtB,YAAI,SAAS,SAAS,CAAC;AAEvB,aAAK,QAAQ,SAAC,KAAK,GAAC;AAClB,cAAI,OAAO,IAAI,SAAS,YAAY;AAClC,sBAAU,IAAI,IAAI,OAAO;iBACpB;AACL,sBAAU;;AAEZ,oBAAU,SAAS,IAAI,CAAC;SACzB;AAED,eAAO,cAAc,MAAM;MAC7B;eAEgB,cAAW;AACzB,iBAAS,MAAK;AACd,0BAAkB,MAAK;MACzB;eAEgB,0BAAuB;AACrC,gCAAwB;MAC1B;eAEgB,sCAAmC;AACjD,wCAAgC;MAClC;eAEgB,uCAAoC;AAClD,wCAAgC;MAClC;AAEA,UAAM,SAAS;QACb;QACA;QACA;QACA;QACA;;AAGF,OAAA,SAAiB,OAAG;AAEhB,cAAA,MAKE,OAAM,KAJR,MAAA,cAIE,OAAM,aAHR,MAAA,0BAGE,OAAM,yBAFR,MAAA,sCAEE,OAAM,qCADR,MAAA,uCACE,OAAM;MACZ,GARiB,QAAA,MAAG,CAAA,EAAA;AAUpB,UAAI,SAAO,IAAG;AAEd,UAAA,QAAe;;;;;;;;;;;;;AC7Kf;AAAA;AAEA,WAAO,UAAU,0BAAoC;AAAA;AAAA;", "names": ["parse"]}