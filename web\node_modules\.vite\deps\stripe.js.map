{"version": 3, "sources": ["../../es-errors/type.js", "../../object-inspect/index.js", "../../side-channel-list/index.js", "../../es-object-atoms/index.js", "../../es-errors/index.js", "../../es-errors/eval.js", "../../es-errors/range.js", "../../es-errors/ref.js", "../../es-errors/syntax.js", "../../es-errors/uri.js", "../../math-intrinsics/abs.js", "../../math-intrinsics/floor.js", "../../math-intrinsics/max.js", "../../math-intrinsics/min.js", "../../math-intrinsics/pow.js", "../../math-intrinsics/round.js", "../../math-intrinsics/isNaN.js", "../../math-intrinsics/sign.js", "../../gopd/gOPD.js", "../../gopd/index.js", "../../es-define-property/index.js", "../../has-symbols/shams.js", "../../has-symbols/index.js", "../../get-proto/Reflect.getPrototypeOf.js", "../../get-proto/Object.getPrototypeOf.js", "../../function-bind/implementation.js", "../../function-bind/index.js", "../../call-bind-apply-helpers/functionCall.js", "../../call-bind-apply-helpers/functionApply.js", "../../call-bind-apply-helpers/reflectApply.js", "../../call-bind-apply-helpers/actualApply.js", "../../call-bind-apply-helpers/index.js", "../../dunder-proto/get.js", "../../get-proto/index.js", "../../hasown/index.js", "../../get-intrinsic/index.js", "../../call-bound/index.js", "../../side-channel-map/index.js", "../../side-channel-weakmap/index.js", "../../side-channel/index.js", "../../qs/lib/formats.js", "../../qs/lib/utils.js", "../../qs/lib/stringify.js", "../../qs/lib/parse.js", "../../qs/lib/index.js", "../../stripe/esm/utils.js", "../../stripe/esm/net/HttpClient.js", "../../stripe/esm/net/FetchHttpClient.js", "../../stripe/esm/crypto/CryptoProvider.js", "../../stripe/esm/crypto/SubtleCryptoProvider.js", "../../stripe/esm/platform/PlatformFunctions.js", "../../stripe/esm/StripeEmitter.js", "../../stripe/esm/platform/WebPlatformFunctions.js", "../../stripe/esm/Error.js", "../../stripe/esm/RequestSender.js", "../../stripe/esm/autoPagination.js", "../../stripe/esm/StripeMethod.js", "../../stripe/esm/StripeResource.js", "../../stripe/esm/Webhooks.js", "../../stripe/esm/apiVersion.js", "../../stripe/esm/resources.js", "../../stripe/esm/ResourceNamespace.js", "../../stripe/esm/resources/FinancialConnections/Accounts.js", "../../stripe/esm/resources/Entitlements/ActiveEntitlements.js", "../../stripe/esm/resources/Billing/Alerts.js", "../../stripe/esm/resources/TestHelpers/Issuing/Authorizations.js", "../../stripe/esm/resources/Issuing/Authorizations.js", "../../stripe/esm/resources/Tax/Calculations.js", "../../stripe/esm/resources/Issuing/Cardholders.js", "../../stripe/esm/resources/TestHelpers/Issuing/Cards.js", "../../stripe/esm/resources/Issuing/Cards.js", "../../stripe/esm/resources/BillingPortal/Configurations.js", "../../stripe/esm/resources/Terminal/Configurations.js", "../../stripe/esm/resources/TestHelpers/ConfirmationTokens.js", "../../stripe/esm/resources/Terminal/ConnectionTokens.js", "../../stripe/esm/resources/Billing/CreditBalanceSummary.js", "../../stripe/esm/resources/Billing/CreditBalanceTransactions.js", "../../stripe/esm/resources/Billing/CreditGrants.js", "../../stripe/esm/resources/Treasury/CreditReversals.js", "../../stripe/esm/resources/TestHelpers/Customers.js", "../../stripe/esm/resources/Treasury/DebitReversals.js", "../../stripe/esm/resources/Issuing/Disputes.js", "../../stripe/esm/resources/Radar/EarlyFraudWarnings.js", "../../stripe/esm/resources/V2/Core/EventDestinations.js", "../../stripe/esm/resources/V2/Core/Events.js", "../../stripe/esm/resources/Entitlements/Features.js", "../../stripe/esm/resources/Treasury/FinancialAccounts.js", "../../stripe/esm/resources/TestHelpers/Treasury/InboundTransfers.js", "../../stripe/esm/resources/Treasury/InboundTransfers.js", "../../stripe/esm/resources/Terminal/Locations.js", "../../stripe/esm/resources/Billing/MeterEventAdjustments.js", "../../stripe/esm/resources/V2/Billing/MeterEventAdjustments.js", "../../stripe/esm/resources/V2/Billing/MeterEventSession.js", "../../stripe/esm/resources/V2/Billing/MeterEventStream.js", "../../stripe/esm/resources/Billing/MeterEvents.js", "../../stripe/esm/resources/V2/Billing/MeterEvents.js", "../../stripe/esm/resources/Billing/Meters.js", "../../stripe/esm/resources/Climate/Orders.js", "../../stripe/esm/resources/TestHelpers/Treasury/OutboundPayments.js", "../../stripe/esm/resources/Treasury/OutboundPayments.js", "../../stripe/esm/resources/TestHelpers/Treasury/OutboundTransfers.js", "../../stripe/esm/resources/Treasury/OutboundTransfers.js", "../../stripe/esm/resources/TestHelpers/Issuing/PersonalizationDesigns.js", "../../stripe/esm/resources/Issuing/PersonalizationDesigns.js", "../../stripe/esm/resources/Issuing/PhysicalBundles.js", "../../stripe/esm/resources/Climate/Products.js", "../../stripe/esm/resources/TestHelpers/Terminal/Readers.js", "../../stripe/esm/resources/Terminal/Readers.js", "../../stripe/esm/resources/TestHelpers/Treasury/ReceivedCredits.js", "../../stripe/esm/resources/Treasury/ReceivedCredits.js", "../../stripe/esm/resources/TestHelpers/Treasury/ReceivedDebits.js", "../../stripe/esm/resources/Treasury/ReceivedDebits.js", "../../stripe/esm/resources/TestHelpers/Refunds.js", "../../stripe/esm/resources/Tax/Registrations.js", "../../stripe/esm/resources/Reporting/ReportRuns.js", "../../stripe/esm/resources/Reporting/ReportTypes.js", "../../stripe/esm/resources/Forwarding/Requests.js", "../../stripe/esm/resources/Sigma/ScheduledQueryRuns.js", "../../stripe/esm/resources/Apps/Secrets.js", "../../stripe/esm/resources/BillingPortal/Sessions.js", "../../stripe/esm/resources/Checkout/Sessions.js", "../../stripe/esm/resources/FinancialConnections/Sessions.js", "../../stripe/esm/resources/Tax/Settings.js", "../../stripe/esm/resources/Climate/Suppliers.js", "../../stripe/esm/resources/TestHelpers/TestClocks.js", "../../stripe/esm/resources/Issuing/Tokens.js", "../../stripe/esm/resources/Treasury/TransactionEntries.js", "../../stripe/esm/resources/TestHelpers/Issuing/Transactions.js", "../../stripe/esm/resources/FinancialConnections/Transactions.js", "../../stripe/esm/resources/Issuing/Transactions.js", "../../stripe/esm/resources/Tax/Transactions.js", "../../stripe/esm/resources/Treasury/Transactions.js", "../../stripe/esm/resources/Radar/ValueListItems.js", "../../stripe/esm/resources/Radar/ValueLists.js", "../../stripe/esm/resources/Identity/VerificationReports.js", "../../stripe/esm/resources/Identity/VerificationSessions.js", "../../stripe/esm/resources/Accounts.js", "../../stripe/esm/resources/AccountLinks.js", "../../stripe/esm/resources/AccountSessions.js", "../../stripe/esm/resources/ApplePayDomains.js", "../../stripe/esm/resources/ApplicationFees.js", "../../stripe/esm/resources/Balance.js", "../../stripe/esm/resources/BalanceTransactions.js", "../../stripe/esm/resources/Charges.js", "../../stripe/esm/resources/ConfirmationTokens.js", "../../stripe/esm/resources/CountrySpecs.js", "../../stripe/esm/resources/Coupons.js", "../../stripe/esm/resources/CreditNotes.js", "../../stripe/esm/resources/CustomerSessions.js", "../../stripe/esm/resources/Customers.js", "../../stripe/esm/resources/Disputes.js", "../../stripe/esm/resources/EphemeralKeys.js", "../../stripe/esm/resources/Events.js", "../../stripe/esm/resources/ExchangeRates.js", "../../stripe/esm/resources/FileLinks.js", "../../stripe/esm/multipart.js", "../../stripe/esm/resources/Files.js", "../../stripe/esm/resources/InvoiceItems.js", "../../stripe/esm/resources/InvoicePayments.js", "../../stripe/esm/resources/InvoiceRenderingTemplates.js", "../../stripe/esm/resources/Invoices.js", "../../stripe/esm/resources/Mandates.js", "../../stripe/esm/resources/OAuth.js", "../../stripe/esm/resources/PaymentIntents.js", "../../stripe/esm/resources/PaymentLinks.js", "../../stripe/esm/resources/PaymentMethodConfigurations.js", "../../stripe/esm/resources/PaymentMethodDomains.js", "../../stripe/esm/resources/PaymentMethods.js", "../../stripe/esm/resources/Payouts.js", "../../stripe/esm/resources/Plans.js", "../../stripe/esm/resources/Prices.js", "../../stripe/esm/resources/Products.js", "../../stripe/esm/resources/PromotionCodes.js", "../../stripe/esm/resources/Quotes.js", "../../stripe/esm/resources/Refunds.js", "../../stripe/esm/resources/Reviews.js", "../../stripe/esm/resources/SetupAttempts.js", "../../stripe/esm/resources/SetupIntents.js", "../../stripe/esm/resources/ShippingRates.js", "../../stripe/esm/resources/Sources.js", "../../stripe/esm/resources/SubscriptionItems.js", "../../stripe/esm/resources/SubscriptionSchedules.js", "../../stripe/esm/resources/Subscriptions.js", "../../stripe/esm/resources/TaxCodes.js", "../../stripe/esm/resources/TaxIds.js", "../../stripe/esm/resources/TaxRates.js", "../../stripe/esm/resources/Tokens.js", "../../stripe/esm/resources/Topups.js", "../../stripe/esm/resources/Transfers.js", "../../stripe/esm/resources/WebhookEndpoints.js", "../../stripe/esm/stripe.core.js", "../../stripe/esm/stripe.esm.worker.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./type')} */\nmodule.exports = TypeError;\n", "var hasMap = typeof Map === 'function' && Map.prototype;\nvar mapSizeDescriptor = Object.getOwnPropertyDescriptor && hasMap ? Object.getOwnPropertyDescriptor(Map.prototype, 'size') : null;\nvar mapSize = hasMap && mapSizeDescriptor && typeof mapSizeDescriptor.get === 'function' ? mapSizeDescriptor.get : null;\nvar mapForEach = hasMap && Map.prototype.forEach;\nvar hasSet = typeof Set === 'function' && Set.prototype;\nvar setSizeDescriptor = Object.getOwnPropertyDescriptor && hasSet ? Object.getOwnPropertyDescriptor(Set.prototype, 'size') : null;\nvar setSize = hasSet && setSizeDescriptor && typeof setSizeDescriptor.get === 'function' ? setSizeDescriptor.get : null;\nvar setForEach = hasSet && Set.prototype.forEach;\nvar hasWeakMap = typeof WeakMap === 'function' && WeakMap.prototype;\nvar weakMapHas = hasWeakMap ? WeakMap.prototype.has : null;\nvar hasWeakSet = typeof WeakSet === 'function' && WeakSet.prototype;\nvar weakSetHas = hasWeakSet ? WeakSet.prototype.has : null;\nvar hasWeakRef = typeof WeakRef === 'function' && WeakRef.prototype;\nvar weakRefDeref = hasWeakRef ? WeakRef.prototype.deref : null;\nvar booleanValueOf = Boolean.prototype.valueOf;\nvar objectToString = Object.prototype.toString;\nvar functionToString = Function.prototype.toString;\nvar $match = String.prototype.match;\nvar $slice = String.prototype.slice;\nvar $replace = String.prototype.replace;\nvar $toUpperCase = String.prototype.toUpperCase;\nvar $toLowerCase = String.prototype.toLowerCase;\nvar $test = RegExp.prototype.test;\nvar $concat = Array.prototype.concat;\nvar $join = Array.prototype.join;\nvar $arrSlice = Array.prototype.slice;\nvar $floor = Math.floor;\nvar bigIntValueOf = typeof BigInt === 'function' ? BigInt.prototype.valueOf : null;\nvar gOPS = Object.getOwnPropertySymbols;\nvar symToString = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ? Symbol.prototype.toString : null;\nvar hasShammedSymbols = typeof Symbol === 'function' && typeof Symbol.iterator === 'object';\n// ie, `has-tostringtag/shams\nvar toStringTag = typeof Symbol === 'function' && Symbol.toStringTag && (typeof Symbol.toStringTag === hasShammedSymbols ? 'object' : 'symbol')\n    ? Symbol.toStringTag\n    : null;\nvar isEnumerable = Object.prototype.propertyIsEnumerable;\n\nvar gPO = (typeof Reflect === 'function' ? Reflect.getPrototypeOf : Object.getPrototypeOf) || (\n    [].__proto__ === Array.prototype // eslint-disable-line no-proto\n        ? function (O) {\n            return O.__proto__; // eslint-disable-line no-proto\n        }\n        : null\n);\n\nfunction addNumericSeparator(num, str) {\n    if (\n        num === Infinity\n        || num === -Infinity\n        || num !== num\n        || (num && num > -1000 && num < 1000)\n        || $test.call(/e/, str)\n    ) {\n        return str;\n    }\n    var sepRegex = /[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;\n    if (typeof num === 'number') {\n        var int = num < 0 ? -$floor(-num) : $floor(num); // trunc(num)\n        if (int !== num) {\n            var intStr = String(int);\n            var dec = $slice.call(str, intStr.length + 1);\n            return $replace.call(intStr, sepRegex, '$&_') + '.' + $replace.call($replace.call(dec, /([0-9]{3})/g, '$&_'), /_$/, '');\n        }\n    }\n    return $replace.call(str, sepRegex, '$&_');\n}\n\nvar utilInspect = require('./util.inspect');\nvar inspectCustom = utilInspect.custom;\nvar inspectSymbol = isSymbol(inspectCustom) ? inspectCustom : null;\n\nvar quotes = {\n    __proto__: null,\n    'double': '\"',\n    single: \"'\"\n};\nvar quoteREs = {\n    __proto__: null,\n    'double': /([\"\\\\])/g,\n    single: /(['\\\\])/g\n};\n\nmodule.exports = function inspect_(obj, options, depth, seen) {\n    var opts = options || {};\n\n    if (has(opts, 'quoteStyle') && !has(quotes, opts.quoteStyle)) {\n        throw new TypeError('option \"quoteStyle\" must be \"single\" or \"double\"');\n    }\n    if (\n        has(opts, 'maxStringLength') && (typeof opts.maxStringLength === 'number'\n            ? opts.maxStringLength < 0 && opts.maxStringLength !== Infinity\n            : opts.maxStringLength !== null\n        )\n    ) {\n        throw new TypeError('option \"maxStringLength\", if provided, must be a positive integer, Infinity, or `null`');\n    }\n    var customInspect = has(opts, 'customInspect') ? opts.customInspect : true;\n    if (typeof customInspect !== 'boolean' && customInspect !== 'symbol') {\n        throw new TypeError('option \"customInspect\", if provided, must be `true`, `false`, or `\\'symbol\\'`');\n    }\n\n    if (\n        has(opts, 'indent')\n        && opts.indent !== null\n        && opts.indent !== '\\t'\n        && !(parseInt(opts.indent, 10) === opts.indent && opts.indent > 0)\n    ) {\n        throw new TypeError('option \"indent\" must be \"\\\\t\", an integer > 0, or `null`');\n    }\n    if (has(opts, 'numericSeparator') && typeof opts.numericSeparator !== 'boolean') {\n        throw new TypeError('option \"numericSeparator\", if provided, must be `true` or `false`');\n    }\n    var numericSeparator = opts.numericSeparator;\n\n    if (typeof obj === 'undefined') {\n        return 'undefined';\n    }\n    if (obj === null) {\n        return 'null';\n    }\n    if (typeof obj === 'boolean') {\n        return obj ? 'true' : 'false';\n    }\n\n    if (typeof obj === 'string') {\n        return inspectString(obj, opts);\n    }\n    if (typeof obj === 'number') {\n        if (obj === 0) {\n            return Infinity / obj > 0 ? '0' : '-0';\n        }\n        var str = String(obj);\n        return numericSeparator ? addNumericSeparator(obj, str) : str;\n    }\n    if (typeof obj === 'bigint') {\n        var bigIntStr = String(obj) + 'n';\n        return numericSeparator ? addNumericSeparator(obj, bigIntStr) : bigIntStr;\n    }\n\n    var maxDepth = typeof opts.depth === 'undefined' ? 5 : opts.depth;\n    if (typeof depth === 'undefined') { depth = 0; }\n    if (depth >= maxDepth && maxDepth > 0 && typeof obj === 'object') {\n        return isArray(obj) ? '[Array]' : '[Object]';\n    }\n\n    var indent = getIndent(opts, depth);\n\n    if (typeof seen === 'undefined') {\n        seen = [];\n    } else if (indexOf(seen, obj) >= 0) {\n        return '[Circular]';\n    }\n\n    function inspect(value, from, noIndent) {\n        if (from) {\n            seen = $arrSlice.call(seen);\n            seen.push(from);\n        }\n        if (noIndent) {\n            var newOpts = {\n                depth: opts.depth\n            };\n            if (has(opts, 'quoteStyle')) {\n                newOpts.quoteStyle = opts.quoteStyle;\n            }\n            return inspect_(value, newOpts, depth + 1, seen);\n        }\n        return inspect_(value, opts, depth + 1, seen);\n    }\n\n    if (typeof obj === 'function' && !isRegExp(obj)) { // in older engines, regexes are callable\n        var name = nameOf(obj);\n        var keys = arrObjKeys(obj, inspect);\n        return '[Function' + (name ? ': ' + name : ' (anonymous)') + ']' + (keys.length > 0 ? ' { ' + $join.call(keys, ', ') + ' }' : '');\n    }\n    if (isSymbol(obj)) {\n        var symString = hasShammedSymbols ? $replace.call(String(obj), /^(Symbol\\(.*\\))_[^)]*$/, '$1') : symToString.call(obj);\n        return typeof obj === 'object' && !hasShammedSymbols ? markBoxed(symString) : symString;\n    }\n    if (isElement(obj)) {\n        var s = '<' + $toLowerCase.call(String(obj.nodeName));\n        var attrs = obj.attributes || [];\n        for (var i = 0; i < attrs.length; i++) {\n            s += ' ' + attrs[i].name + '=' + wrapQuotes(quote(attrs[i].value), 'double', opts);\n        }\n        s += '>';\n        if (obj.childNodes && obj.childNodes.length) { s += '...'; }\n        s += '</' + $toLowerCase.call(String(obj.nodeName)) + '>';\n        return s;\n    }\n    if (isArray(obj)) {\n        if (obj.length === 0) { return '[]'; }\n        var xs = arrObjKeys(obj, inspect);\n        if (indent && !singleLineValues(xs)) {\n            return '[' + indentedJoin(xs, indent) + ']';\n        }\n        return '[ ' + $join.call(xs, ', ') + ' ]';\n    }\n    if (isError(obj)) {\n        var parts = arrObjKeys(obj, inspect);\n        if (!('cause' in Error.prototype) && 'cause' in obj && !isEnumerable.call(obj, 'cause')) {\n            return '{ [' + String(obj) + '] ' + $join.call($concat.call('[cause]: ' + inspect(obj.cause), parts), ', ') + ' }';\n        }\n        if (parts.length === 0) { return '[' + String(obj) + ']'; }\n        return '{ [' + String(obj) + '] ' + $join.call(parts, ', ') + ' }';\n    }\n    if (typeof obj === 'object' && customInspect) {\n        if (inspectSymbol && typeof obj[inspectSymbol] === 'function' && utilInspect) {\n            return utilInspect(obj, { depth: maxDepth - depth });\n        } else if (customInspect !== 'symbol' && typeof obj.inspect === 'function') {\n            return obj.inspect();\n        }\n    }\n    if (isMap(obj)) {\n        var mapParts = [];\n        if (mapForEach) {\n            mapForEach.call(obj, function (value, key) {\n                mapParts.push(inspect(key, obj, true) + ' => ' + inspect(value, obj));\n            });\n        }\n        return collectionOf('Map', mapSize.call(obj), mapParts, indent);\n    }\n    if (isSet(obj)) {\n        var setParts = [];\n        if (setForEach) {\n            setForEach.call(obj, function (value) {\n                setParts.push(inspect(value, obj));\n            });\n        }\n        return collectionOf('Set', setSize.call(obj), setParts, indent);\n    }\n    if (isWeakMap(obj)) {\n        return weakCollectionOf('WeakMap');\n    }\n    if (isWeakSet(obj)) {\n        return weakCollectionOf('WeakSet');\n    }\n    if (isWeakRef(obj)) {\n        return weakCollectionOf('WeakRef');\n    }\n    if (isNumber(obj)) {\n        return markBoxed(inspect(Number(obj)));\n    }\n    if (isBigInt(obj)) {\n        return markBoxed(inspect(bigIntValueOf.call(obj)));\n    }\n    if (isBoolean(obj)) {\n        return markBoxed(booleanValueOf.call(obj));\n    }\n    if (isString(obj)) {\n        return markBoxed(inspect(String(obj)));\n    }\n    // note: in IE 8, sometimes `global !== window` but both are the prototypes of each other\n    /* eslint-env browser */\n    if (typeof window !== 'undefined' && obj === window) {\n        return '{ [object Window] }';\n    }\n    if (\n        (typeof globalThis !== 'undefined' && obj === globalThis)\n        || (typeof global !== 'undefined' && obj === global)\n    ) {\n        return '{ [object globalThis] }';\n    }\n    if (!isDate(obj) && !isRegExp(obj)) {\n        var ys = arrObjKeys(obj, inspect);\n        var isPlainObject = gPO ? gPO(obj) === Object.prototype : obj instanceof Object || obj.constructor === Object;\n        var protoTag = obj instanceof Object ? '' : 'null prototype';\n        var stringTag = !isPlainObject && toStringTag && Object(obj) === obj && toStringTag in obj ? $slice.call(toStr(obj), 8, -1) : protoTag ? 'Object' : '';\n        var constructorTag = isPlainObject || typeof obj.constructor !== 'function' ? '' : obj.constructor.name ? obj.constructor.name + ' ' : '';\n        var tag = constructorTag + (stringTag || protoTag ? '[' + $join.call($concat.call([], stringTag || [], protoTag || []), ': ') + '] ' : '');\n        if (ys.length === 0) { return tag + '{}'; }\n        if (indent) {\n            return tag + '{' + indentedJoin(ys, indent) + '}';\n        }\n        return tag + '{ ' + $join.call(ys, ', ') + ' }';\n    }\n    return String(obj);\n};\n\nfunction wrapQuotes(s, defaultStyle, opts) {\n    var style = opts.quoteStyle || defaultStyle;\n    var quoteChar = quotes[style];\n    return quoteChar + s + quoteChar;\n}\n\nfunction quote(s) {\n    return $replace.call(String(s), /\"/g, '&quot;');\n}\n\nfunction canTrustToString(obj) {\n    return !toStringTag || !(typeof obj === 'object' && (toStringTag in obj || typeof obj[toStringTag] !== 'undefined'));\n}\nfunction isArray(obj) { return toStr(obj) === '[object Array]' && canTrustToString(obj); }\nfunction isDate(obj) { return toStr(obj) === '[object Date]' && canTrustToString(obj); }\nfunction isRegExp(obj) { return toStr(obj) === '[object RegExp]' && canTrustToString(obj); }\nfunction isError(obj) { return toStr(obj) === '[object Error]' && canTrustToString(obj); }\nfunction isString(obj) { return toStr(obj) === '[object String]' && canTrustToString(obj); }\nfunction isNumber(obj) { return toStr(obj) === '[object Number]' && canTrustToString(obj); }\nfunction isBoolean(obj) { return toStr(obj) === '[object Boolean]' && canTrustToString(obj); }\n\n// Symbol and BigInt do have Symbol.toStringTag by spec, so that can't be used to eliminate false positives\nfunction isSymbol(obj) {\n    if (hasShammedSymbols) {\n        return obj && typeof obj === 'object' && obj instanceof Symbol;\n    }\n    if (typeof obj === 'symbol') {\n        return true;\n    }\n    if (!obj || typeof obj !== 'object' || !symToString) {\n        return false;\n    }\n    try {\n        symToString.call(obj);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nfunction isBigInt(obj) {\n    if (!obj || typeof obj !== 'object' || !bigIntValueOf) {\n        return false;\n    }\n    try {\n        bigIntValueOf.call(obj);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nvar hasOwn = Object.prototype.hasOwnProperty || function (key) { return key in this; };\nfunction has(obj, key) {\n    return hasOwn.call(obj, key);\n}\n\nfunction toStr(obj) {\n    return objectToString.call(obj);\n}\n\nfunction nameOf(f) {\n    if (f.name) { return f.name; }\n    var m = $match.call(functionToString.call(f), /^function\\s*([\\w$]+)/);\n    if (m) { return m[1]; }\n    return null;\n}\n\nfunction indexOf(xs, x) {\n    if (xs.indexOf) { return xs.indexOf(x); }\n    for (var i = 0, l = xs.length; i < l; i++) {\n        if (xs[i] === x) { return i; }\n    }\n    return -1;\n}\n\nfunction isMap(x) {\n    if (!mapSize || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        mapSize.call(x);\n        try {\n            setSize.call(x);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof Map; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakMap(x) {\n    if (!weakMapHas || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakMapHas.call(x, weakMapHas);\n        try {\n            weakSetHas.call(x, weakSetHas);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof WeakMap; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakRef(x) {\n    if (!weakRefDeref || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakRefDeref.call(x);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nfunction isSet(x) {\n    if (!setSize || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        setSize.call(x);\n        try {\n            mapSize.call(x);\n        } catch (m) {\n            return true;\n        }\n        return x instanceof Set; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakSet(x) {\n    if (!weakSetHas || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakSetHas.call(x, weakSetHas);\n        try {\n            weakMapHas.call(x, weakMapHas);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof WeakSet; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isElement(x) {\n    if (!x || typeof x !== 'object') { return false; }\n    if (typeof HTMLElement !== 'undefined' && x instanceof HTMLElement) {\n        return true;\n    }\n    return typeof x.nodeName === 'string' && typeof x.getAttribute === 'function';\n}\n\nfunction inspectString(str, opts) {\n    if (str.length > opts.maxStringLength) {\n        var remaining = str.length - opts.maxStringLength;\n        var trailer = '... ' + remaining + ' more character' + (remaining > 1 ? 's' : '');\n        return inspectString($slice.call(str, 0, opts.maxStringLength), opts) + trailer;\n    }\n    var quoteRE = quoteREs[opts.quoteStyle || 'single'];\n    quoteRE.lastIndex = 0;\n    // eslint-disable-next-line no-control-regex\n    var s = $replace.call($replace.call(str, quoteRE, '\\\\$1'), /[\\x00-\\x1f]/g, lowbyte);\n    return wrapQuotes(s, 'single', opts);\n}\n\nfunction lowbyte(c) {\n    var n = c.charCodeAt(0);\n    var x = {\n        8: 'b',\n        9: 't',\n        10: 'n',\n        12: 'f',\n        13: 'r'\n    }[n];\n    if (x) { return '\\\\' + x; }\n    return '\\\\x' + (n < 0x10 ? '0' : '') + $toUpperCase.call(n.toString(16));\n}\n\nfunction markBoxed(str) {\n    return 'Object(' + str + ')';\n}\n\nfunction weakCollectionOf(type) {\n    return type + ' { ? }';\n}\n\nfunction collectionOf(type, size, entries, indent) {\n    var joinedEntries = indent ? indentedJoin(entries, indent) : $join.call(entries, ', ');\n    return type + ' (' + size + ') {' + joinedEntries + '}';\n}\n\nfunction singleLineValues(xs) {\n    for (var i = 0; i < xs.length; i++) {\n        if (indexOf(xs[i], '\\n') >= 0) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getIndent(opts, depth) {\n    var baseIndent;\n    if (opts.indent === '\\t') {\n        baseIndent = '\\t';\n    } else if (typeof opts.indent === 'number' && opts.indent > 0) {\n        baseIndent = $join.call(Array(opts.indent + 1), ' ');\n    } else {\n        return null;\n    }\n    return {\n        base: baseIndent,\n        prev: $join.call(Array(depth + 1), baseIndent)\n    };\n}\n\nfunction indentedJoin(xs, indent) {\n    if (xs.length === 0) { return ''; }\n    var lineJoiner = '\\n' + indent.prev + indent.base;\n    return lineJoiner + $join.call(xs, ',' + lineJoiner) + '\\n' + indent.prev;\n}\n\nfunction arrObjKeys(obj, inspect) {\n    var isArr = isArray(obj);\n    var xs = [];\n    if (isArr) {\n        xs.length = obj.length;\n        for (var i = 0; i < obj.length; i++) {\n            xs[i] = has(obj, i) ? inspect(obj[i], obj) : '';\n        }\n    }\n    var syms = typeof gOPS === 'function' ? gOPS(obj) : [];\n    var symMap;\n    if (hasShammedSymbols) {\n        symMap = {};\n        for (var k = 0; k < syms.length; k++) {\n            symMap['$' + syms[k]] = syms[k];\n        }\n    }\n\n    for (var key in obj) { // eslint-disable-line no-restricted-syntax\n        if (!has(obj, key)) { continue; } // eslint-disable-line no-restricted-syntax, no-continue\n        if (isArr && String(Number(key)) === key && key < obj.length) { continue; } // eslint-disable-line no-restricted-syntax, no-continue\n        if (hasShammedSymbols && symMap['$' + key] instanceof Symbol) {\n            // this is to prevent shammed Symbols, which are stored as strings, from being included in the string key section\n            continue; // eslint-disable-line no-restricted-syntax, no-continue\n        } else if ($test.call(/[^\\w$]/, key)) {\n            xs.push(inspect(key, obj) + ': ' + inspect(obj[key], obj));\n        } else {\n            xs.push(key + ': ' + inspect(obj[key], obj));\n        }\n    }\n    if (typeof gOPS === 'function') {\n        for (var j = 0; j < syms.length; j++) {\n            if (isEnumerable.call(obj, syms[j])) {\n                xs.push('[' + inspect(syms[j]) + ']: ' + inspect(obj[syms[j]], obj));\n            }\n        }\n    }\n    return xs;\n}\n", "'use strict';\n\nvar inspect = require('object-inspect');\n\nvar $TypeError = require('es-errors/type');\n\n/*\n* This function traverses the list returning the node corresponding to the given key.\n*\n* That node is also moved to the head of the list, so that if it's accessed again we don't need to traverse the whole list.\n* By doing so, all the recently used nodes can be accessed relatively quickly.\n*/\n/** @type {import('./list.d.ts').listGetNode} */\n// eslint-disable-next-line consistent-return\nvar listGetNode = function (list, key, isDelete) {\n\t/** @type {typeof list | NonNullable<(typeof list)['next']>} */\n\tvar prev = list;\n\t/** @type {(typeof list)['next']} */\n\tvar curr;\n\t// eslint-disable-next-line eqeqeq\n\tfor (; (curr = prev.next) != null; prev = curr) {\n\t\tif (curr.key === key) {\n\t\t\tprev.next = curr.next;\n\t\t\tif (!isDelete) {\n\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\tcurr.next = /** @type {NonNullable<typeof list.next>} */ (list.next);\n\t\t\t\tlist.next = curr; // eslint-disable-line no-param-reassign\n\t\t\t}\n\t\t\treturn curr;\n\t\t}\n\t}\n};\n\n/** @type {import('./list.d.ts').listGet} */\nvar listGet = function (objects, key) {\n\tif (!objects) {\n\t\treturn void undefined;\n\t}\n\tvar node = listGetNode(objects, key);\n\treturn node && node.value;\n};\n/** @type {import('./list.d.ts').listSet} */\nvar listSet = function (objects, key, value) {\n\tvar node = listGetNode(objects, key);\n\tif (node) {\n\t\tnode.value = value;\n\t} else {\n\t\t// Prepend the new node to the beginning of the list\n\t\tobjects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */ ({ // eslint-disable-line no-param-reassign, no-extra-parens\n\t\t\tkey: key,\n\t\t\tnext: objects.next,\n\t\t\tvalue: value\n\t\t});\n\t}\n};\n/** @type {import('./list.d.ts').listHas} */\nvar listHas = function (objects, key) {\n\tif (!objects) {\n\t\treturn false;\n\t}\n\treturn !!listGetNode(objects, key);\n};\n/** @type {import('./list.d.ts').listDelete} */\n// eslint-disable-next-line consistent-return\nvar listDelete = function (objects, key) {\n\tif (objects) {\n\t\treturn listGetNode(objects, key, true);\n\t}\n};\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannelList() {\n\t/** @typedef {ReturnType<typeof getSideChannelList>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {import('./list.d.ts').RootNode<V, K> | undefined} */ var $o;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tvar root = $o && $o.next;\n\t\t\tvar deletedNode = listDelete($o, key);\n\t\t\tif (deletedNode && root && root === deletedNode) {\n\t\t\t\t$o = void undefined;\n\t\t\t}\n\t\t\treturn !!deletedNode;\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn listGet($o, key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn listHas($o, key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$o) {\n\t\t\t\t// Initialize the linked list as an empty node, so that we don't have to special-case handling of the first node: we can always refer to it as (previous node).next, instead of something like (list).head\n\t\t\t\t$o = {\n\t\t\t\t\tnext: void undefined\n\t\t\t\t};\n\t\t\t}\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\tlistSet(/** @type {NonNullable<typeof $o>} */ ($o), key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n", "'use strict';\n\n/** @type {import('.')} */\nmodule.exports = Object;\n", "'use strict';\n\n/** @type {import('.')} */\nmodule.exports = Error;\n", "'use strict';\n\n/** @type {import('./eval')} */\nmodule.exports = EvalError;\n", "'use strict';\n\n/** @type {import('./range')} */\nmodule.exports = RangeError;\n", "'use strict';\n\n/** @type {import('./ref')} */\nmodule.exports = ReferenceError;\n", "'use strict';\n\n/** @type {import('./syntax')} */\nmodule.exports = SyntaxError;\n", "'use strict';\n\n/** @type {import('./uri')} */\nmodule.exports = URIError;\n", "'use strict';\n\n/** @type {import('./abs')} */\nmodule.exports = Math.abs;\n", "'use strict';\n\n/** @type {import('./floor')} */\nmodule.exports = Math.floor;\n", "'use strict';\n\n/** @type {import('./max')} */\nmodule.exports = Math.max;\n", "'use strict';\n\n/** @type {import('./min')} */\nmodule.exports = Math.min;\n", "'use strict';\n\n/** @type {import('./pow')} */\nmodule.exports = Math.pow;\n", "'use strict';\n\n/** @type {import('./round')} */\nmodule.exports = Math.round;\n", "'use strict';\n\n/** @type {import('./isNaN')} */\nmodule.exports = Number.isNaN || function isNaN(a) {\n\treturn a !== a;\n};\n", "'use strict';\n\nvar $isNaN = require('./isNaN');\n\n/** @type {import('./sign')} */\nmodule.exports = function sign(number) {\n\tif ($isNaN(number) || number === 0) {\n\t\treturn number;\n\t}\n\treturn number < 0 ? -1 : +1;\n};\n", "'use strict';\n\n/** @type {import('./gOPD')} */\nmodule.exports = Object.getOwnPropertyDescriptor;\n", "'use strict';\n\n/** @type {import('.')} */\nvar $gOPD = require('./gOPD');\n\nif ($gOPD) {\n\ttry {\n\t\t$gOPD([], 'length');\n\t} catch (e) {\n\t\t// IE 8 has a broken gOPD\n\t\t$gOPD = null;\n\t}\n}\n\nmodule.exports = $gOPD;\n", "'use strict';\n\n/** @type {import('.')} */\nvar $defineProperty = Object.defineProperty || false;\nif ($defineProperty) {\n\ttry {\n\t\t$defineProperty({}, 'a', { value: 1 });\n\t} catch (e) {\n\t\t// IE 8 has a broken defineProperty\n\t\t$defineProperty = false;\n\t}\n}\n\nmodule.exports = $defineProperty;\n", "'use strict';\n\n/** @type {import('./shams')} */\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n\tif (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') { return false; }\n\tif (typeof Symbol.iterator === 'symbol') { return true; }\n\n\t/** @type {{ [k in symbol]?: unknown }} */\n\tvar obj = {};\n\tvar sym = Symbol('test');\n\tvar symObj = Object(sym);\n\tif (typeof sym === 'string') { return false; }\n\n\tif (Object.prototype.toString.call(sym) !== '[object Symbol]') { return false; }\n\tif (Object.prototype.toString.call(symObj) !== '[object Symbol]') { return false; }\n\n\t// temp disabled per https://github.com/ljharb/object.assign/issues/17\n\t// if (sym instanceof Symbol) { return false; }\n\t// temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n\t// if (!(symObj instanceof Symbol)) { return false; }\n\n\t// if (typeof Symbol.prototype.toString !== 'function') { return false; }\n\t// if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n\tvar symVal = 42;\n\tobj[sym] = symVal;\n\tfor (var _ in obj) { return false; } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n\tif (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) { return false; }\n\n\tif (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) { return false; }\n\n\tvar syms = Object.getOwnPropertySymbols(obj);\n\tif (syms.length !== 1 || syms[0] !== sym) { return false; }\n\n\tif (!Object.prototype.propertyIsEnumerable.call(obj, sym)) { return false; }\n\n\tif (typeof Object.getOwnPropertyDescriptor === 'function') {\n\t\t// eslint-disable-next-line no-extra-parens\n\t\tvar descriptor = /** @type {PropertyDescriptor} */ (Object.getOwnPropertyDescriptor(obj, sym));\n\t\tif (descriptor.value !== symVal || descriptor.enumerable !== true) { return false; }\n\t}\n\n\treturn true;\n};\n", "'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasNativeSymbols() {\n\tif (typeof origSymbol !== 'function') { return false; }\n\tif (typeof Symbol !== 'function') { return false; }\n\tif (typeof origSymbol('foo') !== 'symbol') { return false; }\n\tif (typeof Symbol('bar') !== 'symbol') { return false; }\n\n\treturn hasSymbolSham();\n};\n", "'use strict';\n\n/** @type {import('./Reflect.getPrototypeOf')} */\nmodule.exports = (typeof Reflect !== 'undefined' && Reflect.getPrototypeOf) || null;\n", "'use strict';\n\nvar $Object = require('es-object-atoms');\n\n/** @type {import('./Object.getPrototypeOf')} */\nmodule.exports = $Object.getPrototypeOf || null;\n", "'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar toStr = Object.prototype.toString;\nvar max = Math.max;\nvar funcType = '[object Function]';\n\nvar concatty = function concatty(a, b) {\n    var arr = [];\n\n    for (var i = 0; i < a.length; i += 1) {\n        arr[i] = a[i];\n    }\n    for (var j = 0; j < b.length; j += 1) {\n        arr[j + a.length] = b[j];\n    }\n\n    return arr;\n};\n\nvar slicy = function slicy(arrLike, offset) {\n    var arr = [];\n    for (var i = offset || 0, j = 0; i < arrLike.length; i += 1, j += 1) {\n        arr[j] = arrLike[i];\n    }\n    return arr;\n};\n\nvar joiny = function (arr, joiner) {\n    var str = '';\n    for (var i = 0; i < arr.length; i += 1) {\n        str += arr[i];\n        if (i + 1 < arr.length) {\n            str += joiner;\n        }\n    }\n    return str;\n};\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.apply(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slicy(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                concatty(args, arguments)\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        }\n        return target.apply(\n            that,\n            concatty(args, arguments)\n        );\n\n    };\n\n    var boundLength = max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs[i] = '$' + i;\n    }\n\n    bound = Function('binder', 'return function (' + joiny(boundArgs, ',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n", "'use strict';\n\n/** @type {import('./functionCall')} */\nmodule.exports = Function.prototype.call;\n", "'use strict';\n\n/** @type {import('./functionApply')} */\nmodule.exports = Function.prototype.apply;\n", "'use strict';\n\n/** @type {import('./reflectApply')} */\nmodule.exports = typeof Reflect !== 'undefined' && Reflect && Reflect.apply;\n", "'use strict';\n\nvar bind = require('function-bind');\n\nvar $apply = require('./functionApply');\nvar $call = require('./functionCall');\nvar $reflectApply = require('./reflectApply');\n\n/** @type {import('./actualApply')} */\nmodule.exports = $reflectApply || bind.call($call, $apply);\n", "'use strict';\n\nvar bind = require('function-bind');\nvar $TypeError = require('es-errors/type');\n\nvar $call = require('./functionCall');\nvar $actualApply = require('./actualApply');\n\n/** @type {(args: [Function, thisArg?: unknown, ...args: unknown[]]) => Function} TODO FIXME, find a way to use import('.') */\nmodule.exports = function callBindBasic(args) {\n\tif (args.length < 1 || typeof args[0] !== 'function') {\n\t\tthrow new $TypeError('a function is required');\n\t}\n\treturn $actualApply(bind, $call, args);\n};\n", "'use strict';\n\nvar callBind = require('call-bind-apply-helpers');\nvar gOPD = require('gopd');\n\nvar hasProtoAccessor;\ntry {\n\t// eslint-disable-next-line no-extra-parens, no-proto\n\thasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */ ([]).__proto__ === Array.prototype;\n} catch (e) {\n\tif (!e || typeof e !== 'object' || !('code' in e) || e.code !== 'ERR_PROTO_ACCESS') {\n\t\tthrow e;\n\t}\n}\n\n// eslint-disable-next-line no-extra-parens\nvar desc = !!hasProtoAccessor && gOPD && gOPD(Object.prototype, /** @type {keyof typeof Object.prototype} */ ('__proto__'));\n\nvar $Object = Object;\nvar $getPrototypeOf = $Object.getPrototypeOf;\n\n/** @type {import('./get')} */\nmodule.exports = desc && typeof desc.get === 'function'\n\t? callBind([desc.get])\n\t: typeof $getPrototypeOf === 'function'\n\t\t? /** @type {import('./get')} */ function getDunder(value) {\n\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\treturn $getPrototypeOf(value == null ? value : $Object(value));\n\t\t}\n\t\t: false;\n", "'use strict';\n\nvar reflectGetProto = require('./Reflect.getPrototypeOf');\nvar originalGetProto = require('./Object.getPrototypeOf');\n\nvar getDunderProto = require('dunder-proto/get');\n\n/** @type {import('.')} */\nmodule.exports = reflectGetProto\n\t? function getProto(O) {\n\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\treturn reflectGetProto(O);\n\t}\n\t: originalGetProto\n\t\t? function getProto(O) {\n\t\t\tif (!O || (typeof O !== 'object' && typeof O !== 'function')) {\n\t\t\t\tthrow new TypeError('getProto: not an object');\n\t\t\t}\n\t\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\t\treturn originalGetProto(O);\n\t\t}\n\t\t: getDunderProto\n\t\t\t? function getProto(O) {\n\t\t\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\t\t\treturn getDunderProto(O);\n\t\t\t}\n\t\t\t: null;\n", "'use strict';\n\nvar call = Function.prototype.call;\nvar $hasOwn = Object.prototype.hasOwnProperty;\nvar bind = require('function-bind');\n\n/** @type {import('.')} */\nmodule.exports = bind.call(call, $hasOwn);\n", "'use strict';\n\nvar undefined;\n\nvar $Object = require('es-object-atoms');\n\nvar $Error = require('es-errors');\nvar $EvalError = require('es-errors/eval');\nvar $RangeError = require('es-errors/range');\nvar $ReferenceError = require('es-errors/ref');\nvar $SyntaxError = require('es-errors/syntax');\nvar $TypeError = require('es-errors/type');\nvar $URIError = require('es-errors/uri');\n\nvar abs = require('math-intrinsics/abs');\nvar floor = require('math-intrinsics/floor');\nvar max = require('math-intrinsics/max');\nvar min = require('math-intrinsics/min');\nvar pow = require('math-intrinsics/pow');\nvar round = require('math-intrinsics/round');\nvar sign = require('math-intrinsics/sign');\n\nvar $Function = Function;\n\n// eslint-disable-next-line consistent-return\nvar getEvalledConstructor = function (expressionSyntax) {\n\ttry {\n\t\treturn $Function('\"use strict\"; return (' + expressionSyntax + ').constructor;')();\n\t} catch (e) {}\n};\n\nvar $gOPD = require('gopd');\nvar $defineProperty = require('es-define-property');\n\nvar throwTypeError = function () {\n\tthrow new $TypeError();\n};\nvar ThrowTypeError = $gOPD\n\t? (function () {\n\t\ttry {\n\t\t\t// eslint-disable-next-line no-unused-expressions, no-caller, no-restricted-properties\n\t\t\targuments.callee; // IE 8 does not throw here\n\t\t\treturn throwTypeError;\n\t\t} catch (calleeThrows) {\n\t\t\ttry {\n\t\t\t\t// IE 8 throws on Object.getOwnPropertyDescriptor(arguments, '')\n\t\t\t\treturn $gOPD(arguments, 'callee').get;\n\t\t\t} catch (gOPDthrows) {\n\t\t\t\treturn throwTypeError;\n\t\t\t}\n\t\t}\n\t}())\n\t: throwTypeError;\n\nvar hasSymbols = require('has-symbols')();\n\nvar getProto = require('get-proto');\nvar $ObjectGPO = require('get-proto/Object.getPrototypeOf');\nvar $ReflectGPO = require('get-proto/Reflect.getPrototypeOf');\n\nvar $apply = require('call-bind-apply-helpers/functionApply');\nvar $call = require('call-bind-apply-helpers/functionCall');\n\nvar needsEval = {};\n\nvar TypedArray = typeof Uint8Array === 'undefined' || !getProto ? undefined : getProto(Uint8Array);\n\nvar INTRINSICS = {\n\t__proto__: null,\n\t'%AggregateError%': typeof AggregateError === 'undefined' ? undefined : AggregateError,\n\t'%Array%': Array,\n\t'%ArrayBuffer%': typeof ArrayBuffer === 'undefined' ? undefined : ArrayBuffer,\n\t'%ArrayIteratorPrototype%': hasSymbols && getProto ? getProto([][Symbol.iterator]()) : undefined,\n\t'%AsyncFromSyncIteratorPrototype%': undefined,\n\t'%AsyncFunction%': needsEval,\n\t'%AsyncGenerator%': needsEval,\n\t'%AsyncGeneratorFunction%': needsEval,\n\t'%AsyncIteratorPrototype%': needsEval,\n\t'%Atomics%': typeof Atomics === 'undefined' ? undefined : Atomics,\n\t'%BigInt%': typeof BigInt === 'undefined' ? undefined : BigInt,\n\t'%BigInt64Array%': typeof BigInt64Array === 'undefined' ? undefined : BigInt64Array,\n\t'%BigUint64Array%': typeof BigUint64Array === 'undefined' ? undefined : BigUint64Array,\n\t'%Boolean%': Boolean,\n\t'%DataView%': typeof DataView === 'undefined' ? undefined : DataView,\n\t'%Date%': Date,\n\t'%decodeURI%': decodeURI,\n\t'%decodeURIComponent%': decodeURIComponent,\n\t'%encodeURI%': encodeURI,\n\t'%encodeURIComponent%': encodeURIComponent,\n\t'%Error%': $Error,\n\t'%eval%': eval, // eslint-disable-line no-eval\n\t'%EvalError%': $EvalError,\n\t'%Float16Array%': typeof Float16Array === 'undefined' ? undefined : Float16Array,\n\t'%Float32Array%': typeof Float32Array === 'undefined' ? undefined : Float32Array,\n\t'%Float64Array%': typeof Float64Array === 'undefined' ? undefined : Float64Array,\n\t'%FinalizationRegistry%': typeof FinalizationRegistry === 'undefined' ? undefined : FinalizationRegistry,\n\t'%Function%': $Function,\n\t'%GeneratorFunction%': needsEval,\n\t'%Int8Array%': typeof Int8Array === 'undefined' ? undefined : Int8Array,\n\t'%Int16Array%': typeof Int16Array === 'undefined' ? undefined : Int16Array,\n\t'%Int32Array%': typeof Int32Array === 'undefined' ? undefined : Int32Array,\n\t'%isFinite%': isFinite,\n\t'%isNaN%': isNaN,\n\t'%IteratorPrototype%': hasSymbols && getProto ? getProto(getProto([][Symbol.iterator]())) : undefined,\n\t'%JSON%': typeof JSON === 'object' ? JSON : undefined,\n\t'%Map%': typeof Map === 'undefined' ? undefined : Map,\n\t'%MapIteratorPrototype%': typeof Map === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Map()[Symbol.iterator]()),\n\t'%Math%': Math,\n\t'%Number%': Number,\n\t'%Object%': $Object,\n\t'%Object.getOwnPropertyDescriptor%': $gOPD,\n\t'%parseFloat%': parseFloat,\n\t'%parseInt%': parseInt,\n\t'%Promise%': typeof Promise === 'undefined' ? undefined : Promise,\n\t'%Proxy%': typeof Proxy === 'undefined' ? undefined : Proxy,\n\t'%RangeError%': $RangeError,\n\t'%ReferenceError%': $ReferenceError,\n\t'%Reflect%': typeof Reflect === 'undefined' ? undefined : Reflect,\n\t'%RegExp%': RegExp,\n\t'%Set%': typeof Set === 'undefined' ? undefined : Set,\n\t'%SetIteratorPrototype%': typeof Set === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Set()[Symbol.iterator]()),\n\t'%SharedArrayBuffer%': typeof SharedArrayBuffer === 'undefined' ? undefined : SharedArrayBuffer,\n\t'%String%': String,\n\t'%StringIteratorPrototype%': hasSymbols && getProto ? getProto(''[Symbol.iterator]()) : undefined,\n\t'%Symbol%': hasSymbols ? Symbol : undefined,\n\t'%SyntaxError%': $SyntaxError,\n\t'%ThrowTypeError%': ThrowTypeError,\n\t'%TypedArray%': TypedArray,\n\t'%TypeError%': $TypeError,\n\t'%Uint8Array%': typeof Uint8Array === 'undefined' ? undefined : Uint8Array,\n\t'%Uint8ClampedArray%': typeof Uint8ClampedArray === 'undefined' ? undefined : Uint8ClampedArray,\n\t'%Uint16Array%': typeof Uint16Array === 'undefined' ? undefined : Uint16Array,\n\t'%Uint32Array%': typeof Uint32Array === 'undefined' ? undefined : Uint32Array,\n\t'%URIError%': $URIError,\n\t'%WeakMap%': typeof WeakMap === 'undefined' ? undefined : WeakMap,\n\t'%WeakRef%': typeof WeakRef === 'undefined' ? undefined : WeakRef,\n\t'%WeakSet%': typeof WeakSet === 'undefined' ? undefined : WeakSet,\n\n\t'%Function.prototype.call%': $call,\n\t'%Function.prototype.apply%': $apply,\n\t'%Object.defineProperty%': $defineProperty,\n\t'%Object.getPrototypeOf%': $ObjectGPO,\n\t'%Math.abs%': abs,\n\t'%Math.floor%': floor,\n\t'%Math.max%': max,\n\t'%Math.min%': min,\n\t'%Math.pow%': pow,\n\t'%Math.round%': round,\n\t'%Math.sign%': sign,\n\t'%Reflect.getPrototypeOf%': $ReflectGPO\n};\n\nif (getProto) {\n\ttry {\n\t\tnull.error; // eslint-disable-line no-unused-expressions\n\t} catch (e) {\n\t\t// https://github.com/tc39/proposal-shadowrealm/pull/384#issuecomment-1364264229\n\t\tvar errorProto = getProto(getProto(e));\n\t\tINTRINSICS['%Error.prototype%'] = errorProto;\n\t}\n}\n\nvar doEval = function doEval(name) {\n\tvar value;\n\tif (name === '%AsyncFunction%') {\n\t\tvalue = getEvalledConstructor('async function () {}');\n\t} else if (name === '%GeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('function* () {}');\n\t} else if (name === '%AsyncGeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('async function* () {}');\n\t} else if (name === '%AsyncGenerator%') {\n\t\tvar fn = doEval('%AsyncGeneratorFunction%');\n\t\tif (fn) {\n\t\t\tvalue = fn.prototype;\n\t\t}\n\t} else if (name === '%AsyncIteratorPrototype%') {\n\t\tvar gen = doEval('%AsyncGenerator%');\n\t\tif (gen && getProto) {\n\t\t\tvalue = getProto(gen.prototype);\n\t\t}\n\t}\n\n\tINTRINSICS[name] = value;\n\n\treturn value;\n};\n\nvar LEGACY_ALIASES = {\n\t__proto__: null,\n\t'%ArrayBufferPrototype%': ['ArrayBuffer', 'prototype'],\n\t'%ArrayPrototype%': ['Array', 'prototype'],\n\t'%ArrayProto_entries%': ['Array', 'prototype', 'entries'],\n\t'%ArrayProto_forEach%': ['Array', 'prototype', 'forEach'],\n\t'%ArrayProto_keys%': ['Array', 'prototype', 'keys'],\n\t'%ArrayProto_values%': ['Array', 'prototype', 'values'],\n\t'%AsyncFunctionPrototype%': ['AsyncFunction', 'prototype'],\n\t'%AsyncGenerator%': ['AsyncGeneratorFunction', 'prototype'],\n\t'%AsyncGeneratorPrototype%': ['AsyncGeneratorFunction', 'prototype', 'prototype'],\n\t'%BooleanPrototype%': ['Boolean', 'prototype'],\n\t'%DataViewPrototype%': ['DataView', 'prototype'],\n\t'%DatePrototype%': ['Date', 'prototype'],\n\t'%ErrorPrototype%': ['Error', 'prototype'],\n\t'%EvalErrorPrototype%': ['EvalError', 'prototype'],\n\t'%Float32ArrayPrototype%': ['Float32Array', 'prototype'],\n\t'%Float64ArrayPrototype%': ['Float64Array', 'prototype'],\n\t'%FunctionPrototype%': ['Function', 'prototype'],\n\t'%Generator%': ['GeneratorFunction', 'prototype'],\n\t'%GeneratorPrototype%': ['GeneratorFunction', 'prototype', 'prototype'],\n\t'%Int8ArrayPrototype%': ['Int8Array', 'prototype'],\n\t'%Int16ArrayPrototype%': ['Int16Array', 'prototype'],\n\t'%Int32ArrayPrototype%': ['Int32Array', 'prototype'],\n\t'%JSONParse%': ['JSON', 'parse'],\n\t'%JSONStringify%': ['JSON', 'stringify'],\n\t'%MapPrototype%': ['Map', 'prototype'],\n\t'%NumberPrototype%': ['Number', 'prototype'],\n\t'%ObjectPrototype%': ['Object', 'prototype'],\n\t'%ObjProto_toString%': ['Object', 'prototype', 'toString'],\n\t'%ObjProto_valueOf%': ['Object', 'prototype', 'valueOf'],\n\t'%PromisePrototype%': ['Promise', 'prototype'],\n\t'%PromiseProto_then%': ['Promise', 'prototype', 'then'],\n\t'%Promise_all%': ['Promise', 'all'],\n\t'%Promise_reject%': ['Promise', 'reject'],\n\t'%Promise_resolve%': ['Promise', 'resolve'],\n\t'%RangeErrorPrototype%': ['RangeError', 'prototype'],\n\t'%ReferenceErrorPrototype%': ['ReferenceError', 'prototype'],\n\t'%RegExpPrototype%': ['RegExp', 'prototype'],\n\t'%SetPrototype%': ['Set', 'prototype'],\n\t'%SharedArrayBufferPrototype%': ['SharedArrayBuffer', 'prototype'],\n\t'%StringPrototype%': ['String', 'prototype'],\n\t'%SymbolPrototype%': ['Symbol', 'prototype'],\n\t'%SyntaxErrorPrototype%': ['SyntaxError', 'prototype'],\n\t'%TypedArrayPrototype%': ['TypedArray', 'prototype'],\n\t'%TypeErrorPrototype%': ['TypeError', 'prototype'],\n\t'%Uint8ArrayPrototype%': ['Uint8Array', 'prototype'],\n\t'%Uint8ClampedArrayPrototype%': ['Uint8ClampedArray', 'prototype'],\n\t'%Uint16ArrayPrototype%': ['Uint16Array', 'prototype'],\n\t'%Uint32ArrayPrototype%': ['Uint32Array', 'prototype'],\n\t'%URIErrorPrototype%': ['URIError', 'prototype'],\n\t'%WeakMapPrototype%': ['WeakMap', 'prototype'],\n\t'%WeakSetPrototype%': ['WeakSet', 'prototype']\n};\n\nvar bind = require('function-bind');\nvar hasOwn = require('hasown');\nvar $concat = bind.call($call, Array.prototype.concat);\nvar $spliceApply = bind.call($apply, Array.prototype.splice);\nvar $replace = bind.call($call, String.prototype.replace);\nvar $strSlice = bind.call($call, String.prototype.slice);\nvar $exec = bind.call($call, RegExp.prototype.exec);\n\n/* adapted from https://github.com/lodash/lodash/blob/4.17.15/dist/lodash.js#L6735-L6744 */\nvar rePropName = /[^%.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|%$))/g;\nvar reEscapeChar = /\\\\(\\\\)?/g; /** Used to match backslashes in property paths. */\nvar stringToPath = function stringToPath(string) {\n\tvar first = $strSlice(string, 0, 1);\n\tvar last = $strSlice(string, -1);\n\tif (first === '%' && last !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected closing `%`');\n\t} else if (last === '%' && first !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected opening `%`');\n\t}\n\tvar result = [];\n\t$replace(string, rePropName, function (match, number, quote, subString) {\n\t\tresult[result.length] = quote ? $replace(subString, reEscapeChar, '$1') : number || match;\n\t});\n\treturn result;\n};\n/* end adaptation */\n\nvar getBaseIntrinsic = function getBaseIntrinsic(name, allowMissing) {\n\tvar intrinsicName = name;\n\tvar alias;\n\tif (hasOwn(LEGACY_ALIASES, intrinsicName)) {\n\t\talias = LEGACY_ALIASES[intrinsicName];\n\t\tintrinsicName = '%' + alias[0] + '%';\n\t}\n\n\tif (hasOwn(INTRINSICS, intrinsicName)) {\n\t\tvar value = INTRINSICS[intrinsicName];\n\t\tif (value === needsEval) {\n\t\t\tvalue = doEval(intrinsicName);\n\t\t}\n\t\tif (typeof value === 'undefined' && !allowMissing) {\n\t\t\tthrow new $TypeError('intrinsic ' + name + ' exists, but is not available. Please file an issue!');\n\t\t}\n\n\t\treturn {\n\t\t\talias: alias,\n\t\t\tname: intrinsicName,\n\t\t\tvalue: value\n\t\t};\n\t}\n\n\tthrow new $SyntaxError('intrinsic ' + name + ' does not exist!');\n};\n\nmodule.exports = function GetIntrinsic(name, allowMissing) {\n\tif (typeof name !== 'string' || name.length === 0) {\n\t\tthrow new $TypeError('intrinsic name must be a non-empty string');\n\t}\n\tif (arguments.length > 1 && typeof allowMissing !== 'boolean') {\n\t\tthrow new $TypeError('\"allowMissing\" argument must be a boolean');\n\t}\n\n\tif ($exec(/^%?[^%]*%?$/, name) === null) {\n\t\tthrow new $SyntaxError('`%` may not be present anywhere but at the beginning and end of the intrinsic name');\n\t}\n\tvar parts = stringToPath(name);\n\tvar intrinsicBaseName = parts.length > 0 ? parts[0] : '';\n\n\tvar intrinsic = getBaseIntrinsic('%' + intrinsicBaseName + '%', allowMissing);\n\tvar intrinsicRealName = intrinsic.name;\n\tvar value = intrinsic.value;\n\tvar skipFurtherCaching = false;\n\n\tvar alias = intrinsic.alias;\n\tif (alias) {\n\t\tintrinsicBaseName = alias[0];\n\t\t$spliceApply(parts, $concat([0, 1], alias));\n\t}\n\n\tfor (var i = 1, isOwn = true; i < parts.length; i += 1) {\n\t\tvar part = parts[i];\n\t\tvar first = $strSlice(part, 0, 1);\n\t\tvar last = $strSlice(part, -1);\n\t\tif (\n\t\t\t(\n\t\t\t\t(first === '\"' || first === \"'\" || first === '`')\n\t\t\t\t|| (last === '\"' || last === \"'\" || last === '`')\n\t\t\t)\n\t\t\t&& first !== last\n\t\t) {\n\t\t\tthrow new $SyntaxError('property names with quotes must have matching quotes');\n\t\t}\n\t\tif (part === 'constructor' || !isOwn) {\n\t\t\tskipFurtherCaching = true;\n\t\t}\n\n\t\tintrinsicBaseName += '.' + part;\n\t\tintrinsicRealName = '%' + intrinsicBaseName + '%';\n\n\t\tif (hasOwn(INTRINSICS, intrinsicRealName)) {\n\t\t\tvalue = INTRINSICS[intrinsicRealName];\n\t\t} else if (value != null) {\n\t\t\tif (!(part in value)) {\n\t\t\t\tif (!allowMissing) {\n\t\t\t\t\tthrow new $TypeError('base intrinsic for ' + name + ' exists, but the property is not available.');\n\t\t\t\t}\n\t\t\t\treturn void undefined;\n\t\t\t}\n\t\t\tif ($gOPD && (i + 1) >= parts.length) {\n\t\t\t\tvar desc = $gOPD(value, part);\n\t\t\t\tisOwn = !!desc;\n\n\t\t\t\t// By convention, when a data property is converted to an accessor\n\t\t\t\t// property to emulate a data property that does not suffer from\n\t\t\t\t// the override mistake, that accessor's getter is marked with\n\t\t\t\t// an `originalValue` property. Here, when we detect this, we\n\t\t\t\t// uphold the illusion by pretending to see that original data\n\t\t\t\t// property, i.e., returning the value rather than the getter\n\t\t\t\t// itself.\n\t\t\t\tif (isOwn && 'get' in desc && !('originalValue' in desc.get)) {\n\t\t\t\t\tvalue = desc.get;\n\t\t\t\t} else {\n\t\t\t\t\tvalue = value[part];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tisOwn = hasOwn(value, part);\n\t\t\t\tvalue = value[part];\n\t\t\t}\n\n\t\t\tif (isOwn && !skipFurtherCaching) {\n\t\t\t\tINTRINSICS[intrinsicRealName] = value;\n\t\t\t}\n\t\t}\n\t}\n\treturn value;\n};\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBindBasic = require('call-bind-apply-helpers');\n\n/** @type {(thisArg: string, searchString: string, position?: number) => number} */\nvar $indexOf = callBindBasic([GetIntrinsic('%String.prototype.indexOf%')]);\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n\t/* eslint no-extra-parens: 0 */\n\n\tvar intrinsic = /** @type {(this: unknown, ...args: unknown[]) => unknown} */ (GetIntrinsic(name, !!allowMissing));\n\tif (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n\t\treturn callBindBasic(/** @type {const} */ ([intrinsic]));\n\t}\n\treturn intrinsic;\n};\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\n\nvar $TypeError = require('es-errors/type');\nvar $Map = GetIntrinsic('%Map%', true);\n\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => V} */\nvar $mapGet = callBound('Map.prototype.get', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K, value: V) => void} */\nvar $mapSet = callBound('Map.prototype.set', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapHas = callBound('Map.prototype.has', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapDelete = callBound('Map.prototype.delete', true);\n/** @type {<K, V>(thisArg: Map<K, V>) => number} */\nvar $mapSize = callBound('Map.prototype.size', true);\n\n/** @type {import('.')} */\nmodule.exports = !!$Map && /** @type {Exclude<import('.'), false>} */ function getSideChannelMap() {\n\t/** @typedef {ReturnType<typeof getSideChannelMap>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {Map<K, V> | undefined} */ var $m;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tif ($m) {\n\t\t\t\tvar result = $mapDelete($m, key);\n\t\t\t\tif ($mapSize($m) === 0) {\n\t\t\t\t\t$m = void undefined;\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tget: function (key) { // eslint-disable-line consistent-return\n\t\t\tif ($m) {\n\t\t\t\treturn $mapGet($m, key);\n\t\t\t}\n\t\t},\n\t\thas: function (key) {\n\t\t\tif ($m) {\n\t\t\t\treturn $mapHas($m, key);\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$m) {\n\t\t\t\t// @ts-expect-error TS can't handle narrowing a variable inside a closure\n\t\t\t\t$m = new $Map();\n\t\t\t}\n\t\t\t$mapSet($m, key, value);\n\t\t}\n\t};\n\n\t// @ts-expect-error TODO: figure out why TS is erroring here\n\treturn channel;\n};\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\nvar getSideChannelMap = require('side-channel-map');\n\nvar $TypeError = require('es-errors/type');\nvar $WeakMap = GetIntrinsic('%WeakMap%', true);\n\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => V} */\nvar $weakMapGet = callBound('WeakMap.prototype.get', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K, value: V) => void} */\nvar $weakMapSet = callBound('WeakMap.prototype.set', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapHas = callBound('WeakMap.prototype.has', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapDelete = callBound('WeakMap.prototype.delete', true);\n\n/** @type {import('.')} */\nmodule.exports = $WeakMap\n\t? /** @type {Exclude<import('.'), false>} */ function getSideChannelWeakMap() {\n\t\t/** @typedef {ReturnType<typeof getSideChannelWeakMap>} Channel */\n\t\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t\t/** @type {WeakMap<K & object, V> | undefined} */ var $wm;\n\t\t/** @type {Channel | undefined} */ var $m;\n\n\t\t/** @type {Channel} */\n\t\tvar channel = {\n\t\t\tassert: function (key) {\n\t\t\t\tif (!channel.has(key)) {\n\t\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t\t}\n\t\t\t},\n\t\t\t'delete': function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapDelete($wm, key);\n\t\t\t\t\t}\n\t\t\t\t} else if (getSideChannelMap) {\n\t\t\t\t\tif ($m) {\n\t\t\t\t\t\treturn $m['delete'](key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tget: function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapGet($wm, key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn $m && $m.get(key);\n\t\t\t},\n\t\t\thas: function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapHas($wm, key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn !!$m && $m.has(key);\n\t\t\t},\n\t\t\tset: function (key, value) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif (!$wm) {\n\t\t\t\t\t\t$wm = new $WeakMap();\n\t\t\t\t\t}\n\t\t\t\t\t$weakMapSet($wm, key, value);\n\t\t\t\t} else if (getSideChannelMap) {\n\t\t\t\t\tif (!$m) {\n\t\t\t\t\t\t$m = getSideChannelMap();\n\t\t\t\t\t}\n\t\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\t\t/** @type {NonNullable<typeof $m>} */ ($m).set(key, value);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\t// @ts-expect-error TODO: figure out why this is erroring\n\t\treturn channel;\n\t}\n\t: getSideChannelMap;\n", "'use strict';\n\nvar $TypeError = require('es-errors/type');\nvar inspect = require('object-inspect');\nvar getSideChannelList = require('side-channel-list');\nvar getSideChannelMap = require('side-channel-map');\nvar getSideChannelWeakMap = require('side-channel-weakmap');\n\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannel() {\n\t/** @typedef {ReturnType<typeof getSideChannel>} Channel */\n\n\t/** @type {Channel | undefined} */ var $channelData;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\treturn !!$channelData && $channelData['delete'](key);\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn $channelData && $channelData.get(key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn !!$channelData && $channelData.has(key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$channelData) {\n\t\t\t\t$channelData = makeChannel();\n\t\t\t}\n\n\t\t\t$channelData.set(key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n", "'use strict';\n\nvar replace = String.prototype.replace;\nvar percentTwenties = /%20/g;\n\nvar Format = {\n    RFC1738: 'RFC1738',\n    RFC3986: 'RFC3986'\n};\n\nmodule.exports = {\n    'default': Format.RFC3986,\n    formatters: {\n        RFC1738: function (value) {\n            return replace.call(value, percentTwenties, '+');\n        },\n        RFC3986: function (value) {\n            return String(value);\n        }\n    },\n    RFC1738: Format.RFC1738,\n    RFC3986: Format.RFC3986\n};\n", "'use strict';\n\nvar formats = require('./formats');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar hexTable = (function () {\n    var array = [];\n    for (var i = 0; i < 256; ++i) {\n        array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());\n    }\n\n    return array;\n}());\n\nvar compactQueue = function compactQueue(queue) {\n    while (queue.length > 1) {\n        var item = queue.pop();\n        var obj = item.obj[item.prop];\n\n        if (isArray(obj)) {\n            var compacted = [];\n\n            for (var j = 0; j < obj.length; ++j) {\n                if (typeof obj[j] !== 'undefined') {\n                    compacted.push(obj[j]);\n                }\n            }\n\n            item.obj[item.prop] = compacted;\n        }\n    }\n};\n\nvar arrayToObject = function arrayToObject(source, options) {\n    var obj = options && options.plainObjects ? { __proto__: null } : {};\n    for (var i = 0; i < source.length; ++i) {\n        if (typeof source[i] !== 'undefined') {\n            obj[i] = source[i];\n        }\n    }\n\n    return obj;\n};\n\nvar merge = function merge(target, source, options) {\n    /* eslint no-param-reassign: 0 */\n    if (!source) {\n        return target;\n    }\n\n    if (typeof source !== 'object' && typeof source !== 'function') {\n        if (isArray(target)) {\n            target.push(source);\n        } else if (target && typeof target === 'object') {\n            if (\n                (options && (options.plainObjects || options.allowPrototypes))\n                || !has.call(Object.prototype, source)\n            ) {\n                target[source] = true;\n            }\n        } else {\n            return [target, source];\n        }\n\n        return target;\n    }\n\n    if (!target || typeof target !== 'object') {\n        return [target].concat(source);\n    }\n\n    var mergeTarget = target;\n    if (isArray(target) && !isArray(source)) {\n        mergeTarget = arrayToObject(target, options);\n    }\n\n    if (isArray(target) && isArray(source)) {\n        source.forEach(function (item, i) {\n            if (has.call(target, i)) {\n                var targetItem = target[i];\n                if (targetItem && typeof targetItem === 'object' && item && typeof item === 'object') {\n                    target[i] = merge(targetItem, item, options);\n                } else {\n                    target.push(item);\n                }\n            } else {\n                target[i] = item;\n            }\n        });\n        return target;\n    }\n\n    return Object.keys(source).reduce(function (acc, key) {\n        var value = source[key];\n\n        if (has.call(acc, key)) {\n            acc[key] = merge(acc[key], value, options);\n        } else {\n            acc[key] = value;\n        }\n        return acc;\n    }, mergeTarget);\n};\n\nvar assign = function assignSingleSource(target, source) {\n    return Object.keys(source).reduce(function (acc, key) {\n        acc[key] = source[key];\n        return acc;\n    }, target);\n};\n\nvar decode = function (str, defaultDecoder, charset) {\n    var strWithoutPlus = str.replace(/\\+/g, ' ');\n    if (charset === 'iso-8859-1') {\n        // unescape never throws, no try...catch needed:\n        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);\n    }\n    // utf-8\n    try {\n        return decodeURIComponent(strWithoutPlus);\n    } catch (e) {\n        return strWithoutPlus;\n    }\n};\n\nvar limit = 1024;\n\n/* eslint operator-linebreak: [2, \"before\"] */\n\nvar encode = function encode(str, defaultEncoder, charset, kind, format) {\n    // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n    // It has been adapted here for stricter adherence to RFC 3986\n    if (str.length === 0) {\n        return str;\n    }\n\n    var string = str;\n    if (typeof str === 'symbol') {\n        string = Symbol.prototype.toString.call(str);\n    } else if (typeof str !== 'string') {\n        string = String(str);\n    }\n\n    if (charset === 'iso-8859-1') {\n        return escape(string).replace(/%u[0-9a-f]{4}/gi, function ($0) {\n            return '%26%23' + parseInt($0.slice(2), 16) + '%3B';\n        });\n    }\n\n    var out = '';\n    for (var j = 0; j < string.length; j += limit) {\n        var segment = string.length >= limit ? string.slice(j, j + limit) : string;\n        var arr = [];\n\n        for (var i = 0; i < segment.length; ++i) {\n            var c = segment.charCodeAt(i);\n            if (\n                c === 0x2D // -\n                || c === 0x2E // .\n                || c === 0x5F // _\n                || c === 0x7E // ~\n                || (c >= 0x30 && c <= 0x39) // 0-9\n                || (c >= 0x41 && c <= 0x5A) // a-z\n                || (c >= 0x61 && c <= 0x7A) // A-Z\n                || (format === formats.RFC1738 && (c === 0x28 || c === 0x29)) // ( )\n            ) {\n                arr[arr.length] = segment.charAt(i);\n                continue;\n            }\n\n            if (c < 0x80) {\n                arr[arr.length] = hexTable[c];\n                continue;\n            }\n\n            if (c < 0x800) {\n                arr[arr.length] = hexTable[0xC0 | (c >> 6)]\n                    + hexTable[0x80 | (c & 0x3F)];\n                continue;\n            }\n\n            if (c < 0xD800 || c >= 0xE000) {\n                arr[arr.length] = hexTable[0xE0 | (c >> 12)]\n                    + hexTable[0x80 | ((c >> 6) & 0x3F)]\n                    + hexTable[0x80 | (c & 0x3F)];\n                continue;\n            }\n\n            i += 1;\n            c = 0x10000 + (((c & 0x3FF) << 10) | (segment.charCodeAt(i) & 0x3FF));\n\n            arr[arr.length] = hexTable[0xF0 | (c >> 18)]\n                + hexTable[0x80 | ((c >> 12) & 0x3F)]\n                + hexTable[0x80 | ((c >> 6) & 0x3F)]\n                + hexTable[0x80 | (c & 0x3F)];\n        }\n\n        out += arr.join('');\n    }\n\n    return out;\n};\n\nvar compact = function compact(value) {\n    var queue = [{ obj: { o: value }, prop: 'o' }];\n    var refs = [];\n\n    for (var i = 0; i < queue.length; ++i) {\n        var item = queue[i];\n        var obj = item.obj[item.prop];\n\n        var keys = Object.keys(obj);\n        for (var j = 0; j < keys.length; ++j) {\n            var key = keys[j];\n            var val = obj[key];\n            if (typeof val === 'object' && val !== null && refs.indexOf(val) === -1) {\n                queue.push({ obj: obj, prop: key });\n                refs.push(val);\n            }\n        }\n    }\n\n    compactQueue(queue);\n\n    return value;\n};\n\nvar isRegExp = function isRegExp(obj) {\n    return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\n\nvar isBuffer = function isBuffer(obj) {\n    if (!obj || typeof obj !== 'object') {\n        return false;\n    }\n\n    return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));\n};\n\nvar combine = function combine(a, b) {\n    return [].concat(a, b);\n};\n\nvar maybeMap = function maybeMap(val, fn) {\n    if (isArray(val)) {\n        var mapped = [];\n        for (var i = 0; i < val.length; i += 1) {\n            mapped.push(fn(val[i]));\n        }\n        return mapped;\n    }\n    return fn(val);\n};\n\nmodule.exports = {\n    arrayToObject: arrayToObject,\n    assign: assign,\n    combine: combine,\n    compact: compact,\n    decode: decode,\n    encode: encode,\n    isBuffer: isBuffer,\n    isRegExp: isRegExp,\n    maybeMap: maybeMap,\n    merge: merge\n};\n", "'use strict';\n\nvar getSideChannel = require('side-channel');\nvar utils = require('./utils');\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\n\nvar arrayPrefixGenerators = {\n    brackets: function brackets(prefix) {\n        return prefix + '[]';\n    },\n    comma: 'comma',\n    indices: function indices(prefix, key) {\n        return prefix + '[' + key + ']';\n    },\n    repeat: function repeat(prefix) {\n        return prefix;\n    }\n};\n\nvar isArray = Array.isArray;\nvar push = Array.prototype.push;\nvar pushToArray = function (arr, valueOrArray) {\n    push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);\n};\n\nvar toISO = Date.prototype.toISOString;\n\nvar defaultFormat = formats['default'];\nvar defaults = {\n    addQueryPrefix: false,\n    allowDots: false,\n    allowEmptyArrays: false,\n    arrayFormat: 'indices',\n    charset: 'utf-8',\n    charsetSentinel: false,\n    commaRoundTrip: false,\n    delimiter: '&',\n    encode: true,\n    encodeDotInKeys: false,\n    encoder: utils.encode,\n    encodeValuesOnly: false,\n    filter: void undefined,\n    format: defaultFormat,\n    formatter: formats.formatters[defaultFormat],\n    // deprecated\n    indices: false,\n    serializeDate: function serializeDate(date) {\n        return toISO.call(date);\n    },\n    skipNulls: false,\n    strictNullHandling: false\n};\n\nvar isNonNullishPrimitive = function isNonNullishPrimitive(v) {\n    return typeof v === 'string'\n        || typeof v === 'number'\n        || typeof v === 'boolean'\n        || typeof v === 'symbol'\n        || typeof v === 'bigint';\n};\n\nvar sentinel = {};\n\nvar stringify = function stringify(\n    object,\n    prefix,\n    generateArrayPrefix,\n    commaRoundTrip,\n    allowEmptyArrays,\n    strictNullHandling,\n    skipNulls,\n    encodeDotInKeys,\n    encoder,\n    filter,\n    sort,\n    allowDots,\n    serializeDate,\n    format,\n    formatter,\n    encodeValuesOnly,\n    charset,\n    sideChannel\n) {\n    var obj = object;\n\n    var tmpSc = sideChannel;\n    var step = 0;\n    var findFlag = false;\n    while ((tmpSc = tmpSc.get(sentinel)) !== void undefined && !findFlag) {\n        // Where object last appeared in the ref tree\n        var pos = tmpSc.get(object);\n        step += 1;\n        if (typeof pos !== 'undefined') {\n            if (pos === step) {\n                throw new RangeError('Cyclic object value');\n            } else {\n                findFlag = true; // Break while\n            }\n        }\n        if (typeof tmpSc.get(sentinel) === 'undefined') {\n            step = 0;\n        }\n    }\n\n    if (typeof filter === 'function') {\n        obj = filter(prefix, obj);\n    } else if (obj instanceof Date) {\n        obj = serializeDate(obj);\n    } else if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        obj = utils.maybeMap(obj, function (value) {\n            if (value instanceof Date) {\n                return serializeDate(value);\n            }\n            return value;\n        });\n    }\n\n    if (obj === null) {\n        if (strictNullHandling) {\n            return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, 'key', format) : prefix;\n        }\n\n        obj = '';\n    }\n\n    if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {\n        if (encoder) {\n            var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, 'key', format);\n            return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults.encoder, charset, 'value', format))];\n        }\n        return [formatter(prefix) + '=' + formatter(String(obj))];\n    }\n\n    var values = [];\n\n    if (typeof obj === 'undefined') {\n        return values;\n    }\n\n    var objKeys;\n    if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        // we need to join elements in\n        if (encodeValuesOnly && encoder) {\n            obj = utils.maybeMap(obj, encoder);\n        }\n        objKeys = [{ value: obj.length > 0 ? obj.join(',') || null : void undefined }];\n    } else if (isArray(filter)) {\n        objKeys = filter;\n    } else {\n        var keys = Object.keys(obj);\n        objKeys = sort ? keys.sort(sort) : keys;\n    }\n\n    var encodedPrefix = encodeDotInKeys ? String(prefix).replace(/\\./g, '%2E') : String(prefix);\n\n    var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? encodedPrefix + '[]' : encodedPrefix;\n\n    if (allowEmptyArrays && isArray(obj) && obj.length === 0) {\n        return adjustedPrefix + '[]';\n    }\n\n    for (var j = 0; j < objKeys.length; ++j) {\n        var key = objKeys[j];\n        var value = typeof key === 'object' && key && typeof key.value !== 'undefined'\n            ? key.value\n            : obj[key];\n\n        if (skipNulls && value === null) {\n            continue;\n        }\n\n        var encodedKey = allowDots && encodeDotInKeys ? String(key).replace(/\\./g, '%2E') : String(key);\n        var keyPrefix = isArray(obj)\n            ? typeof generateArrayPrefix === 'function' ? generateArrayPrefix(adjustedPrefix, encodedKey) : adjustedPrefix\n            : adjustedPrefix + (allowDots ? '.' + encodedKey : '[' + encodedKey + ']');\n\n        sideChannel.set(object, step);\n        var valueSideChannel = getSideChannel();\n        valueSideChannel.set(sentinel, sideChannel);\n        pushToArray(values, stringify(\n            value,\n            keyPrefix,\n            generateArrayPrefix,\n            commaRoundTrip,\n            allowEmptyArrays,\n            strictNullHandling,\n            skipNulls,\n            encodeDotInKeys,\n            generateArrayPrefix === 'comma' && encodeValuesOnly && isArray(obj) ? null : encoder,\n            filter,\n            sort,\n            allowDots,\n            serializeDate,\n            format,\n            formatter,\n            encodeValuesOnly,\n            charset,\n            valueSideChannel\n        ));\n    }\n\n    return values;\n};\n\nvar normalizeStringifyOptions = function normalizeStringifyOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (typeof opts.allowEmptyArrays !== 'undefined' && typeof opts.allowEmptyArrays !== 'boolean') {\n        throw new TypeError('`allowEmptyArrays` option can only be `true` or `false`, when provided');\n    }\n\n    if (typeof opts.encodeDotInKeys !== 'undefined' && typeof opts.encodeDotInKeys !== 'boolean') {\n        throw new TypeError('`encodeDotInKeys` option can only be `true` or `false`, when provided');\n    }\n\n    if (opts.encoder !== null && typeof opts.encoder !== 'undefined' && typeof opts.encoder !== 'function') {\n        throw new TypeError('Encoder has to be a function.');\n    }\n\n    var charset = opts.charset || defaults.charset;\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n\n    var format = formats['default'];\n    if (typeof opts.format !== 'undefined') {\n        if (!has.call(formats.formatters, opts.format)) {\n            throw new TypeError('Unknown format option provided.');\n        }\n        format = opts.format;\n    }\n    var formatter = formats.formatters[format];\n\n    var filter = defaults.filter;\n    if (typeof opts.filter === 'function' || isArray(opts.filter)) {\n        filter = opts.filter;\n    }\n\n    var arrayFormat;\n    if (opts.arrayFormat in arrayPrefixGenerators) {\n        arrayFormat = opts.arrayFormat;\n    } else if ('indices' in opts) {\n        arrayFormat = opts.indices ? 'indices' : 'repeat';\n    } else {\n        arrayFormat = defaults.arrayFormat;\n    }\n\n    if ('commaRoundTrip' in opts && typeof opts.commaRoundTrip !== 'boolean') {\n        throw new TypeError('`commaRoundTrip` must be a boolean, or absent');\n    }\n\n    var allowDots = typeof opts.allowDots === 'undefined' ? opts.encodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;\n\n    return {\n        addQueryPrefix: typeof opts.addQueryPrefix === 'boolean' ? opts.addQueryPrefix : defaults.addQueryPrefix,\n        allowDots: allowDots,\n        allowEmptyArrays: typeof opts.allowEmptyArrays === 'boolean' ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,\n        arrayFormat: arrayFormat,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        commaRoundTrip: !!opts.commaRoundTrip,\n        delimiter: typeof opts.delimiter === 'undefined' ? defaults.delimiter : opts.delimiter,\n        encode: typeof opts.encode === 'boolean' ? opts.encode : defaults.encode,\n        encodeDotInKeys: typeof opts.encodeDotInKeys === 'boolean' ? opts.encodeDotInKeys : defaults.encodeDotInKeys,\n        encoder: typeof opts.encoder === 'function' ? opts.encoder : defaults.encoder,\n        encodeValuesOnly: typeof opts.encodeValuesOnly === 'boolean' ? opts.encodeValuesOnly : defaults.encodeValuesOnly,\n        filter: filter,\n        format: format,\n        formatter: formatter,\n        serializeDate: typeof opts.serializeDate === 'function' ? opts.serializeDate : defaults.serializeDate,\n        skipNulls: typeof opts.skipNulls === 'boolean' ? opts.skipNulls : defaults.skipNulls,\n        sort: typeof opts.sort === 'function' ? opts.sort : null,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n    };\n};\n\nmodule.exports = function (object, opts) {\n    var obj = object;\n    var options = normalizeStringifyOptions(opts);\n\n    var objKeys;\n    var filter;\n\n    if (typeof options.filter === 'function') {\n        filter = options.filter;\n        obj = filter('', obj);\n    } else if (isArray(options.filter)) {\n        filter = options.filter;\n        objKeys = filter;\n    }\n\n    var keys = [];\n\n    if (typeof obj !== 'object' || obj === null) {\n        return '';\n    }\n\n    var generateArrayPrefix = arrayPrefixGenerators[options.arrayFormat];\n    var commaRoundTrip = generateArrayPrefix === 'comma' && options.commaRoundTrip;\n\n    if (!objKeys) {\n        objKeys = Object.keys(obj);\n    }\n\n    if (options.sort) {\n        objKeys.sort(options.sort);\n    }\n\n    var sideChannel = getSideChannel();\n    for (var i = 0; i < objKeys.length; ++i) {\n        var key = objKeys[i];\n        var value = obj[key];\n\n        if (options.skipNulls && value === null) {\n            continue;\n        }\n        pushToArray(keys, stringify(\n            value,\n            key,\n            generateArrayPrefix,\n            commaRoundTrip,\n            options.allowEmptyArrays,\n            options.strictNullHandling,\n            options.skipNulls,\n            options.encodeDotInKeys,\n            options.encode ? options.encoder : null,\n            options.filter,\n            options.sort,\n            options.allowDots,\n            options.serializeDate,\n            options.format,\n            options.formatter,\n            options.encodeValuesOnly,\n            options.charset,\n            sideChannel\n        ));\n    }\n\n    var joined = keys.join(options.delimiter);\n    var prefix = options.addQueryPrefix === true ? '?' : '';\n\n    if (options.charsetSentinel) {\n        if (options.charset === 'iso-8859-1') {\n            // encodeURIComponent('&#10003;'), the \"numeric entity\" representation of a checkmark\n            prefix += 'utf8=%26%2310003%3B&';\n        } else {\n            // encodeURIComponent('✓')\n            prefix += 'utf8=%E2%9C%93&';\n        }\n    }\n\n    return joined.length > 0 ? prefix + joined : '';\n};\n", "'use strict';\n\nvar utils = require('./utils');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar defaults = {\n    allowDots: false,\n    allowEmptyArrays: false,\n    allowPrototypes: false,\n    allowSparse: false,\n    arrayLimit: 20,\n    charset: 'utf-8',\n    charsetSentinel: false,\n    comma: false,\n    decodeDotInKeys: false,\n    decoder: utils.decode,\n    delimiter: '&',\n    depth: 5,\n    duplicates: 'combine',\n    ignoreQueryPrefix: false,\n    interpretNumericEntities: false,\n    parameterLimit: 1000,\n    parseArrays: true,\n    plainObjects: false,\n    strictDepth: false,\n    strictNullHandling: false,\n    throwOnLimitExceeded: false\n};\n\nvar interpretNumericEntities = function (str) {\n    return str.replace(/&#(\\d+);/g, function ($0, numberStr) {\n        return String.fromCharCode(parseInt(numberStr, 10));\n    });\n};\n\nvar parseArrayValue = function (val, options, currentArrayLength) {\n    if (val && typeof val === 'string' && options.comma && val.indexOf(',') > -1) {\n        return val.split(',');\n    }\n\n    if (options.throwOnLimitExceeded && currentArrayLength >= options.arrayLimit) {\n        throw new RangeError('Array limit exceeded. Only ' + options.arrayLimit + ' element' + (options.arrayLimit === 1 ? '' : 's') + ' allowed in an array.');\n    }\n\n    return val;\n};\n\n// This is what browsers will submit when the ✓ character occurs in an\n// application/x-www-form-urlencoded body and the encoding of the page containing\n// the form is iso-8859-1, or when the submitted form has an accept-charset\n// attribute of iso-8859-1. Presumably also with other charsets that do not contain\n// the ✓ character, such as us-ascii.\nvar isoSentinel = 'utf8=%26%2310003%3B'; // encodeURIComponent('&#10003;')\n\n// These are the percent-encoded utf-8 octets representing a checkmark, indicating that the request actually is utf-8 encoded.\nvar charsetSentinel = 'utf8=%E2%9C%93'; // encodeURIComponent('✓')\n\nvar parseValues = function parseQueryStringValues(str, options) {\n    var obj = { __proto__: null };\n\n    var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\\?/, '') : str;\n    cleanStr = cleanStr.replace(/%5B/gi, '[').replace(/%5D/gi, ']');\n\n    var limit = options.parameterLimit === Infinity ? undefined : options.parameterLimit;\n    var parts = cleanStr.split(\n        options.delimiter,\n        options.throwOnLimitExceeded ? limit + 1 : limit\n    );\n\n    if (options.throwOnLimitExceeded && parts.length > limit) {\n        throw new RangeError('Parameter limit exceeded. Only ' + limit + ' parameter' + (limit === 1 ? '' : 's') + ' allowed.');\n    }\n\n    var skipIndex = -1; // Keep track of where the utf8 sentinel was found\n    var i;\n\n    var charset = options.charset;\n    if (options.charsetSentinel) {\n        for (i = 0; i < parts.length; ++i) {\n            if (parts[i].indexOf('utf8=') === 0) {\n                if (parts[i] === charsetSentinel) {\n                    charset = 'utf-8';\n                } else if (parts[i] === isoSentinel) {\n                    charset = 'iso-8859-1';\n                }\n                skipIndex = i;\n                i = parts.length; // The eslint settings do not allow break;\n            }\n        }\n    }\n\n    for (i = 0; i < parts.length; ++i) {\n        if (i === skipIndex) {\n            continue;\n        }\n        var part = parts[i];\n\n        var bracketEqualsPos = part.indexOf(']=');\n        var pos = bracketEqualsPos === -1 ? part.indexOf('=') : bracketEqualsPos + 1;\n\n        var key;\n        var val;\n        if (pos === -1) {\n            key = options.decoder(part, defaults.decoder, charset, 'key');\n            val = options.strictNullHandling ? null : '';\n        } else {\n            key = options.decoder(part.slice(0, pos), defaults.decoder, charset, 'key');\n\n            val = utils.maybeMap(\n                parseArrayValue(\n                    part.slice(pos + 1),\n                    options,\n                    isArray(obj[key]) ? obj[key].length : 0\n                ),\n                function (encodedVal) {\n                    return options.decoder(encodedVal, defaults.decoder, charset, 'value');\n                }\n            );\n        }\n\n        if (val && options.interpretNumericEntities && charset === 'iso-8859-1') {\n            val = interpretNumericEntities(String(val));\n        }\n\n        if (part.indexOf('[]=') > -1) {\n            val = isArray(val) ? [val] : val;\n        }\n\n        var existing = has.call(obj, key);\n        if (existing && options.duplicates === 'combine') {\n            obj[key] = utils.combine(obj[key], val);\n        } else if (!existing || options.duplicates === 'last') {\n            obj[key] = val;\n        }\n    }\n\n    return obj;\n};\n\nvar parseObject = function (chain, val, options, valuesParsed) {\n    var currentArrayLength = 0;\n    if (chain.length > 0 && chain[chain.length - 1] === '[]') {\n        var parentKey = chain.slice(0, -1).join('');\n        currentArrayLength = Array.isArray(val) && val[parentKey] ? val[parentKey].length : 0;\n    }\n\n    var leaf = valuesParsed ? val : parseArrayValue(val, options, currentArrayLength);\n\n    for (var i = chain.length - 1; i >= 0; --i) {\n        var obj;\n        var root = chain[i];\n\n        if (root === '[]' && options.parseArrays) {\n            obj = options.allowEmptyArrays && (leaf === '' || (options.strictNullHandling && leaf === null))\n                ? []\n                : utils.combine([], leaf);\n        } else {\n            obj = options.plainObjects ? { __proto__: null } : {};\n            var cleanRoot = root.charAt(0) === '[' && root.charAt(root.length - 1) === ']' ? root.slice(1, -1) : root;\n            var decodedRoot = options.decodeDotInKeys ? cleanRoot.replace(/%2E/g, '.') : cleanRoot;\n            var index = parseInt(decodedRoot, 10);\n            if (!options.parseArrays && decodedRoot === '') {\n                obj = { 0: leaf };\n            } else if (\n                !isNaN(index)\n                && root !== decodedRoot\n                && String(index) === decodedRoot\n                && index >= 0\n                && (options.parseArrays && index <= options.arrayLimit)\n            ) {\n                obj = [];\n                obj[index] = leaf;\n            } else if (decodedRoot !== '__proto__') {\n                obj[decodedRoot] = leaf;\n            }\n        }\n\n        leaf = obj;\n    }\n\n    return leaf;\n};\n\nvar parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {\n    if (!givenKey) {\n        return;\n    }\n\n    // Transform dot notation to bracket notation\n    var key = options.allowDots ? givenKey.replace(/\\.([^.[]+)/g, '[$1]') : givenKey;\n\n    // The regex chunks\n\n    var brackets = /(\\[[^[\\]]*])/;\n    var child = /(\\[[^[\\]]*])/g;\n\n    // Get the parent\n\n    var segment = options.depth > 0 && brackets.exec(key);\n    var parent = segment ? key.slice(0, segment.index) : key;\n\n    // Stash the parent if it exists\n\n    var keys = [];\n    if (parent) {\n        // If we aren't using plain objects, optionally prefix keys that would overwrite object prototype properties\n        if (!options.plainObjects && has.call(Object.prototype, parent)) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n\n        keys.push(parent);\n    }\n\n    // Loop through children appending to the array until we hit depth\n\n    var i = 0;\n    while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {\n        i += 1;\n        if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n        keys.push(segment[1]);\n    }\n\n    // If there's a remainder, check strictDepth option for throw, else just add whatever is left\n\n    if (segment) {\n        if (options.strictDepth === true) {\n            throw new RangeError('Input depth exceeded depth option of ' + options.depth + ' and strictDepth is true');\n        }\n        keys.push('[' + key.slice(segment.index) + ']');\n    }\n\n    return parseObject(keys, val, options, valuesParsed);\n};\n\nvar normalizeParseOptions = function normalizeParseOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (typeof opts.allowEmptyArrays !== 'undefined' && typeof opts.allowEmptyArrays !== 'boolean') {\n        throw new TypeError('`allowEmptyArrays` option can only be `true` or `false`, when provided');\n    }\n\n    if (typeof opts.decodeDotInKeys !== 'undefined' && typeof opts.decodeDotInKeys !== 'boolean') {\n        throw new TypeError('`decodeDotInKeys` option can only be `true` or `false`, when provided');\n    }\n\n    if (opts.decoder !== null && typeof opts.decoder !== 'undefined' && typeof opts.decoder !== 'function') {\n        throw new TypeError('Decoder has to be a function.');\n    }\n\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n\n    if (typeof opts.throwOnLimitExceeded !== 'undefined' && typeof opts.throwOnLimitExceeded !== 'boolean') {\n        throw new TypeError('`throwOnLimitExceeded` option must be a boolean');\n    }\n\n    var charset = typeof opts.charset === 'undefined' ? defaults.charset : opts.charset;\n\n    var duplicates = typeof opts.duplicates === 'undefined' ? defaults.duplicates : opts.duplicates;\n\n    if (duplicates !== 'combine' && duplicates !== 'first' && duplicates !== 'last') {\n        throw new TypeError('The duplicates option must be either combine, first, or last');\n    }\n\n    var allowDots = typeof opts.allowDots === 'undefined' ? opts.decodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;\n\n    return {\n        allowDots: allowDots,\n        allowEmptyArrays: typeof opts.allowEmptyArrays === 'boolean' ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,\n        allowPrototypes: typeof opts.allowPrototypes === 'boolean' ? opts.allowPrototypes : defaults.allowPrototypes,\n        allowSparse: typeof opts.allowSparse === 'boolean' ? opts.allowSparse : defaults.allowSparse,\n        arrayLimit: typeof opts.arrayLimit === 'number' ? opts.arrayLimit : defaults.arrayLimit,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        comma: typeof opts.comma === 'boolean' ? opts.comma : defaults.comma,\n        decodeDotInKeys: typeof opts.decodeDotInKeys === 'boolean' ? opts.decodeDotInKeys : defaults.decodeDotInKeys,\n        decoder: typeof opts.decoder === 'function' ? opts.decoder : defaults.decoder,\n        delimiter: typeof opts.delimiter === 'string' || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,\n        // eslint-disable-next-line no-implicit-coercion, no-extra-parens\n        depth: (typeof opts.depth === 'number' || opts.depth === false) ? +opts.depth : defaults.depth,\n        duplicates: duplicates,\n        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,\n        interpretNumericEntities: typeof opts.interpretNumericEntities === 'boolean' ? opts.interpretNumericEntities : defaults.interpretNumericEntities,\n        parameterLimit: typeof opts.parameterLimit === 'number' ? opts.parameterLimit : defaults.parameterLimit,\n        parseArrays: opts.parseArrays !== false,\n        plainObjects: typeof opts.plainObjects === 'boolean' ? opts.plainObjects : defaults.plainObjects,\n        strictDepth: typeof opts.strictDepth === 'boolean' ? !!opts.strictDepth : defaults.strictDepth,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling,\n        throwOnLimitExceeded: typeof opts.throwOnLimitExceeded === 'boolean' ? opts.throwOnLimitExceeded : false\n    };\n};\n\nmodule.exports = function (str, opts) {\n    var options = normalizeParseOptions(opts);\n\n    if (str === '' || str === null || typeof str === 'undefined') {\n        return options.plainObjects ? { __proto__: null } : {};\n    }\n\n    var tempObj = typeof str === 'string' ? parseValues(str, options) : str;\n    var obj = options.plainObjects ? { __proto__: null } : {};\n\n    // Iterate over the keys and setup the new object\n\n    var keys = Object.keys(tempObj);\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        var newObj = parseKeys(key, tempObj[key], options, typeof str === 'string');\n        obj = utils.merge(obj, newObj, options);\n    }\n\n    if (options.allowSparse === true) {\n        return obj;\n    }\n\n    return utils.compact(obj);\n};\n", "'use strict';\n\nvar stringify = require('./stringify');\nvar parse = require('./parse');\nvar formats = require('./formats');\n\nmodule.exports = {\n    formats: formats,\n    parse: parse,\n    stringify: stringify\n};\n", "import * as qs from 'qs';\nconst OPTIONS_KEYS = [\n    'apiKey',\n    'idempotencyKey',\n    'stripeAccount',\n    'apiVersion',\n    'maxNetworkRetries',\n    'timeout',\n    'host',\n    'authenticator',\n    'stripeContext',\n    'additionalHeaders',\n    'streaming',\n];\nexport function isOptionsHash(o) {\n    return (o &&\n        typeof o === 'object' &&\n        OPTIONS_KEYS.some((prop) => Object.prototype.hasOwnProperty.call(o, prop)));\n}\n/**\n * Stringifies an Object, accommodating nested objects\n * (forming the conventional key 'parent[child]=value')\n */\nexport function queryStringifyRequestData(data, apiMode) {\n    return (qs\n        .stringify(data, {\n        serializeDate: (d) => Math.floor(d.getTime() / 1000).toString(),\n        arrayFormat: apiMode == 'v2' ? 'repeat' : 'indices',\n    })\n        // Don't use strict form encoding by changing the square bracket control\n        // characters back to their literals. This is fine by the server, and\n        // makes these parameter strings easier to read.\n        .replace(/%5B/g, '[')\n        .replace(/%5D/g, ']'));\n}\n/**\n * Outputs a new function with interpolated object property values.\n * Use like so:\n *   const fn = makeURLInterpolator('some/url/{param1}/{param2}');\n *   fn({ param1: 123, param2: 456 }); // => 'some/url/123/456'\n */\nexport const makeURLInterpolator = (() => {\n    const rc = {\n        '\\n': '\\\\n',\n        '\"': '\\\\\"',\n        '\\u2028': '\\\\u2028',\n        '\\u2029': '\\\\u2029',\n    };\n    return (str) => {\n        const cleanString = str.replace(/[\"\\n\\r\\u2028\\u2029]/g, ($0) => rc[$0]);\n        return (outputs) => {\n            return cleanString.replace(/\\{([\\s\\S]+?)\\}/g, ($0, $1) => {\n                const output = outputs[$1];\n                if (isValidEncodeUriComponentType(output))\n                    return encodeURIComponent(output);\n                return '';\n            });\n        };\n    };\n})();\nfunction isValidEncodeUriComponentType(value) {\n    return ['number', 'string', 'boolean'].includes(typeof value);\n}\nexport function extractUrlParams(path) {\n    const params = path.match(/\\{\\w+\\}/g);\n    if (!params) {\n        return [];\n    }\n    return params.map((param) => param.replace(/[{}]/g, ''));\n}\n/**\n * Return the data argument from a list of arguments\n *\n * @param {object[]} args\n * @returns {object}\n */\nexport function getDataFromArgs(args) {\n    if (!Array.isArray(args) || !args[0] || typeof args[0] !== 'object') {\n        return {};\n    }\n    if (!isOptionsHash(args[0])) {\n        return args.shift();\n    }\n    const argKeys = Object.keys(args[0]);\n    const optionKeysInArgs = argKeys.filter((key) => OPTIONS_KEYS.includes(key));\n    // In some cases options may be the provided as the first argument.\n    // Here we're detecting a case where there are two distinct arguments\n    // (the first being args and the second options) and with known\n    // option keys in the first so that we can warn the user about it.\n    if (optionKeysInArgs.length > 0 &&\n        optionKeysInArgs.length !== argKeys.length) {\n        emitWarning(`Options found in arguments (${optionKeysInArgs.join(', ')}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`);\n    }\n    return {};\n}\n/**\n * Return the options hash from a list of arguments\n */\nexport function getOptionsFromArgs(args) {\n    const opts = {\n        host: null,\n        headers: {},\n        settings: {},\n        streaming: false,\n    };\n    if (args.length > 0) {\n        const arg = args[args.length - 1];\n        if (typeof arg === 'string') {\n            opts.authenticator = createApiKeyAuthenticator(args.pop());\n        }\n        else if (isOptionsHash(arg)) {\n            const params = Object.assign({}, args.pop());\n            const extraKeys = Object.keys(params).filter((key) => !OPTIONS_KEYS.includes(key));\n            if (extraKeys.length) {\n                emitWarning(`Invalid options found (${extraKeys.join(', ')}); ignoring.`);\n            }\n            if (params.apiKey) {\n                opts.authenticator = createApiKeyAuthenticator(params.apiKey);\n            }\n            if (params.idempotencyKey) {\n                opts.headers['Idempotency-Key'] = params.idempotencyKey;\n            }\n            if (params.stripeAccount) {\n                opts.headers['Stripe-Account'] = params.stripeAccount;\n            }\n            if (params.stripeContext) {\n                if (opts.headers['Stripe-Account']) {\n                    throw new Error(\"Can't specify both stripeAccount and stripeContext.\");\n                }\n                opts.headers['Stripe-Context'] = params.stripeContext;\n            }\n            if (params.apiVersion) {\n                opts.headers['Stripe-Version'] = params.apiVersion;\n            }\n            if (Number.isInteger(params.maxNetworkRetries)) {\n                opts.settings.maxNetworkRetries = params.maxNetworkRetries;\n            }\n            if (Number.isInteger(params.timeout)) {\n                opts.settings.timeout = params.timeout;\n            }\n            if (params.host) {\n                opts.host = params.host;\n            }\n            if (params.authenticator) {\n                if (params.apiKey) {\n                    throw new Error(\"Can't specify both apiKey and authenticator.\");\n                }\n                if (typeof params.authenticator !== 'function') {\n                    throw new Error('The authenticator must be a function ' +\n                        'receiving a request as the first parameter.');\n                }\n                opts.authenticator = params.authenticator;\n            }\n            if (params.additionalHeaders) {\n                opts.headers = params.additionalHeaders;\n            }\n            if (params.streaming) {\n                opts.streaming = true;\n            }\n        }\n    }\n    return opts;\n}\n/**\n * Provide simple \"Class\" extension mechanism.\n * <!-- Public API accessible via Stripe.StripeResource.extend -->\n */\nexport function protoExtend(sub) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const Super = this;\n    const Constructor = Object.prototype.hasOwnProperty.call(sub, 'constructor')\n        ? sub.constructor\n        : function (...args) {\n            Super.apply(this, args);\n        };\n    // This initialization logic is somewhat sensitive to be compatible with\n    // divergent JS implementations like the one found in Qt. See here for more\n    // context:\n    //\n    // https://github.com/stripe/stripe-node/pull/334\n    Object.assign(Constructor, Super);\n    Constructor.prototype = Object.create(Super.prototype);\n    Object.assign(Constructor.prototype, sub);\n    return Constructor;\n}\n/**\n * Remove empty values from an object\n */\nexport function removeNullish(obj) {\n    if (typeof obj !== 'object') {\n        throw new Error('Argument must be an object');\n    }\n    return Object.keys(obj).reduce((result, key) => {\n        if (obj[key] != null) {\n            result[key] = obj[key];\n        }\n        return result;\n    }, {});\n}\n/**\n * Normalize standard HTTP Headers:\n * {'foo-bar': 'hi'}\n * becomes\n * {'Foo-Bar': 'hi'}\n */\nexport function normalizeHeaders(obj) {\n    if (!(obj && typeof obj === 'object')) {\n        return obj;\n    }\n    return Object.keys(obj).reduce((result, header) => {\n        result[normalizeHeader(header)] = obj[header];\n        return result;\n    }, {});\n}\n/**\n * Stolen from https://github.com/marten-de-vries/header-case-normalizer/blob/master/index.js#L36-L41\n * without the exceptions which are irrelevant to us.\n */\nexport function normalizeHeader(header) {\n    return header\n        .split('-')\n        .map((text) => text.charAt(0).toUpperCase() + text.substr(1).toLowerCase())\n        .join('-');\n}\nexport function callbackifyPromiseWithTimeout(promise, callback) {\n    if (callback) {\n        // Ensure callback is called outside of promise stack.\n        return promise.then((res) => {\n            setTimeout(() => {\n                callback(null, res);\n            }, 0);\n        }, (err) => {\n            setTimeout(() => {\n                callback(err, null);\n            }, 0);\n        });\n    }\n    return promise;\n}\n/**\n * Allow for special capitalization cases (such as OAuth)\n */\nexport function pascalToCamelCase(name) {\n    if (name === 'OAuth') {\n        return 'oauth';\n    }\n    else {\n        return name[0].toLowerCase() + name.substring(1);\n    }\n}\nexport function emitWarning(warning) {\n    if (typeof process.emitWarning !== 'function') {\n        return console.warn(`Stripe: ${warning}`); /* eslint-disable-line no-console */\n    }\n    return process.emitWarning(warning, 'Stripe');\n}\nexport function isObject(obj) {\n    const type = typeof obj;\n    return (type === 'function' || type === 'object') && !!obj;\n}\n// For use in multipart requests\nexport function flattenAndStringify(data) {\n    const result = {};\n    const step = (obj, prevKey) => {\n        Object.entries(obj).forEach(([key, value]) => {\n            const newKey = prevKey ? `${prevKey}[${key}]` : key;\n            if (isObject(value)) {\n                if (!(value instanceof Uint8Array) &&\n                    !Object.prototype.hasOwnProperty.call(value, 'data')) {\n                    // Non-buffer non-file Objects are recursively flattened\n                    return step(value, newKey);\n                }\n                else {\n                    // Buffers and file objects are stored without modification\n                    result[newKey] = value;\n                }\n            }\n            else {\n                // Primitives are converted to strings\n                result[newKey] = String(value);\n            }\n        });\n    };\n    step(data, null);\n    return result;\n}\nexport function validateInteger(name, n, defaultVal) {\n    if (!Number.isInteger(n)) {\n        if (defaultVal !== undefined) {\n            return defaultVal;\n        }\n        else {\n            throw new Error(`${name} must be an integer`);\n        }\n    }\n    return n;\n}\nexport function determineProcessUserAgentProperties() {\n    return typeof process === 'undefined'\n        ? {}\n        : {\n            lang_version: process.version,\n            platform: process.platform,\n        };\n}\nexport function createApiKeyAuthenticator(apiKey) {\n    const authenticator = (request) => {\n        request.headers.Authorization = 'Bearer ' + apiKey;\n        return Promise.resolve();\n    };\n    // For testing\n    authenticator._apiKey = apiKey;\n    return authenticator;\n}\n/**\n * Joins an array of Uint8Arrays into a single Uint8Array\n */\nexport function concat(arrays) {\n    const totalLength = arrays.reduce((len, array) => len + array.length, 0);\n    const merged = new Uint8Array(totalLength);\n    let offset = 0;\n    arrays.forEach((array) => {\n        merged.set(array, offset);\n        offset += array.length;\n    });\n    return merged;\n}\n/**\n * Replaces Date objects with Unix timestamps\n */\nfunction dateTimeReplacer(key, value) {\n    if (this[key] instanceof Date) {\n        return Math.floor(this[key].getTime() / 1000).toString();\n    }\n    return value;\n}\n/**\n * JSON stringifies an Object, replacing Date objects with Unix timestamps\n */\nexport function jsonStringifyRequestData(data) {\n    return JSON.stringify(data, dateTimeReplacer);\n}\n/**\n * Inspects the given path to determine if the endpoint is for v1 or v2 API\n */\nexport function getAPIMode(path) {\n    if (!path) {\n        return 'v1';\n    }\n    return path.startsWith('/v2') ? 'v2' : 'v1';\n}\nexport function parseHttpHeaderAsString(header) {\n    if (Array.isArray(header)) {\n        return header.join(', ');\n    }\n    return String(header);\n}\nexport function parseHttpHeaderAsNumber(header) {\n    const number = Array.isArray(header) ? header[0] : header;\n    return Number(number);\n}\nexport function parseHeadersForFetch(headers) {\n    return Object.entries(headers).map(([key, value]) => {\n        return [key, parseHttpHeaderAsString(value)];\n    });\n}\n", "/**\n * Encapsulates the logic for issuing a request to the Stripe API.\n *\n * A custom HTTP client should should implement:\n * 1. A response class which extends HttpClientResponse and wraps around their\n *    own internal representation of a response.\n * 2. A client class which extends HttpClient and implements all methods,\n *    returning their own response class when making requests.\n */\nexport class HttpClient {\n    /** The client name used for diagnostics. */\n    getClientName() {\n        throw new Error('getClientName not implemented.');\n    }\n    makeRequest(host, port, path, method, headers, requestData, protocol, timeout) {\n        throw new Error('makeRequest not implemented.');\n    }\n    /** Helper to make a consistent timeout error across implementations. */\n    static makeTimeoutError() {\n        const timeoutErr = new TypeError(HttpClient.TIMEOUT_ERROR_CODE);\n        timeoutErr.code = HttpClient.TIMEOUT_ERROR_CODE;\n        return timeoutErr;\n    }\n}\n// Public API accessible via Stripe.HttpClient\nHttpClient.CONNECTION_CLOSED_ERROR_CODES = ['ECONNRESET', 'EPIPE'];\nHttpClient.TIMEOUT_ERROR_CODE = 'ETIMEDOUT';\nexport class HttpClientResponse {\n    constructor(statusCode, headers) {\n        this._statusCode = statusCode;\n        this._headers = headers;\n    }\n    getStatusCode() {\n        return this._statusCode;\n    }\n    getHeaders() {\n        return this._headers;\n    }\n    getRawResponse() {\n        throw new Error('getRawResponse not implemented.');\n    }\n    toStream(streamCompleteCallback) {\n        throw new Error('toStream not implemented.');\n    }\n    toJSON() {\n        throw new Error('toJSON not implemented.');\n    }\n}\n", "import { parseHeadersForFetch } from '../utils.js';\nimport { HttpClient, HttpClientResponse, } from './HttpClient.js';\n/**\n * HTTP client which uses a `fetch` function to issue requests.\n *\n * By default relies on the global `fetch` function, but an optional function\n * can be passed in. If passing in a function, it is expected to match the Web\n * Fetch API. As an example, this could be the function provided by the\n * node-fetch package (https://github.com/node-fetch/node-fetch).\n */\nexport class FetchHttpClient extends HttpClient {\n    constructor(fetchFn) {\n        super();\n        // Default to global fetch if available\n        if (!fetchFn) {\n            if (!globalThis.fetch) {\n                throw new Error('fetch() function not provided and is not defined in the global scope. ' +\n                    'You must provide a fetch implementation.');\n            }\n            fetchFn = globalThis.fetch;\n        }\n        // Both timeout behaviors differs from Node:\n        // - <PERSON><PERSON> uses a single timeout for the entire length of the request.\n        // - Node is more fine-grained and resets the timeout after each stage of the request.\n        if (globalThis.AbortController) {\n            // Utilise native AbortController if available\n            // AbortController was added in Node v15.0.0, v14.17.0\n            this._fetchFn = FetchHttpClient.makeFetchWithAbortTimeout(fetchFn);\n        }\n        else {\n            // Fall back to racing against a timeout promise if not available in the runtime\n            // This does not actually cancel the underlying fetch operation or resources\n            this._fetchFn = FetchHttpClient.makeFetchWithRaceTimeout(fetchFn);\n        }\n    }\n    static makeFetchWithRaceTimeout(fetchFn) {\n        return (url, init, timeout) => {\n            let pendingTimeoutId;\n            const timeoutPromise = new Promise((_, reject) => {\n                pendingTimeoutId = setTimeout(() => {\n                    pendingTimeoutId = null;\n                    reject(HttpClient.makeTimeoutError());\n                }, timeout);\n            });\n            const fetchPromise = fetchFn(url, init);\n            return Promise.race([fetchPromise, timeoutPromise]).finally(() => {\n                if (pendingTimeoutId) {\n                    clearTimeout(pendingTimeoutId);\n                }\n            });\n        };\n    }\n    static makeFetchWithAbortTimeout(fetchFn) {\n        return async (url, init, timeout) => {\n            // Use AbortController because AbortSignal.timeout() was added later in Node v17.3.0, v16.14.0\n            const abort = new AbortController();\n            let timeoutId = setTimeout(() => {\n                timeoutId = null;\n                abort.abort(HttpClient.makeTimeoutError());\n            }, timeout);\n            try {\n                return await fetchFn(url, Object.assign(Object.assign({}, init), { signal: abort.signal }));\n            }\n            catch (err) {\n                // Some implementations, like node-fetch, do not respect the reason passed to AbortController.abort()\n                // and instead it always throws an AbortError\n                // We catch this case to normalise all timeout errors\n                if (err.name === 'AbortError') {\n                    throw HttpClient.makeTimeoutError();\n                }\n                else {\n                    throw err;\n                }\n            }\n            finally {\n                if (timeoutId) {\n                    clearTimeout(timeoutId);\n                }\n            }\n        };\n    }\n    /** @override. */\n    getClientName() {\n        return 'fetch';\n    }\n    async makeRequest(host, port, path, method, headers, requestData, protocol, timeout) {\n        const isInsecureConnection = protocol === 'http';\n        const url = new URL(path, `${isInsecureConnection ? 'http' : 'https'}://${host}`);\n        url.port = port;\n        // For methods which expect payloads, we should always pass a body value\n        // even when it is empty. Without this, some JS runtimes (eg. Deno) will\n        // inject a second Content-Length header. See https://github.com/stripe/stripe-node/issues/1519\n        // for more details.\n        const methodHasPayload = method == 'POST' || method == 'PUT' || method == 'PATCH';\n        const body = requestData || (methodHasPayload ? '' : undefined);\n        const res = await this._fetchFn(url.toString(), {\n            method,\n            headers: parseHeadersForFetch(headers),\n            body: typeof body === 'object' ? JSON.stringify(body) : body,\n        }, timeout);\n        return new FetchHttpClientResponse(res);\n    }\n}\nexport class FetchHttpClientResponse extends HttpClientResponse {\n    constructor(res) {\n        super(res.status, FetchHttpClientResponse._transformHeadersToObject(res.headers));\n        this._res = res;\n    }\n    getRawResponse() {\n        return this._res;\n    }\n    toStream(streamCompleteCallback) {\n        // Unfortunately `fetch` does not have event handlers for when the stream is\n        // completely read. We therefore invoke the streamCompleteCallback right\n        // away. This callback emits a response event with metadata and completes\n        // metrics, so it's ok to do this without waiting for the stream to be\n        // completely read.\n        streamCompleteCallback();\n        // Fetch's `body` property is expected to be a readable stream of the body.\n        return this._res.body;\n    }\n    toJSON() {\n        return this._res.json();\n    }\n    static _transformHeadersToObject(headers) {\n        // Fetch uses a Headers instance so this must be converted to a barebones\n        // JS object to meet the HttpClient interface.\n        const headersObj = {};\n        for (const entry of headers) {\n            if (!Array.isArray(entry) || entry.length != 2) {\n                throw new Error('Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.');\n            }\n            headersObj[entry[0]] = entry[1];\n        }\n        return headersObj;\n    }\n}\n", "/**\n * Interface encapsulating the various crypto computations used by the library,\n * allowing pluggable underlying crypto implementations.\n */\nexport class Crypto<PERSON>rovider {\n    /**\n     * Computes a SHA-256 HMAC given a secret and a payload (encoded in UTF-8).\n     * The output HMAC should be encoded in hexadecimal.\n     *\n     * Sample values for implementations:\n     * - computeHMACSignature('', 'test_secret') => 'f7f9bd47fb987337b5796fdc1fdb9ba221d0d5396814bfcaf9521f43fd8927fd'\n     * - computeHMACSignature('\\ud83d\\ude00', 'test_secret') => '837da296d05c4fe31f61d5d7ead035099d9585a5bcde87de952012a78f0b0c43\n     */\n    computeHMACSignature(payload, secret) {\n        throw new Error('computeHMACSignature not implemented.');\n    }\n    /**\n     * Asynchronous version of `computeHMACSignature`. Some implementations may\n     * only allow support async signature computation.\n     *\n     * Computes a SHA-256 HMAC given a secret and a payload (encoded in UTF-8).\n     * The output HMAC should be encoded in hexadecimal.\n     *\n     * Sample values for implementations:\n     * - computeHMACSignature('', 'test_secret') => 'f7f9bd47fb987337b5796fdc1fdb9ba221d0d5396814bfcaf9521f43fd8927fd'\n     * - computeHMACSignature('\\ud83d\\ude00', 'test_secret') => '837da296d05c4fe31f61d5d7ead035099d9585a5bcde87de952012a78f0b0c43\n     */\n    computeHMACSignatureAsync(payload, secret) {\n        throw new Error('computeHMACSignatureAsync not implemented.');\n    }\n    /**\n     * Computes a SHA-256 hash of the data.\n     */\n    computeSHA256Async(data) {\n        throw new Error('computeSHA256 not implemented.');\n    }\n}\n/**\n * If the crypto provider only supports asynchronous operations,\n * throw CryptoProviderOnlySupportsAsyncError instead of\n * a generic error so that the caller can choose to provide\n * a more helpful error message to direct the user to use\n * an asynchronous pathway.\n */\nexport class CryptoProviderOnlySupportsAsyncError extends Error {\n}\n", "import { CryptoProvider, CryptoProviderOnlySupportsAsyncError, } from './CryptoProvider.js';\n/**\n * `CryptoProvider which uses the SubtleCrypto interface of the Web Crypto API.\n *\n * This only supports asynchronous operations.\n */\nexport class SubtleCryptoProvider extends CryptoProvider {\n    constructor(subtleCrypto) {\n        super();\n        // If no subtle crypto is interface, default to the global namespace. This\n        // is to allow custom interfaces (eg. using the Node webcrypto interface in\n        // tests).\n        this.subtleCrypto = subtleCrypto || crypto.subtle;\n    }\n    /** @override */\n    computeHMACSignature(payload, secret) {\n        throw new CryptoProviderOnlySupportsAsyncError('SubtleCryptoProvider cannot be used in a synchronous context.');\n    }\n    /** @override */\n    async computeHMACSignatureAsync(payload, secret) {\n        const encoder = new TextEncoder();\n        const key = await this.subtleCrypto.importKey('raw', encoder.encode(secret), {\n            name: 'HM<PERSON>',\n            hash: { name: 'SHA-256' },\n        }, false, ['sign']);\n        const signatureBuffer = await this.subtleCrypto.sign('hmac', key, encoder.encode(payload));\n        // crypto.subtle returns the signature in base64 format. This must be\n        // encoded in hex to match the CryptoProvider contract. We map each byte in\n        // the buffer to its corresponding hex octet and then combine into a string.\n        const signatureBytes = new Uint8Array(signatureBuffer);\n        const signatureHexCodes = new Array(signatureBytes.length);\n        for (let i = 0; i < signatureBytes.length; i++) {\n            signatureHexCodes[i] = byteHexMapping[signatureBytes[i]];\n        }\n        return signatureHexCodes.join('');\n    }\n    /** @override */\n    async computeSHA256Async(data) {\n        return new Uint8Array(await this.subtleCrypto.digest('SHA-256', data));\n    }\n}\n// Cached mapping of byte to hex representation. We do this once to avoid re-\n// computing every time we need to convert the result of a signature to hex.\nconst byteHexMapping = new Array(256);\nfor (let i = 0; i < byteHexMapping.length; i++) {\n    byteHexMapping[i] = i.toString(16).padStart(2, '0');\n}\n", "import { FetchHttpClient } from '../net/FetchHttpClient.js';\nimport { SubtleCryptoProvider } from '../crypto/SubtleCryptoProvider.js';\n/**\n * Interface encapsulating various utility functions whose\n * implementations depend on the platform / JS runtime.\n */\nexport class PlatformFunctions {\n    constructor() {\n        this._fetchFn = null;\n        this._agent = null;\n    }\n    /**\n     * Gets uname with Node's built-in `exec` function, if available.\n     */\n    getUname() {\n        throw new Error('getUname not implemented.');\n    }\n    /**\n     * Generates a v4 UUID. See https://stackoverflow.com/a/2117523\n     */\n    uuid4() {\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n            const r = (Math.random() * 16) | 0;\n            const v = c === 'x' ? r : (r & 0x3) | 0x8;\n            return v.toString(16);\n        });\n    }\n    /**\n     * Compares strings in constant time.\n     */\n    secureCompare(a, b) {\n        // return early here if buffer lengths are not equal\n        if (a.length !== b.length) {\n            return false;\n        }\n        const len = a.length;\n        let result = 0;\n        for (let i = 0; i < len; ++i) {\n            result |= a.charCodeAt(i) ^ b.charCodeAt(i);\n        }\n        return result === 0;\n    }\n    /**\n     * Creates an event emitter.\n     */\n    createEmitter() {\n        throw new Error('createEmitter not implemented.');\n    }\n    /**\n     * Checks if the request data is a stream. If so, read the entire stream\n     * to a buffer and return the buffer.\n     */\n    tryBufferData(data) {\n        throw new Error('tryBufferData not implemented.');\n    }\n    /**\n     * Creates an HTTP client which uses the Node `http` and `https` packages\n     * to issue requests.\n     */\n    createNodeHttpClient(agent) {\n        throw new Error('createNodeHttpClient not implemented.');\n    }\n    /**\n     * Creates an HTTP client for issuing Stripe API requests which uses the Web\n     * Fetch API.\n     *\n     * A fetch function can optionally be passed in as a parameter. If none is\n     * passed, will default to the default `fetch` function in the global scope.\n     */\n    createFetchHttpClient(fetchFn) {\n        return new FetchHttpClient(fetchFn);\n    }\n    /**\n     * Creates an HTTP client using runtime-specific APIs.\n     */\n    createDefaultHttpClient() {\n        throw new Error('createDefaultHttpClient not implemented.');\n    }\n    /**\n     * Creates a CryptoProvider which uses the Node `crypto` package for its computations.\n     */\n    createNodeCryptoProvider() {\n        throw new Error('createNodeCryptoProvider not implemented.');\n    }\n    /**\n     * Creates a CryptoProvider which uses the SubtleCrypto interface of the Web Crypto API.\n     */\n    createSubtleCryptoProvider(subtleCrypto) {\n        return new SubtleCryptoProvider(subtleCrypto);\n    }\n    createDefaultCryptoProvider() {\n        throw new Error('createDefaultCryptoProvider not implemented.');\n    }\n}\n", "/**\n * @private\n * (For internal use in stripe-node.)\n * Wrapper around the Event Web API.\n */\nclass _StripeEvent extends Event {\n    constructor(eventName, data) {\n        super(eventName);\n        this.data = data;\n    }\n}\n/** Minimal EventEmitter wrapper around EventTarget. */\nexport class StripeEmitter {\n    constructor() {\n        this.eventTarget = new EventTarget();\n        this.listenerMapping = new Map();\n    }\n    on(eventName, listener) {\n        const listenerWrapper = (event) => {\n            listener(event.data);\n        };\n        this.listenerMapping.set(listener, listenerWrapper);\n        return this.eventTarget.addEventListener(eventName, listenerWrapper);\n    }\n    removeListener(eventName, listener) {\n        const listenerWrapper = this.listenerMapping.get(listener);\n        this.listenerMapping.delete(listener);\n        return this.eventTarget.removeEventListener(eventName, listenerWrapper);\n    }\n    once(eventName, listener) {\n        const listenerWrapper = (event) => {\n            listener(event.data);\n        };\n        this.listenerMapping.set(listener, listenerWrapper);\n        return this.eventTarget.addEventListener(eventName, listenerWrapper, {\n            once: true,\n        });\n    }\n    emit(eventName, data) {\n        return this.eventTarget.dispatchEvent(new _StripeEvent(eventName, data));\n    }\n}\n", "import { PlatformFunctions } from './PlatformFunctions.js';\nimport { StripeEmitter } from '../StripeEmitter.js';\n/**\n * Specializes WebPlatformFunctions using APIs available in Web workers.\n */\nexport class WebPlatformFunctions extends PlatformFunctions {\n    /** @override */\n    getUname() {\n        return Promise.resolve(null);\n    }\n    /** @override */\n    createEmitter() {\n        return new StripeEmitter();\n    }\n    /** @override */\n    tryBufferData(data) {\n        if (data.file.data instanceof ReadableStream) {\n            throw new Error('Uploading a file as a stream is not supported in non-Node environments. Please open or upvote an issue at github.com/stripe/stripe-node if you use this, detailing your use-case.');\n        }\n        return Promise.resolve(data);\n    }\n    /** @override */\n    createNodeHttpClient() {\n        throw new Error('Stripe: `createNodeHttpClient()` is not available in non-Node environments. Please use `createFetchHttpClient()` instead.');\n    }\n    /** @override */\n    createDefaultHttpClient() {\n        return super.createFetchHttpClient();\n    }\n    /** @override */\n    createNodeCryptoProvider() {\n        throw new Error('Stripe: `createNodeCryptoProvider()` is not available in non-Node environments. Please use `createSubtleCryptoProvider()` instead.');\n    }\n    /** @override */\n    createDefaultCryptoProvider() {\n        return this.createSubtleCryptoProvider();\n    }\n}\n", "/* eslint-disable camelcase */\n/* eslint-disable no-warning-comments */\nexport const generateV1Error = (rawStripeError) => {\n    switch (rawStripeError.type) {\n        case 'card_error':\n            return new StripeCardError(rawStripeError);\n        case 'invalid_request_error':\n            return new StripeInvalidRequestError(rawStripeError);\n        case 'api_error':\n            return new StripeAPIError(rawStripeError);\n        case 'authentication_error':\n            return new StripeAuthenticationError(rawStripeError);\n        case 'rate_limit_error':\n            return new StripeRateLimitError(rawStripeError);\n        case 'idempotency_error':\n            return new StripeIdempotencyError(rawStripeError);\n        case 'invalid_grant':\n            return new StripeInvalidGrantError(rawStripeError);\n        default:\n            return new StripeUnknownError(rawStripeError);\n    }\n};\n// eslint-disable-next-line complexity\nexport const generateV2Error = (rawStripeError) => {\n    switch (rawStripeError.type) {\n        // switchCases: The beginning of the section generated from our OpenAPI spec\n        case 'temporary_session_expired':\n            return new TemporarySessionExpiredError(rawStripeError);\n        // switchCases: The end of the section generated from our OpenAPI spec\n    }\n    // Special handling for requests with missing required fields in V2 APIs.\n    // invalid_field response in V2 APIs returns the field 'code' instead of 'type'.\n    switch (rawStripeError.code) {\n        case 'invalid_fields':\n            return new StripeInvalidRequestError(rawStripeError);\n    }\n    return generateV1Error(rawStripeError);\n};\n/**\n * StripeError is the base error from which all other more specific Stripe errors derive.\n * Specifically for errors returned from Stripe's REST API.\n */\nexport class StripeError extends Error {\n    constructor(raw = {}, type = null) {\n        var _a;\n        super(raw.message);\n        this.type = type || this.constructor.name;\n        this.raw = raw;\n        this.rawType = raw.type;\n        this.code = raw.code;\n        this.doc_url = raw.doc_url;\n        this.param = raw.param;\n        this.detail = raw.detail;\n        this.headers = raw.headers;\n        this.requestId = raw.requestId;\n        this.statusCode = raw.statusCode;\n        this.message = (_a = raw.message) !== null && _a !== void 0 ? _a : '';\n        this.userMessage = raw.user_message;\n        this.charge = raw.charge;\n        this.decline_code = raw.decline_code;\n        this.payment_intent = raw.payment_intent;\n        this.payment_method = raw.payment_method;\n        this.payment_method_type = raw.payment_method_type;\n        this.setup_intent = raw.setup_intent;\n        this.source = raw.source;\n    }\n}\n/**\n * Helper factory which takes raw stripe errors and outputs wrapping instances\n */\nStripeError.generate = generateV1Error;\n// Specific Stripe Error types:\n/**\n * CardError is raised when a user enters a card that can't be charged for\n * some reason.\n */\nexport class StripeCardError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeCardError');\n    }\n}\n/**\n * InvalidRequestError is raised when a request is initiated with invalid\n * parameters.\n */\nexport class StripeInvalidRequestError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeInvalidRequestError');\n    }\n}\n/**\n * APIError is a generic error that may be raised in cases where none of the\n * other named errors cover the problem. It could also be raised in the case\n * that a new error has been introduced in the API, but this version of the\n * Node.JS SDK doesn't know how to handle it.\n */\nexport class StripeAPIError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeAPIError');\n    }\n}\n/**\n * AuthenticationError is raised when invalid credentials are used to connect\n * to Stripe's servers.\n */\nexport class StripeAuthenticationError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeAuthenticationError');\n    }\n}\n/**\n * PermissionError is raised in cases where access was attempted on a resource\n * that wasn't allowed.\n */\nexport class StripePermissionError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripePermissionError');\n    }\n}\n/**\n * RateLimitError is raised in cases where an account is putting too much load\n * on Stripe's API servers (usually by performing too many requests). Please\n * back off on request rate.\n */\nexport class StripeRateLimitError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeRateLimitError');\n    }\n}\n/**\n * StripeConnectionError is raised in the event that the SDK can't connect to\n * Stripe's servers. That can be for a variety of different reasons from a\n * downed network to a bad TLS certificate.\n */\nexport class StripeConnectionError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeConnectionError');\n    }\n}\n/**\n * SignatureVerificationError is raised when the signature verification for a\n * webhook fails\n */\nexport class StripeSignatureVerificationError extends StripeError {\n    constructor(header, payload, raw = {}) {\n        super(raw, 'StripeSignatureVerificationError');\n        this.header = header;\n        this.payload = payload;\n    }\n}\n/**\n * IdempotencyError is raised in cases where an idempotency key was used\n * improperly.\n */\nexport class StripeIdempotencyError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeIdempotencyError');\n    }\n}\n/**\n * InvalidGrantError is raised when a specified code doesn't exist, is\n * expired, has been used, or doesn't belong to you; a refresh token doesn't\n * exist, or doesn't belong to you; or if an API key's mode (live or test)\n * doesn't match the mode of a code or refresh token.\n */\nexport class StripeInvalidGrantError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeInvalidGrantError');\n    }\n}\n/**\n * Any other error from Stripe not specifically captured above\n */\nexport class StripeUnknownError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeUnknownError');\n    }\n}\n// classDefinitions: The beginning of the section generated from our OpenAPI spec\nexport class TemporarySessionExpiredError extends StripeError {\n    constructor(rawStripeError = {}) {\n        super(rawStripeError, 'TemporarySessionExpiredError');\n    }\n}\n// classDefinitions: The end of the section generated from our OpenAPI spec\n", "import { StripeAPIError, StripeAuthenticationError, StripeConnectionError, StripeError, StripePermissionError, StripeRateLimitError, generateV1Error, generateV2Error, } from './Error.js';\nimport { HttpClient } from './net/HttpClient.js';\nimport { emitWarning, jsonStringifyRequestData, normalizeHeaders, queryStringifyRequestData, removeNullish, getAPIMode, getOptionsFromArgs, getDataFromArgs, parseHttpHeaderAsString, parseHttpHeaderAsNumber, } from './utils.js';\nconst MAX_RETRY_AFTER_WAIT = 60;\nexport class RequestSender {\n    constructor(stripe, maxBufferedRequestMetric) {\n        this._stripe = stripe;\n        this._maxBufferedRequestMetric = maxBufferedRequestMetric;\n    }\n    _addHeadersDirectlyToObject(obj, headers) {\n        // For convenience, make some headers easily accessible on\n        // lastResponse.\n        // NOTE: <PERSON><PERSON> responds with lowercase header names/keys.\n        obj.requestId = headers['request-id'];\n        obj.stripeAccount = obj.stripeAccount || headers['stripe-account'];\n        obj.apiVersion = obj.apiVersion || headers['stripe-version'];\n        obj.idempotencyKey = obj.idempotencyKey || headers['idempotency-key'];\n    }\n    _makeResponseEvent(requestEvent, statusCode, headers) {\n        const requestEndTime = Date.now();\n        const requestDurationMs = requestEndTime - requestEvent.request_start_time;\n        return removeNullish({\n            api_version: headers['stripe-version'],\n            account: headers['stripe-account'],\n            idempotency_key: headers['idempotency-key'],\n            method: requestEvent.method,\n            path: requestEvent.path,\n            status: statusCode,\n            request_id: this._getRequestId(headers),\n            elapsed: requestDurationMs,\n            request_start_time: requestEvent.request_start_time,\n            request_end_time: requestEndTime,\n        });\n    }\n    _getRequestId(headers) {\n        return headers['request-id'];\n    }\n    /**\n     * Used by methods with spec.streaming === true. For these methods, we do not\n     * buffer successful responses into memory or do parse them into stripe\n     * objects, we delegate that all of that to the user and pass back the raw\n     * http.Response object to the callback.\n     *\n     * (Unsuccessful responses shouldn't make it here, they should\n     * still be buffered/parsed and handled by _jsonResponseHandler -- see\n     * makeRequest)\n     */\n    _streamingResponseHandler(requestEvent, usage, callback) {\n        return (res) => {\n            const headers = res.getHeaders();\n            const streamCompleteCallback = () => {\n                const responseEvent = this._makeResponseEvent(requestEvent, res.getStatusCode(), headers);\n                this._stripe._emitter.emit('response', responseEvent);\n                this._recordRequestMetrics(this._getRequestId(headers), responseEvent.elapsed, usage);\n            };\n            const stream = res.toStream(streamCompleteCallback);\n            // This is here for backwards compatibility, as the stream is a raw\n            // HTTP response in Node and the legacy behavior was to mutate this\n            // response.\n            this._addHeadersDirectlyToObject(stream, headers);\n            return callback(null, stream);\n        };\n    }\n    /**\n     * Default handler for Stripe responses. Buffers the response into memory,\n     * parses the JSON and returns it (i.e. passes it to the callback) if there\n     * is no \"error\" field. Otherwise constructs/passes an appropriate Error.\n     */\n    _jsonResponseHandler(requestEvent, apiMode, usage, callback) {\n        return (res) => {\n            const headers = res.getHeaders();\n            const requestId = this._getRequestId(headers);\n            const statusCode = res.getStatusCode();\n            const responseEvent = this._makeResponseEvent(requestEvent, statusCode, headers);\n            this._stripe._emitter.emit('response', responseEvent);\n            res\n                .toJSON()\n                .then((jsonResponse) => {\n                if (jsonResponse.error) {\n                    let err;\n                    // Convert OAuth error responses into a standard format\n                    // so that the rest of the error logic can be shared\n                    if (typeof jsonResponse.error === 'string') {\n                        jsonResponse.error = {\n                            type: jsonResponse.error,\n                            message: jsonResponse.error_description,\n                        };\n                    }\n                    jsonResponse.error.headers = headers;\n                    jsonResponse.error.statusCode = statusCode;\n                    jsonResponse.error.requestId = requestId;\n                    if (statusCode === 401) {\n                        err = new StripeAuthenticationError(jsonResponse.error);\n                    }\n                    else if (statusCode === 403) {\n                        err = new StripePermissionError(jsonResponse.error);\n                    }\n                    else if (statusCode === 429) {\n                        err = new StripeRateLimitError(jsonResponse.error);\n                    }\n                    else if (apiMode === 'v2') {\n                        err = generateV2Error(jsonResponse.error);\n                    }\n                    else {\n                        err = generateV1Error(jsonResponse.error);\n                    }\n                    throw err;\n                }\n                return jsonResponse;\n            }, (e) => {\n                throw new StripeAPIError({\n                    message: 'Invalid JSON received from the Stripe API',\n                    exception: e,\n                    requestId: headers['request-id'],\n                });\n            })\n                .then((jsonResponse) => {\n                this._recordRequestMetrics(requestId, responseEvent.elapsed, usage);\n                // Expose raw response object.\n                const rawResponse = res.getRawResponse();\n                this._addHeadersDirectlyToObject(rawResponse, headers);\n                Object.defineProperty(jsonResponse, 'lastResponse', {\n                    enumerable: false,\n                    writable: false,\n                    value: rawResponse,\n                });\n                callback(null, jsonResponse);\n            }, (e) => callback(e, null));\n        };\n    }\n    static _generateConnectionErrorMessage(requestRetries) {\n        return `An error occurred with our connection to Stripe.${requestRetries > 0 ? ` Request was retried ${requestRetries} times.` : ''}`;\n    }\n    // For more on when and how to retry API requests, see https://stripe.com/docs/error-handling#safely-retrying-requests-with-idempotency\n    static _shouldRetry(res, numRetries, maxRetries, error) {\n        if (error &&\n            numRetries === 0 &&\n            HttpClient.CONNECTION_CLOSED_ERROR_CODES.includes(error.code)) {\n            return true;\n        }\n        // Do not retry if we are out of retries.\n        if (numRetries >= maxRetries) {\n            return false;\n        }\n        // Retry on connection error.\n        if (!res) {\n            return true;\n        }\n        // The API may ask us not to retry (e.g., if doing so would be a no-op)\n        // or advise us to retry (e.g., in cases of lock timeouts); we defer to that.\n        if (res.getHeaders()['stripe-should-retry'] === 'false') {\n            return false;\n        }\n        if (res.getHeaders()['stripe-should-retry'] === 'true') {\n            return true;\n        }\n        // Retry on conflict errors.\n        if (res.getStatusCode() === 409) {\n            return true;\n        }\n        // Retry on 500, 503, and other internal errors.\n        //\n        // Note that we expect the stripe-should-retry header to be false\n        // in most cases when a 500 is returned, since our idempotency framework\n        // would typically replay it anyway.\n        if (res.getStatusCode() >= 500) {\n            return true;\n        }\n        return false;\n    }\n    _getSleepTimeInMS(numRetries, retryAfter = null) {\n        const initialNetworkRetryDelay = this._stripe.getInitialNetworkRetryDelay();\n        const maxNetworkRetryDelay = this._stripe.getMaxNetworkRetryDelay();\n        // Apply exponential backoff with initialNetworkRetryDelay on the\n        // number of numRetries so far as inputs. Do not allow the number to exceed\n        // maxNetworkRetryDelay.\n        let sleepSeconds = Math.min(initialNetworkRetryDelay * Math.pow(2, numRetries - 1), maxNetworkRetryDelay);\n        // Apply some jitter by randomizing the value in the range of\n        // (sleepSeconds / 2) to (sleepSeconds).\n        sleepSeconds *= 0.5 * (1 + Math.random());\n        // But never sleep less than the base sleep seconds.\n        sleepSeconds = Math.max(initialNetworkRetryDelay, sleepSeconds);\n        // And never sleep less than the time the API asks us to wait, assuming it's a reasonable ask.\n        if (Number.isInteger(retryAfter) && retryAfter <= MAX_RETRY_AFTER_WAIT) {\n            sleepSeconds = Math.max(sleepSeconds, retryAfter);\n        }\n        return sleepSeconds * 1000;\n    }\n    // Max retries can be set on a per request basis. Favor those over the global setting\n    _getMaxNetworkRetries(settings = {}) {\n        return settings.maxNetworkRetries !== undefined &&\n            Number.isInteger(settings.maxNetworkRetries)\n            ? settings.maxNetworkRetries\n            : this._stripe.getMaxNetworkRetries();\n    }\n    _defaultIdempotencyKey(method, settings, apiMode) {\n        // If this is a POST and we allow multiple retries, ensure an idempotency key.\n        const maxRetries = this._getMaxNetworkRetries(settings);\n        const genKey = () => `stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`;\n        // more verbose than it needs to be, but gives clear separation between V1 and V2 behavior\n        if (apiMode === 'v2') {\n            if (method === 'POST' || method === 'DELETE') {\n                return genKey();\n            }\n        }\n        else if (apiMode === 'v1') {\n            if (method === 'POST' && maxRetries > 0) {\n                return genKey();\n            }\n        }\n        return null;\n    }\n    _makeHeaders({ contentType, contentLength, apiVersion, clientUserAgent, method, userSuppliedHeaders, userSuppliedSettings, stripeAccount, stripeContext, apiMode, }) {\n        const defaultHeaders = {\n            Accept: 'application/json',\n            'Content-Type': contentType,\n            'User-Agent': this._getUserAgentString(apiMode),\n            'X-Stripe-Client-User-Agent': clientUserAgent,\n            'X-Stripe-Client-Telemetry': this._getTelemetryHeader(),\n            'Stripe-Version': apiVersion,\n            'Stripe-Account': stripeAccount,\n            'Stripe-Context': stripeContext,\n            'Idempotency-Key': this._defaultIdempotencyKey(method, userSuppliedSettings, apiMode),\n        };\n        // As per https://datatracker.ietf.org/doc/html/rfc7230#section-3.3.2:\n        //   A user agent SHOULD send a Content-Length in a request message when\n        //   no Transfer-Encoding is sent and the request method defines a meaning\n        //   for an enclosed payload body.  For example, a Content-Length header\n        //   field is normally sent in a POST request even when the value is 0\n        //   (indicating an empty payload body).  A user agent SHOULD NOT send a\n        //   Content-Length header field when the request message does not contain\n        //   a payload body and the method semantics do not anticipate such a\n        //   body.\n        //\n        // These method types are expected to have bodies and so we should always\n        // include a Content-Length.\n        const methodHasPayload = method == 'POST' || method == 'PUT' || method == 'PATCH';\n        // If a content length was specified, we always include it regardless of\n        // whether the method semantics anticipate such a body. This keeps us\n        // consistent with historical behavior. We do however want to warn on this\n        // and fix these cases as they are semantically incorrect.\n        if (methodHasPayload || contentLength) {\n            if (!methodHasPayload) {\n                emitWarning(`${method} method had non-zero contentLength but no payload is expected for this verb`);\n            }\n            defaultHeaders['Content-Length'] = contentLength;\n        }\n        return Object.assign(removeNullish(defaultHeaders), \n        // If the user supplied, say 'idempotency-key', override instead of appending by ensuring caps are the same.\n        normalizeHeaders(userSuppliedHeaders));\n    }\n    _getUserAgentString(apiMode) {\n        const packageVersion = this._stripe.getConstant('PACKAGE_VERSION');\n        const appInfo = this._stripe._appInfo\n            ? this._stripe.getAppInfoAsString()\n            : '';\n        return `Stripe/${apiMode} NodeBindings/${packageVersion} ${appInfo}`.trim();\n    }\n    _getTelemetryHeader() {\n        if (this._stripe.getTelemetryEnabled() &&\n            this._stripe._prevRequestMetrics.length > 0) {\n            const metrics = this._stripe._prevRequestMetrics.shift();\n            return JSON.stringify({\n                last_request_metrics: metrics,\n            });\n        }\n    }\n    _recordRequestMetrics(requestId, requestDurationMs, usage) {\n        if (this._stripe.getTelemetryEnabled() && requestId) {\n            if (this._stripe._prevRequestMetrics.length > this._maxBufferedRequestMetric) {\n                emitWarning('Request metrics buffer is full, dropping telemetry message.');\n            }\n            else {\n                const m = {\n                    request_id: requestId,\n                    request_duration_ms: requestDurationMs,\n                };\n                if (usage && usage.length > 0) {\n                    m.usage = usage;\n                }\n                this._stripe._prevRequestMetrics.push(m);\n            }\n        }\n    }\n    _rawRequest(method, path, params, options) {\n        const requestPromise = new Promise((resolve, reject) => {\n            let opts;\n            try {\n                const requestMethod = method.toUpperCase();\n                if (requestMethod !== 'POST' &&\n                    params &&\n                    Object.keys(params).length !== 0) {\n                    throw new Error('rawRequest only supports params on POST requests. Please pass null and add your parameters to path.');\n                }\n                const args = [].slice.call([params, options]);\n                // Pull request data and options (headers, auth) from args.\n                const dataFromArgs = getDataFromArgs(args);\n                const data = Object.assign({}, dataFromArgs);\n                const calculatedOptions = getOptionsFromArgs(args);\n                const headers = calculatedOptions.headers;\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const authenticator = calculatedOptions.authenticator;\n                opts = {\n                    requestMethod,\n                    requestPath: path,\n                    bodyData: data,\n                    queryData: {},\n                    authenticator,\n                    headers,\n                    host: calculatedOptions.host,\n                    streaming: !!calculatedOptions.streaming,\n                    settings: {},\n                    usage: ['raw_request'],\n                };\n            }\n            catch (err) {\n                reject(err);\n                return;\n            }\n            function requestCallback(err, response) {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(response);\n                }\n            }\n            const { headers, settings } = opts;\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const authenticator = opts.authenticator;\n            this._request(opts.requestMethod, opts.host, path, opts.bodyData, authenticator, { headers, settings, streaming: opts.streaming }, opts.usage, requestCallback);\n        });\n        return requestPromise;\n    }\n    _request(method, host, path, data, authenticator, options, usage = [], callback, requestDataProcessor = null) {\n        var _a;\n        let requestData;\n        authenticator = (_a = authenticator !== null && authenticator !== void 0 ? authenticator : this._stripe._authenticator) !== null && _a !== void 0 ? _a : null;\n        const apiMode = getAPIMode(path);\n        const retryRequest = (requestFn, apiVersion, headers, requestRetries, retryAfter) => {\n            return setTimeout(requestFn, this._getSleepTimeInMS(requestRetries, retryAfter), apiVersion, headers, requestRetries + 1);\n        };\n        const makeRequest = (apiVersion, headers, numRetries) => {\n            // timeout can be set on a per-request basis. Favor that over the global setting\n            const timeout = options.settings &&\n                options.settings.timeout &&\n                Number.isInteger(options.settings.timeout) &&\n                options.settings.timeout >= 0\n                ? options.settings.timeout\n                : this._stripe.getApiField('timeout');\n            const request = {\n                host: host || this._stripe.getApiField('host'),\n                port: this._stripe.getApiField('port'),\n                path: path,\n                method: method,\n                headers: Object.assign({}, headers),\n                body: requestData,\n                protocol: this._stripe.getApiField('protocol'),\n            };\n            authenticator(request)\n                .then(() => {\n                const req = this._stripe\n                    .getApiField('httpClient')\n                    .makeRequest(request.host, request.port, request.path, request.method, request.headers, request.body, request.protocol, timeout);\n                const requestStartTime = Date.now();\n                const requestEvent = removeNullish({\n                    api_version: apiVersion,\n                    account: parseHttpHeaderAsString(headers['Stripe-Account']),\n                    idempotency_key: parseHttpHeaderAsString(headers['Idempotency-Key']),\n                    method,\n                    path,\n                    request_start_time: requestStartTime,\n                });\n                const requestRetries = numRetries || 0;\n                const maxRetries = this._getMaxNetworkRetries(options.settings || {});\n                this._stripe._emitter.emit('request', requestEvent);\n                req\n                    .then((res) => {\n                    if (RequestSender._shouldRetry(res, requestRetries, maxRetries)) {\n                        return retryRequest(makeRequest, apiVersion, headers, requestRetries, parseHttpHeaderAsNumber(res.getHeaders()['retry-after']));\n                    }\n                    else if (options.streaming && res.getStatusCode() < 400) {\n                        return this._streamingResponseHandler(requestEvent, usage, callback)(res);\n                    }\n                    else {\n                        return this._jsonResponseHandler(requestEvent, apiMode, usage, callback)(res);\n                    }\n                })\n                    .catch((error) => {\n                    if (RequestSender._shouldRetry(null, requestRetries, maxRetries, error)) {\n                        return retryRequest(makeRequest, apiVersion, headers, requestRetries, null);\n                    }\n                    else {\n                        const isTimeoutError = error.code && error.code === HttpClient.TIMEOUT_ERROR_CODE;\n                        return callback(new StripeConnectionError({\n                            message: isTimeoutError\n                                ? `Request aborted due to timeout being reached (${timeout}ms)`\n                                : RequestSender._generateConnectionErrorMessage(requestRetries),\n                            detail: error,\n                        }));\n                    }\n                });\n            })\n                .catch((e) => {\n                throw new StripeError({\n                    message: 'Unable to authenticate the request',\n                    exception: e,\n                });\n            });\n        };\n        const prepareAndMakeRequest = (error, data) => {\n            if (error) {\n                return callback(error);\n            }\n            requestData = data;\n            this._stripe.getClientUserAgent((clientUserAgent) => {\n                const apiVersion = this._stripe.getApiField('version');\n                const headers = this._makeHeaders({\n                    contentType: apiMode == 'v2'\n                        ? 'application/json'\n                        : 'application/x-www-form-urlencoded',\n                    contentLength: requestData.length,\n                    apiVersion: apiVersion,\n                    clientUserAgent,\n                    method,\n                    userSuppliedHeaders: options.headers,\n                    userSuppliedSettings: options.settings,\n                    stripeAccount: apiMode == 'v2' ? null : this._stripe.getApiField('stripeAccount'),\n                    stripeContext: apiMode == 'v2' ? this._stripe.getApiField('stripeContext') : null,\n                    apiMode: apiMode,\n                });\n                makeRequest(apiVersion, headers, 0);\n            });\n        };\n        if (requestDataProcessor) {\n            requestDataProcessor(method, data, options.headers, prepareAndMakeRequest);\n        }\n        else {\n            let stringifiedData;\n            if (apiMode == 'v2') {\n                stringifiedData = data ? jsonStringifyRequestData(data) : '';\n            }\n            else {\n                stringifiedData = queryStringifyRequestData(data || {}, apiMode);\n            }\n            prepareAndMakeRequest(null, stringifiedData);\n        }\n    }\n}\n", "import { callbackifyPromiseWithTimeout, getDataFromArgs, getAPIMode, } from './utils.js';\nclass V1Iterator {\n    constructor(firstPagePromise, requestArgs, spec, stripeResource) {\n        this.index = 0;\n        this.pagePromise = firstPagePromise;\n        this.promiseCache = { currentPromise: null };\n        this.requestArgs = requestArgs;\n        this.spec = spec;\n        this.stripeResource = stripeResource;\n    }\n    async iterate(pageResult) {\n        if (!(pageResult &&\n            pageResult.data &&\n            typeof pageResult.data.length === 'number')) {\n            throw Error('Unexpected: Stripe API response does not have a well-formed `data` array.');\n        }\n        const reverseIteration = isReverseIteration(this.requestArgs);\n        if (this.index < pageResult.data.length) {\n            const idx = reverseIteration\n                ? pageResult.data.length - 1 - this.index\n                : this.index;\n            const value = pageResult.data[idx];\n            this.index += 1;\n            return { value, done: false };\n        }\n        else if (pageResult.has_more) {\n            // Reset counter, request next page, and recurse.\n            this.index = 0;\n            this.pagePromise = this.getNextPage(pageResult);\n            const nextPageResult = await this.pagePromise;\n            return this.iterate(nextPageResult);\n        }\n        return { done: true, value: undefined };\n    }\n    /** @abstract */\n    getNextPage(_pageResult) {\n        throw new Error('Unimplemented');\n    }\n    async _next() {\n        return this.iterate(await this.pagePromise);\n    }\n    next() {\n        /**\n         * If a user calls `.next()` multiple times in parallel,\n         * return the same result until something has resolved\n         * to prevent page-turning race conditions.\n         */\n        if (this.promiseCache.currentPromise) {\n            return this.promiseCache.currentPromise;\n        }\n        const nextPromise = (async () => {\n            const ret = await this._next();\n            this.promiseCache.currentPromise = null;\n            return ret;\n        })();\n        this.promiseCache.currentPromise = nextPromise;\n        return nextPromise;\n    }\n}\nclass V1ListIterator extends V1Iterator {\n    getNextPage(pageResult) {\n        const reverseIteration = isReverseIteration(this.requestArgs);\n        const lastId = getLastId(pageResult, reverseIteration);\n        return this.stripeResource._makeRequest(this.requestArgs, this.spec, {\n            [reverseIteration ? 'ending_before' : 'starting_after']: lastId,\n        });\n    }\n}\nclass V1SearchIterator extends V1Iterator {\n    getNextPage(pageResult) {\n        if (!pageResult.next_page) {\n            throw Error('Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.');\n        }\n        return this.stripeResource._makeRequest(this.requestArgs, this.spec, {\n            page: pageResult.next_page,\n        });\n    }\n}\nclass V2ListIterator {\n    constructor(firstPagePromise, requestArgs, spec, stripeResource) {\n        this.currentPageIterator = (async () => {\n            const page = await firstPagePromise;\n            return page.data[Symbol.iterator]();\n        })();\n        this.nextPageUrl = (async () => {\n            const page = await firstPagePromise;\n            return page.next_page_url || null;\n        })();\n        this.requestArgs = requestArgs;\n        this.spec = spec;\n        this.stripeResource = stripeResource;\n    }\n    async turnPage() {\n        const nextPageUrl = await this.nextPageUrl;\n        if (!nextPageUrl)\n            return null;\n        this.spec.fullPath = nextPageUrl;\n        const page = await this.stripeResource._makeRequest([], this.spec, {});\n        this.nextPageUrl = Promise.resolve(page.next_page_url);\n        this.currentPageIterator = Promise.resolve(page.data[Symbol.iterator]());\n        return this.currentPageIterator;\n    }\n    async next() {\n        {\n            const result = (await this.currentPageIterator).next();\n            if (!result.done)\n                return { done: false, value: result.value };\n        }\n        const nextPageIterator = await this.turnPage();\n        if (!nextPageIterator) {\n            return { done: true, value: undefined };\n        }\n        const result = nextPageIterator.next();\n        if (!result.done)\n            return { done: false, value: result.value };\n        return { done: true, value: undefined };\n    }\n}\nexport const makeAutoPaginationMethods = (stripeResource, requestArgs, spec, firstPagePromise) => {\n    const apiMode = getAPIMode(spec.fullPath || spec.path);\n    if (apiMode !== 'v2' && spec.methodType === 'search') {\n        return makeAutoPaginationMethodsFromIterator(new V1SearchIterator(firstPagePromise, requestArgs, spec, stripeResource));\n    }\n    if (apiMode !== 'v2' && spec.methodType === 'list') {\n        return makeAutoPaginationMethodsFromIterator(new V1ListIterator(firstPagePromise, requestArgs, spec, stripeResource));\n    }\n    if (apiMode === 'v2' && spec.methodType === 'list') {\n        return makeAutoPaginationMethodsFromIterator(new V2ListIterator(firstPagePromise, requestArgs, spec, stripeResource));\n    }\n    return null;\n};\nconst makeAutoPaginationMethodsFromIterator = (iterator) => {\n    const autoPagingEach = makeAutoPagingEach((...args) => iterator.next(...args));\n    const autoPagingToArray = makeAutoPagingToArray(autoPagingEach);\n    const autoPaginationMethods = {\n        autoPagingEach,\n        autoPagingToArray,\n        // Async iterator functions:\n        next: () => iterator.next(),\n        return: () => {\n            // This is required for `break`.\n            return {};\n        },\n        [getAsyncIteratorSymbol()]: () => {\n            return autoPaginationMethods;\n        },\n    };\n    return autoPaginationMethods;\n};\n/**\n * ----------------\n * Private Helpers:\n * ----------------\n */\nfunction getAsyncIteratorSymbol() {\n    if (typeof Symbol !== 'undefined' && Symbol.asyncIterator) {\n        return Symbol.asyncIterator;\n    }\n    // Follow the convention from libraries like iterall: https://github.com/leebyron/iterall#asynciterator-1\n    return '@@asyncIterator';\n}\nfunction getDoneCallback(args) {\n    if (args.length < 2) {\n        return null;\n    }\n    const onDone = args[1];\n    if (typeof onDone !== 'function') {\n        throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof onDone}`);\n    }\n    return onDone;\n}\n/**\n * We allow four forms of the `onItem` callback (the middle two being equivalent),\n *\n *   1. `.autoPagingEach((item) => { doSomething(item); return false; });`\n *   2. `.autoPagingEach(async (item) => { await doSomething(item); return false; });`\n *   3. `.autoPagingEach((item) => doSomething(item).then(() => false));`\n *   4. `.autoPagingEach((item, next) => { doSomething(item); next(false); });`\n *\n * In addition to standard validation, this helper\n * coalesces the former forms into the latter form.\n */\nfunction getItemCallback(args) {\n    if (args.length === 0) {\n        return undefined;\n    }\n    const onItem = args[0];\n    if (typeof onItem !== 'function') {\n        throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof onItem}`);\n    }\n    // 4. `.autoPagingEach((item, next) => { doSomething(item); next(false); });`\n    if (onItem.length === 2) {\n        return onItem;\n    }\n    if (onItem.length > 2) {\n        throw Error(`The \\`onItem\\` callback function passed to autoPagingEach must accept at most two arguments; got ${onItem}`);\n    }\n    // This magically handles all three of these usecases (the latter two being functionally identical):\n    // 1. `.autoPagingEach((item) => { doSomething(item); return false; });`\n    // 2. `.autoPagingEach(async (item) => { await doSomething(item); return false; });`\n    // 3. `.autoPagingEach((item) => doSomething(item).then(() => false));`\n    return function _onItem(item, next) {\n        const shouldContinue = onItem(item);\n        next(shouldContinue);\n    };\n}\nfunction getLastId(listResult, reverseIteration) {\n    const lastIdx = reverseIteration ? 0 : listResult.data.length - 1;\n    const lastItem = listResult.data[lastIdx];\n    const lastId = lastItem && lastItem.id;\n    if (!lastId) {\n        throw Error('Unexpected: No `id` found on the last item while auto-paging a list.');\n    }\n    return lastId;\n}\nfunction makeAutoPagingEach(asyncIteratorNext) {\n    return function autoPagingEach( /* onItem?, onDone? */) {\n        const args = [].slice.call(arguments);\n        const onItem = getItemCallback(args);\n        const onDone = getDoneCallback(args);\n        if (args.length > 2) {\n            throw Error(`autoPagingEach takes up to two arguments; received ${args}`);\n        }\n        const autoPagePromise = wrapAsyncIteratorWithCallback(asyncIteratorNext, \n        // @ts-ignore we might need a null check\n        onItem);\n        return callbackifyPromiseWithTimeout(autoPagePromise, onDone);\n    };\n}\nfunction makeAutoPagingToArray(autoPagingEach) {\n    return function autoPagingToArray(opts, onDone) {\n        const limit = opts && opts.limit;\n        if (!limit) {\n            throw Error('You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.');\n        }\n        if (limit > 10000) {\n            throw Error('You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.');\n        }\n        const promise = new Promise((resolve, reject) => {\n            const items = [];\n            autoPagingEach((item) => {\n                items.push(item);\n                if (items.length >= limit) {\n                    return false;\n                }\n            })\n                .then(() => {\n                resolve(items);\n            })\n                .catch(reject);\n        });\n        // @ts-ignore\n        return callbackifyPromiseWithTimeout(promise, onDone);\n    };\n}\nfunction wrapAsyncIteratorWithCallback(asyncIteratorNext, onItem) {\n    return new Promise((resolve, reject) => {\n        function handleIteration(iterResult) {\n            if (iterResult.done) {\n                resolve();\n                return;\n            }\n            const item = iterResult.value;\n            return new Promise((next) => {\n                // Bit confusing, perhaps; we pass a `resolve` fn\n                // to the user, so they can decide when and if to continue.\n                // They can return false, or a promise which resolves to false, to break.\n                onItem(item, next);\n            }).then((shouldContinue) => {\n                if (shouldContinue === false) {\n                    return handleIteration({ done: true, value: undefined });\n                }\n                else {\n                    return asyncIteratorNext().then(handleIteration);\n                }\n            });\n        }\n        asyncIteratorNext()\n            .then(handleIteration)\n            .catch(reject);\n    });\n}\nfunction isReverseIteration(requestArgs) {\n    const args = [].slice.call(requestArgs);\n    const dataFromArgs = getDataFromArgs(args);\n    return !!dataFromArgs.ending_before;\n}\n", "import { callbackifyPromiseWithTimeout, extractUrlParams } from './utils.js';\nimport { makeAutoPaginationMethods } from './autoPagination.js';\n/**\n * Create an API method from the declared spec.\n *\n * @param [spec.method='GET'] Request Method (POST, GET, DELETE, PUT)\n * @param [spec.path=''] Path to be appended to the API BASE_PATH, joined with\n *  the instance's path (e.g. 'charges' or 'customers')\n * @param [spec.fullPath=''] Fully qualified path to the method (eg. /v1/a/b/c).\n *  If this is specified, path should not be specified.\n * @param [spec.urlParams=[]] Array of required arguments in the order that they\n *  must be passed by the consumer of the API. Subsequent optional arguments are\n *  optionally passed through a hash (Object) as the penultimate argument\n *  (preceding the also-optional callback argument\n * @param [spec.encode] Function for mutating input parameters to a method.\n *  Usefully for applying transforms to data on a per-method basis.\n * @param [spec.host] Hostname for the request.\n *\n * <!-- Public API accessible via Stripe.StripeResource.method -->\n */\nexport function stripeMethod(spec) {\n    if (spec.path !== undefined && spec.fullPath !== undefined) {\n        throw new Error(`Method spec specified both a 'path' (${spec.path}) and a 'fullPath' (${spec.fullPath}).`);\n    }\n    return function (...args) {\n        const callback = typeof args[args.length - 1] == 'function' && args.pop();\n        spec.urlParams = extractUrlParams(spec.fullPath || this.createResourcePathWithSymbols(spec.path || ''));\n        const requestPromise = callbackifyPromiseWithTimeout(this._makeRequest(args, spec, {}), callback);\n        Object.assign(requestPromise, makeAutoPaginationMethods(this, args, spec, requestPromise));\n        return requestPromise;\n    };\n}\n", "import { getDataFromArgs, getOptionsFromArgs, makeURLInterpolator, protoExtend, queryStringifyRequestData, getAPIMode, } from './utils.js';\nimport { stripeMethod } from './StripeMethod.js';\n// Provide extension mechanism for Stripe Resource Sub-Classes\nStripeResource.extend = protoExtend;\n// Expose method-creator\nStripeResource.method = stripeMethod;\nStripeResource.MAX_BUFFERED_REQUEST_METRICS = 100;\n/**\n * Encapsulates request logic for a Stripe Resource\n */\nfunction StripeResource(stripe, deprecatedUrlData) {\n    this._stripe = stripe;\n    if (deprecatedUrlData) {\n        throw new Error('Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.');\n    }\n    this.basePath = makeURLInterpolator(\n    // @ts-ignore changing type of basePath\n    this.basePath || stripe.getApiField('basePath'));\n    // @ts-ignore changing type of path\n    this.resourcePath = this.path;\n    // @ts-ignore changing type of path\n    this.path = makeURLInterpolator(this.path);\n    this.initialize(...arguments);\n}\nStripeResource.prototype = {\n    _stripe: null,\n    // @ts-ignore the type of path changes in ctor\n    path: '',\n    resourcePath: '',\n    // Methods that don't use the API's default '/v1' path can override it with this setting.\n    basePath: null,\n    initialize() { },\n    // Function to override the default data processor. This allows full control\n    // over how a StripeResource's request data will get converted into an HTTP\n    // body. This is useful for non-standard HTTP requests. The function should\n    // take method name, data, and headers as arguments.\n    requestDataProcessor: null,\n    // Function to add a validation checks before sending the request, errors should\n    // be thrown, and they will be passed to the callback/promise.\n    validateRequest: null,\n    createFullPath(commandPath, urlData) {\n        const urlParts = [this.basePath(urlData), this.path(urlData)];\n        if (typeof commandPath === 'function') {\n            const computedCommandPath = commandPath(urlData);\n            // If we have no actual command path, we just omit it to avoid adding a\n            // trailing slash. This is important for top-level listing requests, which\n            // do not have a command path.\n            if (computedCommandPath) {\n                urlParts.push(computedCommandPath);\n            }\n        }\n        else {\n            urlParts.push(commandPath);\n        }\n        return this._joinUrlParts(urlParts);\n    },\n    // Creates a relative resource path with symbols left in (unlike\n    // createFullPath which takes some data to replace them with). For example it\n    // might produce: /invoices/{id}\n    createResourcePathWithSymbols(pathWithSymbols) {\n        // If there is no path beyond the resource path, we want to produce just\n        // /<resource path> rather than /<resource path>/.\n        if (pathWithSymbols) {\n            return `/${this._joinUrlParts([this.resourcePath, pathWithSymbols])}`;\n        }\n        else {\n            return `/${this.resourcePath}`;\n        }\n    },\n    _joinUrlParts(parts) {\n        // Replace any accidentally doubled up slashes. This previously used\n        // path.join, which would do this as well. Unfortunately we need to do this\n        // as the functions for creating paths are technically part of the public\n        // interface and so we need to preserve backwards compatibility.\n        return parts.join('/').replace(/\\/{2,}/g, '/');\n    },\n    _getRequestOpts(requestArgs, spec, overrideData) {\n        var _a;\n        // Extract spec values with defaults.\n        const requestMethod = (spec.method || 'GET').toUpperCase();\n        const usage = spec.usage || [];\n        const urlParams = spec.urlParams || [];\n        const encode = spec.encode || ((data) => data);\n        const isUsingFullPath = !!spec.fullPath;\n        const commandPath = makeURLInterpolator(isUsingFullPath ? spec.fullPath : spec.path || '');\n        // When using fullPath, we ignore the resource path as it should already be\n        // fully qualified.\n        const path = isUsingFullPath\n            ? spec.fullPath\n            : this.createResourcePathWithSymbols(spec.path);\n        // Don't mutate args externally.\n        const args = [].slice.call(requestArgs);\n        // Generate and validate url params.\n        const urlData = urlParams.reduce((urlData, param) => {\n            const arg = args.shift();\n            if (typeof arg !== 'string') {\n                throw new Error(`Stripe: Argument \"${param}\" must be a string, but got: ${arg} (on API request to \\`${requestMethod} ${path}\\`)`);\n            }\n            urlData[param] = arg;\n            return urlData;\n        }, {});\n        // Pull request data and options (headers, auth) from args.\n        const dataFromArgs = getDataFromArgs(args);\n        const data = encode(Object.assign({}, dataFromArgs, overrideData));\n        const options = getOptionsFromArgs(args);\n        const host = options.host || spec.host;\n        const streaming = !!spec.streaming || !!options.streaming;\n        // Validate that there are no more args.\n        if (args.filter((x) => x != null).length) {\n            throw new Error(`Stripe: Unknown arguments (${args}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${requestMethod} \\`${path}\\`)`);\n        }\n        // When using full path, we can just invoke the URL interpolator directly\n        // as we don't need to use the resource to create a full path.\n        const requestPath = isUsingFullPath\n            ? commandPath(urlData)\n            : this.createFullPath(commandPath, urlData);\n        const headers = Object.assign(options.headers, spec.headers);\n        if (spec.validator) {\n            spec.validator(data, { headers });\n        }\n        const dataInQuery = spec.method === 'GET' || spec.method === 'DELETE';\n        const bodyData = dataInQuery ? null : data;\n        const queryData = dataInQuery ? data : {};\n        return {\n            requestMethod,\n            requestPath,\n            bodyData,\n            queryData,\n            authenticator: (_a = options.authenticator) !== null && _a !== void 0 ? _a : null,\n            headers,\n            host: host !== null && host !== void 0 ? host : null,\n            streaming,\n            settings: options.settings,\n            usage,\n        };\n    },\n    _makeRequest(requestArgs, spec, overrideData) {\n        return new Promise((resolve, reject) => {\n            var _a;\n            let opts;\n            try {\n                opts = this._getRequestOpts(requestArgs, spec, overrideData);\n            }\n            catch (err) {\n                reject(err);\n                return;\n            }\n            function requestCallback(err, response) {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(spec.transformResponseData\n                        ? spec.transformResponseData(response)\n                        : response);\n                }\n            }\n            const emptyQuery = Object.keys(opts.queryData).length === 0;\n            const path = [\n                opts.requestPath,\n                emptyQuery ? '' : '?',\n                queryStringifyRequestData(opts.queryData, getAPIMode(opts.requestPath)),\n            ].join('');\n            const { headers, settings } = opts;\n            this._stripe._requestSender._request(opts.requestMethod, opts.host, path, opts.bodyData, opts.authenticator, {\n                headers,\n                settings,\n                streaming: opts.streaming,\n            }, opts.usage, requestCallback, (_a = this.requestDataProcessor) === null || _a === void 0 ? void 0 : _a.bind(this));\n        });\n    },\n};\nexport { StripeResource };\n", "import { StripeError, StripeSignatureVerificationError } from './Error.js';\nimport { CryptoProviderOnlySupportsAsyncError, } from './crypto/CryptoProvider.js';\nexport function createWebhooks(platformFunctions) {\n    const Webhook = {\n        DEFAULT_TOLERANCE: 300,\n        signature: null,\n        constructEvent(payload, header, secret, tolerance, cryptoProvider, receivedAt) {\n            try {\n                if (!this.signature) {\n                    throw new Error('ERR: missing signature helper, unable to verify');\n                }\n                this.signature.verifyHeader(payload, header, secret, tolerance || Webhook.DEFAULT_TOLERANCE, cryptoProvider, receivedAt);\n            }\n            catch (e) {\n                if (e instanceof CryptoProviderOnlySupportsAsyncError) {\n                    e.message +=\n                        '\\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`';\n                }\n                throw e;\n            }\n            const jsonPayload = payload instanceof Uint8Array\n                ? JSON.parse(new TextDecoder('utf8').decode(payload))\n                : JSON.parse(payload);\n            return jsonPayload;\n        },\n        async constructEventAsync(payload, header, secret, tolerance, cryptoProvider, receivedAt) {\n            if (!this.signature) {\n                throw new Error('ERR: missing signature helper, unable to verify');\n            }\n            await this.signature.verifyHeaderAsync(payload, header, secret, tolerance || Webhook.DEFAULT_TOLERANCE, cryptoProvider, receivedAt);\n            const jsonPayload = payload instanceof Uint8Array\n                ? JSON.parse(new TextDecoder('utf8').decode(payload))\n                : JSON.parse(payload);\n            return jsonPayload;\n        },\n        /**\n         * Generates a header to be used for webhook mocking\n         *\n         * @typedef {object} opts\n         * @property {number} timestamp - Timestamp of the header. Defaults to Date.now()\n         * @property {string} payload - JSON stringified payload object, containing the 'id' and 'object' parameters\n         * @property {string} secret - Stripe webhook secret 'whsec_...'\n         * @property {string} scheme - Version of API to hit. Defaults to 'v1'.\n         * @property {string} signature - Computed webhook signature\n         * @property {CryptoProvider} cryptoProvider - Crypto provider to use for computing the signature if none was provided. Defaults to NodeCryptoProvider.\n         */\n        generateTestHeaderString: function (opts) {\n            const preparedOpts = prepareOptions(opts);\n            const signature = preparedOpts.signature ||\n                preparedOpts.cryptoProvider.computeHMACSignature(preparedOpts.payloadString, preparedOpts.secret);\n            return preparedOpts.generateHeaderString(signature);\n        },\n        generateTestHeaderStringAsync: async function (opts) {\n            const preparedOpts = prepareOptions(opts);\n            const signature = preparedOpts.signature ||\n                (await preparedOpts.cryptoProvider.computeHMACSignatureAsync(preparedOpts.payloadString, preparedOpts.secret));\n            return preparedOpts.generateHeaderString(signature);\n        },\n    };\n    const signature = {\n        EXPECTED_SCHEME: 'v1',\n        verifyHeader(encodedPayload, encodedHeader, secret, tolerance, cryptoProvider, receivedAt) {\n            const { decodedHeader: header, decodedPayload: payload, details, suspectPayloadType, } = parseEventDetails(encodedPayload, encodedHeader, this.EXPECTED_SCHEME);\n            const secretContainsWhitespace = /\\s/.test(secret);\n            cryptoProvider = cryptoProvider || getCryptoProvider();\n            const expectedSignature = cryptoProvider.computeHMACSignature(makeHMACContent(payload, details), secret);\n            validateComputedSignature(payload, header, details, expectedSignature, tolerance, suspectPayloadType, secretContainsWhitespace, receivedAt);\n            return true;\n        },\n        async verifyHeaderAsync(encodedPayload, encodedHeader, secret, tolerance, cryptoProvider, receivedAt) {\n            const { decodedHeader: header, decodedPayload: payload, details, suspectPayloadType, } = parseEventDetails(encodedPayload, encodedHeader, this.EXPECTED_SCHEME);\n            const secretContainsWhitespace = /\\s/.test(secret);\n            cryptoProvider = cryptoProvider || getCryptoProvider();\n            const expectedSignature = await cryptoProvider.computeHMACSignatureAsync(makeHMACContent(payload, details), secret);\n            return validateComputedSignature(payload, header, details, expectedSignature, tolerance, suspectPayloadType, secretContainsWhitespace, receivedAt);\n        },\n    };\n    function makeHMACContent(payload, details) {\n        return `${details.timestamp}.${payload}`;\n    }\n    function parseEventDetails(encodedPayload, encodedHeader, expectedScheme) {\n        if (!encodedPayload) {\n            throw new StripeSignatureVerificationError(encodedHeader, encodedPayload, {\n                message: 'No webhook payload was provided.',\n            });\n        }\n        const suspectPayloadType = typeof encodedPayload != 'string' &&\n            !(encodedPayload instanceof Uint8Array);\n        const textDecoder = new TextDecoder('utf8');\n        const decodedPayload = encodedPayload instanceof Uint8Array\n            ? textDecoder.decode(encodedPayload)\n            : encodedPayload;\n        // Express's type for `Request#headers` is `string | []string`\n        // which is because the `set-cookie` header is an array,\n        // but no other headers are an array (docs: https://nodejs.org/api/http.html#http_message_headers)\n        // (Express's Request class is an extension of http.IncomingMessage, and doesn't appear to be relevantly modified: https://github.com/expressjs/express/blob/master/lib/request.js#L31)\n        if (Array.isArray(encodedHeader)) {\n            throw new Error('Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.');\n        }\n        if (encodedHeader == null || encodedHeader == '') {\n            throw new StripeSignatureVerificationError(encodedHeader, encodedPayload, {\n                message: 'No stripe-signature header value was provided.',\n            });\n        }\n        const decodedHeader = encodedHeader instanceof Uint8Array\n            ? textDecoder.decode(encodedHeader)\n            : encodedHeader;\n        const details = parseHeader(decodedHeader, expectedScheme);\n        if (!details || details.timestamp === -1) {\n            throw new StripeSignatureVerificationError(decodedHeader, decodedPayload, {\n                message: 'Unable to extract timestamp and signatures from header',\n            });\n        }\n        if (!details.signatures.length) {\n            throw new StripeSignatureVerificationError(decodedHeader, decodedPayload, {\n                message: 'No signatures found with expected scheme',\n            });\n        }\n        return {\n            decodedPayload,\n            decodedHeader,\n            details,\n            suspectPayloadType,\n        };\n    }\n    function validateComputedSignature(payload, header, details, expectedSignature, tolerance, suspectPayloadType, secretContainsWhitespace, receivedAt) {\n        const signatureFound = !!details.signatures.filter(platformFunctions.secureCompare.bind(platformFunctions, expectedSignature)).length;\n        const docsLocation = '\\nLearn more about webhook signing and explore webhook integration examples for various frameworks at ' +\n            'https://docs.stripe.com/webhooks/signature';\n        const whitespaceMessage = secretContainsWhitespace\n            ? '\\n\\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value'\n            : '';\n        if (!signatureFound) {\n            if (suspectPayloadType) {\n                throw new StripeSignatureVerificationError(header, payload, {\n                    message: 'Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.' +\n                        'Payload was provided as a parsed JavaScript object instead. \\n' +\n                        'Signature verification is impossible without access to the original signed material. \\n' +\n                        docsLocation +\n                        '\\n' +\n                        whitespaceMessage,\n                });\n            }\n            throw new StripeSignatureVerificationError(header, payload, {\n                message: 'No signatures found matching the expected signature for payload.' +\n                    ' Are you passing the raw request body you received from Stripe? \\n' +\n                    ' If a webhook request is being forwarded by a third-party tool,' +\n                    ' ensure that the exact request body, including JSON formatting and new line style, is preserved.\\n' +\n                    docsLocation +\n                    '\\n' +\n                    whitespaceMessage,\n            });\n        }\n        const timestampAge = Math.floor((typeof receivedAt === 'number' ? receivedAt : Date.now()) / 1000) - details.timestamp;\n        if (tolerance > 0 && timestampAge > tolerance) {\n            throw new StripeSignatureVerificationError(header, payload, {\n                message: 'Timestamp outside the tolerance zone',\n            });\n        }\n        return true;\n    }\n    function parseHeader(header, scheme) {\n        if (typeof header !== 'string') {\n            return null;\n        }\n        return header.split(',').reduce((accum, item) => {\n            const kv = item.split('=');\n            if (kv[0] === 't') {\n                accum.timestamp = parseInt(kv[1], 10);\n            }\n            if (kv[0] === scheme) {\n                accum.signatures.push(kv[1]);\n            }\n            return accum;\n        }, {\n            timestamp: -1,\n            signatures: [],\n        });\n    }\n    let webhooksCryptoProviderInstance = null;\n    /**\n     * Lazily instantiate a CryptoProvider instance. This is a stateless object\n     * so a singleton can be used here.\n     */\n    function getCryptoProvider() {\n        if (!webhooksCryptoProviderInstance) {\n            webhooksCryptoProviderInstance = platformFunctions.createDefaultCryptoProvider();\n        }\n        return webhooksCryptoProviderInstance;\n    }\n    function prepareOptions(opts) {\n        if (!opts) {\n            throw new StripeError({\n                message: 'Options are required',\n            });\n        }\n        const timestamp = Math.floor(opts.timestamp) || Math.floor(Date.now() / 1000);\n        const scheme = opts.scheme || signature.EXPECTED_SCHEME;\n        const cryptoProvider = opts.cryptoProvider || getCryptoProvider();\n        const payloadString = `${timestamp}.${opts.payload}`;\n        const generateHeaderString = (signature) => {\n            return `t=${timestamp},${scheme}=${signature}`;\n        };\n        return Object.assign(Object.assign({}, opts), { timestamp,\n            scheme,\n            cryptoProvider,\n            payloadString,\n            generateHeaderString });\n    }\n    Webhook.signature = signature;\n    return Webhook;\n}\n", "// File generated from our OpenAPI spec\nexport const ApiVersion = '2025-05-28.basil';\n", "// File generated from our OpenAPI spec\nimport { resourceNamespace } from './ResourceNamespace.js';\nimport { Accounts as FinancialConnectionsAccounts } from './resources/FinancialConnections/Accounts.js';\nimport { ActiveEntitlements as EntitlementsActiveEntitlements } from './resources/Entitlements/ActiveEntitlements.js';\nimport { Alerts as BillingAlerts } from './resources/Billing/Alerts.js';\nimport { Authorizations as TestHelpersIssuingAuthorizations } from './resources/TestHelpers/Issuing/Authorizations.js';\nimport { Authorizations as IssuingAuthorizations } from './resources/Issuing/Authorizations.js';\nimport { Calculations as TaxCalculations } from './resources/Tax/Calculations.js';\nimport { Cardholders as IssuingCardholders } from './resources/Issuing/Cardholders.js';\nimport { Cards as TestHelpersIssuingCards } from './resources/TestHelpers/Issuing/Cards.js';\nimport { Cards as IssuingCards } from './resources/Issuing/Cards.js';\nimport { Configurations as BillingPortalConfigurations } from './resources/BillingPortal/Configurations.js';\nimport { Configurations as TerminalConfigurations } from './resources/Terminal/Configurations.js';\nimport { ConfirmationTokens as TestHelpersConfirmationTokens } from './resources/TestHelpers/ConfirmationTokens.js';\nimport { ConnectionTokens as TerminalConnectionTokens } from './resources/Terminal/ConnectionTokens.js';\nimport { CreditBalanceSummary as BillingCreditBalanceSummary } from './resources/Billing/CreditBalanceSummary.js';\nimport { CreditBalanceTransactions as BillingCreditBalanceTransactions } from './resources/Billing/CreditBalanceTransactions.js';\nimport { CreditGrants as BillingCreditGrants } from './resources/Billing/CreditGrants.js';\nimport { CreditReversals as TreasuryCreditReversals } from './resources/Treasury/CreditReversals.js';\nimport { Customers as TestHelpersCustomers } from './resources/TestHelpers/Customers.js';\nimport { DebitReversals as TreasuryDebitReversals } from './resources/Treasury/DebitReversals.js';\nimport { Disputes as IssuingDisputes } from './resources/Issuing/Disputes.js';\nimport { EarlyFraudWarnings as RadarEarlyFraudWarnings } from './resources/Radar/EarlyFraudWarnings.js';\nimport { EventDestinations as V2CoreEventDestinations } from './resources/V2/Core/EventDestinations.js';\nimport { Events as V2CoreEvents } from './resources/V2/Core/Events.js';\nimport { Features as EntitlementsFeatures } from './resources/Entitlements/Features.js';\nimport { FinancialAccounts as TreasuryFinancialAccounts } from './resources/Treasury/FinancialAccounts.js';\nimport { InboundTransfers as TestHelpersTreasuryInboundTransfers } from './resources/TestHelpers/Treasury/InboundTransfers.js';\nimport { InboundTransfers as TreasuryInboundTransfers } from './resources/Treasury/InboundTransfers.js';\nimport { Locations as TerminalLocations } from './resources/Terminal/Locations.js';\nimport { MeterEventAdjustments as BillingMeterEventAdjustments } from './resources/Billing/MeterEventAdjustments.js';\nimport { MeterEventAdjustments as V2BillingMeterEventAdjustments } from './resources/V2/Billing/MeterEventAdjustments.js';\nimport { MeterEventSession as V2BillingMeterEventSession } from './resources/V2/Billing/MeterEventSession.js';\nimport { MeterEventStream as V2BillingMeterEventStream } from './resources/V2/Billing/MeterEventStream.js';\nimport { MeterEvents as BillingMeterEvents } from './resources/Billing/MeterEvents.js';\nimport { MeterEvents as V2BillingMeterEvents } from './resources/V2/Billing/MeterEvents.js';\nimport { Meters as BillingMeters } from './resources/Billing/Meters.js';\nimport { Orders as ClimateOrders } from './resources/Climate/Orders.js';\nimport { OutboundPayments as TestHelpersTreasuryOutboundPayments } from './resources/TestHelpers/Treasury/OutboundPayments.js';\nimport { OutboundPayments as TreasuryOutboundPayments } from './resources/Treasury/OutboundPayments.js';\nimport { OutboundTransfers as TestHelpersTreasuryOutboundTransfers } from './resources/TestHelpers/Treasury/OutboundTransfers.js';\nimport { OutboundTransfers as TreasuryOutboundTransfers } from './resources/Treasury/OutboundTransfers.js';\nimport { PersonalizationDesigns as TestHelpersIssuingPersonalizationDesigns } from './resources/TestHelpers/Issuing/PersonalizationDesigns.js';\nimport { PersonalizationDesigns as IssuingPersonalizationDesigns } from './resources/Issuing/PersonalizationDesigns.js';\nimport { PhysicalBundles as IssuingPhysicalBundles } from './resources/Issuing/PhysicalBundles.js';\nimport { Products as ClimateProducts } from './resources/Climate/Products.js';\nimport { Readers as TestHelpersTerminalReaders } from './resources/TestHelpers/Terminal/Readers.js';\nimport { Readers as TerminalReaders } from './resources/Terminal/Readers.js';\nimport { ReceivedCredits as TestHelpersTreasuryReceivedCredits } from './resources/TestHelpers/Treasury/ReceivedCredits.js';\nimport { ReceivedCredits as TreasuryReceivedCredits } from './resources/Treasury/ReceivedCredits.js';\nimport { ReceivedDebits as TestHelpersTreasuryReceivedDebits } from './resources/TestHelpers/Treasury/ReceivedDebits.js';\nimport { ReceivedDebits as TreasuryReceivedDebits } from './resources/Treasury/ReceivedDebits.js';\nimport { Refunds as TestHelpersRefunds } from './resources/TestHelpers/Refunds.js';\nimport { Registrations as TaxRegistrations } from './resources/Tax/Registrations.js';\nimport { ReportRuns as ReportingReportRuns } from './resources/Reporting/ReportRuns.js';\nimport { ReportTypes as ReportingReportTypes } from './resources/Reporting/ReportTypes.js';\nimport { Requests as ForwardingRequests } from './resources/Forwarding/Requests.js';\nimport { ScheduledQueryRuns as SigmaScheduledQueryRuns } from './resources/Sigma/ScheduledQueryRuns.js';\nimport { Secrets as AppsSecrets } from './resources/Apps/Secrets.js';\nimport { Sessions as BillingPortalSessions } from './resources/BillingPortal/Sessions.js';\nimport { Sessions as CheckoutSessions } from './resources/Checkout/Sessions.js';\nimport { Sessions as FinancialConnectionsSessions } from './resources/FinancialConnections/Sessions.js';\nimport { Settings as TaxSettings } from './resources/Tax/Settings.js';\nimport { Suppliers as ClimateSuppliers } from './resources/Climate/Suppliers.js';\nimport { TestClocks as TestHelpersTestClocks } from './resources/TestHelpers/TestClocks.js';\nimport { Tokens as IssuingTokens } from './resources/Issuing/Tokens.js';\nimport { TransactionEntries as TreasuryTransactionEntries } from './resources/Treasury/TransactionEntries.js';\nimport { Transactions as TestHelpersIssuingTransactions } from './resources/TestHelpers/Issuing/Transactions.js';\nimport { Transactions as FinancialConnectionsTransactions } from './resources/FinancialConnections/Transactions.js';\nimport { Transactions as IssuingTransactions } from './resources/Issuing/Transactions.js';\nimport { Transactions as TaxTransactions } from './resources/Tax/Transactions.js';\nimport { Transactions as TreasuryTransactions } from './resources/Treasury/Transactions.js';\nimport { ValueListItems as RadarValueListItems } from './resources/Radar/ValueListItems.js';\nimport { ValueLists as RadarValueLists } from './resources/Radar/ValueLists.js';\nimport { VerificationReports as IdentityVerificationReports } from './resources/Identity/VerificationReports.js';\nimport { VerificationSessions as IdentityVerificationSessions } from './resources/Identity/VerificationSessions.js';\nexport { Accounts as Account } from './resources/Accounts.js';\nexport { AccountLinks } from './resources/AccountLinks.js';\nexport { AccountSessions } from './resources/AccountSessions.js';\nexport { Accounts } from './resources/Accounts.js';\nexport { ApplePayDomains } from './resources/ApplePayDomains.js';\nexport { ApplicationFees } from './resources/ApplicationFees.js';\nexport { Balance } from './resources/Balance.js';\nexport { BalanceTransactions } from './resources/BalanceTransactions.js';\nexport { Charges } from './resources/Charges.js';\nexport { ConfirmationTokens } from './resources/ConfirmationTokens.js';\nexport { CountrySpecs } from './resources/CountrySpecs.js';\nexport { Coupons } from './resources/Coupons.js';\nexport { CreditNotes } from './resources/CreditNotes.js';\nexport { CustomerSessions } from './resources/CustomerSessions.js';\nexport { Customers } from './resources/Customers.js';\nexport { Disputes } from './resources/Disputes.js';\nexport { EphemeralKeys } from './resources/EphemeralKeys.js';\nexport { Events } from './resources/Events.js';\nexport { ExchangeRates } from './resources/ExchangeRates.js';\nexport { FileLinks } from './resources/FileLinks.js';\nexport { Files } from './resources/Files.js';\nexport { InvoiceItems } from './resources/InvoiceItems.js';\nexport { InvoicePayments } from './resources/InvoicePayments.js';\nexport { InvoiceRenderingTemplates } from './resources/InvoiceRenderingTemplates.js';\nexport { Invoices } from './resources/Invoices.js';\nexport { Mandates } from './resources/Mandates.js';\nexport { OAuth } from './resources/OAuth.js';\nexport { PaymentIntents } from './resources/PaymentIntents.js';\nexport { PaymentLinks } from './resources/PaymentLinks.js';\nexport { PaymentMethodConfigurations } from './resources/PaymentMethodConfigurations.js';\nexport { PaymentMethodDomains } from './resources/PaymentMethodDomains.js';\nexport { PaymentMethods } from './resources/PaymentMethods.js';\nexport { Payouts } from './resources/Payouts.js';\nexport { Plans } from './resources/Plans.js';\nexport { Prices } from './resources/Prices.js';\nexport { Products } from './resources/Products.js';\nexport { PromotionCodes } from './resources/PromotionCodes.js';\nexport { Quotes } from './resources/Quotes.js';\nexport { Refunds } from './resources/Refunds.js';\nexport { Reviews } from './resources/Reviews.js';\nexport { SetupAttempts } from './resources/SetupAttempts.js';\nexport { SetupIntents } from './resources/SetupIntents.js';\nexport { ShippingRates } from './resources/ShippingRates.js';\nexport { Sources } from './resources/Sources.js';\nexport { SubscriptionItems } from './resources/SubscriptionItems.js';\nexport { SubscriptionSchedules } from './resources/SubscriptionSchedules.js';\nexport { Subscriptions } from './resources/Subscriptions.js';\nexport { TaxCodes } from './resources/TaxCodes.js';\nexport { TaxIds } from './resources/TaxIds.js';\nexport { TaxRates } from './resources/TaxRates.js';\nexport { Tokens } from './resources/Tokens.js';\nexport { Topups } from './resources/Topups.js';\nexport { Transfers } from './resources/Transfers.js';\nexport { WebhookEndpoints } from './resources/WebhookEndpoints.js';\nexport const Apps = resourceNamespace('apps', { Secrets: AppsSecrets });\nexport const Billing = resourceNamespace('billing', {\n    Alerts: BillingAlerts,\n    CreditBalanceSummary: BillingCreditBalanceSummary,\n    CreditBalanceTransactions: BillingCreditBalanceTransactions,\n    CreditGrants: BillingCreditGrants,\n    MeterEventAdjustments: BillingMeterEventAdjustments,\n    MeterEvents: BillingMeterEvents,\n    Meters: BillingMeters,\n});\nexport const BillingPortal = resourceNamespace('billingPortal', {\n    Configurations: BillingPortalConfigurations,\n    Sessions: BillingPortalSessions,\n});\nexport const Checkout = resourceNamespace('checkout', {\n    Sessions: CheckoutSessions,\n});\nexport const Climate = resourceNamespace('climate', {\n    Orders: ClimateOrders,\n    Products: ClimateProducts,\n    Suppliers: ClimateSuppliers,\n});\nexport const Entitlements = resourceNamespace('entitlements', {\n    ActiveEntitlements: EntitlementsActiveEntitlements,\n    Features: EntitlementsFeatures,\n});\nexport const FinancialConnections = resourceNamespace('financialConnections', {\n    Accounts: FinancialConnectionsAccounts,\n    Sessions: FinancialConnectionsSessions,\n    Transactions: FinancialConnectionsTransactions,\n});\nexport const Forwarding = resourceNamespace('forwarding', {\n    Requests: ForwardingRequests,\n});\nexport const Identity = resourceNamespace('identity', {\n    VerificationReports: IdentityVerificationReports,\n    VerificationSessions: IdentityVerificationSessions,\n});\nexport const Issuing = resourceNamespace('issuing', {\n    Authorizations: IssuingAuthorizations,\n    Cardholders: IssuingCardholders,\n    Cards: IssuingCards,\n    Disputes: IssuingDisputes,\n    PersonalizationDesigns: IssuingPersonalizationDesigns,\n    PhysicalBundles: IssuingPhysicalBundles,\n    Tokens: IssuingTokens,\n    Transactions: IssuingTransactions,\n});\nexport const Radar = resourceNamespace('radar', {\n    EarlyFraudWarnings: RadarEarlyFraudWarnings,\n    ValueListItems: RadarValueListItems,\n    ValueLists: RadarValueLists,\n});\nexport const Reporting = resourceNamespace('reporting', {\n    ReportRuns: ReportingReportRuns,\n    ReportTypes: ReportingReportTypes,\n});\nexport const Sigma = resourceNamespace('sigma', {\n    ScheduledQueryRuns: SigmaScheduledQueryRuns,\n});\nexport const Tax = resourceNamespace('tax', {\n    Calculations: TaxCalculations,\n    Registrations: TaxRegistrations,\n    Settings: TaxSettings,\n    Transactions: TaxTransactions,\n});\nexport const Terminal = resourceNamespace('terminal', {\n    Configurations: TerminalConfigurations,\n    ConnectionTokens: TerminalConnectionTokens,\n    Locations: TerminalLocations,\n    Readers: TerminalReaders,\n});\nexport const TestHelpers = resourceNamespace('testHelpers', {\n    ConfirmationTokens: TestHelpersConfirmationTokens,\n    Customers: TestHelpersCustomers,\n    Refunds: TestHelpersRefunds,\n    TestClocks: TestHelpersTestClocks,\n    Issuing: resourceNamespace('issuing', {\n        Authorizations: TestHelpersIssuingAuthorizations,\n        Cards: TestHelpersIssuingCards,\n        PersonalizationDesigns: TestHelpersIssuingPersonalizationDesigns,\n        Transactions: TestHelpersIssuingTransactions,\n    }),\n    Terminal: resourceNamespace('terminal', {\n        Readers: TestHelpersTerminalReaders,\n    }),\n    Treasury: resourceNamespace('treasury', {\n        InboundTransfers: TestHelpersTreasuryInboundTransfers,\n        OutboundPayments: TestHelpersTreasuryOutboundPayments,\n        OutboundTransfers: TestHelpersTreasuryOutboundTransfers,\n        ReceivedCredits: TestHelpersTreasuryReceivedCredits,\n        ReceivedDebits: TestHelpersTreasuryReceivedDebits,\n    }),\n});\nexport const Treasury = resourceNamespace('treasury', {\n    CreditReversals: TreasuryCreditReversals,\n    DebitReversals: TreasuryDebitReversals,\n    FinancialAccounts: TreasuryFinancialAccounts,\n    InboundTransfers: TreasuryInboundTransfers,\n    OutboundPayments: TreasuryOutboundPayments,\n    OutboundTransfers: TreasuryOutboundTransfers,\n    ReceivedCredits: TreasuryReceivedCredits,\n    ReceivedDebits: TreasuryReceivedDebits,\n    TransactionEntries: TreasuryTransactionEntries,\n    Transactions: TreasuryTransactions,\n});\nexport const V2 = resourceNamespace('v2', {\n    Billing: resourceNamespace('billing', {\n        MeterEventAdjustments: V2BillingMeterEventAdjustments,\n        MeterEventSession: V2BillingMeterEventSession,\n        MeterEventStream: V2BillingMeterEventStream,\n        MeterEvents: V2BillingMeterEvents,\n    }),\n    Core: resourceNamespace('core', {\n        EventDestinations: V2CoreEventDestinations,\n        Events: V2CoreEvents,\n    }),\n});\n", "// ResourceNamespace allows you to create nested resources, i.e. `stripe.issuing.cards`.\n// It also works recursively, so you could do i.e. `stripe.billing.invoicing.pay`.\nfunction ResourceNamespace(stripe, resources) {\n    for (const name in resources) {\n        if (!Object.prototype.hasOwnProperty.call(resources, name)) {\n            continue;\n        }\n        const camelCaseName = name[0].toLowerCase() + name.substring(1);\n        const resource = new resources[name](stripe);\n        this[camelCaseName] = resource;\n    }\n}\nexport function resourceNamespace(namespace, resources) {\n    return function (stripe) {\n        return new ResourceNamespace(stripe, resources);\n    };\n}\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Accounts = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/accounts/{account}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/accounts',\n        methodType: 'list',\n    }),\n    disconnect: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/accounts/{account}/disconnect',\n    }),\n    listOwners: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/accounts/{account}/owners',\n        methodType: 'list',\n    }),\n    refresh: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/accounts/{account}/refresh',\n    }),\n    subscribe: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/accounts/{account}/subscribe',\n    }),\n    unsubscribe: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/accounts/{account}/unsubscribe',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ActiveEntitlements = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/entitlements/active_entitlements/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/entitlements/active_entitlements',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Alerts = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/alerts' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/billing/alerts/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/alerts',\n        methodType: 'list',\n    }),\n    activate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/alerts/{id}/activate',\n    }),\n    archive: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/alerts/{id}/archive',\n    }),\n    deactivate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/alerts/{id}/deactivate',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Authorizations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations',\n    }),\n    capture: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/capture',\n    }),\n    expire: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/expire',\n    }),\n    finalizeAmount: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount',\n    }),\n    increment: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/increment',\n    }),\n    respond: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/fraud_challenges/respond',\n    }),\n    reverse: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/reverse',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Authorizations = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/authorizations/{authorization}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/authorizations/{authorization}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/authorizations',\n        methodType: 'list',\n    }),\n    approve: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/authorizations/{authorization}/approve',\n    }),\n    decline: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/authorizations/{authorization}/decline',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Calculations = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tax/calculations' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/calculations/{calculation}',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/calculations/{calculation}/line_items',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Cardholders = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/issuing/cardholders' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/cardholders/{cardholder}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/cardholders/{cardholder}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/cardholders',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Cards = StripeResource.extend({\n    deliverCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/deliver',\n    }),\n    failCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/fail',\n    }),\n    returnCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/return',\n    }),\n    shipCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/ship',\n    }),\n    submitCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/submit',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Cards = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/issuing/cards' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/issuing/cards/{card}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/issuing/cards/{card}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/cards',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Configurations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing_portal/configurations',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing_portal/configurations/{configuration}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing_portal/configurations/{configuration}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing_portal/configurations',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Configurations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/configurations',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/configurations/{configuration}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/configurations/{configuration}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/configurations',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/terminal/configurations/{configuration}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ConfirmationTokens = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/confirmation_tokens',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ConnectionTokens = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/connection_tokens',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditBalanceSummary = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_balance_summary',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditBalanceTransactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_balance_transactions/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_balance_transactions',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditGrants = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/credit_grants' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_grants/{id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/credit_grants/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_grants',\n        methodType: 'list',\n    }),\n    expire: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/credit_grants/{id}/expire',\n    }),\n    voidGrant: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/credit_grants/{id}/void',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditReversals = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/credit_reversals',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/credit_reversals/{credit_reversal}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/credit_reversals',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Customers = StripeResource.extend({\n    fundCashBalance: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/customers/{customer}/fund_cash_balance',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const DebitReversals = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/debit_reversals',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/debit_reversals/{debit_reversal}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/debit_reversals',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Disputes = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/issuing/disputes' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/disputes/{dispute}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/disputes/{dispute}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/disputes',\n        methodType: 'list',\n    }),\n    submit: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/disputes/{dispute}/submit',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const EarlyFraudWarnings = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/early_fraud_warnings/{early_fraud_warning}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/early_fraud_warnings',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const EventDestinations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v2/core/event_destinations/{id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v2/core/event_destinations',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v2/core/event_destinations/{id}',\n    }),\n    disable: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations/{id}/disable',\n    }),\n    enable: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations/{id}/enable',\n    }),\n    ping: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations/{id}/ping',\n    }),\n});\n", "// This file is manually maintained\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Events = StripeResource.extend({\n    retrieve(...args) {\n        const transformResponseData = (response) => {\n            return this.addFetchRelatedObjectIfNeeded(response);\n        };\n        return stripeMethod({\n            method: 'GET',\n            fullPath: '/v2/core/events/{id}',\n            transformResponseData,\n        }).apply(this, args);\n    },\n    list(...args) {\n        const transformResponseData = (response) => {\n            return Object.assign(Object.assign({}, response), { data: response.data.map(this.addFetchRelatedObjectIfNeeded.bind(this)) });\n        };\n        return stripeMethod({\n            method: 'GET',\n            fullPath: '/v2/core/events',\n            methodType: 'list',\n            transformResponseData,\n        }).apply(this, args);\n    },\n    /**\n     * @private\n     *\n     * For internal use in stripe-node.\n     *\n     * @param pulledEvent The retrieved event object\n     * @returns The retrieved event object with a fetchRelatedObject method,\n     * if pulledEvent.related_object is valid (non-null and has a url)\n     */\n    addFetchRelatedObjectIfNeeded(pulledEvent) {\n        if (!pulledEvent.related_object || !pulledEvent.related_object.url) {\n            return pulledEvent;\n        }\n        return Object.assign(Object.assign({}, pulledEvent), { fetchRelatedObject: () => \n            // call stripeMethod with 'this' resource to fetch\n            // the related object. 'this' is needed to construct\n            // and send the request, but the method spec controls\n            // the url endpoint and method, so it doesn't matter\n            // that 'this' is an Events resource object here\n            stripeMethod({\n                method: 'GET',\n                fullPath: pulledEvent.related_object.url,\n            }).apply(this, [\n                {\n                    stripeAccount: pulledEvent.context,\n                },\n            ]) });\n    },\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Features = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/entitlements/features' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/entitlements/features/{id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/entitlements/features/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/entitlements/features',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const FinancialAccounts = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/financial_accounts',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/financial_accounts',\n        methodType: 'list',\n    }),\n    close: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}/close',\n    }),\n    retrieveFeatures: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}/features',\n    }),\n    updateFeatures: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}/features',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InboundTransfers = StripeResource.extend({\n    fail: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/inbound_transfers/{id}/fail',\n    }),\n    returnInboundTransfer: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/inbound_transfers/{id}/return',\n    }),\n    succeed: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/inbound_transfers/{id}/succeed',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InboundTransfers = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/inbound_transfers',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/inbound_transfers/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/inbound_transfers',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/inbound_transfers/{inbound_transfer}/cancel',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Locations = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/terminal/locations' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/locations/{location}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/locations/{location}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/locations',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/terminal/locations/{location}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEventAdjustments = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/meter_event_adjustments',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEventAdjustments = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/billing/meter_event_adjustments',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEventSession = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/billing/meter_event_session',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEventStream = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/billing/meter_event_stream',\n        host: 'meter-events.stripe.com',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEvents = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meter_events' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEvents = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v2/billing/meter_events' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Meters = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meters' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/billing/meters/{id}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meters/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/meters',\n        methodType: 'list',\n    }),\n    deactivate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/meters/{id}/deactivate',\n    }),\n    listEventSummaries: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/meters/{id}/event_summaries',\n        methodType: 'list',\n    }),\n    reactivate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/meters/{id}/reactivate',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Orders = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/climate/orders' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/orders/{order}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/climate/orders/{order}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/orders',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/climate/orders/{order}/cancel',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const OutboundPayments = StripeResource.extend({\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_payments/{id}',\n    }),\n    fail: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_payments/{id}/fail',\n    }),\n    post: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_payments/{id}/post',\n    }),\n    returnOutboundPayment: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_payments/{id}/return',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const OutboundPayments = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/outbound_payments',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/outbound_payments/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/outbound_payments',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/outbound_payments/{id}/cancel',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const OutboundTransfers = StripeResource.extend({\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}',\n    }),\n    fail: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail',\n    }),\n    post: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post',\n    }),\n    returnOutboundTransfer: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const OutboundTransfers = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/outbound_transfers',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/outbound_transfers/{outbound_transfer}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/outbound_transfers',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/outbound_transfers/{outbound_transfer}/cancel',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PersonalizationDesigns = StripeResource.extend({\n    activate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate',\n    }),\n    deactivate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate',\n    }),\n    reject: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PersonalizationDesigns = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/personalization_designs',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/personalization_designs/{personalization_design}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/personalization_designs/{personalization_design}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/personalization_designs',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PhysicalBundles = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/physical_bundles/{physical_bundle}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/physical_bundles',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Products = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/products/{product}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/products',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Readers = StripeResource.extend({\n    presentPaymentMethod: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/terminal/readers/{reader}/present_payment_method',\n    }),\n    succeedInputCollection: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/terminal/readers/{reader}/succeed_input_collection',\n    }),\n    timeoutInputCollection: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/terminal/readers/{reader}/timeout_input_collection',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Readers = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/terminal/readers' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/readers/{reader}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/readers',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/terminal/readers/{reader}',\n    }),\n    cancelAction: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/cancel_action',\n    }),\n    collectInputs: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/collect_inputs',\n    }),\n    processPaymentIntent: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/process_payment_intent',\n    }),\n    processSetupIntent: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/process_setup_intent',\n    }),\n    refundPayment: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/refund_payment',\n    }),\n    setReaderDisplay: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/set_reader_display',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReceivedCredits = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/received_credits',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReceivedCredits = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/received_credits/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/received_credits',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReceivedDebits = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/received_debits',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReceivedDebits = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/received_debits/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/received_debits',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Refunds = StripeResource.extend({\n    expire: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/refunds/{refund}/expire',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Registrations = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tax/registrations' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/registrations/{id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/tax/registrations/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/registrations',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReportRuns = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/reporting/report_runs' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reporting/report_runs/{report_run}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reporting/report_runs',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReportTypes = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reporting/report_types/{report_type}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reporting/report_types',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Requests = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/forwarding/requests' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/forwarding/requests/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/forwarding/requests',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ScheduledQueryRuns = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/sigma/scheduled_query_runs/{scheduled_query_run}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/sigma/scheduled_query_runs',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Secrets = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/apps/secrets' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/apps/secrets',\n        methodType: 'list',\n    }),\n    deleteWhere: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/apps/secrets/delete',\n    }),\n    find: stripeMethod({ method: 'GET', fullPath: '/v1/apps/secrets/find' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Sessions = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing_portal/sessions',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Sessions = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/checkout/sessions' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/checkout/sessions/{session}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/checkout/sessions/{session}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/checkout/sessions',\n        methodType: 'list',\n    }),\n    expire: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/checkout/sessions/{session}/expire',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/checkout/sessions/{session}/line_items',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Sessions = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/sessions',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/sessions/{session}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Settings = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tax/settings' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/tax/settings' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Suppliers = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/suppliers/{supplier}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/suppliers',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TestClocks = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/test_clocks',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/test_helpers/test_clocks/{test_clock}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/test_helpers/test_clocks',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/test_helpers/test_clocks/{test_clock}',\n    }),\n    advance: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/test_clocks/{test_clock}/advance',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Tokens = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/tokens/{token}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/tokens/{token}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/tokens',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TransactionEntries = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/transaction_entries/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/transaction_entries',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    createForceCapture: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/transactions/create_force_capture',\n    }),\n    createUnlinkedRefund: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/transactions/create_unlinked_refund',\n    }),\n    refund: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/transactions/{transaction}/refund',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/transactions/{transaction}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/transactions',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/transactions/{transaction}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/transactions/{transaction}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/transactions',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/transactions/{transaction}',\n    }),\n    createFromCalculation: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/tax/transactions/create_from_calculation',\n    }),\n    createReversal: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/tax/transactions/create_reversal',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/transactions/{transaction}/line_items',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/transactions/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/transactions',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ValueListItems = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/radar/value_list_items',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/value_list_items/{item}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/value_list_items',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/radar/value_list_items/{item}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ValueLists = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/radar/value_lists' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/value_lists/{value_list}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/radar/value_lists/{value_list}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/value_lists',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/radar/value_lists/{value_list}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const VerificationReports = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/identity/verification_reports/{report}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/identity/verification_reports',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const VerificationSessions = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/identity/verification_sessions',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/identity/verification_sessions/{session}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/identity/verification_sessions/{session}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/identity/verification_sessions',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/identity/verification_sessions/{session}/cancel',\n    }),\n    redact: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/identity/verification_sessions/{session}/redact',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\n// Since path can either be `account` or `accounts`, support both through stripeMethod path\nexport const Accounts = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/accounts' }),\n    retrieve(id, ...args) {\n        // No longer allow an api key to be passed as the first string to this function due to ambiguity between\n        // old account ids and api keys. To request the account for an api key, send null as the id\n        if (typeof id === 'string') {\n            return stripeMethod({\n                method: 'GET',\n                fullPath: '/v1/accounts/{id}',\n            }).apply(this, [id, ...args]);\n        }\n        else {\n            if (id === null || id === undefined) {\n                // Remove id as stripeMethod would complain of unexpected argument\n                [].shift.apply([id, ...args]);\n            }\n            return stripeMethod({\n                method: 'GET',\n                fullPath: '/v1/account',\n            }).apply(this, [id, ...args]);\n        }\n    },\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/accounts/{account}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/accounts/{account}' }),\n    createExternalAccount: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/external_accounts',\n    }),\n    createLoginLink: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/login_links',\n    }),\n    createPerson: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/persons',\n    }),\n    deleteExternalAccount: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/accounts/{account}/external_accounts/{id}',\n    }),\n    deletePerson: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/accounts/{account}/persons/{person}',\n    }),\n    listCapabilities: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/capabilities',\n        methodType: 'list',\n    }),\n    listExternalAccounts: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/external_accounts',\n        methodType: 'list',\n    }),\n    listPersons: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/persons',\n        methodType: 'list',\n    }),\n    reject: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/reject',\n    }),\n    retrieveCurrent: stripeMethod({ method: 'GET', fullPath: '/v1/account' }),\n    retrieveCapability: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/capabilities/{capability}',\n    }),\n    retrieveExternalAccount: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/external_accounts/{id}',\n    }),\n    retrievePerson: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/persons/{person}',\n    }),\n    updateCapability: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/capabilities/{capability}',\n    }),\n    updateExternalAccount: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/external_accounts/{id}',\n    }),\n    updatePerson: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/persons/{person}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const AccountLinks = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/account_links' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const AccountSessions = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/account_sessions' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ApplePayDomains = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/apple_pay/domains' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/apple_pay/domains/{domain}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/apple_pay/domains',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/apple_pay/domains/{domain}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ApplicationFees = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/application_fees/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/application_fees',\n        methodType: 'list',\n    }),\n    createRefund: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/application_fees/{id}/refunds',\n    }),\n    listRefunds: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/application_fees/{id}/refunds',\n        methodType: 'list',\n    }),\n    retrieveRefund: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/application_fees/{fee}/refunds/{id}',\n    }),\n    updateRefund: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/application_fees/{fee}/refunds/{id}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Balance = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/balance' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const BalanceTransactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/balance_transactions/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/balance_transactions',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Charges = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/charges' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/charges/{charge}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/charges/{charge}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/charges',\n        methodType: 'list',\n    }),\n    capture: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/charges/{charge}/capture',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/charges/search',\n        methodType: 'search',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ConfirmationTokens = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/confirmation_tokens/{confirmation_token}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CountrySpecs = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/country_specs/{country}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/country_specs',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Coupons = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/coupons' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/coupons/{coupon}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/coupons/{coupon}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/coupons',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/coupons/{coupon}' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditNotes = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/credit_notes' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/credit_notes/{id}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/credit_notes/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/credit_notes',\n        methodType: 'list',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/credit_notes/{credit_note}/lines',\n        methodType: 'list',\n    }),\n    listPreviewLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/credit_notes/preview/lines',\n        methodType: 'list',\n    }),\n    preview: stripeMethod({ method: 'GET', fullPath: '/v1/credit_notes/preview' }),\n    voidCreditNote: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/credit_notes/{id}/void',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CustomerSessions = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/customer_sessions' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Customers = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/customers' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/customers/{customer}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/customers/{customer}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/customers/{customer}' }),\n    createBalanceTransaction: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/balance_transactions',\n    }),\n    createFundingInstructions: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/funding_instructions',\n    }),\n    createSource: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/sources',\n    }),\n    createTaxId: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/tax_ids',\n    }),\n    deleteDiscount: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/customers/{customer}/discount',\n    }),\n    deleteSource: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/customers/{customer}/sources/{id}',\n    }),\n    deleteTaxId: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/customers/{customer}/tax_ids/{id}',\n    }),\n    listBalanceTransactions: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/balance_transactions',\n        methodType: 'list',\n    }),\n    listCashBalanceTransactions: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/cash_balance_transactions',\n        methodType: 'list',\n    }),\n    listPaymentMethods: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/payment_methods',\n        methodType: 'list',\n    }),\n    listSources: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/sources',\n        methodType: 'list',\n    }),\n    listTaxIds: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/tax_ids',\n        methodType: 'list',\n    }),\n    retrieveBalanceTransaction: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/balance_transactions/{transaction}',\n    }),\n    retrieveCashBalance: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/cash_balance',\n    }),\n    retrieveCashBalanceTransaction: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/cash_balance_transactions/{transaction}',\n    }),\n    retrievePaymentMethod: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/payment_methods/{payment_method}',\n    }),\n    retrieveSource: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/sources/{id}',\n    }),\n    retrieveTaxId: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/tax_ids/{id}',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/search',\n        methodType: 'search',\n    }),\n    updateBalanceTransaction: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/balance_transactions/{transaction}',\n    }),\n    updateCashBalance: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/cash_balance',\n    }),\n    updateSource: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/sources/{id}',\n    }),\n    verifySource: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/sources/{id}/verify',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Disputes = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/disputes/{dispute}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/disputes/{dispute}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/disputes',\n        methodType: 'list',\n    }),\n    close: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/disputes/{dispute}/close',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const EphemeralKeys = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/ephemeral_keys',\n        validator: (data, options) => {\n            if (!options.headers || !options.headers['Stripe-Version']) {\n                throw new Error('Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node');\n            }\n        },\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/ephemeral_keys/{key}' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Events = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/events/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/events',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ExchangeRates = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/exchange_rates/{rate_id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/exchange_rates',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const FileLinks = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/file_links' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/file_links/{link}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/file_links/{link}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/file_links',\n        methodType: 'list',\n    }),\n});\n", "import { flattenAndStringify, queryStringifyRequestData } from './utils.js';\n// Method for formatting HTTP body for the multipart/form-data specification\n// Mostly taken from Fermata.js\n// https://github.com/natevw/fermata/blob/5d9732a33d776ce925013a265935facd1626cc88/fermata.js#L315-L343\nconst multipartDataGenerator = (method, data, headers) => {\n    const segno = (Math.round(Math.random() * 1e16) + Math.round(Math.random() * 1e16)).toString();\n    headers['Content-Type'] = `multipart/form-data; boundary=${segno}`;\n    const textEncoder = new TextEncoder();\n    let buffer = new Uint8Array(0);\n    const endBuffer = textEncoder.encode('\\r\\n');\n    function push(l) {\n        const prevBuffer = buffer;\n        const newBuffer = l instanceof Uint8Array ? l : new Uint8Array(textEncoder.encode(l));\n        buffer = new Uint8Array(prevBuffer.length + newBuffer.length + 2);\n        buffer.set(prevBuffer);\n        buffer.set(newBuffer, prevBuffer.length);\n        buffer.set(endBuffer, buffer.length - 2);\n    }\n    function q(s) {\n        return `\"${s.replace(/\"|\"/g, '%22').replace(/\\r\\n|\\r|\\n/g, ' ')}\"`;\n    }\n    const flattenedData = flattenAndStringify(data);\n    for (const k in flattenedData) {\n        if (!Object.prototype.hasOwnProperty.call(flattenedData, k)) {\n            continue;\n        }\n        const v = flattenedData[k];\n        push(`--${segno}`);\n        if (Object.prototype.hasOwnProperty.call(v, 'data')) {\n            const typedEntry = v;\n            push(`Content-Disposition: form-data; name=${q(k)}; filename=${q(typedEntry.name || 'blob')}`);\n            push(`Content-Type: ${typedEntry.type || 'application/octet-stream'}`);\n            push('');\n            push(typedEntry.data);\n        }\n        else {\n            push(`Content-Disposition: form-data; name=${q(k)}`);\n            push('');\n            push(v);\n        }\n    }\n    push(`--${segno}--`);\n    return buffer;\n};\nexport function multipartRequestDataProcessor(method, data, headers, callback) {\n    data = data || {};\n    if (method !== 'POST') {\n        return callback(null, queryStringifyRequestData(data));\n    }\n    this._stripe._platformFunctions\n        .tryBufferData(data)\n        .then((bufferedData) => {\n        const buffer = multipartDataGenerator(method, bufferedData, headers);\n        return callback(null, buffer);\n    })\n        .catch((err) => callback(err, null));\n}\n", "// File generated from our OpenAPI spec\nimport { multipartRequestDataProcessor } from '../multipart.js';\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Files = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/files',\n        headers: {\n            'Content-Type': 'multipart/form-data',\n        },\n        host: 'files.stripe.com',\n    }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/files/{file}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/files',\n        methodType: 'list',\n    }),\n    requestDataProcessor: multipartRequestDataProcessor,\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InvoiceItems = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/invoiceitems' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoiceitems/{invoiceitem}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoiceitems/{invoiceitem}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoiceitems',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/invoiceitems/{invoiceitem}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InvoicePayments = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoice_payments/{invoice_payment}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoice_payments',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InvoiceRenderingTemplates = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoice_rendering_templates/{template}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoice_rendering_templates',\n        methodType: 'list',\n    }),\n    archive: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoice_rendering_templates/{template}/archive',\n    }),\n    unarchive: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoice_rendering_templates/{template}/unarchive',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Invoices = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/invoices' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/invoices/{invoice}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/invoices/{invoice}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoices',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/invoices/{invoice}' }),\n    addLines: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/add_lines',\n    }),\n    attachPayment: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/attach_payment',\n    }),\n    createPreview: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/create_preview',\n    }),\n    finalizeInvoice: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/finalize',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoices/{invoice}/lines',\n        methodType: 'list',\n    }),\n    markUncollectible: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/mark_uncollectible',\n    }),\n    pay: stripeMethod({ method: 'POST', fullPath: '/v1/invoices/{invoice}/pay' }),\n    removeLines: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/remove_lines',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoices/search',\n        methodType: 'search',\n    }),\n    sendInvoice: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/send',\n    }),\n    updateLines: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/update_lines',\n    }),\n    updateLineItem: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/lines/{line_item_id}',\n    }),\n    voidInvoice: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/void',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Mandates = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/mandates/{mandate}' }),\n});\n", "'use strict';\nimport { StripeResource } from '../StripeResource.js';\nimport { queryStringifyRequestData } from '../utils.js';\nconst stripeMethod = StripeResource.method;\nconst oAuthHost = 'connect.stripe.com';\nexport const OAuth = StripeResource.extend({\n    basePath: '/',\n    authorizeUrl(params, options) {\n        params = params || {};\n        options = options || {};\n        let path = 'oauth/authorize';\n        // For Express accounts, the path changes\n        if (options.express) {\n            path = `express/${path}`;\n        }\n        if (!params.response_type) {\n            params.response_type = 'code';\n        }\n        if (!params.client_id) {\n            params.client_id = this._stripe.getClientId();\n        }\n        if (!params.scope) {\n            params.scope = 'read_write';\n        }\n        return `https://${oAuthHost}/${path}?${queryStringifyRequestData(params)}`;\n    },\n    token: stripeMethod({\n        method: 'POST',\n        path: 'oauth/token',\n        host: oAuthHost,\n    }),\n    deauthorize(spec, ...args) {\n        if (!spec.client_id) {\n            spec.client_id = this._stripe.getClientId();\n        }\n        return stripeMethod({\n            method: 'POST',\n            path: 'oauth/deauthorize',\n            host: oAuthHost,\n        }).apply(this, [spec, ...args]);\n    },\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentIntents = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/payment_intents' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_intents/{intent}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_intents',\n        methodType: 'list',\n    }),\n    applyCustomerBalance: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/apply_customer_balance',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/cancel',\n    }),\n    capture: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/capture',\n    }),\n    confirm: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/confirm',\n    }),\n    incrementAuthorization: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/increment_authorization',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_intents/search',\n        methodType: 'search',\n    }),\n    verifyMicrodeposits: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/verify_microdeposits',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentLinks = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/payment_links' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_links/{payment_link}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_links/{payment_link}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_links',\n        methodType: 'list',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_links/{payment_link}/line_items',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentMethodConfigurations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_configurations',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_method_configurations/{configuration}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_configurations/{configuration}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_method_configurations',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentMethodDomains = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_domains',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_method_domains/{payment_method_domain}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_domains/{payment_method_domain}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_method_domains',\n        methodType: 'list',\n    }),\n    validate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_domains/{payment_method_domain}/validate',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentMethods = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/payment_methods' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_methods/{payment_method}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_methods/{payment_method}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_methods',\n        methodType: 'list',\n    }),\n    attach: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_methods/{payment_method}/attach',\n    }),\n    detach: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_methods/{payment_method}/detach',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Payouts = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/payouts' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/payouts/{payout}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/payouts/{payout}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payouts',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payouts/{payout}/cancel',\n    }),\n    reverse: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payouts/{payout}/reverse',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Plans = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/plans' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/plans/{plan}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/plans/{plan}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/plans',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/plans/{plan}' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Prices = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/prices' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/prices/{price}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/prices/{price}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/prices',\n        methodType: 'list',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/prices/search',\n        methodType: 'search',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Products = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/products' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/products/{id}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/products/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/products',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/products/{id}' }),\n    createFeature: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/products/{product}/features',\n    }),\n    deleteFeature: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/products/{product}/features/{id}',\n    }),\n    listFeatures: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/products/{product}/features',\n        methodType: 'list',\n    }),\n    retrieveFeature: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/products/{product}/features/{id}',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/products/search',\n        methodType: 'search',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PromotionCodes = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/promotion_codes' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/promotion_codes/{promotion_code}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/promotion_codes/{promotion_code}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/promotion_codes',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Quotes = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/quotes' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/quotes/{quote}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/quotes/{quote}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/quotes',\n        methodType: 'list',\n    }),\n    accept: stripeMethod({ method: 'POST', fullPath: '/v1/quotes/{quote}/accept' }),\n    cancel: stripeMethod({ method: 'POST', fullPath: '/v1/quotes/{quote}/cancel' }),\n    finalizeQuote: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/quotes/{quote}/finalize',\n    }),\n    listComputedUpfrontLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/quotes/{quote}/computed_upfront_line_items',\n        methodType: 'list',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/quotes/{quote}/line_items',\n        methodType: 'list',\n    }),\n    pdf: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/quotes/{quote}/pdf',\n        host: 'files.stripe.com',\n        streaming: true,\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Refunds = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/refunds' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/refunds/{refund}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/refunds/{refund}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/refunds',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/refunds/{refund}/cancel',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Reviews = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/reviews/{review}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reviews',\n        methodType: 'list',\n    }),\n    approve: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/reviews/{review}/approve',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const SetupAttempts = StripeResource.extend({\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/setup_attempts',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const SetupIntents = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/setup_intents' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/setup_intents/{intent}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/setup_intents/{intent}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/setup_intents',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/setup_intents/{intent}/cancel',\n    }),\n    confirm: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/setup_intents/{intent}/confirm',\n    }),\n    verifyMicrodeposits: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/setup_intents/{intent}/verify_microdeposits',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ShippingRates = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/shipping_rates' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/shipping_rates/{shipping_rate_token}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/shipping_rates/{shipping_rate_token}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/shipping_rates',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Sources = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/sources' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/sources/{source}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/sources/{source}' }),\n    listSourceTransactions: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/sources/{source}/source_transactions',\n        methodType: 'list',\n    }),\n    verify: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/sources/{source}/verify',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const SubscriptionItems = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/subscription_items' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscription_items/{item}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_items/{item}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscription_items',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/subscription_items/{item}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const SubscriptionSchedules = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_schedules',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscription_schedules/{schedule}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_schedules/{schedule}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscription_schedules',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_schedules/{schedule}/cancel',\n    }),\n    release: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_schedules/{schedule}/release',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Subscriptions = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/subscriptions' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscriptions/{subscription_exposed_id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscriptions/{subscription_exposed_id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscriptions',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/subscriptions/{subscription_exposed_id}',\n    }),\n    deleteDiscount: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/subscriptions/{subscription_exposed_id}/discount',\n    }),\n    resume: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscriptions/{subscription}/resume',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscriptions/search',\n        methodType: 'search',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TaxCodes = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tax_codes/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax_codes',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TaxIds = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tax_ids' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tax_ids/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax_ids',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/tax_ids/{id}' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TaxRates = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tax_rates' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tax_rates/{tax_rate}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/tax_rates/{tax_rate}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax_rates',\n        methodType: 'list',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Tokens = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tokens' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tokens/{token}' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Topups = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/topups' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/topups/{topup}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/topups/{topup}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/topups',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({ method: 'POST', fullPath: '/v1/topups/{topup}/cancel' }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transfers = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/transfers' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/transfers/{transfer}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/transfers/{transfer}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/transfers',\n        methodType: 'list',\n    }),\n    createReversal: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/transfers/{id}/reversals',\n    }),\n    listReversals: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/transfers/{id}/reversals',\n        methodType: 'list',\n    }),\n    retrieveReversal: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/transfers/{transfer}/reversals/{id}',\n    }),\n    updateReversal: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/transfers/{transfer}/reversals/{id}',\n    }),\n});\n", "// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const WebhookEndpoints = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/webhook_endpoints' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/webhook_endpoints/{webhook_endpoint}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/webhook_endpoints/{webhook_endpoint}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/webhook_endpoints',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/webhook_endpoints/{webhook_endpoint}',\n    }),\n});\n", "import * as _Error from './Error.js';\nimport { RequestSender } from './RequestSender.js';\nimport { StripeResource } from './StripeResource.js';\nimport { createWebhooks } from './Webhooks.js';\nimport { ApiVersion } from './apiVersion.js';\nimport { CryptoProvider } from './crypto/CryptoProvider.js';\nimport { HttpClient, HttpClientResponse } from './net/HttpClient.js';\nimport * as resources from './resources.js';\nimport { createApiKeyAuthenticator, determineProcessUserAgentProperties, pascalToCamelCase, validateInteger, } from './utils.js';\nconst DEFAULT_HOST = 'api.stripe.com';\nconst DEFAULT_PORT = '443';\nconst DEFAULT_BASE_PATH = '/v1/';\nconst DEFAULT_API_VERSION = ApiVersion;\nconst DEFAULT_TIMEOUT = 80000;\nconst MAX_NETWORK_RETRY_DELAY_SEC = 5;\nconst INITIAL_NETWORK_RETRY_DELAY_SEC = 0.5;\nconst APP_INFO_PROPERTIES = ['name', 'version', 'url', 'partner_id'];\nconst ALLOWED_CONFIG_PROPERTIES = [\n    'authenticator',\n    'apiVersion',\n    'typescript',\n    'maxNetworkRetries',\n    'httpAgent',\n    'httpClient',\n    'timeout',\n    'host',\n    'port',\n    'protocol',\n    'telemetry',\n    'appInfo',\n    'stripeAccount',\n    'stripeContext',\n];\nconst defaultRequestSenderFactory = (stripe) => new RequestSender(stripe, StripeResource.MAX_BUFFERED_REQUEST_METRICS);\nexport function createStripe(platformFunctions, requestSender = defaultRequestSenderFactory) {\n    Stripe.PACKAGE_VERSION = '18.2.0';\n    Stripe.USER_AGENT = Object.assign({ bindings_version: Stripe.PACKAGE_VERSION, lang: 'node', publisher: 'stripe', uname: null, typescript: false }, determineProcessUserAgentProperties());\n    Stripe.StripeResource = StripeResource;\n    Stripe.resources = resources;\n    Stripe.HttpClient = HttpClient;\n    Stripe.HttpClientResponse = HttpClientResponse;\n    Stripe.CryptoProvider = CryptoProvider;\n    Stripe.webhooks = createWebhooks(platformFunctions);\n    function Stripe(key, config = {}) {\n        if (!(this instanceof Stripe)) {\n            return new Stripe(key, config);\n        }\n        const props = this._getPropsFromConfig(config);\n        this._platformFunctions = platformFunctions;\n        Object.defineProperty(this, '_emitter', {\n            value: this._platformFunctions.createEmitter(),\n            enumerable: false,\n            configurable: false,\n            writable: false,\n        });\n        this.VERSION = Stripe.PACKAGE_VERSION;\n        this.on = this._emitter.on.bind(this._emitter);\n        this.once = this._emitter.once.bind(this._emitter);\n        this.off = this._emitter.removeListener.bind(this._emitter);\n        const agent = props.httpAgent || null;\n        this._api = {\n            host: props.host || DEFAULT_HOST,\n            port: props.port || DEFAULT_PORT,\n            protocol: props.protocol || 'https',\n            basePath: DEFAULT_BASE_PATH,\n            version: props.apiVersion || DEFAULT_API_VERSION,\n            timeout: validateInteger('timeout', props.timeout, DEFAULT_TIMEOUT),\n            maxNetworkRetries: validateInteger('maxNetworkRetries', props.maxNetworkRetries, 2),\n            agent: agent,\n            httpClient: props.httpClient ||\n                (agent\n                    ? this._platformFunctions.createNodeHttpClient(agent)\n                    : this._platformFunctions.createDefaultHttpClient()),\n            dev: false,\n            stripeAccount: props.stripeAccount || null,\n            stripeContext: props.stripeContext || null,\n        };\n        const typescript = props.typescript || false;\n        if (typescript !== Stripe.USER_AGENT.typescript) {\n            // The mutation here is uncomfortable, but likely fastest;\n            // serializing the user agent involves shelling out to the system,\n            // and given some users may instantiate the library many times without switching between TS and non-TS,\n            // we only want to incur the performance hit when that actually happens.\n            Stripe.USER_AGENT.typescript = typescript;\n        }\n        if (props.appInfo) {\n            this._setAppInfo(props.appInfo);\n        }\n        this._prepResources();\n        this._setAuthenticator(key, props.authenticator);\n        this.errors = _Error;\n        this.webhooks = Stripe.webhooks;\n        this._prevRequestMetrics = [];\n        this._enableTelemetry = props.telemetry !== false;\n        this._requestSender = requestSender(this);\n        // Expose StripeResource on the instance too\n        // @ts-ignore\n        this.StripeResource = Stripe.StripeResource;\n    }\n    Stripe.errors = _Error;\n    Stripe.createNodeHttpClient = platformFunctions.createNodeHttpClient;\n    /**\n     * Creates an HTTP client for issuing Stripe API requests which uses the Web\n     * Fetch API.\n     *\n     * A fetch function can optionally be passed in as a parameter. If none is\n     * passed, will default to the default `fetch` function in the global scope.\n     */\n    Stripe.createFetchHttpClient = platformFunctions.createFetchHttpClient;\n    /**\n     * Create a CryptoProvider which uses the built-in Node crypto libraries for\n     * its crypto operations.\n     */\n    Stripe.createNodeCryptoProvider = platformFunctions.createNodeCryptoProvider;\n    /**\n     * Creates a CryptoProvider which uses the Subtle Crypto API from the Web\n     * Crypto API spec for its crypto operations.\n     *\n     * A SubtleCrypto interface can optionally be passed in as a parameter. If none\n     * is passed, will default to the default `crypto.subtle` object in the global\n     * scope.\n     */\n    Stripe.createSubtleCryptoProvider =\n        platformFunctions.createSubtleCryptoProvider;\n    Stripe.prototype = {\n        // Properties are set in the constructor above\n        _appInfo: undefined,\n        on: null,\n        off: null,\n        once: null,\n        VERSION: null,\n        StripeResource: null,\n        webhooks: null,\n        errors: null,\n        _api: null,\n        _prevRequestMetrics: null,\n        _emitter: null,\n        _enableTelemetry: null,\n        _requestSender: null,\n        _platformFunctions: null,\n        rawRequest(method, path, params, options) {\n            return this._requestSender._rawRequest(method, path, params, options);\n        },\n        /**\n         * @private\n         */\n        _setAuthenticator(key, authenticator) {\n            if (key && authenticator) {\n                throw new Error(\"Can't specify both apiKey and authenticator\");\n            }\n            if (!key && !authenticator) {\n                throw new Error('Neither apiKey nor config.authenticator provided');\n            }\n            this._authenticator = key\n                ? createApiKeyAuthenticator(key)\n                : authenticator;\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _setAppInfo(info) {\n            if (info && typeof info !== 'object') {\n                throw new Error('AppInfo must be an object.');\n            }\n            if (info && !info.name) {\n                throw new Error('AppInfo.name is required');\n            }\n            info = info || {};\n            this._appInfo = APP_INFO_PROPERTIES.reduce((accum, prop) => {\n                if (typeof info[prop] == 'string') {\n                    accum = accum || {};\n                    accum[prop] = info[prop];\n                }\n                return accum;\n            }, {});\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _setApiField(key, value) {\n            this._api[key] = value;\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         */\n        getApiField(key) {\n            return this._api[key];\n        },\n        setClientId(clientId) {\n            this._clientId = clientId;\n        },\n        getClientId() {\n            return this._clientId;\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         */\n        getConstant: (c) => {\n            switch (c) {\n                case 'DEFAULT_HOST':\n                    return DEFAULT_HOST;\n                case 'DEFAULT_PORT':\n                    return DEFAULT_PORT;\n                case 'DEFAULT_BASE_PATH':\n                    return DEFAULT_BASE_PATH;\n                case 'DEFAULT_API_VERSION':\n                    return DEFAULT_API_VERSION;\n                case 'DEFAULT_TIMEOUT':\n                    return DEFAULT_TIMEOUT;\n                case 'MAX_NETWORK_RETRY_DELAY_SEC':\n                    return MAX_NETWORK_RETRY_DELAY_SEC;\n                case 'INITIAL_NETWORK_RETRY_DELAY_SEC':\n                    return INITIAL_NETWORK_RETRY_DELAY_SEC;\n            }\n            return Stripe[c];\n        },\n        getMaxNetworkRetries() {\n            return this.getApiField('maxNetworkRetries');\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _setApiNumberField(prop, n, defaultVal) {\n            const val = validateInteger(prop, n, defaultVal);\n            this._setApiField(prop, val);\n        },\n        getMaxNetworkRetryDelay() {\n            return MAX_NETWORK_RETRY_DELAY_SEC;\n        },\n        getInitialNetworkRetryDelay() {\n            return INITIAL_NETWORK_RETRY_DELAY_SEC;\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         *\n         * Gets a JSON version of a User-Agent and uses a cached version for a slight\n         * speed advantage.\n         */\n        getClientUserAgent(cb) {\n            return this.getClientUserAgentSeeded(Stripe.USER_AGENT, cb);\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         *\n         * Gets a JSON version of a User-Agent by encoding a seeded object and\n         * fetching a uname from the system.\n         */\n        getClientUserAgentSeeded(seed, cb) {\n            this._platformFunctions.getUname().then((uname) => {\n                var _a;\n                const userAgent = {};\n                for (const field in seed) {\n                    if (!Object.prototype.hasOwnProperty.call(seed, field)) {\n                        continue;\n                    }\n                    userAgent[field] = encodeURIComponent((_a = seed[field]) !== null && _a !== void 0 ? _a : 'null');\n                }\n                // URI-encode in case there are unusual characters in the system's uname.\n                userAgent.uname = encodeURIComponent(uname || 'UNKNOWN');\n                const client = this.getApiField('httpClient');\n                if (client) {\n                    userAgent.httplib = encodeURIComponent(client.getClientName());\n                }\n                if (this._appInfo) {\n                    userAgent.application = this._appInfo;\n                }\n                cb(JSON.stringify(userAgent));\n            });\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         */\n        getAppInfoAsString() {\n            if (!this._appInfo) {\n                return '';\n            }\n            let formatted = this._appInfo.name;\n            if (this._appInfo.version) {\n                formatted += `/${this._appInfo.version}`;\n            }\n            if (this._appInfo.url) {\n                formatted += ` (${this._appInfo.url})`;\n            }\n            return formatted;\n        },\n        getTelemetryEnabled() {\n            return this._enableTelemetry;\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _prepResources() {\n            for (const name in resources) {\n                if (!Object.prototype.hasOwnProperty.call(resources, name)) {\n                    continue;\n                }\n                // @ts-ignore\n                this[pascalToCamelCase(name)] = new resources[name](this);\n            }\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _getPropsFromConfig(config) {\n            // If config is null or undefined, just bail early with no props\n            if (!config) {\n                return {};\n            }\n            // config can be an object or a string\n            const isString = typeof config === 'string';\n            const isObject = config === Object(config) && !Array.isArray(config);\n            if (!isObject && !isString) {\n                throw new Error('Config must either be an object or a string');\n            }\n            // If config is a string, we assume the old behavior of passing in a string representation of the api version\n            if (isString) {\n                return {\n                    apiVersion: config,\n                };\n            }\n            // If config is an object, we assume the new behavior and make sure it doesn't contain any unexpected values\n            const values = Object.keys(config).filter((value) => !ALLOWED_CONFIG_PROPERTIES.includes(value));\n            if (values.length > 0) {\n                throw new Error(`Config object may only contain the following: ${ALLOWED_CONFIG_PROPERTIES.join(', ')}`);\n            }\n            return config;\n        },\n        parseThinEvent(payload, header, secret, tolerance, cryptoProvider, receivedAt) {\n            // parses and validates the event payload all in one go\n            return this.webhooks.constructEvent(payload, header, secret, tolerance, cryptoProvider, receivedAt);\n        },\n    };\n    return Stripe;\n}\n", "import { WebPlatformFunctions } from './platform/WebPlatformFunctions.js';\nimport { createStripe } from './stripe.core.js';\nexport const Stripe = createStripe(new WebPlatformFunctions());\nexport default Stripe;\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;;;;;;;ACHjB;AAAA;AAAA,QAAI,SAAS,OAAO,QAAQ,cAAc,IAAI;AAC9C,QAAI,oBAAoB,OAAO,4BAA4B,SAAS,OAAO,yBAAyB,IAAI,WAAW,MAAM,IAAI;AAC7H,QAAI,UAAU,UAAU,qBAAqB,OAAO,kBAAkB,QAAQ,aAAa,kBAAkB,MAAM;AACnH,QAAI,aAAa,UAAU,IAAI,UAAU;AACzC,QAAI,SAAS,OAAO,QAAQ,cAAc,IAAI;AAC9C,QAAI,oBAAoB,OAAO,4BAA4B,SAAS,OAAO,yBAAyB,IAAI,WAAW,MAAM,IAAI;AAC7H,QAAI,UAAU,UAAU,qBAAqB,OAAO,kBAAkB,QAAQ,aAAa,kBAAkB,MAAM;AACnH,QAAI,aAAa,UAAU,IAAI,UAAU;AACzC,QAAI,aAAa,OAAO,YAAY,cAAc,QAAQ;AAC1D,QAAI,aAAa,aAAa,QAAQ,UAAU,MAAM;AACtD,QAAI,aAAa,OAAO,YAAY,cAAc,QAAQ;AAC1D,QAAI,aAAa,aAAa,QAAQ,UAAU,MAAM;AACtD,QAAI,aAAa,OAAO,YAAY,cAAc,QAAQ;AAC1D,QAAI,eAAe,aAAa,QAAQ,UAAU,QAAQ;AAC1D,QAAI,iBAAiB,QAAQ,UAAU;AACvC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,SAAS,UAAU;AAC1C,QAAI,SAAS,OAAO,UAAU;AAC9B,QAAI,SAAS,OAAO,UAAU;AAC9B,QAAI,WAAW,OAAO,UAAU;AAChC,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,UAAU,MAAM,UAAU;AAC9B,QAAI,QAAQ,MAAM,UAAU;AAC5B,QAAI,YAAY,MAAM,UAAU;AAChC,QAAI,SAAS,KAAK;AAClB,QAAI,gBAAgB,OAAO,WAAW,aAAa,OAAO,UAAU,UAAU;AAC9E,QAAI,OAAO,OAAO;AAClB,QAAI,cAAc,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,OAAO,UAAU,WAAW;AACpH,QAAI,oBAAoB,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa;AAEnF,QAAI,cAAc,OAAO,WAAW,cAAc,OAAO,gBAAgB,OAAO,OAAO,gBAAgB,oBAAoB,WAAW,YAChI,OAAO,cACP;AACN,QAAI,eAAe,OAAO,UAAU;AAEpC,QAAI,OAAO,OAAO,YAAY,aAAa,QAAQ,iBAAiB,OAAO,oBACvE,CAAC,EAAE,cAAc,MAAM,YACjB,SAAU,GAAG;AACX,aAAO,EAAE;AAAA,IACb,IACE;AAGV,aAAS,oBAAoB,KAAK,KAAK;AACnC,UACI,QAAQ,YACL,QAAQ,aACR,QAAQ,OACP,OAAO,MAAM,QAAS,MAAM,OAC7B,MAAM,KAAK,KAAK,GAAG,GACxB;AACE,eAAO;AAAA,MACX;AACA,UAAI,WAAW;AACf,UAAI,OAAO,QAAQ,UAAU;AACzB,YAAI,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,GAAG;AAC9C,YAAI,QAAQ,KAAK;AACb,cAAI,SAAS,OAAO,GAAG;AACvB,cAAI,MAAM,OAAO,KAAK,KAAK,OAAO,SAAS,CAAC;AAC5C,iBAAO,SAAS,KAAK,QAAQ,UAAU,KAAK,IAAI,MAAM,SAAS,KAAK,SAAS,KAAK,KAAK,eAAe,KAAK,GAAG,MAAM,EAAE;AAAA,QAC1H;AAAA,MACJ;AACA,aAAO,SAAS,KAAK,KAAK,UAAU,KAAK;AAAA,IAC7C;AAEA,QAAI,cAAc;AAClB,QAAI,gBAAgB,YAAY;AAChC,QAAI,gBAAgB,SAAS,aAAa,IAAI,gBAAgB;AAE9D,QAAI,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,IACZ;AACA,QAAI,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,IACZ;AAEA,WAAO,UAAU,SAAS,SAAS,KAAK,SAAS,OAAO,MAAM;AAC1D,UAAI,OAAO,WAAW,CAAC;AAEvB,UAAI,IAAI,MAAM,YAAY,KAAK,CAAC,IAAI,QAAQ,KAAK,UAAU,GAAG;AAC1D,cAAM,IAAI,UAAU,kDAAkD;AAAA,MAC1E;AACA,UACI,IAAI,MAAM,iBAAiB,MAAM,OAAO,KAAK,oBAAoB,WAC3D,KAAK,kBAAkB,KAAK,KAAK,oBAAoB,WACrD,KAAK,oBAAoB,OAEjC;AACE,cAAM,IAAI,UAAU,wFAAwF;AAAA,MAChH;AACA,UAAI,gBAAgB,IAAI,MAAM,eAAe,IAAI,KAAK,gBAAgB;AACtE,UAAI,OAAO,kBAAkB,aAAa,kBAAkB,UAAU;AAClE,cAAM,IAAI,UAAU,+EAA+E;AAAA,MACvG;AAEA,UACI,IAAI,MAAM,QAAQ,KACf,KAAK,WAAW,QAChB,KAAK,WAAW,OAChB,EAAE,SAAS,KAAK,QAAQ,EAAE,MAAM,KAAK,UAAU,KAAK,SAAS,IAClE;AACE,cAAM,IAAI,UAAU,0DAA0D;AAAA,MAClF;AACA,UAAI,IAAI,MAAM,kBAAkB,KAAK,OAAO,KAAK,qBAAqB,WAAW;AAC7E,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AACA,UAAI,mBAAmB,KAAK;AAE5B,UAAI,OAAO,QAAQ,aAAa;AAC5B,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,MAAM;AACd,eAAO;AAAA,MACX;AACA,UAAI,OAAO,QAAQ,WAAW;AAC1B,eAAO,MAAM,SAAS;AAAA,MAC1B;AAEA,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO,cAAc,KAAK,IAAI;AAAA,MAClC;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,YAAI,QAAQ,GAAG;AACX,iBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,QACtC;AACA,YAAI,MAAM,OAAO,GAAG;AACpB,eAAO,mBAAmB,oBAAoB,KAAK,GAAG,IAAI;AAAA,MAC9D;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,YAAI,YAAY,OAAO,GAAG,IAAI;AAC9B,eAAO,mBAAmB,oBAAoB,KAAK,SAAS,IAAI;AAAA,MACpE;AAEA,UAAI,WAAW,OAAO,KAAK,UAAU,cAAc,IAAI,KAAK;AAC5D,UAAI,OAAO,UAAU,aAAa;AAAE,gBAAQ;AAAA,MAAG;AAC/C,UAAI,SAAS,YAAY,WAAW,KAAK,OAAO,QAAQ,UAAU;AAC9D,eAAO,QAAQ,GAAG,IAAI,YAAY;AAAA,MACtC;AAEA,UAAI,SAAS,UAAU,MAAM,KAAK;AAElC,UAAI,OAAO,SAAS,aAAa;AAC7B,eAAO,CAAC;AAAA,MACZ,WAAW,QAAQ,MAAM,GAAG,KAAK,GAAG;AAChC,eAAO;AAAA,MACX;AAEA,eAAS,QAAQ,OAAO,MAAM,UAAU;AACpC,YAAI,MAAM;AACN,iBAAO,UAAU,KAAK,IAAI;AAC1B,eAAK,KAAK,IAAI;AAAA,QAClB;AACA,YAAI,UAAU;AACV,cAAI,UAAU;AAAA,YACV,OAAO,KAAK;AAAA,UAChB;AACA,cAAI,IAAI,MAAM,YAAY,GAAG;AACzB,oBAAQ,aAAa,KAAK;AAAA,UAC9B;AACA,iBAAO,SAAS,OAAO,SAAS,QAAQ,GAAG,IAAI;AAAA,QACnD;AACA,eAAO,SAAS,OAAO,MAAM,QAAQ,GAAG,IAAI;AAAA,MAChD;AAEA,UAAI,OAAO,QAAQ,cAAc,CAAC,SAAS,GAAG,GAAG;AAC7C,YAAI,OAAO,OAAO,GAAG;AACrB,YAAI,OAAO,WAAW,KAAK,OAAO;AAClC,eAAO,eAAe,OAAO,OAAO,OAAO,kBAAkB,OAAO,KAAK,SAAS,IAAI,QAAQ,MAAM,KAAK,MAAM,IAAI,IAAI,OAAO;AAAA,MAClI;AACA,UAAI,SAAS,GAAG,GAAG;AACf,YAAI,YAAY,oBAAoB,SAAS,KAAK,OAAO,GAAG,GAAG,0BAA0B,IAAI,IAAI,YAAY,KAAK,GAAG;AACrH,eAAO,OAAO,QAAQ,YAAY,CAAC,oBAAoB,UAAU,SAAS,IAAI;AAAA,MAClF;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,YAAI,IAAI,MAAM,aAAa,KAAK,OAAO,IAAI,QAAQ,CAAC;AACpD,YAAI,QAAQ,IAAI,cAAc,CAAC;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAK,MAAM,MAAM,CAAC,EAAE,OAAO,MAAM,WAAW,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,UAAU,IAAI;AAAA,QACrF;AACA,aAAK;AACL,YAAI,IAAI,cAAc,IAAI,WAAW,QAAQ;AAAE,eAAK;AAAA,QAAO;AAC3D,aAAK,OAAO,aAAa,KAAK,OAAO,IAAI,QAAQ,CAAC,IAAI;AACtD,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,GAAG,GAAG;AACd,YAAI,IAAI,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAM;AACrC,YAAI,KAAK,WAAW,KAAK,OAAO;AAChC,YAAI,UAAU,CAAC,iBAAiB,EAAE,GAAG;AACjC,iBAAO,MAAM,aAAa,IAAI,MAAM,IAAI;AAAA,QAC5C;AACA,eAAO,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI;AAAA,MACzC;AACA,UAAI,QAAQ,GAAG,GAAG;AACd,YAAI,QAAQ,WAAW,KAAK,OAAO;AACnC,YAAI,EAAE,WAAW,MAAM,cAAc,WAAW,OAAO,CAAC,aAAa,KAAK,KAAK,OAAO,GAAG;AACrF,iBAAO,QAAQ,OAAO,GAAG,IAAI,OAAO,MAAM,KAAK,QAAQ,KAAK,cAAc,QAAQ,IAAI,KAAK,GAAG,KAAK,GAAG,IAAI,IAAI;AAAA,QAClH;AACA,YAAI,MAAM,WAAW,GAAG;AAAE,iBAAO,MAAM,OAAO,GAAG,IAAI;AAAA,QAAK;AAC1D,eAAO,QAAQ,OAAO,GAAG,IAAI,OAAO,MAAM,KAAK,OAAO,IAAI,IAAI;AAAA,MAClE;AACA,UAAI,OAAO,QAAQ,YAAY,eAAe;AAC1C,YAAI,iBAAiB,OAAO,IAAI,aAAa,MAAM,cAAc,aAAa;AAC1E,iBAAO,YAAY,KAAK,EAAE,OAAO,WAAW,MAAM,CAAC;AAAA,QACvD,WAAW,kBAAkB,YAAY,OAAO,IAAI,YAAY,YAAY;AACxE,iBAAO,IAAI,QAAQ;AAAA,QACvB;AAAA,MACJ;AACA,UAAI,MAAM,GAAG,GAAG;AACZ,YAAI,WAAW,CAAC;AAChB,YAAI,YAAY;AACZ,qBAAW,KAAK,KAAK,SAAU,OAAO,KAAK;AACvC,qBAAS,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,SAAS,QAAQ,OAAO,GAAG,CAAC;AAAA,UACxE,CAAC;AAAA,QACL;AACA,eAAO,aAAa,OAAO,QAAQ,KAAK,GAAG,GAAG,UAAU,MAAM;AAAA,MAClE;AACA,UAAI,MAAM,GAAG,GAAG;AACZ,YAAI,WAAW,CAAC;AAChB,YAAI,YAAY;AACZ,qBAAW,KAAK,KAAK,SAAU,OAAO;AAClC,qBAAS,KAAK,QAAQ,OAAO,GAAG,CAAC;AAAA,UACrC,CAAC;AAAA,QACL;AACA,eAAO,aAAa,OAAO,QAAQ,KAAK,GAAG,GAAG,UAAU,MAAM;AAAA,MAClE;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,iBAAiB,SAAS;AAAA,MACrC;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,iBAAiB,SAAS;AAAA,MACrC;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,iBAAiB,SAAS;AAAA,MACrC;AACA,UAAI,SAAS,GAAG,GAAG;AACf,eAAO,UAAU,QAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,MACzC;AACA,UAAI,SAAS,GAAG,GAAG;AACf,eAAO,UAAU,QAAQ,cAAc,KAAK,GAAG,CAAC,CAAC;AAAA,MACrD;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,MAC7C;AACA,UAAI,SAAS,GAAG,GAAG;AACf,eAAO,UAAU,QAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,MACzC;AAGA,UAAI,OAAO,WAAW,eAAe,QAAQ,QAAQ;AACjD,eAAO;AAAA,MACX;AACA,UACK,OAAO,eAAe,eAAe,QAAQ,cAC1C,OAAO,WAAW,eAAe,QAAQ,QAC/C;AACE,eAAO;AAAA,MACX;AACA,UAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,GAAG,GAAG;AAChC,YAAI,KAAK,WAAW,KAAK,OAAO;AAChC,YAAI,gBAAgB,MAAM,IAAI,GAAG,MAAM,OAAO,YAAY,eAAe,UAAU,IAAI,gBAAgB;AACvG,YAAI,WAAW,eAAe,SAAS,KAAK;AAC5C,YAAI,YAAY,CAAC,iBAAiB,eAAe,OAAO,GAAG,MAAM,OAAO,eAAe,MAAM,OAAO,KAAK,MAAM,GAAG,GAAG,GAAG,EAAE,IAAI,WAAW,WAAW;AACpJ,YAAI,iBAAiB,iBAAiB,OAAO,IAAI,gBAAgB,aAAa,KAAK,IAAI,YAAY,OAAO,IAAI,YAAY,OAAO,MAAM;AACvI,YAAI,MAAM,kBAAkB,aAAa,WAAW,MAAM,MAAM,KAAK,QAAQ,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,IAAI,IAAI,OAAO;AACvI,YAAI,GAAG,WAAW,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAM;AAC1C,YAAI,QAAQ;AACR,iBAAO,MAAM,MAAM,aAAa,IAAI,MAAM,IAAI;AAAA,QAClD;AACA,eAAO,MAAM,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI;AAAA,MAC/C;AACA,aAAO,OAAO,GAAG;AAAA,IACrB;AAEA,aAAS,WAAW,GAAG,cAAc,MAAM;AACvC,UAAI,QAAQ,KAAK,cAAc;AAC/B,UAAI,YAAY,OAAO,KAAK;AAC5B,aAAO,YAAY,IAAI;AAAA,IAC3B;AAEA,aAAS,MAAM,GAAG;AACd,aAAO,SAAS,KAAK,OAAO,CAAC,GAAG,MAAM,QAAQ;AAAA,IAClD;AAEA,aAAS,iBAAiB,KAAK;AAC3B,aAAO,CAAC,eAAe,EAAE,OAAO,QAAQ,aAAa,eAAe,OAAO,OAAO,IAAI,WAAW,MAAM;AAAA,IAC3G;AACA,aAAS,QAAQ,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,oBAAoB,iBAAiB,GAAG;AAAA,IAAG;AACzF,aAAS,OAAO,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,mBAAmB,iBAAiB,GAAG;AAAA,IAAG;AACvF,aAAS,SAAS,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,qBAAqB,iBAAiB,GAAG;AAAA,IAAG;AAC3F,aAAS,QAAQ,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,oBAAoB,iBAAiB,GAAG;AAAA,IAAG;AACzF,aAAS,SAAS,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,qBAAqB,iBAAiB,GAAG;AAAA,IAAG;AAC3F,aAAS,SAAS,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,qBAAqB,iBAAiB,GAAG;AAAA,IAAG;AAC3F,aAAS,UAAU,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,sBAAsB,iBAAiB,GAAG;AAAA,IAAG;AAG7F,aAAS,SAAS,KAAK;AACnB,UAAI,mBAAmB;AACnB,eAAO,OAAO,OAAO,QAAQ,YAAY,eAAe;AAAA,MAC5D;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO;AAAA,MACX;AACA,UAAI,CAAC,OAAO,OAAO,QAAQ,YAAY,CAAC,aAAa;AACjD,eAAO;AAAA,MACX;AACA,UAAI;AACA,oBAAY,KAAK,GAAG;AACpB,eAAO;AAAA,MACX,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,SAAS,KAAK;AACnB,UAAI,CAAC,OAAO,OAAO,QAAQ,YAAY,CAAC,eAAe;AACnD,eAAO;AAAA,MACX;AACA,UAAI;AACA,sBAAc,KAAK,GAAG;AACtB,eAAO;AAAA,MACX,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,QAAI,SAAS,OAAO,UAAU,kBAAkB,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAM;AACrF,aAAS,IAAI,KAAK,KAAK;AACnB,aAAO,OAAO,KAAK,KAAK,GAAG;AAAA,IAC/B;AAEA,aAAS,MAAM,KAAK;AAChB,aAAO,eAAe,KAAK,GAAG;AAAA,IAClC;AAEA,aAAS,OAAO,GAAG;AACf,UAAI,EAAE,MAAM;AAAE,eAAO,EAAE;AAAA,MAAM;AAC7B,UAAI,IAAI,OAAO,KAAK,iBAAiB,KAAK,CAAC,GAAG,sBAAsB;AACpE,UAAI,GAAG;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG;AACtB,aAAO;AAAA,IACX;AAEA,aAAS,QAAQ,IAAI,GAAG;AACpB,UAAI,GAAG,SAAS;AAAE,eAAO,GAAG,QAAQ,CAAC;AAAA,MAAG;AACxC,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,KAAK;AACvC,YAAI,GAAG,CAAC,MAAM,GAAG;AAAE,iBAAO;AAAA,QAAG;AAAA,MACjC;AACA,aAAO;AAAA,IACX;AAEA,aAAS,MAAM,GAAG;AACd,UAAI,CAAC,WAAW,CAAC,KAAK,OAAO,MAAM,UAAU;AACzC,eAAO;AAAA,MACX;AACA,UAAI;AACA,gBAAQ,KAAK,CAAC;AACd,YAAI;AACA,kBAAQ,KAAK,CAAC;AAAA,QAClB,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AACA,eAAO,aAAa;AAAA,MACxB,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,GAAG;AAClB,UAAI,CAAC,cAAc,CAAC,KAAK,OAAO,MAAM,UAAU;AAC5C,eAAO;AAAA,MACX;AACA,UAAI;AACA,mBAAW,KAAK,GAAG,UAAU;AAC7B,YAAI;AACA,qBAAW,KAAK,GAAG,UAAU;AAAA,QACjC,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AACA,eAAO,aAAa;AAAA,MACxB,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,GAAG;AAClB,UAAI,CAAC,gBAAgB,CAAC,KAAK,OAAO,MAAM,UAAU;AAC9C,eAAO;AAAA,MACX;AACA,UAAI;AACA,qBAAa,KAAK,CAAC;AACnB,eAAO;AAAA,MACX,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,MAAM,GAAG;AACd,UAAI,CAAC,WAAW,CAAC,KAAK,OAAO,MAAM,UAAU;AACzC,eAAO;AAAA,MACX;AACA,UAAI;AACA,gBAAQ,KAAK,CAAC;AACd,YAAI;AACA,kBAAQ,KAAK,CAAC;AAAA,QAClB,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AACA,eAAO,aAAa;AAAA,MACxB,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,GAAG;AAClB,UAAI,CAAC,cAAc,CAAC,KAAK,OAAO,MAAM,UAAU;AAC5C,eAAO;AAAA,MACX;AACA,UAAI;AACA,mBAAW,KAAK,GAAG,UAAU;AAC7B,YAAI;AACA,qBAAW,KAAK,GAAG,UAAU;AAAA,QACjC,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AACA,eAAO,aAAa;AAAA,MACxB,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,GAAG;AAClB,UAAI,CAAC,KAAK,OAAO,MAAM,UAAU;AAAE,eAAO;AAAA,MAAO;AACjD,UAAI,OAAO,gBAAgB,eAAe,aAAa,aAAa;AAChE,eAAO;AAAA,MACX;AACA,aAAO,OAAO,EAAE,aAAa,YAAY,OAAO,EAAE,iBAAiB;AAAA,IACvE;AAEA,aAAS,cAAc,KAAK,MAAM;AAC9B,UAAI,IAAI,SAAS,KAAK,iBAAiB;AACnC,YAAI,YAAY,IAAI,SAAS,KAAK;AAClC,YAAI,UAAU,SAAS,YAAY,qBAAqB,YAAY,IAAI,MAAM;AAC9E,eAAO,cAAc,OAAO,KAAK,KAAK,GAAG,KAAK,eAAe,GAAG,IAAI,IAAI;AAAA,MAC5E;AACA,UAAI,UAAU,SAAS,KAAK,cAAc,QAAQ;AAClD,cAAQ,YAAY;AAEpB,UAAI,IAAI,SAAS,KAAK,SAAS,KAAK,KAAK,SAAS,MAAM,GAAG,gBAAgB,OAAO;AAClF,aAAO,WAAW,GAAG,UAAU,IAAI;AAAA,IACvC;AAEA,aAAS,QAAQ,GAAG;AAChB,UAAI,IAAI,EAAE,WAAW,CAAC;AACtB,UAAI,IAAI;AAAA,QACJ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACR,EAAE,CAAC;AACH,UAAI,GAAG;AAAE,eAAO,OAAO;AAAA,MAAG;AAC1B,aAAO,SAAS,IAAI,KAAO,MAAM,MAAM,aAAa,KAAK,EAAE,SAAS,EAAE,CAAC;AAAA,IAC3E;AAEA,aAAS,UAAU,KAAK;AACpB,aAAO,YAAY,MAAM;AAAA,IAC7B;AAEA,aAAS,iBAAiB,MAAM;AAC5B,aAAO,OAAO;AAAA,IAClB;AAEA,aAAS,aAAa,MAAM,MAAM,SAAS,QAAQ;AAC/C,UAAI,gBAAgB,SAAS,aAAa,SAAS,MAAM,IAAI,MAAM,KAAK,SAAS,IAAI;AACrF,aAAO,OAAO,OAAO,OAAO,QAAQ,gBAAgB;AAAA,IACxD;AAEA,aAAS,iBAAiB,IAAI;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,YAAI,QAAQ,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG;AAC3B,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,MAAM,OAAO;AAC5B,UAAI;AACJ,UAAI,KAAK,WAAW,KAAM;AACtB,qBAAa;AAAA,MACjB,WAAW,OAAO,KAAK,WAAW,YAAY,KAAK,SAAS,GAAG;AAC3D,qBAAa,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,GAAG,GAAG;AAAA,MACvD,OAAO;AACH,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,MAAM;AAAA,QACN,MAAM,MAAM,KAAK,MAAM,QAAQ,CAAC,GAAG,UAAU;AAAA,MACjD;AAAA,IACJ;AAEA,aAAS,aAAa,IAAI,QAAQ;AAC9B,UAAI,GAAG,WAAW,GAAG;AAAE,eAAO;AAAA,MAAI;AAClC,UAAI,aAAa,OAAO,OAAO,OAAO,OAAO;AAC7C,aAAO,aAAa,MAAM,KAAK,IAAI,MAAM,UAAU,IAAI,OAAO,OAAO;AAAA,IACzE;AAEA,aAAS,WAAW,KAAK,SAAS;AAC9B,UAAI,QAAQ,QAAQ,GAAG;AACvB,UAAI,KAAK,CAAC;AACV,UAAI,OAAO;AACP,WAAG,SAAS,IAAI;AAChB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,aAAG,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI;AAAA,QACjD;AAAA,MACJ;AACA,UAAI,OAAO,OAAO,SAAS,aAAa,KAAK,GAAG,IAAI,CAAC;AACrD,UAAI;AACJ,UAAI,mBAAmB;AACnB,iBAAS,CAAC;AACV,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,iBAAO,MAAM,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AAAA,QAClC;AAAA,MACJ;AAEA,eAAS,OAAO,KAAK;AACjB,YAAI,CAAC,IAAI,KAAK,GAAG,GAAG;AAAE;AAAA,QAAU;AAChC,YAAI,SAAS,OAAO,OAAO,GAAG,CAAC,MAAM,OAAO,MAAM,IAAI,QAAQ;AAAE;AAAA,QAAU;AAC1E,YAAI,qBAAqB,OAAO,MAAM,GAAG,aAAa,QAAQ;AAE1D;AAAA,QACJ,WAAW,MAAM,KAAK,UAAU,GAAG,GAAG;AAClC,aAAG,KAAK,QAAQ,KAAK,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;AAAA,QAC7D,OAAO;AACH,aAAG,KAAK,MAAM,OAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;AAAA,QAC/C;AAAA,MACJ;AACA,UAAI,OAAO,SAAS,YAAY;AAC5B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,cAAI,aAAa,KAAK,KAAK,KAAK,CAAC,CAAC,GAAG;AACjC,eAAG,KAAK,MAAM,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,QAAQ,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;AAAA,UACvE;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;AC/hBA;AAAA;AAAA;AAEA,QAAI,UAAU;AAEd,QAAI,aAAa;AAUjB,QAAI,cAAc,SAAU,MAAM,KAAK,UAAU;AAEhD,UAAI,OAAO;AAEX,UAAI;AAEJ,cAAQ,OAAO,KAAK,SAAS,MAAM,OAAO,MAAM;AAC/C,YAAI,KAAK,QAAQ,KAAK;AACrB,eAAK,OAAO,KAAK;AACjB,cAAI,CAAC,UAAU;AAEd,iBAAK;AAAA,YAAqD,KAAK;AAC/D,iBAAK,OAAO;AAAA,UACb;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAGA,QAAI,UAAU,SAAU,SAAS,KAAK;AACrC,UAAI,CAAC,SAAS;AACb,eAAO;AAAA,MACR;AACA,UAAI,OAAO,YAAY,SAAS,GAAG;AACnC,aAAO,QAAQ,KAAK;AAAA,IACrB;AAEA,QAAI,UAAU,SAAU,SAAS,KAAK,OAAO;AAC5C,UAAI,OAAO,YAAY,SAAS,GAAG;AACnC,UAAI,MAAM;AACT,aAAK,QAAQ;AAAA,MACd,OAAO;AAEN,gBAAQ;AAAA,QAAgF;AAAA;AAAA,UACvF;AAAA,UACA,MAAM,QAAQ;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,UAAU,SAAU,SAAS,KAAK;AACrC,UAAI,CAAC,SAAS;AACb,eAAO;AAAA,MACR;AACA,aAAO,CAAC,CAAC,YAAY,SAAS,GAAG;AAAA,IAClC;AAGA,QAAI,aAAa,SAAU,SAAS,KAAK;AACxC,UAAI,SAAS;AACZ,eAAO,YAAY,SAAS,KAAK,IAAI;AAAA,MACtC;AAAA,IACD;AAGA,WAAO,UAAU,SAAS,qBAAqB;AAKkB,UAAI;AAGpE,UAAI,UAAU;AAAA,QACb,QAAQ,SAAU,KAAK;AACtB,cAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACtB,kBAAM,IAAI,WAAW,mCAAmC,QAAQ,GAAG,CAAC;AAAA,UACrE;AAAA,QACD;AAAA,QACA,UAAU,SAAU,KAAK;AACxB,cAAI,OAAO,MAAM,GAAG;AACpB,cAAI,cAAc,WAAW,IAAI,GAAG;AACpC,cAAI,eAAe,QAAQ,SAAS,aAAa;AAChD,iBAAK;AAAA,UACN;AACA,iBAAO,CAAC,CAAC;AAAA,QACV;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,iBAAO,QAAQ,IAAI,GAAG;AAAA,QACvB;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,iBAAO,QAAQ,IAAI,GAAG;AAAA,QACvB;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AAC1B,cAAI,CAAC,IAAI;AAER,iBAAK;AAAA,cACJ,MAAM;AAAA,YACP;AAAA,UACD;AAEA;AAAA;AAAA,YAA+C;AAAA,YAAK;AAAA,YAAK;AAAA,UAAK;AAAA,QAC/D;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AChHA;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,OAAO,SAAS,SAASA,OAAM,GAAG;AAClD,aAAO,MAAM;AAAA,IACd;AAAA;AAAA;;;ACLA;AAAA;AAAA;AAEA,QAAI,SAAS;AAGb,WAAO,UAAU,SAAS,KAAK,QAAQ;AACtC,UAAI,OAAO,MAAM,KAAK,WAAW,GAAG;AACnC,eAAO;AAAA,MACR;AACA,aAAO,SAAS,IAAI,KAAK;AAAA,IAC1B;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAGA,WAAO,UAAU,OAAO;AAAA;AAAA;;;ACHxB;AAAA;AAAA;AAGA,QAAI,QAAQ;AAEZ,QAAI,OAAO;AACV,UAAI;AACH,cAAM,CAAC,GAAG,QAAQ;AAAA,MACnB,SAAS,GAAG;AAEX,gBAAQ;AAAA,MACT;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACdjB;AAAA;AAAA;AAGA,QAAI,kBAAkB,OAAO,kBAAkB;AAC/C,QAAI,iBAAiB;AACpB,UAAI;AACH,wBAAgB,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC;AAAA,MACtC,SAAS,GAAG;AAEX,0BAAkB;AAAA,MACnB;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACbjB;AAAA;AAAA;AAIA,WAAO,UAAU,SAAS,aAAa;AACtC,UAAI,OAAO,WAAW,cAAc,OAAO,OAAO,0BAA0B,YAAY;AAAE,eAAO;AAAA,MAAO;AACxG,UAAI,OAAO,OAAO,aAAa,UAAU;AAAE,eAAO;AAAA,MAAM;AAGxD,UAAI,MAAM,CAAC;AACX,UAAI,MAAM,OAAO,MAAM;AACvB,UAAI,SAAS,OAAO,GAAG;AACvB,UAAI,OAAO,QAAQ,UAAU;AAAE,eAAO;AAAA,MAAO;AAE7C,UAAI,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,mBAAmB;AAAE,eAAO;AAAA,MAAO;AAC/E,UAAI,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,mBAAmB;AAAE,eAAO;AAAA,MAAO;AAUlF,UAAI,SAAS;AACb,UAAI,GAAG,IAAI;AACX,eAAS,KAAK,KAAK;AAAE,eAAO;AAAA,MAAO;AACnC,UAAI,OAAO,OAAO,SAAS,cAAc,OAAO,KAAK,GAAG,EAAE,WAAW,GAAG;AAAE,eAAO;AAAA,MAAO;AAExF,UAAI,OAAO,OAAO,wBAAwB,cAAc,OAAO,oBAAoB,GAAG,EAAE,WAAW,GAAG;AAAE,eAAO;AAAA,MAAO;AAEtH,UAAI,OAAO,OAAO,sBAAsB,GAAG;AAC3C,UAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK;AAAE,eAAO;AAAA,MAAO;AAE1D,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,KAAK,GAAG,GAAG;AAAE,eAAO;AAAA,MAAO;AAE3E,UAAI,OAAO,OAAO,6BAA6B,YAAY;AAE1D,YAAI;AAAA;AAAA,UAAgD,OAAO,yBAAyB,KAAK,GAAG;AAAA;AAC5F,YAAI,WAAW,UAAU,UAAU,WAAW,eAAe,MAAM;AAAE,iBAAO;AAAA,QAAO;AAAA,MACpF;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC5CA;AAAA;AAAA;AAEA,QAAI,aAAa,OAAO,WAAW,eAAe;AAClD,QAAI,gBAAgB;AAGpB,WAAO,UAAU,SAAS,mBAAmB;AAC5C,UAAI,OAAO,eAAe,YAAY;AAAE,eAAO;AAAA,MAAO;AACtD,UAAI,OAAO,WAAW,YAAY;AAAE,eAAO;AAAA,MAAO;AAClD,UAAI,OAAO,WAAW,KAAK,MAAM,UAAU;AAAE,eAAO;AAAA,MAAO;AAC3D,UAAI,OAAO,OAAO,KAAK,MAAM,UAAU;AAAE,eAAO;AAAA,MAAO;AAEvD,aAAO,cAAc;AAAA,IACtB;AAAA;AAAA;;;ACbA;AAAA;AAAA;AAGA,WAAO,UAAW,OAAO,YAAY,eAAe,QAAQ,kBAAmB;AAAA;AAAA;;;ACH/E;AAAA;AAAA;AAEA,QAAI,UAAU;AAGd,WAAO,UAAU,QAAQ,kBAAkB;AAAA;AAAA;;;ACL3C;AAAA;AAAA;AAIA,QAAI,gBAAgB;AACpB,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,MAAM,KAAK;AACf,QAAI,WAAW;AAEf,QAAI,WAAW,SAASC,UAAS,GAAG,GAAG;AACnC,UAAI,MAAM,CAAC;AAEX,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AAClC,YAAI,CAAC,IAAI,EAAE,CAAC;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AAClC,YAAI,IAAI,EAAE,MAAM,IAAI,EAAE,CAAC;AAAA,MAC3B;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,QAAQ,SAASC,OAAM,SAAS,QAAQ;AACxC,UAAI,MAAM,CAAC;AACX,eAAS,IAAI,UAAU,GAAG,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG,KAAK,GAAG;AACjE,YAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AAEA,QAAI,QAAQ,SAAU,KAAK,QAAQ;AAC/B,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACpC,eAAO,IAAI,CAAC;AACZ,YAAI,IAAI,IAAI,IAAI,QAAQ;AACpB,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,WAAO,UAAU,SAAS,KAAK,MAAM;AACjC,UAAI,SAAS;AACb,UAAI,OAAO,WAAW,cAAc,MAAM,MAAM,MAAM,MAAM,UAAU;AAClE,cAAM,IAAI,UAAU,gBAAgB,MAAM;AAAA,MAC9C;AACA,UAAI,OAAO,MAAM,WAAW,CAAC;AAE7B,UAAI;AACJ,UAAI,SAAS,WAAY;AACrB,YAAI,gBAAgB,OAAO;AACvB,cAAI,SAAS,OAAO;AAAA,YAChB;AAAA,YACA,SAAS,MAAM,SAAS;AAAA,UAC5B;AACA,cAAI,OAAO,MAAM,MAAM,QAAQ;AAC3B,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AACA,eAAO,OAAO;AAAA,UACV;AAAA,UACA,SAAS,MAAM,SAAS;AAAA,QAC5B;AAAA,MAEJ;AAEA,UAAI,cAAc,IAAI,GAAG,OAAO,SAAS,KAAK,MAAM;AACpD,UAAI,YAAY,CAAC;AACjB,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,kBAAU,CAAC,IAAI,MAAM;AAAA,MACzB;AAEA,cAAQ,SAAS,UAAU,sBAAsB,MAAM,WAAW,GAAG,IAAI,2CAA2C,EAAE,MAAM;AAE5H,UAAI,OAAO,WAAW;AAClB,YAAI,QAAQ,SAASC,SAAQ;AAAA,QAAC;AAC9B,cAAM,YAAY,OAAO;AACzB,cAAM,YAAY,IAAI,MAAM;AAC5B,cAAM,YAAY;AAAA,MACtB;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACnFA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AAErB,WAAO,UAAU,SAAS,UAAU,QAAQ;AAAA;AAAA;;;ACJ5C;AAAA;AAAA;AAGA,WAAO,UAAU,SAAS,UAAU;AAAA;AAAA;;;ACHpC;AAAA;AAAA;AAGA,WAAO,UAAU,SAAS,UAAU;AAAA;AAAA;;;ACHpC;AAAA;AAAA;AAGA,WAAO,UAAU,OAAO,YAAY,eAAe,WAAW,QAAQ;AAAA;AAAA;;;ACHtE;AAAA;AAAA;AAEA,QAAI,OAAO;AAEX,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,QAAI,gBAAgB;AAGpB,WAAO,UAAU,iBAAiB,KAAK,KAAK,OAAO,MAAM;AAAA;AAAA;;;ACTzD;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,aAAa;AAEjB,QAAI,QAAQ;AACZ,QAAI,eAAe;AAGnB,WAAO,UAAU,SAAS,cAAc,MAAM;AAC7C,UAAI,KAAK,SAAS,KAAK,OAAO,KAAK,CAAC,MAAM,YAAY;AACrD,cAAM,IAAI,WAAW,wBAAwB;AAAA,MAC9C;AACA,aAAO,aAAa,MAAM,OAAO,IAAI;AAAA,IACtC;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,WAAW;AACf,QAAI,OAAO;AAEX,QAAI;AACJ,QAAI;AAEH;AAAA,MAA0E,CAAC,EAAG,cAAc,MAAM;AAAA,IACnG,SAAS,GAAG;AACX,UAAI,CAAC,KAAK,OAAO,MAAM,YAAY,EAAE,UAAU,MAAM,EAAE,SAAS,oBAAoB;AACnF,cAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,OAAO,CAAC,CAAC,oBAAoB,QAAQ;AAAA,MAAK,OAAO;AAAA;AAAA,MAAyD;AAAA,IAAY;AAE1H,QAAI,UAAU;AACd,QAAI,kBAAkB,QAAQ;AAG9B,WAAO,UAAU,QAAQ,OAAO,KAAK,QAAQ,aAC1C,SAAS,CAAC,KAAK,GAAG,CAAC,IACnB,OAAO,oBAAoB;AAAA;AAAA,MACK,SAAS,UAAU,OAAO;AAE1D,eAAO,gBAAgB,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,MAC9D;AAAA,QACE;AAAA;AAAA;;;AC7BJ;AAAA;AAAA;AAEA,QAAI,kBAAkB;AACtB,QAAI,mBAAmB;AAEvB,QAAI,iBAAiB;AAGrB,WAAO,UAAU,kBACd,SAAS,SAAS,GAAG;AAEtB,aAAO,gBAAgB,CAAC;AAAA,IACzB,IACE,mBACC,SAAS,SAAS,GAAG;AACtB,UAAI,CAAC,KAAM,OAAO,MAAM,YAAY,OAAO,MAAM,YAAa;AAC7D,cAAM,IAAI,UAAU,yBAAyB;AAAA,MAC9C;AAEA,aAAO,iBAAiB,CAAC;AAAA,IAC1B,IACE,iBACC,SAAS,SAAS,GAAG;AAEtB,aAAO,eAAe,CAAC;AAAA,IACxB,IACE;AAAA;AAAA;;;AC1BL;AAAA;AAAA;AAEA,QAAI,OAAO,SAAS,UAAU;AAC9B,QAAI,UAAU,OAAO,UAAU;AAC/B,QAAI,OAAO;AAGX,WAAO,UAAU,KAAK,KAAK,MAAM,OAAO;AAAA;AAAA;;;ACPxC;AAAA;AAAA;AAEA,QAAIC;AAEJ,QAAI,UAAU;AAEd,QAAI,SAAS;AACb,QAAI,aAAa;AACjB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AACtB,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,QAAI,YAAY;AAEhB,QAAI,MAAM;AACV,QAAI,QAAQ;AACZ,QAAI,MAAM;AACV,QAAI,MAAM;AACV,QAAI,MAAM;AACV,QAAI,QAAQ;AACZ,QAAI,OAAO;AAEX,QAAI,YAAY;AAGhB,QAAI,wBAAwB,SAAU,kBAAkB;AACvD,UAAI;AACH,eAAO,UAAU,2BAA2B,mBAAmB,gBAAgB,EAAE;AAAA,MAClF,SAAS,GAAG;AAAA,MAAC;AAAA,IACd;AAEA,QAAI,QAAQ;AACZ,QAAI,kBAAkB;AAEtB,QAAI,iBAAiB,WAAY;AAChC,YAAM,IAAI,WAAW;AAAA,IACtB;AACA,QAAI,iBAAiB,QACjB,WAAY;AACd,UAAI;AAEH,kBAAU;AACV,eAAO;AAAA,MACR,SAAS,cAAc;AACtB,YAAI;AAEH,iBAAO,MAAM,WAAW,QAAQ,EAAE;AAAA,QACnC,SAAS,YAAY;AACpB,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD,EAAE,IACA;AAEH,QAAI,aAAa,sBAAuB;AAExC,QAAI,WAAW;AACf,QAAI,aAAa;AACjB,QAAI,cAAc;AAElB,QAAI,SAAS;AACb,QAAI,QAAQ;AAEZ,QAAI,YAAY,CAAC;AAEjB,QAAI,aAAa,OAAO,eAAe,eAAe,CAAC,WAAWA,aAAY,SAAS,UAAU;AAEjG,QAAI,aAAa;AAAA,MAChB,WAAW;AAAA,MACX,oBAAoB,OAAO,mBAAmB,cAAcA,aAAY;AAAA,MACxE,WAAW;AAAA,MACX,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,4BAA4B,cAAc,WAAW,SAAS,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC,IAAIA;AAAA,MACvF,oCAAoCA;AAAA,MACpC,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,4BAA4B;AAAA,MAC5B,4BAA4B;AAAA,MAC5B,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,YAAY,OAAO,WAAW,cAAcA,aAAY;AAAA,MACxD,mBAAmB,OAAO,kBAAkB,cAAcA,aAAY;AAAA,MACtE,oBAAoB,OAAO,mBAAmB,cAAcA,aAAY;AAAA,MACxE,aAAa;AAAA,MACb,cAAc,OAAO,aAAa,cAAcA,aAAY;AAAA,MAC5D,UAAU;AAAA,MACV,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,WAAW;AAAA,MACX,UAAU;AAAA;AAAA,MACV,eAAe;AAAA,MACf,kBAAkB,OAAO,iBAAiB,cAAcA,aAAY;AAAA,MACpE,kBAAkB,OAAO,iBAAiB,cAAcA,aAAY;AAAA,MACpE,kBAAkB,OAAO,iBAAiB,cAAcA,aAAY;AAAA,MACpE,0BAA0B,OAAO,yBAAyB,cAAcA,aAAY;AAAA,MACpF,cAAc;AAAA,MACd,uBAAuB;AAAA,MACvB,eAAe,OAAO,cAAc,cAAcA,aAAY;AAAA,MAC9D,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,cAAc;AAAA,MACd,WAAW;AAAA,MACX,uBAAuB,cAAc,WAAW,SAAS,SAAS,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC,CAAC,IAAIA;AAAA,MAC5F,UAAU,OAAO,SAAS,WAAW,OAAOA;AAAA,MAC5C,SAAS,OAAO,QAAQ,cAAcA,aAAY;AAAA,MAClD,0BAA0B,OAAO,QAAQ,eAAe,CAAC,cAAc,CAAC,WAAWA,aAAY,UAAS,oBAAI,IAAI,GAAE,OAAO,QAAQ,EAAE,CAAC;AAAA,MACpI,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,qCAAqC;AAAA,MACrC,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,WAAW,OAAO,UAAU,cAAcA,aAAY;AAAA,MACtD,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,YAAY;AAAA,MACZ,SAAS,OAAO,QAAQ,cAAcA,aAAY;AAAA,MAClD,0BAA0B,OAAO,QAAQ,eAAe,CAAC,cAAc,CAAC,WAAWA,aAAY,UAAS,oBAAI,IAAI,GAAE,OAAO,QAAQ,EAAE,CAAC;AAAA,MACpI,uBAAuB,OAAO,sBAAsB,cAAcA,aAAY;AAAA,MAC9E,YAAY;AAAA,MACZ,6BAA6B,cAAc,WAAW,SAAS,GAAG,OAAO,QAAQ,EAAE,CAAC,IAAIA;AAAA,MACxF,YAAY,aAAa,SAASA;AAAA,MAClC,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,uBAAuB,OAAO,sBAAsB,cAAcA,aAAY;AAAA,MAC9E,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,cAAc;AAAA,MACd,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAE1D,6BAA6B;AAAA,MAC7B,8BAA8B;AAAA,MAC9B,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,4BAA4B;AAAA,IAC7B;AAEA,QAAI,UAAU;AACb,UAAI;AACH,aAAK;AAAA,MACN,SAAS,GAAG;AAEP,qBAAa,SAAS,SAAS,CAAC,CAAC;AACrC,mBAAW,mBAAmB,IAAI;AAAA,MACnC;AAAA,IACD;AAHM;AAKN,QAAI,SAAS,SAASC,QAAO,MAAM;AAClC,UAAI;AACJ,UAAI,SAAS,mBAAmB;AAC/B,gBAAQ,sBAAsB,sBAAsB;AAAA,MACrD,WAAW,SAAS,uBAAuB;AAC1C,gBAAQ,sBAAsB,iBAAiB;AAAA,MAChD,WAAW,SAAS,4BAA4B;AAC/C,gBAAQ,sBAAsB,uBAAuB;AAAA,MACtD,WAAW,SAAS,oBAAoB;AACvC,YAAI,KAAKA,QAAO,0BAA0B;AAC1C,YAAI,IAAI;AACP,kBAAQ,GAAG;AAAA,QACZ;AAAA,MACD,WAAW,SAAS,4BAA4B;AAC/C,YAAI,MAAMA,QAAO,kBAAkB;AACnC,YAAI,OAAO,UAAU;AACpB,kBAAQ,SAAS,IAAI,SAAS;AAAA,QAC/B;AAAA,MACD;AAEA,iBAAW,IAAI,IAAI;AAEnB,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB;AAAA,MACpB,WAAW;AAAA,MACX,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,oBAAoB,CAAC,SAAS,WAAW;AAAA,MACzC,wBAAwB,CAAC,SAAS,aAAa,SAAS;AAAA,MACxD,wBAAwB,CAAC,SAAS,aAAa,SAAS;AAAA,MACxD,qBAAqB,CAAC,SAAS,aAAa,MAAM;AAAA,MAClD,uBAAuB,CAAC,SAAS,aAAa,QAAQ;AAAA,MACtD,4BAA4B,CAAC,iBAAiB,WAAW;AAAA,MACzD,oBAAoB,CAAC,0BAA0B,WAAW;AAAA,MAC1D,6BAA6B,CAAC,0BAA0B,aAAa,WAAW;AAAA,MAChF,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,mBAAmB,CAAC,QAAQ,WAAW;AAAA,MACvC,oBAAoB,CAAC,SAAS,WAAW;AAAA,MACzC,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,2BAA2B,CAAC,gBAAgB,WAAW;AAAA,MACvD,2BAA2B,CAAC,gBAAgB,WAAW;AAAA,MACvD,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,eAAe,CAAC,qBAAqB,WAAW;AAAA,MAChD,wBAAwB,CAAC,qBAAqB,aAAa,WAAW;AAAA,MACtE,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,eAAe,CAAC,QAAQ,OAAO;AAAA,MAC/B,mBAAmB,CAAC,QAAQ,WAAW;AAAA,MACvC,kBAAkB,CAAC,OAAO,WAAW;AAAA,MACrC,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,uBAAuB,CAAC,UAAU,aAAa,UAAU;AAAA,MACzD,sBAAsB,CAAC,UAAU,aAAa,SAAS;AAAA,MACvD,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,uBAAuB,CAAC,WAAW,aAAa,MAAM;AAAA,MACtD,iBAAiB,CAAC,WAAW,KAAK;AAAA,MAClC,oBAAoB,CAAC,WAAW,QAAQ;AAAA,MACxC,qBAAqB,CAAC,WAAW,SAAS;AAAA,MAC1C,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,6BAA6B,CAAC,kBAAkB,WAAW;AAAA,MAC3D,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,kBAAkB,CAAC,OAAO,WAAW;AAAA,MACrC,gCAAgC,CAAC,qBAAqB,WAAW;AAAA,MACjE,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,gCAAgC,CAAC,qBAAqB,WAAW;AAAA,MACjE,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,sBAAsB,CAAC,WAAW,WAAW;AAAA,IAC9C;AAEA,QAAI,OAAO;AACX,QAAI,SAAS;AACb,QAAI,UAAU,KAAK,KAAK,OAAO,MAAM,UAAU,MAAM;AACrD,QAAI,eAAe,KAAK,KAAK,QAAQ,MAAM,UAAU,MAAM;AAC3D,QAAI,WAAW,KAAK,KAAK,OAAO,OAAO,UAAU,OAAO;AACxD,QAAI,YAAY,KAAK,KAAK,OAAO,OAAO,UAAU,KAAK;AACvD,QAAI,QAAQ,KAAK,KAAK,OAAO,OAAO,UAAU,IAAI;AAGlD,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,eAAe,SAASC,cAAa,QAAQ;AAChD,UAAI,QAAQ,UAAU,QAAQ,GAAG,CAAC;AAClC,UAAI,OAAO,UAAU,QAAQ,EAAE;AAC/B,UAAI,UAAU,OAAO,SAAS,KAAK;AAClC,cAAM,IAAI,aAAa,gDAAgD;AAAA,MACxE,WAAW,SAAS,OAAO,UAAU,KAAK;AACzC,cAAM,IAAI,aAAa,gDAAgD;AAAA,MACxE;AACA,UAAI,SAAS,CAAC;AACd,eAAS,QAAQ,YAAY,SAAU,OAAO,QAAQ,OAAO,WAAW;AACvE,eAAO,OAAO,MAAM,IAAI,QAAQ,SAAS,WAAW,cAAc,IAAI,IAAI,UAAU;AAAA,MACrF,CAAC;AACD,aAAO;AAAA,IACR;AAGA,QAAI,mBAAmB,SAASC,kBAAiB,MAAM,cAAc;AACpE,UAAI,gBAAgB;AACpB,UAAI;AACJ,UAAI,OAAO,gBAAgB,aAAa,GAAG;AAC1C,gBAAQ,eAAe,aAAa;AACpC,wBAAgB,MAAM,MAAM,CAAC,IAAI;AAAA,MAClC;AAEA,UAAI,OAAO,YAAY,aAAa,GAAG;AACtC,YAAI,QAAQ,WAAW,aAAa;AACpC,YAAI,UAAU,WAAW;AACxB,kBAAQ,OAAO,aAAa;AAAA,QAC7B;AACA,YAAI,OAAO,UAAU,eAAe,CAAC,cAAc;AAClD,gBAAM,IAAI,WAAW,eAAe,OAAO,sDAAsD;AAAA,QAClG;AAEA,eAAO;AAAA,UACN;AAAA,UACA,MAAM;AAAA,UACN;AAAA,QACD;AAAA,MACD;AAEA,YAAM,IAAI,aAAa,eAAe,OAAO,kBAAkB;AAAA,IAChE;AAEA,WAAO,UAAU,SAAS,aAAa,MAAM,cAAc;AAC1D,UAAI,OAAO,SAAS,YAAY,KAAK,WAAW,GAAG;AAClD,cAAM,IAAI,WAAW,2CAA2C;AAAA,MACjE;AACA,UAAI,UAAU,SAAS,KAAK,OAAO,iBAAiB,WAAW;AAC9D,cAAM,IAAI,WAAW,2CAA2C;AAAA,MACjE;AAEA,UAAI,MAAM,eAAe,IAAI,MAAM,MAAM;AACxC,cAAM,IAAI,aAAa,oFAAoF;AAAA,MAC5G;AACA,UAAI,QAAQ,aAAa,IAAI;AAC7B,UAAI,oBAAoB,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AAEtD,UAAI,YAAY,iBAAiB,MAAM,oBAAoB,KAAK,YAAY;AAC5E,UAAI,oBAAoB,UAAU;AAClC,UAAI,QAAQ,UAAU;AACtB,UAAI,qBAAqB;AAEzB,UAAI,QAAQ,UAAU;AACtB,UAAI,OAAO;AACV,4BAAoB,MAAM,CAAC;AAC3B,qBAAa,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAAA,MAC3C;AAEA,eAAS,IAAI,GAAG,QAAQ,MAAM,IAAI,MAAM,QAAQ,KAAK,GAAG;AACvD,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,QAAQ,UAAU,MAAM,GAAG,CAAC;AAChC,YAAI,OAAO,UAAU,MAAM,EAAE;AAC7B,aAEG,UAAU,OAAO,UAAU,OAAO,UAAU,QACzC,SAAS,OAAO,SAAS,OAAO,SAAS,SAE3C,UAAU,MACZ;AACD,gBAAM,IAAI,aAAa,sDAAsD;AAAA,QAC9E;AACA,YAAI,SAAS,iBAAiB,CAAC,OAAO;AACrC,+BAAqB;AAAA,QACtB;AAEA,6BAAqB,MAAM;AAC3B,4BAAoB,MAAM,oBAAoB;AAE9C,YAAI,OAAO,YAAY,iBAAiB,GAAG;AAC1C,kBAAQ,WAAW,iBAAiB;AAAA,QACrC,WAAW,SAAS,MAAM;AACzB,cAAI,EAAE,QAAQ,QAAQ;AACrB,gBAAI,CAAC,cAAc;AAClB,oBAAM,IAAI,WAAW,wBAAwB,OAAO,6CAA6C;AAAA,YAClG;AACA,mBAAO;AAAA,UACR;AACA,cAAI,SAAU,IAAI,KAAM,MAAM,QAAQ;AACrC,gBAAI,OAAO,MAAM,OAAO,IAAI;AAC5B,oBAAQ,CAAC,CAAC;AASV,gBAAI,SAAS,SAAS,QAAQ,EAAE,mBAAmB,KAAK,MAAM;AAC7D,sBAAQ,KAAK;AAAA,YACd,OAAO;AACN,sBAAQ,MAAM,IAAI;AAAA,YACnB;AAAA,UACD,OAAO;AACN,oBAAQ,OAAO,OAAO,IAAI;AAC1B,oBAAQ,MAAM,IAAI;AAAA,UACnB;AAEA,cAAI,SAAS,CAAC,oBAAoB;AACjC,uBAAW,iBAAiB,IAAI;AAAA,UACjC;AAAA,QACD;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzXA;AAAA;AAAA;AAEA,QAAI,eAAe;AAEnB,QAAI,gBAAgB;AAGpB,QAAI,WAAW,cAAc,CAAC,aAAa,4BAA4B,CAAC,CAAC;AAGzE,WAAO,UAAU,SAAS,mBAAmB,MAAM,cAAc;AAGhE,UAAI;AAAA;AAAA,QAA2E,aAAa,MAAM,CAAC,CAAC,YAAY;AAAA;AAChH,UAAI,OAAO,cAAc,cAAc,SAAS,MAAM,aAAa,IAAI,IAAI;AAC1E,eAAO;AAAA;AAAA,UAAoC,CAAC,SAAS;AAAA,QAAE;AAAA,MACxD;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;AClBA;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,YAAY;AAChB,QAAI,UAAU;AAEd,QAAI,aAAa;AACjB,QAAI,OAAO,aAAa,SAAS,IAAI;AAGrC,QAAI,UAAU,UAAU,qBAAqB,IAAI;AAEjD,QAAI,UAAU,UAAU,qBAAqB,IAAI;AAEjD,QAAI,UAAU,UAAU,qBAAqB,IAAI;AAEjD,QAAI,aAAa,UAAU,wBAAwB,IAAI;AAEvD,QAAI,WAAW,UAAU,sBAAsB,IAAI;AAGnD,WAAO,UAAU,CAAC,CAAC;AAAA,IAAmD,SAAS,oBAAoB;AAK7D,UAAI;AAGzC,UAAI,UAAU;AAAA,QACb,QAAQ,SAAU,KAAK;AACtB,cAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACtB,kBAAM,IAAI,WAAW,mCAAmC,QAAQ,GAAG,CAAC;AAAA,UACrE;AAAA,QACD;AAAA,QACA,UAAU,SAAU,KAAK;AACxB,cAAI,IAAI;AACP,gBAAI,SAAS,WAAW,IAAI,GAAG;AAC/B,gBAAI,SAAS,EAAE,MAAM,GAAG;AACvB,mBAAK;AAAA,YACN;AACA,mBAAO;AAAA,UACR;AACA,iBAAO;AAAA,QACR;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,cAAI,IAAI;AACP,mBAAO,QAAQ,IAAI,GAAG;AAAA,UACvB;AAAA,QACD;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,cAAI,IAAI;AACP,mBAAO,QAAQ,IAAI,GAAG;AAAA,UACvB;AACA,iBAAO;AAAA,QACR;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AAC1B,cAAI,CAAC,IAAI;AAER,iBAAK,IAAI,KAAK;AAAA,UACf;AACA,kBAAQ,IAAI,KAAK,KAAK;AAAA,QACvB;AAAA,MACD;AAGA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACnEA;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,oBAAoB;AAExB,QAAI,aAAa;AACjB,QAAI,WAAW,aAAa,aAAa,IAAI;AAG7C,QAAI,cAAc,UAAU,yBAAyB,IAAI;AAEzD,QAAI,cAAc,UAAU,yBAAyB,IAAI;AAEzD,QAAI,cAAc,UAAU,yBAAyB,IAAI;AAEzD,QAAI,iBAAiB,UAAU,4BAA4B,IAAI;AAG/D,WAAO,UAAU;AAAA;AAAA,MAC6B,SAAS,wBAAwB;AAK3B,YAAI;AACnB,YAAI;AAGvC,YAAI,UAAU;AAAA,UACb,QAAQ,SAAU,KAAK;AACtB,gBAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACtB,oBAAM,IAAI,WAAW,mCAAmC,QAAQ,GAAG,CAAC;AAAA,YACrE;AAAA,UACD;AAAA,UACA,UAAU,SAAU,KAAK;AACxB,gBAAI,YAAY,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AAC9E,kBAAI,KAAK;AACR,uBAAO,eAAe,KAAK,GAAG;AAAA,cAC/B;AAAA,YACD,WAAW,mBAAmB;AAC7B,kBAAI,IAAI;AACP,uBAAO,GAAG,QAAQ,EAAE,GAAG;AAAA,cACxB;AAAA,YACD;AACA,mBAAO;AAAA,UACR;AAAA,UACA,KAAK,SAAU,KAAK;AACnB,gBAAI,YAAY,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AAC9E,kBAAI,KAAK;AACR,uBAAO,YAAY,KAAK,GAAG;AAAA,cAC5B;AAAA,YACD;AACA,mBAAO,MAAM,GAAG,IAAI,GAAG;AAAA,UACxB;AAAA,UACA,KAAK,SAAU,KAAK;AACnB,gBAAI,YAAY,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AAC9E,kBAAI,KAAK;AACR,uBAAO,YAAY,KAAK,GAAG;AAAA,cAC5B;AAAA,YACD;AACA,mBAAO,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG;AAAA,UAC1B;AAAA,UACA,KAAK,SAAU,KAAK,OAAO;AAC1B,gBAAI,YAAY,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AAC9E,kBAAI,CAAC,KAAK;AACT,sBAAM,IAAI,SAAS;AAAA,cACpB;AACA,0BAAY,KAAK,KAAK,KAAK;AAAA,YAC5B,WAAW,mBAAmB;AAC7B,kBAAI,CAAC,IAAI;AACR,qBAAK,kBAAkB;AAAA,cACxB;AAEsC,cAAC,GAAI,IAAI,KAAK,KAAK;AAAA,YAC1D;AAAA,UACD;AAAA,QACD;AAGA,eAAO;AAAA,MACR;AAAA,QACE;AAAA;AAAA;;;ACnFH;AAAA;AAAA;AAEA,QAAI,aAAa;AACjB,QAAI,UAAU;AACd,QAAI,qBAAqB;AACzB,QAAI,oBAAoB;AACxB,QAAI,wBAAwB;AAE5B,QAAI,cAAc,yBAAyB,qBAAqB;AAGhE,WAAO,UAAU,SAAS,iBAAiB;AAGP,UAAI;AAGvC,UAAI,UAAU;AAAA,QACb,QAAQ,SAAU,KAAK;AACtB,cAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACtB,kBAAM,IAAI,WAAW,mCAAmC,QAAQ,GAAG,CAAC;AAAA,UACrE;AAAA,QACD;AAAA,QACA,UAAU,SAAU,KAAK;AACxB,iBAAO,CAAC,CAAC,gBAAgB,aAAa,QAAQ,EAAE,GAAG;AAAA,QACpD;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,iBAAO,gBAAgB,aAAa,IAAI,GAAG;AAAA,QAC5C;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,iBAAO,CAAC,CAAC,gBAAgB,aAAa,IAAI,GAAG;AAAA,QAC9C;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AAC1B,cAAI,CAAC,cAAc;AAClB,2BAAe,YAAY;AAAA,UAC5B;AAEA,uBAAa,IAAI,KAAK,KAAK;AAAA,QAC5B;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC1CA;AAAA;AAAA;AAEA,QAAI,UAAU,OAAO,UAAU;AAC/B,QAAI,kBAAkB;AAEtB,QAAI,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACb;AAEA,WAAO,UAAU;AAAA,MACb,WAAW,OAAO;AAAA,MAClB,YAAY;AAAA,QACR,SAAS,SAAU,OAAO;AACtB,iBAAO,QAAQ,KAAK,OAAO,iBAAiB,GAAG;AAAA,QACnD;AAAA,QACA,SAAS,SAAU,OAAO;AACtB,iBAAO,OAAO,KAAK;AAAA,QACvB;AAAA,MACJ;AAAA,MACA,SAAS,OAAO;AAAA,MAChB,SAAS,OAAO;AAAA,IACpB;AAAA;AAAA;;;ACtBA;AAAA;AAAA;AAEA,QAAI,UAAU;AAEd,QAAI,MAAM,OAAO,UAAU;AAC3B,QAAI,UAAU,MAAM;AAEpB,QAAI,WAAY,WAAY;AACxB,UAAI,QAAQ,CAAC;AACb,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,cAAM,KAAK,QAAQ,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,EAAE,GAAG,YAAY,CAAC;AAAA,MACzE;AAEA,aAAO;AAAA,IACX,EAAE;AAEF,QAAI,eAAe,SAASC,cAAa,OAAO;AAC5C,aAAO,MAAM,SAAS,GAAG;AACrB,YAAI,OAAO,MAAM,IAAI;AACrB,YAAI,MAAM,KAAK,IAAI,KAAK,IAAI;AAE5B,YAAI,QAAQ,GAAG,GAAG;AACd,cAAI,YAAY,CAAC;AAEjB,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACjC,gBAAI,OAAO,IAAI,CAAC,MAAM,aAAa;AAC/B,wBAAU,KAAK,IAAI,CAAC,CAAC;AAAA,YACzB;AAAA,UACJ;AAEA,eAAK,IAAI,KAAK,IAAI,IAAI;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,gBAAgB,SAASC,eAAc,QAAQ,SAAS;AACxD,UAAI,MAAM,WAAW,QAAQ,eAAe,EAAE,WAAW,KAAK,IAAI,CAAC;AACnE,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,YAAI,OAAO,OAAO,CAAC,MAAM,aAAa;AAClC,cAAI,CAAC,IAAI,OAAO,CAAC;AAAA,QACrB;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,QAAQ,SAASC,OAAM,QAAQ,QAAQ,SAAS;AAEhD,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,WAAW,YAAY,OAAO,WAAW,YAAY;AAC5D,YAAI,QAAQ,MAAM,GAAG;AACjB,iBAAO,KAAK,MAAM;AAAA,QACtB,WAAW,UAAU,OAAO,WAAW,UAAU;AAC7C,cACK,YAAY,QAAQ,gBAAgB,QAAQ,oBAC1C,CAAC,IAAI,KAAK,OAAO,WAAW,MAAM,GACvC;AACE,mBAAO,MAAM,IAAI;AAAA,UACrB;AAAA,QACJ,OAAO;AACH,iBAAO,CAAC,QAAQ,MAAM;AAAA,QAC1B;AAEA,eAAO;AAAA,MACX;AAEA,UAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACvC,eAAO,CAAC,MAAM,EAAE,OAAO,MAAM;AAAA,MACjC;AAEA,UAAI,cAAc;AAClB,UAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,GAAG;AACrC,sBAAc,cAAc,QAAQ,OAAO;AAAA,MAC/C;AAEA,UAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,GAAG;AACpC,eAAO,QAAQ,SAAU,MAAM,GAAG;AAC9B,cAAI,IAAI,KAAK,QAAQ,CAAC,GAAG;AACrB,gBAAI,aAAa,OAAO,CAAC;AACzB,gBAAI,cAAc,OAAO,eAAe,YAAY,QAAQ,OAAO,SAAS,UAAU;AAClF,qBAAO,CAAC,IAAIA,OAAM,YAAY,MAAM,OAAO;AAAA,YAC/C,OAAO;AACH,qBAAO,KAAK,IAAI;AAAA,YACpB;AAAA,UACJ,OAAO;AACH,mBAAO,CAAC,IAAI;AAAA,UAChB;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AAEA,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,KAAK;AAClD,YAAI,QAAQ,OAAO,GAAG;AAEtB,YAAI,IAAI,KAAK,KAAK,GAAG,GAAG;AACpB,cAAI,GAAG,IAAIA,OAAM,IAAI,GAAG,GAAG,OAAO,OAAO;AAAA,QAC7C,OAAO;AACH,cAAI,GAAG,IAAI;AAAA,QACf;AACA,eAAO;AAAA,MACX,GAAG,WAAW;AAAA,IAClB;AAEA,QAAI,SAAS,SAAS,mBAAmB,QAAQ,QAAQ;AACrD,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,KAAK;AAClD,YAAI,GAAG,IAAI,OAAO,GAAG;AACrB,eAAO;AAAA,MACX,GAAG,MAAM;AAAA,IACb;AAEA,QAAI,SAAS,SAAU,KAAK,gBAAgB,SAAS;AACjD,UAAI,iBAAiB,IAAI,QAAQ,OAAO,GAAG;AAC3C,UAAI,YAAY,cAAc;AAE1B,eAAO,eAAe,QAAQ,kBAAkB,QAAQ;AAAA,MAC5D;AAEA,UAAI;AACA,eAAO,mBAAmB,cAAc;AAAA,MAC5C,SAAS,GAAG;AACR,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,QAAQ;AAIZ,QAAI,SAAS,SAASC,QAAO,KAAK,gBAAgB,SAAS,MAAM,QAAQ;AAGrE,UAAI,IAAI,WAAW,GAAG;AAClB,eAAO;AAAA,MACX;AAEA,UAAI,SAAS;AACb,UAAI,OAAO,QAAQ,UAAU;AACzB,iBAAS,OAAO,UAAU,SAAS,KAAK,GAAG;AAAA,MAC/C,WAAW,OAAO,QAAQ,UAAU;AAChC,iBAAS,OAAO,GAAG;AAAA,MACvB;AAEA,UAAI,YAAY,cAAc;AAC1B,eAAO,OAAO,MAAM,EAAE,QAAQ,mBAAmB,SAAU,IAAI;AAC3D,iBAAO,WAAW,SAAS,GAAG,MAAM,CAAC,GAAG,EAAE,IAAI;AAAA,QAClD,CAAC;AAAA,MACL;AAEA,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,OAAO;AAC3C,YAAI,UAAU,OAAO,UAAU,QAAQ,OAAO,MAAM,GAAG,IAAI,KAAK,IAAI;AACpE,YAAI,MAAM,CAAC;AAEX,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,cAAI,IAAI,QAAQ,WAAW,CAAC;AAC5B,cACI,MAAM,MACH,MAAM,MACN,MAAM,MACN,MAAM,OACL,KAAK,MAAQ,KAAK,MAClB,KAAK,MAAQ,KAAK,MAClB,KAAK,MAAQ,KAAK,OAClB,WAAW,QAAQ,YAAY,MAAM,MAAQ,MAAM,KACzD;AACE,gBAAI,IAAI,MAAM,IAAI,QAAQ,OAAO,CAAC;AAClC;AAAA,UACJ;AAEA,cAAI,IAAI,KAAM;AACV,gBAAI,IAAI,MAAM,IAAI,SAAS,CAAC;AAC5B;AAAA,UACJ;AAEA,cAAI,IAAI,MAAO;AACX,gBAAI,IAAI,MAAM,IAAI,SAAS,MAAQ,KAAK,CAAE,IACpC,SAAS,MAAQ,IAAI,EAAK;AAChC;AAAA,UACJ;AAEA,cAAI,IAAI,SAAU,KAAK,OAAQ;AAC3B,gBAAI,IAAI,MAAM,IAAI,SAAS,MAAQ,KAAK,EAAG,IACrC,SAAS,MAAS,KAAK,IAAK,EAAK,IACjC,SAAS,MAAQ,IAAI,EAAK;AAChC;AAAA,UACJ;AAEA,eAAK;AACL,cAAI,UAAa,IAAI,SAAU,KAAO,QAAQ,WAAW,CAAC,IAAI;AAE9D,cAAI,IAAI,MAAM,IAAI,SAAS,MAAQ,KAAK,EAAG,IACrC,SAAS,MAAS,KAAK,KAAM,EAAK,IAClC,SAAS,MAAS,KAAK,IAAK,EAAK,IACjC,SAAS,MAAQ,IAAI,EAAK;AAAA,QACpC;AAEA,eAAO,IAAI,KAAK,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,UAAU,SAASC,SAAQ,OAAO;AAClC,UAAI,QAAQ,CAAC,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG,MAAM,IAAI,CAAC;AAC7C,UAAI,OAAO,CAAC;AAEZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACnC,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,MAAM,KAAK,IAAI,KAAK,IAAI;AAE5B,YAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,cAAI,MAAM,KAAK,CAAC;AAChB,cAAI,MAAM,IAAI,GAAG;AACjB,cAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,KAAK,QAAQ,GAAG,MAAM,IAAI;AACrE,kBAAM,KAAK,EAAE,KAAU,MAAM,IAAI,CAAC;AAClC,iBAAK,KAAK,GAAG;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AAEA,mBAAa,KAAK;AAElB,aAAO;AAAA,IACX;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK;AAClC,aAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,IACnD;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK;AAClC,UAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACjC,eAAO;AAAA,MACX;AAEA,aAAO,CAAC,EAAE,IAAI,eAAe,IAAI,YAAY,YAAY,IAAI,YAAY,SAAS,GAAG;AAAA,IACzF;AAEA,QAAI,UAAU,SAASC,SAAQ,GAAG,GAAG;AACjC,aAAO,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,IACzB;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK,IAAI;AACtC,UAAI,QAAQ,GAAG,GAAG;AACd,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACpC,iBAAO,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,QAC1B;AACA,eAAO;AAAA,MACX;AACA,aAAO,GAAG,GAAG;AAAA,IACjB;AAEA,WAAO,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA;AAAA;;;AC3QA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AACrB,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAI,MAAM,OAAO,UAAU;AAE3B,QAAI,wBAAwB;AAAA,MACxB,UAAU,SAAS,SAAS,QAAQ;AAChC,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,OAAO;AAAA,MACP,SAAS,SAAS,QAAQ,QAAQ,KAAK;AACnC,eAAO,SAAS,MAAM,MAAM;AAAA,MAChC;AAAA,MACA,QAAQ,SAAS,OAAO,QAAQ;AAC5B,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,UAAU,MAAM;AACpB,QAAI,OAAO,MAAM,UAAU;AAC3B,QAAI,cAAc,SAAU,KAAK,cAAc;AAC3C,WAAK,MAAM,KAAK,QAAQ,YAAY,IAAI,eAAe,CAAC,YAAY,CAAC;AAAA,IACzE;AAEA,QAAI,QAAQ,KAAK,UAAU;AAE3B,QAAI,gBAAgB,QAAQ,SAAS;AACrC,QAAI,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,SAAS,MAAM;AAAA,MACf,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,WAAW,QAAQ,WAAW,aAAa;AAAA;AAAA,MAE3C,SAAS;AAAA,MACT,eAAe,SAAS,cAAc,MAAM;AACxC,eAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA,WAAW;AAAA,MACX,oBAAoB;AAAA,IACxB;AAEA,QAAI,wBAAwB,SAASC,uBAAsB,GAAG;AAC1D,aAAO,OAAO,MAAM,YACb,OAAO,MAAM,YACb,OAAO,MAAM,aACb,OAAO,MAAM,YACb,OAAO,MAAM;AAAA,IACxB;AAEA,QAAI,WAAW,CAAC;AAEhB,QAAIC,aAAY,SAASA,WACrB,QACA,QACA,qBACA,gBACA,kBACA,oBACA,WACA,iBACA,SACA,QACA,MACA,WACA,eACA,QACA,WACA,kBACA,SACA,aACF;AACE,UAAI,MAAM;AAEV,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,UAAI,WAAW;AACf,cAAQ,QAAQ,MAAM,IAAI,QAAQ,OAAO,UAAkB,CAAC,UAAU;AAElE,YAAI,MAAM,MAAM,IAAI,MAAM;AAC1B,gBAAQ;AACR,YAAI,OAAO,QAAQ,aAAa;AAC5B,cAAI,QAAQ,MAAM;AACd,kBAAM,IAAI,WAAW,qBAAqB;AAAA,UAC9C,OAAO;AACH,uBAAW;AAAA,UACf;AAAA,QACJ;AACA,YAAI,OAAO,MAAM,IAAI,QAAQ,MAAM,aAAa;AAC5C,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,UAAI,OAAO,WAAW,YAAY;AAC9B,cAAM,OAAO,QAAQ,GAAG;AAAA,MAC5B,WAAW,eAAe,MAAM;AAC5B,cAAM,cAAc,GAAG;AAAA,MAC3B,WAAW,wBAAwB,WAAW,QAAQ,GAAG,GAAG;AACxD,cAAM,MAAM,SAAS,KAAK,SAAUC,QAAO;AACvC,cAAIA,kBAAiB,MAAM;AACvB,mBAAO,cAAcA,MAAK;AAAA,UAC9B;AACA,iBAAOA;AAAA,QACX,CAAC;AAAA,MACL;AAEA,UAAI,QAAQ,MAAM;AACd,YAAI,oBAAoB;AACpB,iBAAO,WAAW,CAAC,mBAAmB,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,MAAM,IAAI;AAAA,QACtG;AAEA,cAAM;AAAA,MACV;AAEA,UAAI,sBAAsB,GAAG,KAAK,MAAM,SAAS,GAAG,GAAG;AACnD,YAAI,SAAS;AACT,cAAI,WAAW,mBAAmB,SAAS,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,MAAM;AACnG,iBAAO,CAAC,UAAU,QAAQ,IAAI,MAAM,UAAU,QAAQ,KAAK,SAAS,SAAS,SAAS,SAAS,MAAM,CAAC,CAAC;AAAA,QAC3G;AACA,eAAO,CAAC,UAAU,MAAM,IAAI,MAAM,UAAU,OAAO,GAAG,CAAC,CAAC;AAAA,MAC5D;AAEA,UAAI,SAAS,CAAC;AAEd,UAAI,OAAO,QAAQ,aAAa;AAC5B,eAAO;AAAA,MACX;AAEA,UAAI;AACJ,UAAI,wBAAwB,WAAW,QAAQ,GAAG,GAAG;AAEjD,YAAI,oBAAoB,SAAS;AAC7B,gBAAM,MAAM,SAAS,KAAK,OAAO;AAAA,QACrC;AACA,kBAAU,CAAC,EAAE,OAAO,IAAI,SAAS,IAAI,IAAI,KAAK,GAAG,KAAK,OAAO,OAAe,CAAC;AAAA,MACjF,WAAW,QAAQ,MAAM,GAAG;AACxB,kBAAU;AAAA,MACd,OAAO;AACH,YAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,kBAAU,OAAO,KAAK,KAAK,IAAI,IAAI;AAAA,MACvC;AAEA,UAAI,gBAAgB,kBAAkB,OAAO,MAAM,EAAE,QAAQ,OAAO,KAAK,IAAI,OAAO,MAAM;AAE1F,UAAI,iBAAiB,kBAAkB,QAAQ,GAAG,KAAK,IAAI,WAAW,IAAI,gBAAgB,OAAO;AAEjG,UAAI,oBAAoB,QAAQ,GAAG,KAAK,IAAI,WAAW,GAAG;AACtD,eAAO,iBAAiB;AAAA,MAC5B;AAEA,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,YAAI,MAAM,QAAQ,CAAC;AACnB,YAAI,QAAQ,OAAO,QAAQ,YAAY,OAAO,OAAO,IAAI,UAAU,cAC7D,IAAI,QACJ,IAAI,GAAG;AAEb,YAAI,aAAa,UAAU,MAAM;AAC7B;AAAA,QACJ;AAEA,YAAI,aAAa,aAAa,kBAAkB,OAAO,GAAG,EAAE,QAAQ,OAAO,KAAK,IAAI,OAAO,GAAG;AAC9F,YAAI,YAAY,QAAQ,GAAG,IACrB,OAAO,wBAAwB,aAAa,oBAAoB,gBAAgB,UAAU,IAAI,iBAC9F,kBAAkB,YAAY,MAAM,aAAa,MAAM,aAAa;AAE1E,oBAAY,IAAI,QAAQ,IAAI;AAC5B,YAAI,mBAAmB,eAAe;AACtC,yBAAiB,IAAI,UAAU,WAAW;AAC1C,oBAAY,QAAQD;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,wBAAwB,WAAW,oBAAoB,QAAQ,GAAG,IAAI,OAAO;AAAA,UAC7E;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,4BAA4B,SAASE,2BAA0B,MAAM;AACrE,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,KAAK,qBAAqB,eAAe,OAAO,KAAK,qBAAqB,WAAW;AAC5F,cAAM,IAAI,UAAU,wEAAwE;AAAA,MAChG;AAEA,UAAI,OAAO,KAAK,oBAAoB,eAAe,OAAO,KAAK,oBAAoB,WAAW;AAC1F,cAAM,IAAI,UAAU,uEAAuE;AAAA,MAC/F;AAEA,UAAI,KAAK,YAAY,QAAQ,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,YAAY,YAAY;AACpG,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACvD;AAEA,UAAI,UAAU,KAAK,WAAW,SAAS;AACvC,UAAI,OAAO,KAAK,YAAY,eAAe,KAAK,YAAY,WAAW,KAAK,YAAY,cAAc;AAClG,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AAEA,UAAI,SAAS,QAAQ,SAAS;AAC9B,UAAI,OAAO,KAAK,WAAW,aAAa;AACpC,YAAI,CAAC,IAAI,KAAK,QAAQ,YAAY,KAAK,MAAM,GAAG;AAC5C,gBAAM,IAAI,UAAU,iCAAiC;AAAA,QACzD;AACA,iBAAS,KAAK;AAAA,MAClB;AACA,UAAI,YAAY,QAAQ,WAAW,MAAM;AAEzC,UAAI,SAAS,SAAS;AACtB,UAAI,OAAO,KAAK,WAAW,cAAc,QAAQ,KAAK,MAAM,GAAG;AAC3D,iBAAS,KAAK;AAAA,MAClB;AAEA,UAAI;AACJ,UAAI,KAAK,eAAe,uBAAuB;AAC3C,sBAAc,KAAK;AAAA,MACvB,WAAW,aAAa,MAAM;AAC1B,sBAAc,KAAK,UAAU,YAAY;AAAA,MAC7C,OAAO;AACH,sBAAc,SAAS;AAAA,MAC3B;AAEA,UAAI,oBAAoB,QAAQ,OAAO,KAAK,mBAAmB,WAAW;AACtE,cAAM,IAAI,UAAU,+CAA+C;AAAA,MACvE;AAEA,UAAI,YAAY,OAAO,KAAK,cAAc,cAAc,KAAK,oBAAoB,OAAO,OAAO,SAAS,YAAY,CAAC,CAAC,KAAK;AAE3H,aAAO;AAAA,QACH,gBAAgB,OAAO,KAAK,mBAAmB,YAAY,KAAK,iBAAiB,SAAS;AAAA,QAC1F;AAAA,QACA,kBAAkB,OAAO,KAAK,qBAAqB,YAAY,CAAC,CAAC,KAAK,mBAAmB,SAAS;AAAA,QAClG;AAAA,QACA;AAAA,QACA,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,gBAAgB,CAAC,CAAC,KAAK;AAAA,QACvB,WAAW,OAAO,KAAK,cAAc,cAAc,SAAS,YAAY,KAAK;AAAA,QAC7E,QAAQ,OAAO,KAAK,WAAW,YAAY,KAAK,SAAS,SAAS;AAAA,QAClE,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,SAAS,OAAO,KAAK,YAAY,aAAa,KAAK,UAAU,SAAS;AAAA,QACtE,kBAAkB,OAAO,KAAK,qBAAqB,YAAY,KAAK,mBAAmB,SAAS;AAAA,QAChG;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe,OAAO,KAAK,kBAAkB,aAAa,KAAK,gBAAgB,SAAS;AAAA,QACxF,WAAW,OAAO,KAAK,cAAc,YAAY,KAAK,YAAY,SAAS;AAAA,QAC3E,MAAM,OAAO,KAAK,SAAS,aAAa,KAAK,OAAO;AAAA,QACpD,oBAAoB,OAAO,KAAK,uBAAuB,YAAY,KAAK,qBAAqB,SAAS;AAAA,MAC1G;AAAA,IACJ;AAEA,WAAO,UAAU,SAAU,QAAQ,MAAM;AACrC,UAAI,MAAM;AACV,UAAI,UAAU,0BAA0B,IAAI;AAE5C,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,QAAQ,WAAW,YAAY;AACtC,iBAAS,QAAQ;AACjB,cAAM,OAAO,IAAI,GAAG;AAAA,MACxB,WAAW,QAAQ,QAAQ,MAAM,GAAG;AAChC,iBAAS,QAAQ;AACjB,kBAAU;AAAA,MACd;AAEA,UAAI,OAAO,CAAC;AAEZ,UAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,eAAO;AAAA,MACX;AAEA,UAAI,sBAAsB,sBAAsB,QAAQ,WAAW;AACnE,UAAI,iBAAiB,wBAAwB,WAAW,QAAQ;AAEhE,UAAI,CAAC,SAAS;AACV,kBAAU,OAAO,KAAK,GAAG;AAAA,MAC7B;AAEA,UAAI,QAAQ,MAAM;AACd,gBAAQ,KAAK,QAAQ,IAAI;AAAA,MAC7B;AAEA,UAAI,cAAc,eAAe;AACjC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,YAAI,MAAM,QAAQ,CAAC;AACnB,YAAI,QAAQ,IAAI,GAAG;AAEnB,YAAI,QAAQ,aAAa,UAAU,MAAM;AACrC;AAAA,QACJ;AACA,oBAAY,MAAMF;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAS,QAAQ,UAAU;AAAA,UACnC,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,UAAI,SAAS,KAAK,KAAK,QAAQ,SAAS;AACxC,UAAI,SAAS,QAAQ,mBAAmB,OAAO,MAAM;AAErD,UAAI,QAAQ,iBAAiB;AACzB,YAAI,QAAQ,YAAY,cAAc;AAElC,oBAAU;AAAA,QACd,OAAO;AAEH,oBAAU;AAAA,QACd;AAAA,MACJ;AAEA,aAAO,OAAO,SAAS,IAAI,SAAS,SAAS;AAAA,IACjD;AAAA;AAAA;;;ACnWA;AAAA;AAAA;AAEA,QAAI,QAAQ;AAEZ,QAAI,MAAM,OAAO,UAAU;AAC3B,QAAI,UAAU,MAAM;AAEpB,QAAI,WAAW;AAAA,MACX,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,SAAS,MAAM;AAAA,MACf,WAAW;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,sBAAsB;AAAA,IAC1B;AAEA,QAAI,2BAA2B,SAAU,KAAK;AAC1C,aAAO,IAAI,QAAQ,aAAa,SAAU,IAAI,WAAW;AACrD,eAAO,OAAO,aAAa,SAAS,WAAW,EAAE,CAAC;AAAA,MACtD,CAAC;AAAA,IACL;AAEA,QAAI,kBAAkB,SAAU,KAAK,SAAS,oBAAoB;AAC9D,UAAI,OAAO,OAAO,QAAQ,YAAY,QAAQ,SAAS,IAAI,QAAQ,GAAG,IAAI,IAAI;AAC1E,eAAO,IAAI,MAAM,GAAG;AAAA,MACxB;AAEA,UAAI,QAAQ,wBAAwB,sBAAsB,QAAQ,YAAY;AAC1E,cAAM,IAAI,WAAW,gCAAgC,QAAQ,aAAa,cAAc,QAAQ,eAAe,IAAI,KAAK,OAAO,uBAAuB;AAAA,MAC1J;AAEA,aAAO;AAAA,IACX;AAOA,QAAI,cAAc;AAGlB,QAAI,kBAAkB;AAEtB,QAAI,cAAc,SAAS,uBAAuB,KAAK,SAAS;AAC5D,UAAI,MAAM,EAAE,WAAW,KAAK;AAE5B,UAAI,WAAW,QAAQ,oBAAoB,IAAI,QAAQ,OAAO,EAAE,IAAI;AACpE,iBAAW,SAAS,QAAQ,SAAS,GAAG,EAAE,QAAQ,SAAS,GAAG;AAE9D,UAAI,QAAQ,QAAQ,mBAAmB,WAAW,SAAY,QAAQ;AACtE,UAAI,QAAQ,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR,QAAQ,uBAAuB,QAAQ,IAAI;AAAA,MAC/C;AAEA,UAAI,QAAQ,wBAAwB,MAAM,SAAS,OAAO;AACtD,cAAM,IAAI,WAAW,oCAAoC,QAAQ,gBAAgB,UAAU,IAAI,KAAK,OAAO,WAAW;AAAA,MAC1H;AAEA,UAAI,YAAY;AAChB,UAAI;AAEJ,UAAI,UAAU,QAAQ;AACtB,UAAI,QAAQ,iBAAiB;AACzB,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AAC/B,cAAI,MAAM,CAAC,EAAE,QAAQ,OAAO,MAAM,GAAG;AACjC,gBAAI,MAAM,CAAC,MAAM,iBAAiB;AAC9B,wBAAU;AAAA,YACd,WAAW,MAAM,CAAC,MAAM,aAAa;AACjC,wBAAU;AAAA,YACd;AACA,wBAAY;AACZ,gBAAI,MAAM;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAEA,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AAC/B,YAAI,MAAM,WAAW;AACjB;AAAA,QACJ;AACA,YAAI,OAAO,MAAM,CAAC;AAElB,YAAI,mBAAmB,KAAK,QAAQ,IAAI;AACxC,YAAI,MAAM,qBAAqB,KAAK,KAAK,QAAQ,GAAG,IAAI,mBAAmB;AAE3E,YAAI;AACJ,YAAI;AACJ,YAAI,QAAQ,IAAI;AACZ,gBAAM,QAAQ,QAAQ,MAAM,SAAS,SAAS,SAAS,KAAK;AAC5D,gBAAM,QAAQ,qBAAqB,OAAO;AAAA,QAC9C,OAAO;AACH,gBAAM,QAAQ,QAAQ,KAAK,MAAM,GAAG,GAAG,GAAG,SAAS,SAAS,SAAS,KAAK;AAE1E,gBAAM,MAAM;AAAA,YACR;AAAA,cACI,KAAK,MAAM,MAAM,CAAC;AAAA,cAClB;AAAA,cACA,QAAQ,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,SAAS;AAAA,YAC1C;AAAA,YACA,SAAU,YAAY;AAClB,qBAAO,QAAQ,QAAQ,YAAY,SAAS,SAAS,SAAS,OAAO;AAAA,YACzE;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,OAAO,QAAQ,4BAA4B,YAAY,cAAc;AACrE,gBAAM,yBAAyB,OAAO,GAAG,CAAC;AAAA,QAC9C;AAEA,YAAI,KAAK,QAAQ,KAAK,IAAI,IAAI;AAC1B,gBAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,IAAI;AAAA,QACjC;AAEA,YAAI,WAAW,IAAI,KAAK,KAAK,GAAG;AAChC,YAAI,YAAY,QAAQ,eAAe,WAAW;AAC9C,cAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,GAAG,GAAG;AAAA,QAC1C,WAAW,CAAC,YAAY,QAAQ,eAAe,QAAQ;AACnD,cAAI,GAAG,IAAI;AAAA,QACf;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,cAAc,SAAU,OAAO,KAAK,SAAS,cAAc;AAC3D,UAAI,qBAAqB;AACzB,UAAI,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC,MAAM,MAAM;AACtD,YAAI,YAAY,MAAM,MAAM,GAAG,EAAE,EAAE,KAAK,EAAE;AAC1C,6BAAqB,MAAM,QAAQ,GAAG,KAAK,IAAI,SAAS,IAAI,IAAI,SAAS,EAAE,SAAS;AAAA,MACxF;AAEA,UAAI,OAAO,eAAe,MAAM,gBAAgB,KAAK,SAAS,kBAAkB;AAEhF,eAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACxC,YAAI;AACJ,YAAI,OAAO,MAAM,CAAC;AAElB,YAAI,SAAS,QAAQ,QAAQ,aAAa;AACtC,gBAAM,QAAQ,qBAAqB,SAAS,MAAO,QAAQ,sBAAsB,SAAS,QACpF,CAAC,IACD,MAAM,QAAQ,CAAC,GAAG,IAAI;AAAA,QAChC,OAAO;AACH,gBAAM,QAAQ,eAAe,EAAE,WAAW,KAAK,IAAI,CAAC;AACpD,cAAI,YAAY,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM,KAAK,MAAM,GAAG,EAAE,IAAI;AACrG,cAAI,cAAc,QAAQ,kBAAkB,UAAU,QAAQ,QAAQ,GAAG,IAAI;AAC7E,cAAI,QAAQ,SAAS,aAAa,EAAE;AACpC,cAAI,CAAC,QAAQ,eAAe,gBAAgB,IAAI;AAC5C,kBAAM,EAAE,GAAG,KAAK;AAAA,UACpB,WACI,CAAC,MAAM,KAAK,KACT,SAAS,eACT,OAAO,KAAK,MAAM,eAClB,SAAS,MACR,QAAQ,eAAe,SAAS,QAAQ,aAC9C;AACE,kBAAM,CAAC;AACP,gBAAI,KAAK,IAAI;AAAA,UACjB,WAAW,gBAAgB,aAAa;AACpC,gBAAI,WAAW,IAAI;AAAA,UACvB;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,YAAY,SAAS,qBAAqB,UAAU,KAAK,SAAS,cAAc;AAChF,UAAI,CAAC,UAAU;AACX;AAAA,MACJ;AAGA,UAAI,MAAM,QAAQ,YAAY,SAAS,QAAQ,eAAe,MAAM,IAAI;AAIxE,UAAI,WAAW;AACf,UAAI,QAAQ;AAIZ,UAAI,UAAU,QAAQ,QAAQ,KAAK,SAAS,KAAK,GAAG;AACpD,UAAI,SAAS,UAAU,IAAI,MAAM,GAAG,QAAQ,KAAK,IAAI;AAIrD,UAAI,OAAO,CAAC;AACZ,UAAI,QAAQ;AAER,YAAI,CAAC,QAAQ,gBAAgB,IAAI,KAAK,OAAO,WAAW,MAAM,GAAG;AAC7D,cAAI,CAAC,QAAQ,iBAAiB;AAC1B;AAAA,UACJ;AAAA,QACJ;AAEA,aAAK,KAAK,MAAM;AAAA,MACpB;AAIA,UAAI,IAAI;AACR,aAAO,QAAQ,QAAQ,MAAM,UAAU,MAAM,KAAK,GAAG,OAAO,QAAQ,IAAI,QAAQ,OAAO;AACnF,aAAK;AACL,YAAI,CAAC,QAAQ,gBAAgB,IAAI,KAAK,OAAO,WAAW,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,GAAG;AAC9E,cAAI,CAAC,QAAQ,iBAAiB;AAC1B;AAAA,UACJ;AAAA,QACJ;AACA,aAAK,KAAK,QAAQ,CAAC,CAAC;AAAA,MACxB;AAIA,UAAI,SAAS;AACT,YAAI,QAAQ,gBAAgB,MAAM;AAC9B,gBAAM,IAAI,WAAW,0CAA0C,QAAQ,QAAQ,0BAA0B;AAAA,QAC7G;AACA,aAAK,KAAK,MAAM,IAAI,MAAM,QAAQ,KAAK,IAAI,GAAG;AAAA,MAClD;AAEA,aAAO,YAAY,MAAM,KAAK,SAAS,YAAY;AAAA,IACvD;AAEA,QAAI,wBAAwB,SAASG,uBAAsB,MAAM;AAC7D,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,KAAK,qBAAqB,eAAe,OAAO,KAAK,qBAAqB,WAAW;AAC5F,cAAM,IAAI,UAAU,wEAAwE;AAAA,MAChG;AAEA,UAAI,OAAO,KAAK,oBAAoB,eAAe,OAAO,KAAK,oBAAoB,WAAW;AAC1F,cAAM,IAAI,UAAU,uEAAuE;AAAA,MAC/F;AAEA,UAAI,KAAK,YAAY,QAAQ,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,YAAY,YAAY;AACpG,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACvD;AAEA,UAAI,OAAO,KAAK,YAAY,eAAe,KAAK,YAAY,WAAW,KAAK,YAAY,cAAc;AAClG,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AAEA,UAAI,OAAO,KAAK,yBAAyB,eAAe,OAAO,KAAK,yBAAyB,WAAW;AACpG,cAAM,IAAI,UAAU,iDAAiD;AAAA,MACzE;AAEA,UAAI,UAAU,OAAO,KAAK,YAAY,cAAc,SAAS,UAAU,KAAK;AAE5E,UAAI,aAAa,OAAO,KAAK,eAAe,cAAc,SAAS,aAAa,KAAK;AAErF,UAAI,eAAe,aAAa,eAAe,WAAW,eAAe,QAAQ;AAC7E,cAAM,IAAI,UAAU,8DAA8D;AAAA,MACtF;AAEA,UAAI,YAAY,OAAO,KAAK,cAAc,cAAc,KAAK,oBAAoB,OAAO,OAAO,SAAS,YAAY,CAAC,CAAC,KAAK;AAE3H,aAAO;AAAA,QACH;AAAA,QACA,kBAAkB,OAAO,KAAK,qBAAqB,YAAY,CAAC,CAAC,KAAK,mBAAmB,SAAS;AAAA,QAClG,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,aAAa,OAAO,KAAK,gBAAgB,YAAY,KAAK,cAAc,SAAS;AAAA,QACjF,YAAY,OAAO,KAAK,eAAe,WAAW,KAAK,aAAa,SAAS;AAAA,QAC7E;AAAA,QACA,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,OAAO,OAAO,KAAK,UAAU,YAAY,KAAK,QAAQ,SAAS;AAAA,QAC/D,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,SAAS,OAAO,KAAK,YAAY,aAAa,KAAK,UAAU,SAAS;AAAA,QACtE,WAAW,OAAO,KAAK,cAAc,YAAY,MAAM,SAAS,KAAK,SAAS,IAAI,KAAK,YAAY,SAAS;AAAA;AAAA,QAE5G,OAAQ,OAAO,KAAK,UAAU,YAAY,KAAK,UAAU,QAAS,CAAC,KAAK,QAAQ,SAAS;AAAA,QACzF;AAAA,QACA,mBAAmB,KAAK,sBAAsB;AAAA,QAC9C,0BAA0B,OAAO,KAAK,6BAA6B,YAAY,KAAK,2BAA2B,SAAS;AAAA,QACxH,gBAAgB,OAAO,KAAK,mBAAmB,WAAW,KAAK,iBAAiB,SAAS;AAAA,QACzF,aAAa,KAAK,gBAAgB;AAAA,QAClC,cAAc,OAAO,KAAK,iBAAiB,YAAY,KAAK,eAAe,SAAS;AAAA,QACpF,aAAa,OAAO,KAAK,gBAAgB,YAAY,CAAC,CAAC,KAAK,cAAc,SAAS;AAAA,QACnF,oBAAoB,OAAO,KAAK,uBAAuB,YAAY,KAAK,qBAAqB,SAAS;AAAA,QACtG,sBAAsB,OAAO,KAAK,yBAAyB,YAAY,KAAK,uBAAuB;AAAA,MACvG;AAAA,IACJ;AAEA,WAAO,UAAU,SAAU,KAAK,MAAM;AAClC,UAAI,UAAU,sBAAsB,IAAI;AAExC,UAAI,QAAQ,MAAM,QAAQ,QAAQ,OAAO,QAAQ,aAAa;AAC1D,eAAO,QAAQ,eAAe,EAAE,WAAW,KAAK,IAAI,CAAC;AAAA,MACzD;AAEA,UAAI,UAAU,OAAO,QAAQ,WAAW,YAAY,KAAK,OAAO,IAAI;AACpE,UAAI,MAAM,QAAQ,eAAe,EAAE,WAAW,KAAK,IAAI,CAAC;AAIxD,UAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,SAAS,UAAU,KAAK,QAAQ,GAAG,GAAG,SAAS,OAAO,QAAQ,QAAQ;AAC1E,cAAM,MAAM,MAAM,KAAK,QAAQ,OAAO;AAAA,MAC1C;AAEA,UAAI,QAAQ,gBAAgB,MAAM;AAC9B,eAAO;AAAA,MACX;AAEA,aAAO,MAAM,QAAQ,GAAG;AAAA,IAC5B;AAAA;AAAA;;;ACvUA;AAAA;AAAA;AAEA,QAAIC,aAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,UAAU;AAEd,WAAO,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA,WAAWA;AAAA,IACf;AAAA;AAAA;;;ACVA,SAAoB;AACpB,IAAM,eAAe;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,SAAS,cAAc,GAAG;AAC7B,SAAQ,KACJ,OAAO,MAAM,YACb,aAAa,KAAK,CAAC,SAAS,OAAO,UAAU,eAAe,KAAK,GAAG,IAAI,CAAC;AACjF;AAKO,SAAS,0BAA0B,MAAM,SAAS;AACrD,SACK,aAAU,MAAM;AAAA,IACjB,eAAe,CAAC,MAAM,KAAK,MAAM,EAAE,QAAQ,IAAI,GAAI,EAAE,SAAS;AAAA,IAC9D,aAAa,WAAW,OAAO,WAAW;AAAA,EAC9C,CAAC,EAII,QAAQ,QAAQ,GAAG,EACnB,QAAQ,QAAQ,GAAG;AAC5B;AAOO,IAAM,sBAAuB,uBAAM;AACtC,QAAM,KAAK;AAAA,IACP,MAAM;AAAA,IACN,KAAK;AAAA,IACL,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AACA,SAAO,CAAC,QAAQ;AACZ,UAAM,cAAc,IAAI,QAAQ,wBAAwB,CAAC,OAAO,GAAG,EAAE,CAAC;AACtE,WAAO,CAAC,YAAY;AAChB,aAAO,YAAY,QAAQ,mBAAmB,CAAC,IAAI,OAAO;AACtD,cAAM,SAAS,QAAQ,EAAE;AACzB,YAAI,8BAA8B,MAAM;AACpC,iBAAO,mBAAmB,MAAM;AACpC,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,GAAG;AACH,SAAS,8BAA8B,OAAO;AAC1C,SAAO,CAAC,UAAU,UAAU,SAAS,EAAE,SAAS,OAAO,KAAK;AAChE;AACO,SAAS,iBAAiB,MAAM;AACnC,QAAM,SAAS,KAAK,MAAM,UAAU;AACpC,MAAI,CAAC,QAAQ;AACT,WAAO,CAAC;AAAA,EACZ;AACA,SAAO,OAAO,IAAI,CAAC,UAAU,MAAM,QAAQ,SAAS,EAAE,CAAC;AAC3D;AAOO,SAAS,gBAAgB,MAAM;AAClC,MAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,OAAO,KAAK,CAAC,MAAM,UAAU;AACjE,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,CAAC,cAAc,KAAK,CAAC,CAAC,GAAG;AACzB,WAAO,KAAK,MAAM;AAAA,EACtB;AACA,QAAM,UAAU,OAAO,KAAK,KAAK,CAAC,CAAC;AACnC,QAAM,mBAAmB,QAAQ,OAAO,CAAC,QAAQ,aAAa,SAAS,GAAG,CAAC;AAK3E,MAAI,iBAAiB,SAAS,KAC1B,iBAAiB,WAAW,QAAQ,QAAQ;AAC5C,gBAAY,+BAA+B,iBAAiB,KAAK,IAAI,CAAC,4GAA4G;AAAA,EACtL;AACA,SAAO,CAAC;AACZ;AAIO,SAAS,mBAAmB,MAAM;AACrC,QAAM,OAAO;AAAA,IACT,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,UAAU,CAAC;AAAA,IACX,WAAW;AAAA,EACf;AACA,MAAI,KAAK,SAAS,GAAG;AACjB,UAAM,MAAM,KAAK,KAAK,SAAS,CAAC;AAChC,QAAI,OAAO,QAAQ,UAAU;AACzB,WAAK,gBAAgB,0BAA0B,KAAK,IAAI,CAAC;AAAA,IAC7D,WACS,cAAc,GAAG,GAAG;AACzB,YAAM,SAAS,OAAO,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC;AAC3C,YAAM,YAAY,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,aAAa,SAAS,GAAG,CAAC;AACjF,UAAI,UAAU,QAAQ;AAClB,oBAAY,0BAA0B,UAAU,KAAK,IAAI,CAAC,cAAc;AAAA,MAC5E;AACA,UAAI,OAAO,QAAQ;AACf,aAAK,gBAAgB,0BAA0B,OAAO,MAAM;AAAA,MAChE;AACA,UAAI,OAAO,gBAAgB;AACvB,aAAK,QAAQ,iBAAiB,IAAI,OAAO;AAAA,MAC7C;AACA,UAAI,OAAO,eAAe;AACtB,aAAK,QAAQ,gBAAgB,IAAI,OAAO;AAAA,MAC5C;AACA,UAAI,OAAO,eAAe;AACtB,YAAI,KAAK,QAAQ,gBAAgB,GAAG;AAChC,gBAAM,IAAI,MAAM,qDAAqD;AAAA,QACzE;AACA,aAAK,QAAQ,gBAAgB,IAAI,OAAO;AAAA,MAC5C;AACA,UAAI,OAAO,YAAY;AACnB,aAAK,QAAQ,gBAAgB,IAAI,OAAO;AAAA,MAC5C;AACA,UAAI,OAAO,UAAU,OAAO,iBAAiB,GAAG;AAC5C,aAAK,SAAS,oBAAoB,OAAO;AAAA,MAC7C;AACA,UAAI,OAAO,UAAU,OAAO,OAAO,GAAG;AAClC,aAAK,SAAS,UAAU,OAAO;AAAA,MACnC;AACA,UAAI,OAAO,MAAM;AACb,aAAK,OAAO,OAAO;AAAA,MACvB;AACA,UAAI,OAAO,eAAe;AACtB,YAAI,OAAO,QAAQ;AACf,gBAAM,IAAI,MAAM,8CAA8C;AAAA,QAClE;AACA,YAAI,OAAO,OAAO,kBAAkB,YAAY;AAC5C,gBAAM,IAAI,MAAM,kFACiC;AAAA,QACrD;AACA,aAAK,gBAAgB,OAAO;AAAA,MAChC;AACA,UAAI,OAAO,mBAAmB;AAC1B,aAAK,UAAU,OAAO;AAAA,MAC1B;AACA,UAAI,OAAO,WAAW;AAClB,aAAK,YAAY;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAKO,SAAS,YAAY,KAAK;AAE7B,QAAM,QAAQ;AACd,QAAM,cAAc,OAAO,UAAU,eAAe,KAAK,KAAK,aAAa,IACrE,IAAI,cACJ,YAAa,MAAM;AACjB,UAAM,MAAM,MAAM,IAAI;AAAA,EAC1B;AAMJ,SAAO,OAAO,aAAa,KAAK;AAChC,cAAY,YAAY,OAAO,OAAO,MAAM,SAAS;AACrD,SAAO,OAAO,YAAY,WAAW,GAAG;AACxC,SAAO;AACX;AAIO,SAAS,cAAc,KAAK;AAC/B,MAAI,OAAO,QAAQ,UAAU;AACzB,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAChD;AACA,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAC5C,QAAI,IAAI,GAAG,KAAK,MAAM;AAClB,aAAO,GAAG,IAAI,IAAI,GAAG;AAAA,IACzB;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AAOO,SAAS,iBAAiB,KAAK;AAClC,MAAI,EAAE,OAAO,OAAO,QAAQ,WAAW;AACnC,WAAO;AAAA,EACX;AACA,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,CAAC,QAAQ,WAAW;AAC/C,WAAO,gBAAgB,MAAM,CAAC,IAAI,IAAI,MAAM;AAC5C,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AAKO,SAAS,gBAAgB,QAAQ;AACpC,SAAO,OACF,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,OAAO,CAAC,EAAE,YAAY,CAAC,EACzE,KAAK,GAAG;AACjB;AACO,SAAS,8BAA8B,SAAS,UAAU;AAC7D,MAAI,UAAU;AAEV,WAAO,QAAQ,KAAK,CAAC,QAAQ;AACzB,iBAAW,MAAM;AACb,iBAAS,MAAM,GAAG;AAAA,MACtB,GAAG,CAAC;AAAA,IACR,GAAG,CAAC,QAAQ;AACR,iBAAW,MAAM;AACb,iBAAS,KAAK,IAAI;AAAA,MACtB,GAAG,CAAC;AAAA,IACR,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAIO,SAAS,kBAAkB,MAAM;AACpC,MAAI,SAAS,SAAS;AAClB,WAAO;AAAA,EACX,OACK;AACD,WAAO,KAAK,CAAC,EAAE,YAAY,IAAI,KAAK,UAAU,CAAC;AAAA,EACnD;AACJ;AACO,SAAS,YAAY,SAAS;AACjC,MAAI,OAAO,QAAQ,gBAAgB,YAAY;AAC3C,WAAO,QAAQ,KAAK,WAAW,OAAO,EAAE;AAAA,EAC5C;AACA,SAAO,QAAQ,YAAY,SAAS,QAAQ;AAChD;AACO,SAAS,SAAS,KAAK;AAC1B,QAAM,OAAO,OAAO;AACpB,UAAQ,SAAS,cAAc,SAAS,aAAa,CAAC,CAAC;AAC3D;AAEO,SAAS,oBAAoB,MAAM;AACtC,QAAM,SAAS,CAAC;AAChB,QAAM,OAAO,CAAC,KAAK,YAAY;AAC3B,WAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC1C,YAAM,SAAS,UAAU,GAAG,OAAO,IAAI,GAAG,MAAM;AAChD,UAAI,SAAS,KAAK,GAAG;AACjB,YAAI,EAAE,iBAAiB,eACnB,CAAC,OAAO,UAAU,eAAe,KAAK,OAAO,MAAM,GAAG;AAEtD,iBAAO,KAAK,OAAO,MAAM;AAAA,QAC7B,OACK;AAED,iBAAO,MAAM,IAAI;AAAA,QACrB;AAAA,MACJ,OACK;AAED,eAAO,MAAM,IAAI,OAAO,KAAK;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AACA,OAAK,MAAM,IAAI;AACf,SAAO;AACX;AACO,SAAS,gBAAgB,MAAM,GAAG,YAAY;AACjD,MAAI,CAAC,OAAO,UAAU,CAAC,GAAG;AACtB,QAAI,eAAe,QAAW;AAC1B,aAAO;AAAA,IACX,OACK;AACD,YAAM,IAAI,MAAM,GAAG,IAAI,qBAAqB;AAAA,IAChD;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,sCAAsC;AAClD,SAAO,OAAO,YAAY,cACpB,CAAC,IACD;AAAA,IACE,cAAc,QAAQ;AAAA,IACtB,UAAU,QAAQ;AAAA,EACtB;AACR;AACO,SAAS,0BAA0B,QAAQ;AAC9C,QAAM,gBAAgB,CAAC,YAAY;AAC/B,YAAQ,QAAQ,gBAAgB,YAAY;AAC5C,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AAEA,gBAAc,UAAU;AACxB,SAAO;AACX;AAiBA,SAAS,iBAAiB,KAAK,OAAO;AAClC,MAAI,KAAK,GAAG,aAAa,MAAM;AAC3B,WAAO,KAAK,MAAM,KAAK,GAAG,EAAE,QAAQ,IAAI,GAAI,EAAE,SAAS;AAAA,EAC3D;AACA,SAAO;AACX;AAIO,SAAS,yBAAyB,MAAM;AAC3C,SAAO,KAAK,UAAU,MAAM,gBAAgB;AAChD;AAIO,SAAS,WAAW,MAAM;AAC7B,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,SAAO,KAAK,WAAW,KAAK,IAAI,OAAO;AAC3C;AACO,SAAS,wBAAwB,QAAQ;AAC5C,MAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,WAAO,OAAO,KAAK,IAAI;AAAA,EAC3B;AACA,SAAO,OAAO,MAAM;AACxB;AACO,SAAS,wBAAwB,QAAQ;AAC5C,QAAM,SAAS,MAAM,QAAQ,MAAM,IAAI,OAAO,CAAC,IAAI;AACnD,SAAO,OAAO,MAAM;AACxB;AACO,SAAS,qBAAqB,SAAS;AAC1C,SAAO,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACjD,WAAO,CAAC,KAAK,wBAAwB,KAAK,CAAC;AAAA,EAC/C,CAAC;AACL;;;ACpWO,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA,EAEpB,gBAAgB;AACZ,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACpD;AAAA,EACA,YAAY,MAAM,MAAM,MAAM,QAAQ,SAAS,aAAa,UAAU,SAAS;AAC3E,UAAM,IAAI,MAAM,8BAA8B;AAAA,EAClD;AAAA;AAAA,EAEA,OAAO,mBAAmB;AACtB,UAAM,aAAa,IAAI,UAAU,YAAW,kBAAkB;AAC9D,eAAW,OAAO,YAAW;AAC7B,WAAO;AAAA,EACX;AACJ;AAEA,WAAW,gCAAgC,CAAC,cAAc,OAAO;AACjE,WAAW,qBAAqB;AACzB,IAAM,qBAAN,MAAyB;AAAA,EAC5B,YAAY,YAAY,SAAS;AAC7B,SAAK,cAAc;AACnB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,gBAAgB;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,iBAAiB;AACb,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACrD;AAAA,EACA,SAAS,wBAAwB;AAC7B,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC/C;AAAA,EACA,SAAS;AACL,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC7C;AACJ;;;ACrCO,IAAM,kBAAN,MAAM,yBAAwB,WAAW;AAAA,EAC5C,YAAY,SAAS;AACjB,UAAM;AAEN,QAAI,CAAC,SAAS;AACV,UAAI,CAAC,WAAW,OAAO;AACnB,cAAM,IAAI,MAAM,gHAC8B;AAAA,MAClD;AACA,gBAAU,WAAW;AAAA,IACzB;AAIA,QAAI,WAAW,iBAAiB;AAG5B,WAAK,WAAW,iBAAgB,0BAA0B,OAAO;AAAA,IACrE,OACK;AAGD,WAAK,WAAW,iBAAgB,yBAAyB,OAAO;AAAA,IACpE;AAAA,EACJ;AAAA,EACA,OAAO,yBAAyB,SAAS;AACrC,WAAO,CAAC,KAAK,MAAM,YAAY;AAC3B,UAAI;AACJ,YAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAC9C,2BAAmB,WAAW,MAAM;AAChC,6BAAmB;AACnB,iBAAO,WAAW,iBAAiB,CAAC;AAAA,QACxC,GAAG,OAAO;AAAA,MACd,CAAC;AACD,YAAM,eAAe,QAAQ,KAAK,IAAI;AACtC,aAAO,QAAQ,KAAK,CAAC,cAAc,cAAc,CAAC,EAAE,QAAQ,MAAM;AAC9D,YAAI,kBAAkB;AAClB,uBAAa,gBAAgB;AAAA,QACjC;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,OAAO,0BAA0B,SAAS;AACtC,WAAO,OAAO,KAAK,MAAM,YAAY;AAEjC,YAAM,QAAQ,IAAI,gBAAgB;AAClC,UAAI,YAAY,WAAW,MAAM;AAC7B,oBAAY;AACZ,cAAM,MAAM,WAAW,iBAAiB,CAAC;AAAA,MAC7C,GAAG,OAAO;AACV,UAAI;AACA,eAAO,MAAM,QAAQ,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,QAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,MAC9F,SACO,KAAK;AAIR,YAAI,IAAI,SAAS,cAAc;AAC3B,gBAAM,WAAW,iBAAiB;AAAA,QACtC,OACK;AACD,gBAAM;AAAA,QACV;AAAA,MACJ,UACA;AACI,YAAI,WAAW;AACX,uBAAa,SAAS;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB;AACZ,WAAO;AAAA,EACX;AAAA,EACA,MAAM,YAAY,MAAM,MAAM,MAAM,QAAQ,SAAS,aAAa,UAAU,SAAS;AACjF,UAAM,uBAAuB,aAAa;AAC1C,UAAM,MAAM,IAAI,IAAI,MAAM,GAAG,uBAAuB,SAAS,OAAO,MAAM,IAAI,EAAE;AAChF,QAAI,OAAO;AAKX,UAAM,mBAAmB,UAAU,UAAU,UAAU,SAAS,UAAU;AAC1E,UAAM,OAAO,gBAAgB,mBAAmB,KAAK;AACrD,UAAM,MAAM,MAAM,KAAK,SAAS,IAAI,SAAS,GAAG;AAAA,MAC5C;AAAA,MACA,SAAS,qBAAqB,OAAO;AAAA,MACrC,MAAM,OAAO,SAAS,WAAW,KAAK,UAAU,IAAI,IAAI;AAAA,IAC5D,GAAG,OAAO;AACV,WAAO,IAAI,wBAAwB,GAAG;AAAA,EAC1C;AACJ;AACO,IAAM,0BAAN,MAAM,iCAAgC,mBAAmB;AAAA,EAC5D,YAAY,KAAK;AACb,UAAM,IAAI,QAAQ,yBAAwB,0BAA0B,IAAI,OAAO,CAAC;AAChF,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,SAAS,wBAAwB;AAM7B,2BAAuB;AAEvB,WAAO,KAAK,KAAK;AAAA,EACrB;AAAA,EACA,SAAS;AACL,WAAO,KAAK,KAAK,KAAK;AAAA,EAC1B;AAAA,EACA,OAAO,0BAA0B,SAAS;AAGtC,UAAM,aAAa,CAAC;AACpB,eAAW,SAAS,SAAS;AACzB,UAAI,CAAC,MAAM,QAAQ,KAAK,KAAK,MAAM,UAAU,GAAG;AAC5C,cAAM,IAAI,MAAM,8JAA8J;AAAA,MAClL;AACA,iBAAW,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC;AAAA,IAClC;AACA,WAAO;AAAA,EACX;AACJ;;;ACpIO,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASxB,qBAAqB,SAAS,QAAQ;AAClC,UAAM,IAAI,MAAM,uCAAuC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,0BAA0B,SAAS,QAAQ;AACvC,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,MAAM;AACrB,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACpD;AACJ;AAQO,IAAM,uCAAN,cAAmD,MAAM;AAChE;;;ACvCO,IAAM,uBAAN,cAAmC,eAAe;AAAA,EACrD,YAAY,cAAc;AACtB,UAAM;AAIN,SAAK,eAAe,gBAAgB,OAAO;AAAA,EAC/C;AAAA;AAAA,EAEA,qBAAqB,SAAS,QAAQ;AAClC,UAAM,IAAI,qCAAqC,+DAA+D;AAAA,EAClH;AAAA;AAAA,EAEA,MAAM,0BAA0B,SAAS,QAAQ;AAC7C,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,MAAM,MAAM,KAAK,aAAa,UAAU,OAAO,QAAQ,OAAO,MAAM,GAAG;AAAA,MACzE,MAAM;AAAA,MACN,MAAM,EAAE,MAAM,UAAU;AAAA,IAC5B,GAAG,OAAO,CAAC,MAAM,CAAC;AAClB,UAAM,kBAAkB,MAAM,KAAK,aAAa,KAAK,QAAQ,KAAK,QAAQ,OAAO,OAAO,CAAC;AAIzF,UAAM,iBAAiB,IAAI,WAAW,eAAe;AACrD,UAAM,oBAAoB,IAAI,MAAM,eAAe,MAAM;AACzD,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC5C,wBAAkB,CAAC,IAAI,eAAe,eAAe,CAAC,CAAC;AAAA,IAC3D;AACA,WAAO,kBAAkB,KAAK,EAAE;AAAA,EACpC;AAAA;AAAA,EAEA,MAAM,mBAAmB,MAAM;AAC3B,WAAO,IAAI,WAAW,MAAM,KAAK,aAAa,OAAO,WAAW,IAAI,CAAC;AAAA,EACzE;AACJ;AAGA,IAAM,iBAAiB,IAAI,MAAM,GAAG;AACpC,SAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC5C,iBAAe,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AACtD;;;ACxCO,IAAM,oBAAN,MAAwB;AAAA,EAC3B,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,WAAO,uCAAuC,QAAQ,SAAS,CAAC,MAAM;AAClE,YAAM,IAAK,KAAK,OAAO,IAAI,KAAM;AACjC,YAAM,IAAI,MAAM,MAAM,IAAK,IAAI,IAAO;AACtC,aAAO,EAAE,SAAS,EAAE;AAAA,IACxB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,GAAG,GAAG;AAEhB,QAAI,EAAE,WAAW,EAAE,QAAQ;AACvB,aAAO;AAAA,IACX;AACA,UAAM,MAAM,EAAE;AACd,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,gBAAU,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC;AAAA,IAC9C;AACA,WAAO,WAAW;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACZ,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,MAAM;AAChB,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AACxB,UAAM,IAAI,MAAM,uCAAuC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,SAAS;AAC3B,WAAO,IAAI,gBAAgB,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B;AACtB,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAIA,2BAA2B;AACvB,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAIA,2BAA2B,cAAc;AACrC,WAAO,IAAI,qBAAqB,YAAY;AAAA,EAChD;AAAA,EACA,8BAA8B;AAC1B,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAClE;AACJ;;;ACxFA,IAAM,eAAN,cAA2B,MAAM;AAAA,EAC7B,YAAY,WAAW,MAAM;AACzB,UAAM,SAAS;AACf,SAAK,OAAO;AAAA,EAChB;AACJ;AAEO,IAAM,gBAAN,MAAoB;AAAA,EACvB,cAAc;AACV,SAAK,cAAc,IAAI,YAAY;AACnC,SAAK,kBAAkB,oBAAI,IAAI;AAAA,EACnC;AAAA,EACA,GAAG,WAAW,UAAU;AACpB,UAAM,kBAAkB,CAAC,UAAU;AAC/B,eAAS,MAAM,IAAI;AAAA,IACvB;AACA,SAAK,gBAAgB,IAAI,UAAU,eAAe;AAClD,WAAO,KAAK,YAAY,iBAAiB,WAAW,eAAe;AAAA,EACvE;AAAA,EACA,eAAe,WAAW,UAAU;AAChC,UAAM,kBAAkB,KAAK,gBAAgB,IAAI,QAAQ;AACzD,SAAK,gBAAgB,OAAO,QAAQ;AACpC,WAAO,KAAK,YAAY,oBAAoB,WAAW,eAAe;AAAA,EAC1E;AAAA,EACA,KAAK,WAAW,UAAU;AACtB,UAAM,kBAAkB,CAAC,UAAU;AAC/B,eAAS,MAAM,IAAI;AAAA,IACvB;AACA,SAAK,gBAAgB,IAAI,UAAU,eAAe;AAClD,WAAO,KAAK,YAAY,iBAAiB,WAAW,iBAAiB;AAAA,MACjE,MAAM;AAAA,IACV,CAAC;AAAA,EACL;AAAA,EACA,KAAK,WAAW,MAAM;AAClB,WAAO,KAAK,YAAY,cAAc,IAAI,aAAa,WAAW,IAAI,CAAC;AAAA,EAC3E;AACJ;;;ACpCO,IAAM,uBAAN,cAAmC,kBAAkB;AAAA;AAAA,EAExD,WAAW;AACP,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAC/B;AAAA;AAAA,EAEA,gBAAgB;AACZ,WAAO,IAAI,cAAc;AAAA,EAC7B;AAAA;AAAA,EAEA,cAAc,MAAM;AAChB,QAAI,KAAK,KAAK,gBAAgB,gBAAgB;AAC1C,YAAM,IAAI,MAAM,mLAAmL;AAAA,IACvM;AACA,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAC/B;AAAA;AAAA,EAEA,uBAAuB;AACnB,UAAM,IAAI,MAAM,2HAA2H;AAAA,EAC/I;AAAA;AAAA,EAEA,0BAA0B;AACtB,WAAO,MAAM,sBAAsB;AAAA,EACvC;AAAA;AAAA,EAEA,2BAA2B;AACvB,UAAM,IAAI,MAAM,oIAAoI;AAAA,EACxJ;AAAA;AAAA,EAEA,8BAA8B;AAC1B,WAAO,KAAK,2BAA2B;AAAA,EAC3C;AACJ;;;ACrCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,IAAM,kBAAkB,CAAC,mBAAmB;AAC/C,UAAQ,eAAe,MAAM;AAAA,IACzB,KAAK;AACD,aAAO,IAAI,gBAAgB,cAAc;AAAA,IAC7C,KAAK;AACD,aAAO,IAAI,0BAA0B,cAAc;AAAA,IACvD,KAAK;AACD,aAAO,IAAI,eAAe,cAAc;AAAA,IAC5C,KAAK;AACD,aAAO,IAAI,0BAA0B,cAAc;AAAA,IACvD,KAAK;AACD,aAAO,IAAI,qBAAqB,cAAc;AAAA,IAClD,KAAK;AACD,aAAO,IAAI,uBAAuB,cAAc;AAAA,IACpD,KAAK;AACD,aAAO,IAAI,wBAAwB,cAAc;AAAA,IACrD;AACI,aAAO,IAAI,mBAAmB,cAAc;AAAA,EACpD;AACJ;AAEO,IAAM,kBAAkB,CAAC,mBAAmB;AAC/C,UAAQ,eAAe,MAAM;AAAA,IAEzB,KAAK;AACD,aAAO,IAAI,6BAA6B,cAAc;AAAA,EAE9D;AAGA,UAAQ,eAAe,MAAM;AAAA,IACzB,KAAK;AACD,aAAO,IAAI,0BAA0B,cAAc;AAAA,EAC3D;AACA,SAAO,gBAAgB,cAAc;AACzC;AAKO,IAAM,cAAN,cAA0B,MAAM;AAAA,EACnC,YAAY,MAAM,CAAC,GAAG,OAAO,MAAM;AAC/B,QAAI;AACJ,UAAM,IAAI,OAAO;AACjB,SAAK,OAAO,QAAQ,KAAK,YAAY;AACrC,SAAK,MAAM;AACX,SAAK,UAAU,IAAI;AACnB,SAAK,OAAO,IAAI;AAChB,SAAK,UAAU,IAAI;AACnB,SAAK,QAAQ,IAAI;AACjB,SAAK,SAAS,IAAI;AAClB,SAAK,UAAU,IAAI;AACnB,SAAK,YAAY,IAAI;AACrB,SAAK,aAAa,IAAI;AACtB,SAAK,WAAW,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,KAAK;AACnE,SAAK,cAAc,IAAI;AACvB,SAAK,SAAS,IAAI;AAClB,SAAK,eAAe,IAAI;AACxB,SAAK,iBAAiB,IAAI;AAC1B,SAAK,iBAAiB,IAAI;AAC1B,SAAK,sBAAsB,IAAI;AAC/B,SAAK,eAAe,IAAI;AACxB,SAAK,SAAS,IAAI;AAAA,EACtB;AACJ;AAIA,YAAY,WAAW;AAMhB,IAAM,kBAAN,cAA8B,YAAY;AAAA,EAC7C,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,iBAAiB;AAAA,EAChC;AACJ;AAKO,IAAM,4BAAN,cAAwC,YAAY;AAAA,EACvD,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,2BAA2B;AAAA,EAC1C;AACJ;AAOO,IAAM,iBAAN,cAA6B,YAAY;AAAA,EAC5C,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,gBAAgB;AAAA,EAC/B;AACJ;AAKO,IAAM,4BAAN,cAAwC,YAAY;AAAA,EACvD,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,2BAA2B;AAAA,EAC1C;AACJ;AAKO,IAAM,wBAAN,cAAoC,YAAY;AAAA,EACnD,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,uBAAuB;AAAA,EACtC;AACJ;AAMO,IAAM,uBAAN,cAAmC,YAAY;AAAA,EAClD,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,sBAAsB;AAAA,EACrC;AACJ;AAMO,IAAM,wBAAN,cAAoC,YAAY;AAAA,EACnD,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,uBAAuB;AAAA,EACtC;AACJ;AAKO,IAAM,mCAAN,cAA+C,YAAY;AAAA,EAC9D,YAAY,QAAQ,SAAS,MAAM,CAAC,GAAG;AACnC,UAAM,KAAK,kCAAkC;AAC7C,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AACJ;AAKO,IAAM,yBAAN,cAAqC,YAAY;AAAA,EACpD,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,wBAAwB;AAAA,EACvC;AACJ;AAOO,IAAM,0BAAN,cAAsC,YAAY;AAAA,EACrD,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,yBAAyB;AAAA,EACxC;AACJ;AAIO,IAAM,qBAAN,cAAiC,YAAY;AAAA,EAChD,YAAY,MAAM,CAAC,GAAG;AAClB,UAAM,KAAK,oBAAoB;AAAA,EACnC;AACJ;AAEO,IAAM,+BAAN,cAA2C,YAAY;AAAA,EAC1D,YAAY,iBAAiB,CAAC,GAAG;AAC7B,UAAM,gBAAgB,8BAA8B;AAAA,EACxD;AACJ;;;ACpLA,IAAM,uBAAuB;AACtB,IAAM,gBAAN,MAAM,eAAc;AAAA,EACvB,YAAY,QAAQ,0BAA0B;AAC1C,SAAK,UAAU;AACf,SAAK,4BAA4B;AAAA,EACrC;AAAA,EACA,4BAA4B,KAAK,SAAS;AAItC,QAAI,YAAY,QAAQ,YAAY;AACpC,QAAI,gBAAgB,IAAI,iBAAiB,QAAQ,gBAAgB;AACjE,QAAI,aAAa,IAAI,cAAc,QAAQ,gBAAgB;AAC3D,QAAI,iBAAiB,IAAI,kBAAkB,QAAQ,iBAAiB;AAAA,EACxE;AAAA,EACA,mBAAmB,cAAc,YAAY,SAAS;AAClD,UAAM,iBAAiB,KAAK,IAAI;AAChC,UAAM,oBAAoB,iBAAiB,aAAa;AACxD,WAAO,cAAc;AAAA,MACjB,aAAa,QAAQ,gBAAgB;AAAA,MACrC,SAAS,QAAQ,gBAAgB;AAAA,MACjC,iBAAiB,QAAQ,iBAAiB;AAAA,MAC1C,QAAQ,aAAa;AAAA,MACrB,MAAM,aAAa;AAAA,MACnB,QAAQ;AAAA,MACR,YAAY,KAAK,cAAc,OAAO;AAAA,MACtC,SAAS;AAAA,MACT,oBAAoB,aAAa;AAAA,MACjC,kBAAkB;AAAA,IACtB,CAAC;AAAA,EACL;AAAA,EACA,cAAc,SAAS;AACnB,WAAO,QAAQ,YAAY;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,0BAA0B,cAAc,OAAO,UAAU;AACrD,WAAO,CAAC,QAAQ;AACZ,YAAM,UAAU,IAAI,WAAW;AAC/B,YAAM,yBAAyB,MAAM;AACjC,cAAM,gBAAgB,KAAK,mBAAmB,cAAc,IAAI,cAAc,GAAG,OAAO;AACxF,aAAK,QAAQ,SAAS,KAAK,YAAY,aAAa;AACpD,aAAK,sBAAsB,KAAK,cAAc,OAAO,GAAG,cAAc,SAAS,KAAK;AAAA,MACxF;AACA,YAAM,SAAS,IAAI,SAAS,sBAAsB;AAIlD,WAAK,4BAA4B,QAAQ,OAAO;AAChD,aAAO,SAAS,MAAM,MAAM;AAAA,IAChC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,cAAc,SAAS,OAAO,UAAU;AACzD,WAAO,CAAC,QAAQ;AACZ,YAAM,UAAU,IAAI,WAAW;AAC/B,YAAM,YAAY,KAAK,cAAc,OAAO;AAC5C,YAAM,aAAa,IAAI,cAAc;AACrC,YAAM,gBAAgB,KAAK,mBAAmB,cAAc,YAAY,OAAO;AAC/E,WAAK,QAAQ,SAAS,KAAK,YAAY,aAAa;AACpD,UACK,OAAO,EACP,KAAK,CAAC,iBAAiB;AACxB,YAAI,aAAa,OAAO;AACpB,cAAI;AAGJ,cAAI,OAAO,aAAa,UAAU,UAAU;AACxC,yBAAa,QAAQ;AAAA,cACjB,MAAM,aAAa;AAAA,cACnB,SAAS,aAAa;AAAA,YAC1B;AAAA,UACJ;AACA,uBAAa,MAAM,UAAU;AAC7B,uBAAa,MAAM,aAAa;AAChC,uBAAa,MAAM,YAAY;AAC/B,cAAI,eAAe,KAAK;AACpB,kBAAM,IAAI,0BAA0B,aAAa,KAAK;AAAA,UAC1D,WACS,eAAe,KAAK;AACzB,kBAAM,IAAI,sBAAsB,aAAa,KAAK;AAAA,UACtD,WACS,eAAe,KAAK;AACzB,kBAAM,IAAI,qBAAqB,aAAa,KAAK;AAAA,UACrD,WACS,YAAY,MAAM;AACvB,kBAAM,gBAAgB,aAAa,KAAK;AAAA,UAC5C,OACK;AACD,kBAAM,gBAAgB,aAAa,KAAK;AAAA,UAC5C;AACA,gBAAM;AAAA,QACV;AACA,eAAO;AAAA,MACX,GAAG,CAAC,MAAM;AACN,cAAM,IAAI,eAAe;AAAA,UACrB,SAAS;AAAA,UACT,WAAW;AAAA,UACX,WAAW,QAAQ,YAAY;AAAA,QACnC,CAAC;AAAA,MACL,CAAC,EACI,KAAK,CAAC,iBAAiB;AACxB,aAAK,sBAAsB,WAAW,cAAc,SAAS,KAAK;AAElE,cAAM,cAAc,IAAI,eAAe;AACvC,aAAK,4BAA4B,aAAa,OAAO;AACrD,eAAO,eAAe,cAAc,gBAAgB;AAAA,UAChD,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,OAAO;AAAA,QACX,CAAC;AACD,iBAAS,MAAM,YAAY;AAAA,MAC/B,GAAG,CAAC,MAAM,SAAS,GAAG,IAAI,CAAC;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,OAAO,gCAAgC,gBAAgB;AACnD,WAAO,mDAAmD,iBAAiB,IAAI,wBAAwB,cAAc,YAAY,EAAE;AAAA,EACvI;AAAA;AAAA,EAEA,OAAO,aAAa,KAAK,YAAY,YAAY,OAAO;AACpD,QAAI,SACA,eAAe,KACf,WAAW,8BAA8B,SAAS,MAAM,IAAI,GAAG;AAC/D,aAAO;AAAA,IACX;AAEA,QAAI,cAAc,YAAY;AAC1B,aAAO;AAAA,IACX;AAEA,QAAI,CAAC,KAAK;AACN,aAAO;AAAA,IACX;AAGA,QAAI,IAAI,WAAW,EAAE,qBAAqB,MAAM,SAAS;AACrD,aAAO;AAAA,IACX;AACA,QAAI,IAAI,WAAW,EAAE,qBAAqB,MAAM,QAAQ;AACpD,aAAO;AAAA,IACX;AAEA,QAAI,IAAI,cAAc,MAAM,KAAK;AAC7B,aAAO;AAAA,IACX;AAMA,QAAI,IAAI,cAAc,KAAK,KAAK;AAC5B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,YAAY,aAAa,MAAM;AAC7C,UAAM,2BAA2B,KAAK,QAAQ,4BAA4B;AAC1E,UAAM,uBAAuB,KAAK,QAAQ,wBAAwB;AAIlE,QAAI,eAAe,KAAK,IAAI,2BAA2B,KAAK,IAAI,GAAG,aAAa,CAAC,GAAG,oBAAoB;AAGxG,oBAAgB,OAAO,IAAI,KAAK,OAAO;AAEvC,mBAAe,KAAK,IAAI,0BAA0B,YAAY;AAE9D,QAAI,OAAO,UAAU,UAAU,KAAK,cAAc,sBAAsB;AACpE,qBAAe,KAAK,IAAI,cAAc,UAAU;AAAA,IACpD;AACA,WAAO,eAAe;AAAA,EAC1B;AAAA;AAAA,EAEA,sBAAsB,WAAW,CAAC,GAAG;AACjC,WAAO,SAAS,sBAAsB,UAClC,OAAO,UAAU,SAAS,iBAAiB,IACzC,SAAS,oBACT,KAAK,QAAQ,qBAAqB;AAAA,EAC5C;AAAA,EACA,uBAAuB,QAAQ,UAAU,SAAS;AAE9C,UAAM,aAAa,KAAK,sBAAsB,QAAQ;AACtD,UAAM,SAAS,MAAM,qBAAqB,KAAK,QAAQ,mBAAmB,MAAM,CAAC;AAEjF,QAAI,YAAY,MAAM;AAClB,UAAI,WAAW,UAAU,WAAW,UAAU;AAC1C,eAAO,OAAO;AAAA,MAClB;AAAA,IACJ,WACS,YAAY,MAAM;AACvB,UAAI,WAAW,UAAU,aAAa,GAAG;AACrC,eAAO,OAAO;AAAA,MAClB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa,EAAE,aAAa,eAAe,YAAY,iBAAiB,QAAQ,qBAAqB,sBAAsB,eAAe,eAAe,QAAS,GAAG;AACjK,UAAM,iBAAiB;AAAA,MACnB,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,cAAc,KAAK,oBAAoB,OAAO;AAAA,MAC9C,8BAA8B;AAAA,MAC9B,6BAA6B,KAAK,oBAAoB;AAAA,MACtD,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,mBAAmB,KAAK,uBAAuB,QAAQ,sBAAsB,OAAO;AAAA,IACxF;AAaA,UAAM,mBAAmB,UAAU,UAAU,UAAU,SAAS,UAAU;AAK1E,QAAI,oBAAoB,eAAe;AACnC,UAAI,CAAC,kBAAkB;AACnB,oBAAY,GAAG,MAAM,6EAA6E;AAAA,MACtG;AACA,qBAAe,gBAAgB,IAAI;AAAA,IACvC;AACA,WAAO,OAAO;AAAA,MAAO,cAAc,cAAc;AAAA;AAAA,MAEjD,iBAAiB,mBAAmB;AAAA,IAAC;AAAA,EACzC;AAAA,EACA,oBAAoB,SAAS;AACzB,UAAM,iBAAiB,KAAK,QAAQ,YAAY,iBAAiB;AACjE,UAAM,UAAU,KAAK,QAAQ,WACvB,KAAK,QAAQ,mBAAmB,IAChC;AACN,WAAO,UAAU,OAAO,iBAAiB,cAAc,IAAI,OAAO,GAAG,KAAK;AAAA,EAC9E;AAAA,EACA,sBAAsB;AAClB,QAAI,KAAK,QAAQ,oBAAoB,KACjC,KAAK,QAAQ,oBAAoB,SAAS,GAAG;AAC7C,YAAM,UAAU,KAAK,QAAQ,oBAAoB,MAAM;AACvD,aAAO,KAAK,UAAU;AAAA,QAClB,sBAAsB;AAAA,MAC1B,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,sBAAsB,WAAW,mBAAmB,OAAO;AACvD,QAAI,KAAK,QAAQ,oBAAoB,KAAK,WAAW;AACjD,UAAI,KAAK,QAAQ,oBAAoB,SAAS,KAAK,2BAA2B;AAC1E,oBAAY,6DAA6D;AAAA,MAC7E,OACK;AACD,cAAM,IAAI;AAAA,UACN,YAAY;AAAA,UACZ,qBAAqB;AAAA,QACzB;AACA,YAAI,SAAS,MAAM,SAAS,GAAG;AAC3B,YAAE,QAAQ;AAAA,QACd;AACA,aAAK,QAAQ,oBAAoB,KAAK,CAAC;AAAA,MAC3C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,YAAY,QAAQ,MAAM,QAAQ,SAAS;AACvC,UAAM,iBAAiB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpD,UAAI;AACJ,UAAI;AACA,cAAM,gBAAgB,OAAO,YAAY;AACzC,YAAI,kBAAkB,UAClB,UACA,OAAO,KAAK,MAAM,EAAE,WAAW,GAAG;AAClC,gBAAM,IAAI,MAAM,qGAAqG;AAAA,QACzH;AACA,cAAM,OAAO,CAAC,EAAE,MAAM,KAAK,CAAC,QAAQ,OAAO,CAAC;AAE5C,cAAM,eAAe,gBAAgB,IAAI;AACzC,cAAM,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY;AAC3C,cAAM,oBAAoB,mBAAmB,IAAI;AACjD,cAAMC,WAAU,kBAAkB;AAElC,cAAMC,iBAAgB,kBAAkB;AACxC,eAAO;AAAA,UACH;AAAA,UACA,aAAa;AAAA,UACb,UAAU;AAAA,UACV,WAAW,CAAC;AAAA,UACZ,eAAAA;AAAA,UACA,SAAAD;AAAA,UACA,MAAM,kBAAkB;AAAA,UACxB,WAAW,CAAC,CAAC,kBAAkB;AAAA,UAC/B,UAAU,CAAC;AAAA,UACX,OAAO,CAAC,aAAa;AAAA,QACzB;AAAA,MACJ,SACO,KAAK;AACR,eAAO,GAAG;AACV;AAAA,MACJ;AACA,eAAS,gBAAgB,KAAK,UAAU;AACpC,YAAI,KAAK;AACL,iBAAO,GAAG;AAAA,QACd,OACK;AACD,kBAAQ,QAAQ;AAAA,QACpB;AAAA,MACJ;AACA,YAAM,EAAE,SAAS,SAAS,IAAI;AAE9B,YAAM,gBAAgB,KAAK;AAC3B,WAAK,SAAS,KAAK,eAAe,KAAK,MAAM,MAAM,KAAK,UAAU,eAAe,EAAE,SAAS,UAAU,WAAW,KAAK,UAAU,GAAG,KAAK,OAAO,eAAe;AAAA,IAClK,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,SAAS,QAAQ,MAAM,MAAM,MAAM,eAAe,SAAS,QAAQ,CAAC,GAAG,UAAU,uBAAuB,MAAM;AAC1G,QAAI;AACJ,QAAI;AACJ,qBAAiB,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,gBAAgB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AACzJ,UAAM,UAAU,WAAW,IAAI;AAC/B,UAAM,eAAe,CAAC,WAAW,YAAY,SAAS,gBAAgB,eAAe;AACjF,aAAO,WAAW,WAAW,KAAK,kBAAkB,gBAAgB,UAAU,GAAG,YAAY,SAAS,iBAAiB,CAAC;AAAA,IAC5H;AACA,UAAM,cAAc,CAAC,YAAY,SAAS,eAAe;AAErD,YAAM,UAAU,QAAQ,YACpB,QAAQ,SAAS,WACjB,OAAO,UAAU,QAAQ,SAAS,OAAO,KACzC,QAAQ,SAAS,WAAW,IAC1B,QAAQ,SAAS,UACjB,KAAK,QAAQ,YAAY,SAAS;AACxC,YAAM,UAAU;AAAA,QACZ,MAAM,QAAQ,KAAK,QAAQ,YAAY,MAAM;AAAA,QAC7C,MAAM,KAAK,QAAQ,YAAY,MAAM;AAAA,QACrC;AAAA,QACA;AAAA,QACA,SAAS,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,QAClC,MAAM;AAAA,QACN,UAAU,KAAK,QAAQ,YAAY,UAAU;AAAA,MACjD;AACA,oBAAc,OAAO,EAChB,KAAK,MAAM;AACZ,cAAM,MAAM,KAAK,QACZ,YAAY,YAAY,EACxB,YAAY,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,MAAM,QAAQ,UAAU,OAAO;AACnI,cAAM,mBAAmB,KAAK,IAAI;AAClC,cAAM,eAAe,cAAc;AAAA,UAC/B,aAAa;AAAA,UACb,SAAS,wBAAwB,QAAQ,gBAAgB,CAAC;AAAA,UAC1D,iBAAiB,wBAAwB,QAAQ,iBAAiB,CAAC;AAAA,UACnE;AAAA,UACA;AAAA,UACA,oBAAoB;AAAA,QACxB,CAAC;AACD,cAAM,iBAAiB,cAAc;AACrC,cAAM,aAAa,KAAK,sBAAsB,QAAQ,YAAY,CAAC,CAAC;AACpE,aAAK,QAAQ,SAAS,KAAK,WAAW,YAAY;AAClD,YACK,KAAK,CAAC,QAAQ;AACf,cAAI,eAAc,aAAa,KAAK,gBAAgB,UAAU,GAAG;AAC7D,mBAAO,aAAa,aAAa,YAAY,SAAS,gBAAgB,wBAAwB,IAAI,WAAW,EAAE,aAAa,CAAC,CAAC;AAAA,UAClI,WACS,QAAQ,aAAa,IAAI,cAAc,IAAI,KAAK;AACrD,mBAAO,KAAK,0BAA0B,cAAc,OAAO,QAAQ,EAAE,GAAG;AAAA,UAC5E,OACK;AACD,mBAAO,KAAK,qBAAqB,cAAc,SAAS,OAAO,QAAQ,EAAE,GAAG;AAAA,UAChF;AAAA,QACJ,CAAC,EACI,MAAM,CAAC,UAAU;AAClB,cAAI,eAAc,aAAa,MAAM,gBAAgB,YAAY,KAAK,GAAG;AACrE,mBAAO,aAAa,aAAa,YAAY,SAAS,gBAAgB,IAAI;AAAA,UAC9E,OACK;AACD,kBAAM,iBAAiB,MAAM,QAAQ,MAAM,SAAS,WAAW;AAC/D,mBAAO,SAAS,IAAI,sBAAsB;AAAA,cACtC,SAAS,iBACH,iDAAiD,OAAO,QACxD,eAAc,gCAAgC,cAAc;AAAA,cAClE,QAAQ;AAAA,YACZ,CAAC,CAAC;AAAA,UACN;AAAA,QACJ,CAAC;AAAA,MACL,CAAC,EACI,MAAM,CAAC,MAAM;AACd,cAAM,IAAI,YAAY;AAAA,UAClB,SAAS;AAAA,UACT,WAAW;AAAA,QACf,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,UAAM,wBAAwB,CAAC,OAAOE,UAAS;AAC3C,UAAI,OAAO;AACP,eAAO,SAAS,KAAK;AAAA,MACzB;AACA,oBAAcA;AACd,WAAK,QAAQ,mBAAmB,CAAC,oBAAoB;AACjD,cAAM,aAAa,KAAK,QAAQ,YAAY,SAAS;AACrD,cAAM,UAAU,KAAK,aAAa;AAAA,UAC9B,aAAa,WAAW,OAClB,qBACA;AAAA,UACN,eAAe,YAAY;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,UACA,qBAAqB,QAAQ;AAAA,UAC7B,sBAAsB,QAAQ;AAAA,UAC9B,eAAe,WAAW,OAAO,OAAO,KAAK,QAAQ,YAAY,eAAe;AAAA,UAChF,eAAe,WAAW,OAAO,KAAK,QAAQ,YAAY,eAAe,IAAI;AAAA,UAC7E;AAAA,QACJ,CAAC;AACD,oBAAY,YAAY,SAAS,CAAC;AAAA,MACtC,CAAC;AAAA,IACL;AACA,QAAI,sBAAsB;AACtB,2BAAqB,QAAQ,MAAM,QAAQ,SAAS,qBAAqB;AAAA,IAC7E,OACK;AACD,UAAI;AACJ,UAAI,WAAW,MAAM;AACjB,0BAAkB,OAAO,yBAAyB,IAAI,IAAI;AAAA,MAC9D,OACK;AACD,0BAAkB,0BAA0B,QAAQ,CAAC,GAAG,OAAO;AAAA,MACnE;AACA,4BAAsB,MAAM,eAAe;AAAA,IAC/C;AAAA,EACJ;AACJ;;;AC/bA,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,kBAAkB,aAAa,MAAM,gBAAgB;AAC7D,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,eAAe,EAAE,gBAAgB,KAAK;AAC3C,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,MAAM,QAAQ,YAAY;AACtB,QAAI,EAAE,cACF,WAAW,QACX,OAAO,WAAW,KAAK,WAAW,WAAW;AAC7C,YAAM,MAAM,2EAA2E;AAAA,IAC3F;AACA,UAAM,mBAAmB,mBAAmB,KAAK,WAAW;AAC5D,QAAI,KAAK,QAAQ,WAAW,KAAK,QAAQ;AACrC,YAAM,MAAM,mBACN,WAAW,KAAK,SAAS,IAAI,KAAK,QAClC,KAAK;AACX,YAAM,QAAQ,WAAW,KAAK,GAAG;AACjC,WAAK,SAAS;AACd,aAAO,EAAE,OAAO,MAAM,MAAM;AAAA,IAChC,WACS,WAAW,UAAU;AAE1B,WAAK,QAAQ;AACb,WAAK,cAAc,KAAK,YAAY,UAAU;AAC9C,YAAM,iBAAiB,MAAM,KAAK;AAClC,aAAO,KAAK,QAAQ,cAAc;AAAA,IACtC;AACA,WAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,EAC1C;AAAA;AAAA,EAEA,YAAY,aAAa;AACrB,UAAM,IAAI,MAAM,eAAe;AAAA,EACnC;AAAA,EACA,MAAM,QAAQ;AACV,WAAO,KAAK,QAAQ,MAAM,KAAK,WAAW;AAAA,EAC9C;AAAA,EACA,OAAO;AAMH,QAAI,KAAK,aAAa,gBAAgB;AAClC,aAAO,KAAK,aAAa;AAAA,IAC7B;AACA,UAAM,eAAe,YAAY;AAC7B,YAAM,MAAM,MAAM,KAAK,MAAM;AAC7B,WAAK,aAAa,iBAAiB;AACnC,aAAO;AAAA,IACX,GAAG;AACH,SAAK,aAAa,iBAAiB;AACnC,WAAO;AAAA,EACX;AACJ;AACA,IAAM,iBAAN,cAA6B,WAAW;AAAA,EACpC,YAAY,YAAY;AACpB,UAAM,mBAAmB,mBAAmB,KAAK,WAAW;AAC5D,UAAM,SAAS,UAAU,YAAY,gBAAgB;AACrD,WAAO,KAAK,eAAe,aAAa,KAAK,aAAa,KAAK,MAAM;AAAA,MACjE,CAAC,mBAAmB,kBAAkB,gBAAgB,GAAG;AAAA,IAC7D,CAAC;AAAA,EACL;AACJ;AACA,IAAM,mBAAN,cAA+B,WAAW;AAAA,EACtC,YAAY,YAAY;AACpB,QAAI,CAAC,WAAW,WAAW;AACvB,YAAM,MAAM,yGAAyG;AAAA,IACzH;AACA,WAAO,KAAK,eAAe,aAAa,KAAK,aAAa,KAAK,MAAM;AAAA,MACjE,MAAM,WAAW;AAAA,IACrB,CAAC;AAAA,EACL;AACJ;AACA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,kBAAkB,aAAa,MAAM,gBAAgB;AAC7D,SAAK,uBAAuB,YAAY;AACpC,YAAM,OAAO,MAAM;AACnB,aAAO,KAAK,KAAK,OAAO,QAAQ,EAAE;AAAA,IACtC,GAAG;AACH,SAAK,eAAe,YAAY;AAC5B,YAAM,OAAO,MAAM;AACnB,aAAO,KAAK,iBAAiB;AAAA,IACjC,GAAG;AACH,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,MAAM,WAAW;AACb,UAAM,cAAc,MAAM,KAAK;AAC/B,QAAI,CAAC;AACD,aAAO;AACX,SAAK,KAAK,WAAW;AACrB,UAAM,OAAO,MAAM,KAAK,eAAe,aAAa,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC;AACrE,SAAK,cAAc,QAAQ,QAAQ,KAAK,aAAa;AACrD,SAAK,sBAAsB,QAAQ,QAAQ,KAAK,KAAK,OAAO,QAAQ,EAAE,CAAC;AACvE,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,MAAM,OAAO;AACT;AACI,YAAMC,WAAU,MAAM,KAAK,qBAAqB,KAAK;AACrD,UAAI,CAACA,QAAO;AACR,eAAO,EAAE,MAAM,OAAO,OAAOA,QAAO,MAAM;AAAA,IAClD;AACA,UAAM,mBAAmB,MAAM,KAAK,SAAS;AAC7C,QAAI,CAAC,kBAAkB;AACnB,aAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,IAC1C;AACA,UAAM,SAAS,iBAAiB,KAAK;AACrC,QAAI,CAAC,OAAO;AACR,aAAO,EAAE,MAAM,OAAO,OAAO,OAAO,MAAM;AAC9C,WAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,EAC1C;AACJ;AACO,IAAM,4BAA4B,CAAC,gBAAgB,aAAa,MAAM,qBAAqB;AAC9F,QAAM,UAAU,WAAW,KAAK,YAAY,KAAK,IAAI;AACrD,MAAI,YAAY,QAAQ,KAAK,eAAe,UAAU;AAClD,WAAO,sCAAsC,IAAI,iBAAiB,kBAAkB,aAAa,MAAM,cAAc,CAAC;AAAA,EAC1H;AACA,MAAI,YAAY,QAAQ,KAAK,eAAe,QAAQ;AAChD,WAAO,sCAAsC,IAAI,eAAe,kBAAkB,aAAa,MAAM,cAAc,CAAC;AAAA,EACxH;AACA,MAAI,YAAY,QAAQ,KAAK,eAAe,QAAQ;AAChD,WAAO,sCAAsC,IAAI,eAAe,kBAAkB,aAAa,MAAM,cAAc,CAAC;AAAA,EACxH;AACA,SAAO;AACX;AACA,IAAM,wCAAwC,CAAC,aAAa;AACxD,QAAM,iBAAiB,mBAAmB,IAAI,SAAS,SAAS,KAAK,GAAG,IAAI,CAAC;AAC7E,QAAM,oBAAoB,sBAAsB,cAAc;AAC9D,QAAM,wBAAwB;AAAA,IAC1B;AAAA,IACA;AAAA;AAAA,IAEA,MAAM,MAAM,SAAS,KAAK;AAAA,IAC1B,QAAQ,MAAM;AAEV,aAAO,CAAC;AAAA,IACZ;AAAA,IACA,CAAC,uBAAuB,CAAC,GAAG,MAAM;AAC9B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAMA,SAAS,yBAAyB;AAC9B,MAAI,OAAO,WAAW,eAAe,OAAO,eAAe;AACvD,WAAO,OAAO;AAAA,EAClB;AAEA,SAAO;AACX;AACA,SAAS,gBAAgB,MAAM;AAC3B,MAAI,KAAK,SAAS,GAAG;AACjB,WAAO;AAAA,EACX;AACA,QAAM,SAAS,KAAK,CAAC;AACrB,MAAI,OAAO,WAAW,YAAY;AAC9B,UAAM,MAAM,4FAA4F,OAAO,MAAM,EAAE;AAAA,EAC3H;AACA,SAAO;AACX;AAYA,SAAS,gBAAgB,MAAM;AAC3B,MAAI,KAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACX;AACA,QAAM,SAAS,KAAK,CAAC;AACrB,MAAI,OAAO,WAAW,YAAY;AAC9B,UAAM,MAAM,2FAA2F,OAAO,MAAM,EAAE;AAAA,EAC1H;AAEA,MAAI,OAAO,WAAW,GAAG;AACrB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,SAAS,GAAG;AACnB,UAAM,MAAM,oGAAoG,MAAM,EAAE;AAAA,EAC5H;AAKA,SAAO,SAAS,QAAQ,MAAM,MAAM;AAChC,UAAM,iBAAiB,OAAO,IAAI;AAClC,SAAK,cAAc;AAAA,EACvB;AACJ;AACA,SAAS,UAAU,YAAY,kBAAkB;AAC7C,QAAM,UAAU,mBAAmB,IAAI,WAAW,KAAK,SAAS;AAChE,QAAM,WAAW,WAAW,KAAK,OAAO;AACxC,QAAM,SAAS,YAAY,SAAS;AACpC,MAAI,CAAC,QAAQ;AACT,UAAM,MAAM,sEAAsE;AAAA,EACtF;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,mBAAmB;AAC3C,SAAO,SAAS,iBAAwC;AACpD,UAAM,OAAO,CAAC,EAAE,MAAM,KAAK,SAAS;AACpC,UAAM,SAAS,gBAAgB,IAAI;AACnC,UAAM,SAAS,gBAAgB,IAAI;AACnC,QAAI,KAAK,SAAS,GAAG;AACjB,YAAM,MAAM,sDAAsD,IAAI,EAAE;AAAA,IAC5E;AACA,UAAM,kBAAkB;AAAA,MAA8B;AAAA;AAAA,MAEtD;AAAA,IAAM;AACN,WAAO,8BAA8B,iBAAiB,MAAM;AAAA,EAChE;AACJ;AACA,SAAS,sBAAsB,gBAAgB;AAC3C,SAAO,SAAS,kBAAkB,MAAM,QAAQ;AAC5C,UAAM,QAAQ,QAAQ,KAAK;AAC3B,QAAI,CAAC,OAAO;AACR,YAAM,MAAM,iGAAiG;AAAA,IACjH;AACA,QAAI,QAAQ,KAAO;AACf,YAAM,MAAM,6IAA6I;AAAA,IAC7J;AACA,UAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7C,YAAM,QAAQ,CAAC;AACf,qBAAe,CAAC,SAAS;AACrB,cAAM,KAAK,IAAI;AACf,YAAI,MAAM,UAAU,OAAO;AACvB,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC,EACI,KAAK,MAAM;AACZ,gBAAQ,KAAK;AAAA,MACjB,CAAC,EACI,MAAM,MAAM;AAAA,IACrB,CAAC;AAED,WAAO,8BAA8B,SAAS,MAAM;AAAA,EACxD;AACJ;AACA,SAAS,8BAA8B,mBAAmB,QAAQ;AAC9D,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,aAAS,gBAAgB,YAAY;AACjC,UAAI,WAAW,MAAM;AACjB,gBAAQ;AACR;AAAA,MACJ;AACA,YAAM,OAAO,WAAW;AACxB,aAAO,IAAI,QAAQ,CAAC,SAAS;AAIzB,eAAO,MAAM,IAAI;AAAA,MACrB,CAAC,EAAE,KAAK,CAAC,mBAAmB;AACxB,YAAI,mBAAmB,OAAO;AAC1B,iBAAO,gBAAgB,EAAE,MAAM,MAAM,OAAO,OAAU,CAAC;AAAA,QAC3D,OACK;AACD,iBAAO,kBAAkB,EAAE,KAAK,eAAe;AAAA,QACnD;AAAA,MACJ,CAAC;AAAA,IACL;AACA,sBAAkB,EACb,KAAK,eAAe,EACpB,MAAM,MAAM;AAAA,EACrB,CAAC;AACL;AACA,SAAS,mBAAmB,aAAa;AACrC,QAAM,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW;AACtC,QAAM,eAAe,gBAAgB,IAAI;AACzC,SAAO,CAAC,CAAC,aAAa;AAC1B;;;AC1QO,SAAS,aAAa,MAAM;AAC/B,MAAI,KAAK,SAAS,UAAa,KAAK,aAAa,QAAW;AACxD,UAAM,IAAI,MAAM,wCAAwC,KAAK,IAAI,uBAAuB,KAAK,QAAQ,IAAI;AAAA,EAC7G;AACA,SAAO,YAAa,MAAM;AACtB,UAAM,WAAW,OAAO,KAAK,KAAK,SAAS,CAAC,KAAK,cAAc,KAAK,IAAI;AACxE,SAAK,YAAY,iBAAiB,KAAK,YAAY,KAAK,8BAA8B,KAAK,QAAQ,EAAE,CAAC;AACtG,UAAM,iBAAiB,8BAA8B,KAAK,aAAa,MAAM,MAAM,CAAC,CAAC,GAAG,QAAQ;AAChG,WAAO,OAAO,gBAAgB,0BAA0B,MAAM,MAAM,MAAM,cAAc,CAAC;AACzF,WAAO;AAAA,EACX;AACJ;;;AC5BA,eAAe,SAAS;AAExB,eAAe,SAAS;AACxB,eAAe,+BAA+B;AAI9C,SAAS,eAAe,QAAQ,mBAAmB;AAC/C,OAAK,UAAU;AACf,MAAI,mBAAmB;AACnB,UAAM,IAAI,MAAM,0FAA0F;AAAA,EAC9G;AACA,OAAK,WAAW;AAAA;AAAA,IAEhB,KAAK,YAAY,OAAO,YAAY,UAAU;AAAA,EAAC;AAE/C,OAAK,eAAe,KAAK;AAEzB,OAAK,OAAO,oBAAoB,KAAK,IAAI;AACzC,OAAK,WAAW,GAAG,SAAS;AAChC;AACA,eAAe,YAAY;AAAA,EACvB,SAAS;AAAA;AAAA,EAET,MAAM;AAAA,EACN,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA,EACV,aAAa;AAAA,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,sBAAsB;AAAA;AAAA;AAAA,EAGtB,iBAAiB;AAAA,EACjB,eAAe,aAAa,SAAS;AACjC,UAAM,WAAW,CAAC,KAAK,SAAS,OAAO,GAAG,KAAK,KAAK,OAAO,CAAC;AAC5D,QAAI,OAAO,gBAAgB,YAAY;AACnC,YAAM,sBAAsB,YAAY,OAAO;AAI/C,UAAI,qBAAqB;AACrB,iBAAS,KAAK,mBAAmB;AAAA,MACrC;AAAA,IACJ,OACK;AACD,eAAS,KAAK,WAAW;AAAA,IAC7B;AACA,WAAO,KAAK,cAAc,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,8BAA8B,iBAAiB;AAG3C,QAAI,iBAAiB;AACjB,aAAO,IAAI,KAAK,cAAc,CAAC,KAAK,cAAc,eAAe,CAAC,CAAC;AAAA,IACvE,OACK;AACD,aAAO,IAAI,KAAK,YAAY;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,cAAc,OAAO;AAKjB,WAAO,MAAM,KAAK,GAAG,EAAE,QAAQ,WAAW,GAAG;AAAA,EACjD;AAAA,EACA,gBAAgB,aAAa,MAAM,cAAc;AAC7C,QAAI;AAEJ,UAAM,iBAAiB,KAAK,UAAU,OAAO,YAAY;AACzD,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,UAAM,YAAY,KAAK,aAAa,CAAC;AACrC,UAAM,SAAS,KAAK,WAAW,CAACC,UAASA;AACzC,UAAM,kBAAkB,CAAC,CAAC,KAAK;AAC/B,UAAM,cAAc,oBAAoB,kBAAkB,KAAK,WAAW,KAAK,QAAQ,EAAE;AAGzF,UAAM,OAAO,kBACP,KAAK,WACL,KAAK,8BAA8B,KAAK,IAAI;AAElD,UAAM,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW;AAEtC,UAAM,UAAU,UAAU,OAAO,CAACC,UAAS,UAAU;AACjD,YAAM,MAAM,KAAK,MAAM;AACvB,UAAI,OAAO,QAAQ,UAAU;AACzB,cAAM,IAAI,MAAM,qBAAqB,KAAK,gCAAgC,GAAG,yBAAyB,aAAa,IAAI,IAAI,KAAK;AAAA,MACpI;AACA,MAAAA,SAAQ,KAAK,IAAI;AACjB,aAAOA;AAAA,IACX,GAAG,CAAC,CAAC;AAEL,UAAM,eAAe,gBAAgB,IAAI;AACzC,UAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,YAAY,CAAC;AACjE,UAAM,UAAU,mBAAmB,IAAI;AACvC,UAAM,OAAO,QAAQ,QAAQ,KAAK;AAClC,UAAM,YAAY,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,QAAQ;AAEhD,QAAI,KAAK,OAAO,CAAC,MAAM,KAAK,IAAI,EAAE,QAAQ;AACtC,YAAM,IAAI,MAAM,8BAA8B,IAAI,iIAAiI,aAAa,MAAM,IAAI,KAAK;AAAA,IACnN;AAGA,UAAM,cAAc,kBACd,YAAY,OAAO,IACnB,KAAK,eAAe,aAAa,OAAO;AAC9C,UAAM,UAAU,OAAO,OAAO,QAAQ,SAAS,KAAK,OAAO;AAC3D,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,MAAM,EAAE,QAAQ,CAAC;AAAA,IACpC;AACA,UAAM,cAAc,KAAK,WAAW,SAAS,KAAK,WAAW;AAC7D,UAAM,WAAW,cAAc,OAAO;AACtC,UAAM,YAAY,cAAc,OAAO,CAAC;AACxC,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB,KAAK,QAAQ,mBAAmB,QAAQ,OAAO,SAAS,KAAK;AAAA,MAC7E;AAAA,MACA,MAAM,SAAS,QAAQ,SAAS,SAAS,OAAO;AAAA,MAChD;AAAA,MACA,UAAU,QAAQ;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,aAAa,aAAa,MAAM,cAAc;AAC1C,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAI;AACJ,UAAI;AACJ,UAAI;AACA,eAAO,KAAK,gBAAgB,aAAa,MAAM,YAAY;AAAA,MAC/D,SACO,KAAK;AACR,eAAO,GAAG;AACV;AAAA,MACJ;AACA,eAAS,gBAAgB,KAAK,UAAU;AACpC,YAAI,KAAK;AACL,iBAAO,GAAG;AAAA,QACd,OACK;AACD,kBAAQ,KAAK,wBACP,KAAK,sBAAsB,QAAQ,IACnC,QAAQ;AAAA,QAClB;AAAA,MACJ;AACA,YAAM,aAAa,OAAO,KAAK,KAAK,SAAS,EAAE,WAAW;AAC1D,YAAM,OAAO;AAAA,QACT,KAAK;AAAA,QACL,aAAa,KAAK;AAAA,QAClB,0BAA0B,KAAK,WAAW,WAAW,KAAK,WAAW,CAAC;AAAA,MAC1E,EAAE,KAAK,EAAE;AACT,YAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,WAAK,QAAQ,eAAe,SAAS,KAAK,eAAe,KAAK,MAAM,MAAM,KAAK,UAAU,KAAK,eAAe;AAAA,QACzG;AAAA,QACA;AAAA,QACA,WAAW,KAAK;AAAA,MACpB,GAAG,KAAK,OAAO,kBAAkB,KAAK,KAAK,0BAA0B,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,CAAC;AAAA,IACvH,CAAC;AAAA,EACL;AACJ;;;ACzKO,SAAS,eAAe,mBAAmB;AAC9C,QAAM,UAAU;AAAA,IACZ,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe,SAAS,QAAQ,QAAQ,WAAW,gBAAgB,YAAY;AAC3E,UAAI;AACA,YAAI,CAAC,KAAK,WAAW;AACjB,gBAAM,IAAI,MAAM,iDAAiD;AAAA,QACrE;AACA,aAAK,UAAU,aAAa,SAAS,QAAQ,QAAQ,aAAa,QAAQ,mBAAmB,gBAAgB,UAAU;AAAA,MAC3H,SACO,GAAG;AACN,YAAI,aAAa,sCAAsC;AACnD,YAAE,WACE;AAAA,QACR;AACA,cAAM;AAAA,MACV;AACA,YAAM,cAAc,mBAAmB,aACjC,KAAK,MAAM,IAAI,YAAY,MAAM,EAAE,OAAO,OAAO,CAAC,IAClD,KAAK,MAAM,OAAO;AACxB,aAAO;AAAA,IACX;AAAA,IACA,MAAM,oBAAoB,SAAS,QAAQ,QAAQ,WAAW,gBAAgB,YAAY;AACtF,UAAI,CAAC,KAAK,WAAW;AACjB,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACrE;AACA,YAAM,KAAK,UAAU,kBAAkB,SAAS,QAAQ,QAAQ,aAAa,QAAQ,mBAAmB,gBAAgB,UAAU;AAClI,YAAM,cAAc,mBAAmB,aACjC,KAAK,MAAM,IAAI,YAAY,MAAM,EAAE,OAAO,OAAO,CAAC,IAClD,KAAK,MAAM,OAAO;AACxB,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,0BAA0B,SAAU,MAAM;AACtC,YAAM,eAAe,eAAe,IAAI;AACxC,YAAMC,aAAY,aAAa,aAC3B,aAAa,eAAe,qBAAqB,aAAa,eAAe,aAAa,MAAM;AACpG,aAAO,aAAa,qBAAqBA,UAAS;AAAA,IACtD;AAAA,IACA,+BAA+B,eAAgB,MAAM;AACjD,YAAM,eAAe,eAAe,IAAI;AACxC,YAAMA,aAAY,aAAa,aAC1B,MAAM,aAAa,eAAe,0BAA0B,aAAa,eAAe,aAAa,MAAM;AAChH,aAAO,aAAa,qBAAqBA,UAAS;AAAA,IACtD;AAAA,EACJ;AACA,QAAM,YAAY;AAAA,IACd,iBAAiB;AAAA,IACjB,aAAa,gBAAgB,eAAe,QAAQ,WAAW,gBAAgB,YAAY;AACvF,YAAM,EAAE,eAAe,QAAQ,gBAAgB,SAAS,SAAS,mBAAoB,IAAI,kBAAkB,gBAAgB,eAAe,KAAK,eAAe;AAC9J,YAAM,2BAA2B,KAAK,KAAK,MAAM;AACjD,uBAAiB,kBAAkB,kBAAkB;AACrD,YAAM,oBAAoB,eAAe,qBAAqB,gBAAgB,SAAS,OAAO,GAAG,MAAM;AACvG,gCAA0B,SAAS,QAAQ,SAAS,mBAAmB,WAAW,oBAAoB,0BAA0B,UAAU;AAC1I,aAAO;AAAA,IACX;AAAA,IACA,MAAM,kBAAkB,gBAAgB,eAAe,QAAQ,WAAW,gBAAgB,YAAY;AAClG,YAAM,EAAE,eAAe,QAAQ,gBAAgB,SAAS,SAAS,mBAAoB,IAAI,kBAAkB,gBAAgB,eAAe,KAAK,eAAe;AAC9J,YAAM,2BAA2B,KAAK,KAAK,MAAM;AACjD,uBAAiB,kBAAkB,kBAAkB;AACrD,YAAM,oBAAoB,MAAM,eAAe,0BAA0B,gBAAgB,SAAS,OAAO,GAAG,MAAM;AAClH,aAAO,0BAA0B,SAAS,QAAQ,SAAS,mBAAmB,WAAW,oBAAoB,0BAA0B,UAAU;AAAA,IACrJ;AAAA,EACJ;AACA,WAAS,gBAAgB,SAAS,SAAS;AACvC,WAAO,GAAG,QAAQ,SAAS,IAAI,OAAO;AAAA,EAC1C;AACA,WAAS,kBAAkB,gBAAgB,eAAe,gBAAgB;AACtE,QAAI,CAAC,gBAAgB;AACjB,YAAM,IAAI,iCAAiC,eAAe,gBAAgB;AAAA,QACtE,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,UAAM,qBAAqB,OAAO,kBAAkB,YAChD,EAAE,0BAA0B;AAChC,UAAM,cAAc,IAAI,YAAY,MAAM;AAC1C,UAAM,iBAAiB,0BAA0B,aAC3C,YAAY,OAAO,cAAc,IACjC;AAKN,QAAI,MAAM,QAAQ,aAAa,GAAG;AAC9B,YAAM,IAAI,MAAM,4GAA4G;AAAA,IAChI;AACA,QAAI,iBAAiB,QAAQ,iBAAiB,IAAI;AAC9C,YAAM,IAAI,iCAAiC,eAAe,gBAAgB;AAAA,QACtE,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,UAAM,gBAAgB,yBAAyB,aACzC,YAAY,OAAO,aAAa,IAChC;AACN,UAAM,UAAU,YAAY,eAAe,cAAc;AACzD,QAAI,CAAC,WAAW,QAAQ,cAAc,IAAI;AACtC,YAAM,IAAI,iCAAiC,eAAe,gBAAgB;AAAA,QACtE,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,QAAI,CAAC,QAAQ,WAAW,QAAQ;AAC5B,YAAM,IAAI,iCAAiC,eAAe,gBAAgB;AAAA,QACtE,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,0BAA0B,SAAS,QAAQ,SAAS,mBAAmB,WAAW,oBAAoB,0BAA0B,YAAY;AACjJ,UAAM,iBAAiB,CAAC,CAAC,QAAQ,WAAW,OAAO,kBAAkB,cAAc,KAAK,mBAAmB,iBAAiB,CAAC,EAAE;AAC/H,UAAM,eAAe;AAErB,UAAM,oBAAoB,2BACpB,8HACA;AACN,QAAI,CAAC,gBAAgB;AACjB,UAAI,oBAAoB;AACpB,cAAM,IAAI,iCAAiC,QAAQ,SAAS;AAAA,UACxD,SAAS,qSAGL,eACA,OACA;AAAA,QACR,CAAC;AAAA,MACL;AACA,YAAM,IAAI,iCAAiC,QAAQ,SAAS;AAAA,QACxD,SAAS,wSAIL,eACA,OACA;AAAA,MACR,CAAC;AAAA,IACL;AACA,UAAM,eAAe,KAAK,OAAO,OAAO,eAAe,WAAW,aAAa,KAAK,IAAI,KAAK,GAAI,IAAI,QAAQ;AAC7G,QAAI,YAAY,KAAK,eAAe,WAAW;AAC3C,YAAM,IAAI,iCAAiC,QAAQ,SAAS;AAAA,QACxD,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AACA,WAAS,YAAY,QAAQ,QAAQ;AACjC,QAAI,OAAO,WAAW,UAAU;AAC5B,aAAO;AAAA,IACX;AACA,WAAO,OAAO,MAAM,GAAG,EAAE,OAAO,CAAC,OAAO,SAAS;AAC7C,YAAM,KAAK,KAAK,MAAM,GAAG;AACzB,UAAI,GAAG,CAAC,MAAM,KAAK;AACf,cAAM,YAAY,SAAS,GAAG,CAAC,GAAG,EAAE;AAAA,MACxC;AACA,UAAI,GAAG,CAAC,MAAM,QAAQ;AAClB,cAAM,WAAW,KAAK,GAAG,CAAC,CAAC;AAAA,MAC/B;AACA,aAAO;AAAA,IACX,GAAG;AAAA,MACC,WAAW;AAAA,MACX,YAAY,CAAC;AAAA,IACjB,CAAC;AAAA,EACL;AACA,MAAI,iCAAiC;AAKrC,WAAS,oBAAoB;AACzB,QAAI,CAAC,gCAAgC;AACjC,uCAAiC,kBAAkB,4BAA4B;AAAA,IACnF;AACA,WAAO;AAAA,EACX;AACA,WAAS,eAAe,MAAM;AAC1B,QAAI,CAAC,MAAM;AACP,YAAM,IAAI,YAAY;AAAA,QAClB,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,UAAM,YAAY,KAAK,MAAM,KAAK,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAC5E,UAAM,SAAS,KAAK,UAAU,UAAU;AACxC,UAAM,iBAAiB,KAAK,kBAAkB,kBAAkB;AAChE,UAAM,gBAAgB,GAAG,SAAS,IAAI,KAAK,OAAO;AAClD,UAAM,uBAAuB,CAACA,eAAc;AACxC,aAAO,KAAK,SAAS,IAAI,MAAM,IAAIA,UAAS;AAAA,IAChD;AACA,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,MAAE;AAAA,MAC5C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAqB,CAAC;AAAA,EAC9B;AACA,UAAQ,YAAY;AACpB,SAAO;AACX;;;AClNO,IAAM,aAAa;;;ACD1B;AAAA;AAAA,iBAAAC;AAAA,EAAA;AAAA;AAAA,kBAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,mBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,iBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,SAAS,kBAAkB,QAAQ,WAAW;AAC1C,aAAW,QAAQ,WAAW;AAC1B,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,WAAW,IAAI,GAAG;AACxD;AAAA,IACJ;AACA,UAAM,gBAAgB,KAAK,CAAC,EAAE,YAAY,IAAI,KAAK,UAAU,CAAC;AAC9D,UAAM,WAAW,IAAI,UAAU,IAAI,EAAE,MAAM;AAC3C,SAAK,aAAa,IAAI;AAAA,EAC1B;AACJ;AACO,SAAS,kBAAkB,WAAW,WAAW;AACpD,SAAO,SAAU,QAAQ;AACrB,WAAO,IAAI,kBAAkB,QAAQ,SAAS;AAAA,EAClD;AACJ;;;ACdA,IAAMC,gBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,UAAUA,cAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,cAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,YAAYA,cAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,YAAYA,cAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,SAASA,cAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,WAAWA,cAAa;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,aAAaA,cAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AChCD,IAAMC,gBAAe,eAAe;AAC7B,IAAM,qBAAqB,eAAe,OAAO;AAAA,EACpD,UAAUA,cAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,cAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,gBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,QAAQA,cAAa,EAAE,QAAQ,QAAQ,UAAU,qBAAqB,CAAC;AAAA,EACvE,UAAUA,cAAa,EAAE,QAAQ,OAAO,UAAU,0BAA0B,CAAC;AAAA,EAC7E,MAAMA,cAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,UAAUA,cAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,cAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,YAAYA,cAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACrBD,IAAMC,gBAAe,eAAe;AAC7B,IAAM,iBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQA,cAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,cAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,cAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,cAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,WAAWA,cAAa;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,cAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,cAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC9BD,IAAMC,gBAAe,eAAe;AAC7B,IAAMC,kBAAiB,eAAe,OAAO;AAAA,EAChD,UAAUD,cAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,cAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,cAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,SAASA,cAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,cAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACvBD,IAAME,gBAAe,eAAe;AAC7B,IAAM,eAAe,eAAe,OAAO;AAAA,EAC9C,QAAQA,cAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAAA,EACzE,UAAUA,cAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,cAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACZD,IAAMC,gBAAe,eAAe;AAC7B,IAAM,cAAc,eAAe,OAAO;AAAA,EAC7C,QAAQA,cAAa,EAAE,QAAQ,QAAQ,UAAU,0BAA0B,CAAC;AAAA,EAC5E,UAAUA,cAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,cAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,cAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;AChBD,IAAMC,gBAAe,eAAe;AAC7B,IAAM,QAAQ,eAAe,OAAO;AAAA,EACvC,aAAaA,cAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,cAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,YAAYA,cAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,cAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,YAAYA,cAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACtBD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,SAAQ,eAAe,OAAO;AAAA,EACvC,QAAQD,eAAa,EAAE,QAAQ,QAAQ,UAAU,oBAAoB,CAAC;AAAA,EACtE,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,2BAA2B,CAAC;AAAA,EAC9E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,2BAA2B,CAAC;AAAA,EAC7E,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACVD,IAAME,iBAAe,eAAe;AAC7B,IAAM,iBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACnBD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,kBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQD,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACvBD,IAAME,iBAAe,eAAe;AAC7B,IAAM,qBAAqB,eAAe,OAAO;AAAA,EACpD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAM,mBAAmB,eAAe,OAAO;AAAA,EAClD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAM,uBAAuB,eAAe,OAAO;AAAA,EACtD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAM,4BAA4B,eAAe,OAAO;AAAA,EAC3D,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,eAAe,eAAe,OAAO;AAAA,EAC9C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,4BAA4B,CAAC;AAAA,EAC9E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,WAAWA,eAAa;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACxBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,kBAAkB,eAAe,OAAO;AAAA,EACjD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACfD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,YAAY,eAAe,OAAO;AAAA,EAC3C,iBAAiBA,eAAa;AAAA,IAC1B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAM,iBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACfD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAAA,EACzE,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACpBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,qBAAqB,eAAe,OAAO;AAAA,EACpD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,oBAAoB,eAAe,OAAO;AAAA,EACnD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,eAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACnCD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,YAAY,MAAM;AACd,UAAM,wBAAwB,CAAC,aAAa;AACxC,aAAO,KAAK,8BAA8B,QAAQ;AAAA,IACtD;AACA,WAAOA,eAAa;AAAA,MAChB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV;AAAA,IACJ,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,EACvB;AAAA,EACA,QAAQ,MAAM;AACV,UAAM,wBAAwB,CAAC,aAAa;AACxC,aAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG,EAAE,MAAM,SAAS,KAAK,IAAI,KAAK,8BAA8B,KAAK,IAAI,CAAC,EAAE,CAAC;AAAA,IAChI;AACA,WAAOA,eAAa;AAAA,MAChB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,IACJ,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,8BAA8B,aAAa;AACvC,QAAI,CAAC,YAAY,kBAAkB,CAAC,YAAY,eAAe,KAAK;AAChE,aAAO;AAAA,IACX;AACA,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,EAAE,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMvEA,eAAa;AAAA,QACT,QAAQ;AAAA,QACR,UAAU,YAAY,eAAe;AAAA,MACzC,CAAC,EAAE,MAAM,MAAM;AAAA,QACX;AAAA,UACI,eAAe,YAAY;AAAA,QAC/B;AAAA,MACJ,CAAC;AAAA,MAAE,CAAC;AAAA,EACZ;AACJ,CAAC;;;ACnDD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,4BAA4B,CAAC;AAAA,EAC9E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;AChBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,oBAAoB,eAAe,OAAO;AAAA,EACnD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,OAAOA,eAAa;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,kBAAkBA,eAAa;AAAA,IAC3B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,eAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC/BD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,mBAAmB,eAAe,OAAO;AAAA,EAClD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,uBAAuBA,eAAa;AAAA,IAChC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,eAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACdD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,oBAAmB,eAAe,OAAO;AAAA,EAClD,QAAQD,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACnBD,IAAME,iBAAe,eAAe;AAC7B,IAAM,YAAY,eAAe,OAAO;AAAA,EAC3C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,yBAAyB,CAAC;AAAA,EAC3E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACpBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,wBAAwB,eAAe,OAAO;AAAA,EACvD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,yBAAwB,eAAe,OAAO;AAAA,EACvD,QAAQD,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAME,iBAAe,eAAe;AAC7B,IAAM,oBAAoB,eAAe,OAAO;AAAA,EACnD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAM,mBAAmB,eAAe,OAAO;AAAA,EAClD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,MAAM;AAAA,EACV,CAAC;AACL,CAAC;;;ACPD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,cAAc,eAAe,OAAO;AAAA,EAC7C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,2BAA2B,CAAC;AACjF,CAAC;;;ACHD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,eAAc,eAAe,OAAO;AAAA,EAC7C,QAAQD,eAAa,EAAE,QAAQ,QAAQ,UAAU,2BAA2B,CAAC;AACjF,CAAC;;;ACHD,IAAME,iBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,qBAAqB,CAAC;AAAA,EACvE,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,0BAA0B,CAAC;AAAA,EAC7E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,0BAA0B,CAAC;AAAA,EAC5E,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,YAAYA,eAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,oBAAoBA,eAAa;AAAA,IAC7B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,YAAYA,eAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACvBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,qBAAqB,CAAC;AAAA,EACvE,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACpBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,mBAAmB,eAAe,OAAO;AAAA,EAClD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,uBAAuBA,eAAa;AAAA,IAChC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AClBD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,oBAAmB,eAAe,OAAO;AAAA,EAClD,QAAQD,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACnBD,IAAME,iBAAe,eAAe;AAC7B,IAAM,oBAAoB,eAAe,OAAO;AAAA,EACnD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,wBAAwBA,eAAa;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AClBD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,qBAAoB,eAAe,OAAO;AAAA,EACnD,QAAQD,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACnBD,IAAME,iBAAe,eAAe;AAC7B,IAAM,yBAAyB,eAAe,OAAO;AAAA,EACxD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,YAAYA,eAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACdD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,0BAAyB,eAAe,OAAO;AAAA,EACxD,QAAQD,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACnBD,IAAME,iBAAe,eAAe;AAC7B,IAAM,kBAAkB,eAAe,OAAO;AAAA,EACjD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,sBAAsBA,eAAa;AAAA,IAC/B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,wBAAwBA,eAAa;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,wBAAwBA,eAAa;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACdD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,WAAU,eAAe,OAAO;AAAA,EACzC,QAAQD,eAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAAA,EACzE,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,sBAAsBA,eAAa;AAAA,IAC/B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,oBAAoBA,eAAa;AAAA,IAC7B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,kBAAkBA,eAAa;AAAA,IAC3B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC5CD,IAAME,iBAAe,eAAe;AAC7B,IAAM,kBAAkB,eAAe,OAAO;AAAA,EACjD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,mBAAkB,eAAe,OAAO;AAAA,EACjD,UAAUD,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAME,iBAAe,eAAe;AAC7B,IAAM,iBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,kBAAiB,eAAe,OAAO;AAAA,EAChD,UAAUD,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAME,iBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAM,gBAAgB,eAAe,OAAO;AAAA,EAC/C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,wBAAwB,CAAC;AAAA,EAC1E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;AChBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,aAAa,eAAe,OAAO;AAAA,EAC5C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,4BAA4B,CAAC;AAAA,EAC9E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACZD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,cAAc,eAAe,OAAO;AAAA,EAC7C,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,0BAA0B,CAAC;AAAA,EAC5E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACZD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,qBAAqB,eAAe,OAAO;AAAA,EACpD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,mBAAmB,CAAC;AAAA,EACrE,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa,EAAE,QAAQ,OAAO,UAAU,wBAAwB,CAAC;AAC3E,CAAC;;;ACbD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,YAAW,eAAe,OAAO;AAAA,EAC1C,QAAQD,eAAa,EAAE,QAAQ,QAAQ,UAAU,wBAAwB,CAAC;AAAA,EAC1E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACzBD,IAAME,iBAAe,eAAe;AAC7B,IAAMC,YAAW,eAAe,OAAO;AAAA,EAC1C,QAAQD,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACVD,IAAME,iBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,mBAAmB,CAAC;AAAA,EACtE,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,mBAAmB,CAAC;AACzE,CAAC;;;ACJD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,YAAY,eAAe,OAAO;AAAA,EAC3C,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,aAAa,eAAe,OAAO;AAAA,EAC5C,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,eAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACvBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACfD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,qBAAqB,eAAe,OAAO;AAAA,EACpD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,eAAe,eAAe,OAAO;AAAA,EAC9C,oBAAoBA,eAAa;AAAA,IAC7B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,sBAAsBA,eAAa;AAAA,IAC/B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACdD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,gBAAe,eAAe,OAAO;AAAA,EAC9C,UAAUD,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAME,iBAAe,eAAe;AAC7B,IAAMC,gBAAe,eAAe,OAAO;AAAA,EAC9C,UAAUD,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACfD,IAAME,iBAAe,eAAe;AAC7B,IAAMC,gBAAe,eAAe,OAAO;AAAA,EAC9C,UAAUD,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,uBAAuBA,eAAa;AAAA,IAChC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,eAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACnBD,IAAME,iBAAe,eAAe;AAC7B,IAAMC,gBAAe,eAAe,OAAO;AAAA,EAC9C,UAAUD,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAME,iBAAe,eAAe;AAC7B,IAAM,iBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACnBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,aAAa,eAAe,OAAO;AAAA,EAC5C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,wBAAwB,CAAC;AAAA,EAC1E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACpBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,sBAAsB,eAAe,OAAO;AAAA,EACrD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,uBAAuB,eAAe,OAAO;AAAA,EACtD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC3BD,IAAMC,iBAAe,eAAe;AAE7B,IAAMC,YAAW,eAAe,OAAO;AAAA,EAC1C,QAAQD,eAAa,EAAE,QAAQ,QAAQ,UAAU,eAAe,CAAC;AAAA,EACjE,SAAS,OAAO,MAAM;AAGlB,QAAI,OAAO,OAAO,UAAU;AACxB,aAAOA,eAAa;AAAA,QAChB,QAAQ;AAAA,QACR,UAAU;AAAA,MACd,CAAC,EAAE,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AAAA,IAChC,OACK;AACD,UAAI,OAAO,QAAQ,OAAO,QAAW;AAEjC,SAAC,EAAE,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AAAA,MAChC;AACA,aAAOA,eAAa;AAAA,QAChB,QAAQ;AAAA,QACR,UAAU;AAAA,MACd,CAAC,EAAE,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,yBAAyB,CAAC;AAAA,EAC3E,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa,EAAE,QAAQ,UAAU,UAAU,yBAAyB,CAAC;AAAA,EAC1E,uBAAuBA,eAAa;AAAA,IAChC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,iBAAiBA,eAAa;AAAA,IAC1B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,uBAAuBA,eAAa;AAAA,IAChC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,kBAAkBA,eAAa;AAAA,IAC3B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,sBAAsBA,eAAa;AAAA,IAC/B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,iBAAiBA,eAAa,EAAE,QAAQ,OAAO,UAAU,cAAc,CAAC;AAAA,EACxE,oBAAoBA,eAAa;AAAA,IAC7B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,yBAAyBA,eAAa;AAAA,IAClC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,eAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,kBAAkBA,eAAa;AAAA,IAC3B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,uBAAuBA,eAAa;AAAA,IAChC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC/FD,IAAME,iBAAe,eAAe;AAC7B,IAAM,eAAe,eAAe,OAAO;AAAA,EAC9C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,oBAAoB,CAAC;AAC1E,CAAC;;;ACHD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,kBAAkB,eAAe,OAAO;AAAA,EACjD,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAC7E,CAAC;;;ACHD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,kBAAkB,eAAe,OAAO;AAAA,EACjD,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,wBAAwB,CAAC;AAAA,EAC1E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AChBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,kBAAkB,eAAe,OAAO;AAAA,EACjD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,gBAAgBA,eAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC5BD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,cAAc,CAAC;AACrE,CAAC;;;ACHD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,sBAAsB,eAAe,OAAO;AAAA,EACrD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,cAAc,CAAC;AAAA,EAChE,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,uBAAuB,CAAC;AAAA,EAC1E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAAA,EACzE,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,SAASA,eAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACnBD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,sBAAqB,eAAe,OAAO;AAAA,EACpD,UAAUD,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACND,IAAME,iBAAe,eAAe;AAC7B,IAAM,eAAe,eAAe,OAAO;AAAA,EAC9C,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,cAAc,CAAC;AAAA,EAChE,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,uBAAuB,CAAC;AAAA,EAC1E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAAA,EACzE,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa,EAAE,QAAQ,UAAU,UAAU,uBAAuB,CAAC;AAC5E,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,cAAc,eAAe,OAAO;AAAA,EAC7C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,mBAAmB,CAAC;AAAA,EACrE,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,wBAAwB,CAAC;AAAA,EAC3E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,wBAAwB,CAAC;AAAA,EAC1E,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,sBAAsBA,eAAa;AAAA,IAC/B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,SAASA,eAAa,EAAE,QAAQ,OAAO,UAAU,2BAA2B,CAAC;AAAA,EAC7E,gBAAgBA,eAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACzBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,mBAAmB,eAAe,OAAO;AAAA,EAClD,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,wBAAwB,CAAC;AAC9E,CAAC;;;ACHD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,aAAY,eAAe,OAAO;AAAA,EAC3C,QAAQD,eAAa,EAAE,QAAQ,QAAQ,UAAU,gBAAgB,CAAC;AAAA,EAClE,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,2BAA2B,CAAC;AAAA,EAC9E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,2BAA2B,CAAC;AAAA,EAC7E,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa,EAAE,QAAQ,UAAU,UAAU,2BAA2B,CAAC;AAAA,EAC5E,0BAA0BA,eAAa;AAAA,IACnC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,2BAA2BA,eAAa;AAAA,IACpC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,eAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,yBAAyBA,eAAa;AAAA,IAClC,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,6BAA6BA,eAAa;AAAA,IACtC,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,oBAAoBA,eAAa;AAAA,IAC7B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,YAAYA,eAAa;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,4BAA4BA,eAAa;AAAA,IACrC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,qBAAqBA,eAAa;AAAA,IAC9B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gCAAgCA,eAAa;AAAA,IACzC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,uBAAuBA,eAAa;AAAA,IAChC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,eAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,0BAA0BA,eAAa;AAAA,IACnC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,mBAAmBA,eAAa;AAAA,IAC5B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,eAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC7GD,IAAME,iBAAe,eAAe;AAC7B,IAAMC,YAAW,eAAe,OAAO;AAAA,EAC1C,UAAUD,eAAa,EAAE,QAAQ,OAAO,UAAU,yBAAyB,CAAC;AAAA,EAC5E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,yBAAyB,CAAC;AAAA,EAC3E,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,OAAOA,eAAa;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACbD,IAAME,iBAAe,eAAe;AAC7B,IAAM,gBAAgB,eAAe,OAAO;AAAA,EAC/C,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW,CAAC,MAAM,YAAY;AAC1B,UAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,QAAQ,gBAAgB,GAAG;AACxD,cAAM,IAAI,MAAM,4IAA4I;AAAA,MAChK;AAAA,IACJ;AAAA,EACJ,CAAC;AAAA,EACD,KAAKA,eAAa,EAAE,QAAQ,UAAU,UAAU,2BAA2B,CAAC;AAChF,CAAC;;;ACZD,IAAMC,iBAAe,eAAe;AAC7B,IAAMC,UAAS,eAAe,OAAO;AAAA,EACxC,UAAUD,eAAa,EAAE,QAAQ,OAAO,UAAU,kBAAkB,CAAC;AAAA,EACrE,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACRD,IAAME,iBAAe,eAAe;AAC7B,IAAM,gBAAgB,eAAe,OAAO;AAAA,EAC/C,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,YAAY,eAAe,OAAO;AAAA,EAC3C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,iBAAiB,CAAC;AAAA,EACnE,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,wBAAwB,CAAC;AAAA,EAC3E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,wBAAwB,CAAC;AAAA,EAC1E,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACRD,IAAM,yBAAyB,CAAC,QAAQ,MAAM,YAAY;AACtD,QAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,GAAG,SAAS;AAC7F,UAAQ,cAAc,IAAI,iCAAiC,KAAK;AAChE,QAAM,cAAc,IAAI,YAAY;AACpC,MAAI,SAAS,IAAI,WAAW,CAAC;AAC7B,QAAM,YAAY,YAAY,OAAO,MAAM;AAC3C,WAAS,KAAK,GAAG;AACb,UAAM,aAAa;AACnB,UAAM,YAAY,aAAa,aAAa,IAAI,IAAI,WAAW,YAAY,OAAO,CAAC,CAAC;AACpF,aAAS,IAAI,WAAW,WAAW,SAAS,UAAU,SAAS,CAAC;AAChE,WAAO,IAAI,UAAU;AACrB,WAAO,IAAI,WAAW,WAAW,MAAM;AACvC,WAAO,IAAI,WAAW,OAAO,SAAS,CAAC;AAAA,EAC3C;AACA,WAAS,EAAE,GAAG;AACV,WAAO,IAAI,EAAE,QAAQ,QAAQ,KAAK,EAAE,QAAQ,eAAe,GAAG,CAAC;AAAA,EACnE;AACA,QAAM,gBAAgB,oBAAoB,IAAI;AAC9C,aAAW,KAAK,eAAe;AAC3B,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,eAAe,CAAC,GAAG;AACzD;AAAA,IACJ;AACA,UAAM,IAAI,cAAc,CAAC;AACzB,SAAK,KAAK,KAAK,EAAE;AACjB,QAAI,OAAO,UAAU,eAAe,KAAK,GAAG,MAAM,GAAG;AACjD,YAAM,aAAa;AACnB,WAAK,wCAAwC,EAAE,CAAC,CAAC,cAAc,EAAE,WAAW,QAAQ,MAAM,CAAC,EAAE;AAC7F,WAAK,iBAAiB,WAAW,QAAQ,0BAA0B,EAAE;AACrE,WAAK,EAAE;AACP,WAAK,WAAW,IAAI;AAAA,IACxB,OACK;AACD,WAAK,wCAAwC,EAAE,CAAC,CAAC,EAAE;AACnD,WAAK,EAAE;AACP,WAAK,CAAC;AAAA,IACV;AAAA,EACJ;AACA,OAAK,KAAK,KAAK,IAAI;AACnB,SAAO;AACX;AACO,SAAS,8BAA8B,QAAQ,MAAM,SAAS,UAAU;AAC3E,SAAO,QAAQ,CAAC;AAChB,MAAI,WAAW,QAAQ;AACnB,WAAO,SAAS,MAAM,0BAA0B,IAAI,CAAC;AAAA,EACzD;AACA,OAAK,QAAQ,mBACR,cAAc,IAAI,EAClB,KAAK,CAAC,iBAAiB;AACxB,UAAM,SAAS,uBAAuB,QAAQ,cAAc,OAAO;AACnE,WAAO,SAAS,MAAM,MAAM;AAAA,EAChC,CAAC,EACI,MAAM,CAAC,QAAQ,SAAS,KAAK,IAAI,CAAC;AAC3C;;;ACrDA,IAAMC,iBAAe,eAAe;AAC7B,IAAM,QAAQ,eAAe,OAAO;AAAA,EACvC,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,MACL,gBAAgB;AAAA,IACpB;AAAA,IACA,MAAM;AAAA,EACV,CAAC;AAAA,EACD,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,mBAAmB,CAAC;AAAA,EACtE,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,sBAAsB;AAC1B,CAAC;;;AClBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,eAAe,eAAe,OAAO;AAAA,EAC9C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,mBAAmB,CAAC;AAAA,EACrE,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACpBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,kBAAkB,eAAe,OAAO;AAAA,EACjD,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACXD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,4BAA4B,eAAe,OAAO;AAAA,EAC3D,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,SAASA,eAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,WAAWA,eAAa;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACnBD,IAAMC,iBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,eAAe,CAAC;AAAA,EACjE,UAAUA,eAAa,EAAE,QAAQ,OAAO,UAAU,yBAAyB,CAAC;AAAA,EAC5E,QAAQA,eAAa,EAAE,QAAQ,QAAQ,UAAU,yBAAyB,CAAC;AAAA,EAC3E,MAAMA,eAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,eAAa,EAAE,QAAQ,UAAU,UAAU,yBAAyB,CAAC;AAAA,EAC1E,UAAUA,eAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,iBAAiBA,eAAa;AAAA,IAC1B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,eAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,mBAAmBA,eAAa;AAAA,IAC5B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,KAAKA,eAAa,EAAE,QAAQ,QAAQ,UAAU,6BAA6B,CAAC;AAAA,EAC5E,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,eAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,eAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,aAAaA,eAAa;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC9DD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,yBAAyB,CAAC;AAChF,CAAC;;;ACFD,IAAMC,kBAAe,eAAe;AACpC,IAAM,YAAY;AACX,IAAM,QAAQ,eAAe,OAAO;AAAA,EACvC,UAAU;AAAA,EACV,aAAa,QAAQ,SAAS;AAC1B,aAAS,UAAU,CAAC;AACpB,cAAU,WAAW,CAAC;AACtB,QAAI,OAAO;AAEX,QAAI,QAAQ,SAAS;AACjB,aAAO,WAAW,IAAI;AAAA,IAC1B;AACA,QAAI,CAAC,OAAO,eAAe;AACvB,aAAO,gBAAgB;AAAA,IAC3B;AACA,QAAI,CAAC,OAAO,WAAW;AACnB,aAAO,YAAY,KAAK,QAAQ,YAAY;AAAA,IAChD;AACA,QAAI,CAAC,OAAO,OAAO;AACf,aAAO,QAAQ;AAAA,IACnB;AACA,WAAO,WAAW,SAAS,IAAI,IAAI,IAAI,0BAA0B,MAAM,CAAC;AAAA,EAC5E;AAAA,EACA,OAAOA,gBAAa;AAAA,IAChB,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,EACV,CAAC;AAAA,EACD,YAAY,SAAS,MAAM;AACvB,QAAI,CAAC,KAAK,WAAW;AACjB,WAAK,YAAY,KAAK,QAAQ,YAAY;AAAA,IAC9C;AACA,WAAOA,gBAAa;AAAA,MAChB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,IACV,CAAC,EAAE,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;AAAA,EAClC;AACJ,CAAC;;;ACvCD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,iBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,sBAAsB,CAAC;AAAA,EACxE,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,sBAAsBA,gBAAa;AAAA,IAC/B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,gBAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,gBAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,wBAAwBA,gBAAa;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,qBAAqBA,gBAAa;AAAA,IAC9B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC7CD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,eAAe,eAAe,OAAO;AAAA,EAC9C,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,oBAAoB,CAAC;AAAA,EACtE,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,eAAeA,gBAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACrBD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,8BAA8B,eAAe,OAAO;AAAA,EAC7D,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACnBD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,uBAAuB,eAAe,OAAO;AAAA,EACtD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACvBD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,iBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,sBAAsB,CAAC;AAAA,EACxE,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACxBD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,cAAc,CAAC;AAAA,EAChE,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,uBAAuB,CAAC;AAAA,EAC1E,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAAA,EACzE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,gBAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AClBD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,QAAQ,eAAe,OAAO;AAAA,EACvC,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,YAAY,CAAC;AAAA,EAC9D,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,mBAAmB,CAAC;AAAA,EACtE,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,mBAAmB,CAAC;AAAA,EACrE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,gBAAa,EAAE,QAAQ,UAAU,UAAU,mBAAmB,CAAC;AACxE,CAAC;;;ACXD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,aAAa,CAAC;AAAA,EAC/D,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,qBAAqB,CAAC;AAAA,EACxE,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,qBAAqB,CAAC;AAAA,EACvE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACfD,IAAMC,kBAAe,eAAe;AAC7B,IAAMC,YAAW,eAAe,OAAO;AAAA,EAC1C,QAAQD,gBAAa,EAAE,QAAQ,QAAQ,UAAU,eAAe,CAAC;AAAA,EACjE,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,oBAAoB,CAAC;AAAA,EACvE,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,oBAAoB,CAAC;AAAA,EACtE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,gBAAa,EAAE,QAAQ,UAAU,UAAU,oBAAoB,CAAC;AAAA,EACrE,eAAeA,gBAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,gBAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,cAAcA,gBAAa;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,iBAAiBA,gBAAa;AAAA,IAC1B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACjCD,IAAME,kBAAe,eAAe;AAC7B,IAAM,iBAAiB,eAAe,OAAO;AAAA,EAChD,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,sBAAsB,CAAC;AAAA,EACxE,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;AChBD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,aAAa,CAAC;AAAA,EAC/D,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,qBAAqB,CAAC;AAAA,EACxE,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,qBAAqB,CAAC;AAAA,EACvE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,4BAA4B,CAAC;AAAA,EAC9E,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,4BAA4B,CAAC;AAAA,EAC9E,eAAeA,gBAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,8BAA8BA,gBAAa;AAAA,IACvC,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,eAAeA,gBAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,gBAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,MAAM;AAAA,IACN,WAAW;AAAA,EACf,CAAC;AACL,CAAC;;;AChCD,IAAMC,kBAAe,eAAe;AAC7B,IAAMC,WAAU,eAAe,OAAO;AAAA,EACzC,QAAQD,gBAAa,EAAE,QAAQ,QAAQ,UAAU,cAAc,CAAC;AAAA,EAChE,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,uBAAuB,CAAC;AAAA,EAC1E,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAAA,EACzE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACdD,IAAME,kBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,uBAAuB,CAAC;AAAA,EAC1E,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,SAASA,gBAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACZD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,gBAAgB,eAAe,OAAO;AAAA,EAC/C,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACPD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,eAAe,eAAe,OAAO;AAAA,EAC9C,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,oBAAoB,CAAC;AAAA,EACtE,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,gBAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,qBAAqBA,gBAAa;AAAA,IAC9B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC5BD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,gBAAgB,eAAe,OAAO;AAAA,EAC/C,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,qBAAqB,CAAC;AAAA,EACvE,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;AChBD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,UAAU,eAAe,OAAO;AAAA,EACzC,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,cAAc,CAAC;AAAA,EAChE,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,uBAAuB,CAAC;AAAA,EAC1E,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,uBAAuB,CAAC;AAAA,EACzE,wBAAwBA,gBAAa;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACdD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,oBAAoB,eAAe,OAAO;AAAA,EACnD,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,yBAAyB,CAAC;AAAA,EAC3E,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,gBAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;ACpBD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,wBAAwB,eAAe,OAAO;AAAA,EACvD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,SAASA,gBAAa;AAAA,IAClB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC3BD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,gBAAgB,eAAe,OAAO;AAAA,EAC/C,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,oBAAoB,CAAC;AAAA,EACtE,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,gBAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACjCD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,qBAAqB,CAAC;AAAA,EACxE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACRD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,cAAc,CAAC;AAAA,EAChE,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,mBAAmB,CAAC;AAAA,EACtE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,gBAAa,EAAE,QAAQ,UAAU,UAAU,mBAAmB,CAAC;AACxE,CAAC;;;ACVD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,WAAW,eAAe,OAAO;AAAA,EAC1C,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,gBAAgB,CAAC;AAAA,EAClE,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,2BAA2B,CAAC;AAAA,EAC9E,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,2BAA2B,CAAC;AAAA,EAC7E,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AACL,CAAC;;;ACVD,IAAMC,kBAAe,eAAe;AAC7B,IAAMC,UAAS,eAAe,OAAO;AAAA,EACxC,QAAQD,gBAAa,EAAE,QAAQ,QAAQ,UAAU,aAAa,CAAC;AAAA,EAC/D,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,qBAAqB,CAAC;AAC5E,CAAC;;;ACJD,IAAME,kBAAe,eAAe;AAC7B,IAAM,SAAS,eAAe,OAAO;AAAA,EACxC,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,aAAa,CAAC;AAAA,EAC/D,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,qBAAqB,CAAC;AAAA,EACxE,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,qBAAqB,CAAC;AAAA,EACvE,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,4BAA4B,CAAC;AAClF,CAAC;;;ACXD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,YAAY,eAAe,OAAO;AAAA,EAC3C,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,gBAAgB,CAAC;AAAA,EAClE,UAAUA,gBAAa,EAAE,QAAQ,OAAO,UAAU,2BAA2B,CAAC;AAAA,EAC9E,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,2BAA2B,CAAC;AAAA,EAC7E,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,gBAAgBA,gBAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,eAAeA,gBAAa;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,kBAAkBA,gBAAa;AAAA,IAC3B,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,gBAAgBA,gBAAa;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AC3BD,IAAMC,kBAAe,eAAe;AAC7B,IAAM,mBAAmB,eAAe,OAAO;AAAA,EAClD,QAAQA,gBAAa,EAAE,QAAQ,QAAQ,UAAU,wBAAwB,CAAC;AAAA,EAC1E,UAAUA,gBAAa;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,QAAQA,gBAAa;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AAAA,EACD,MAAMA,gBAAa;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EAChB,CAAC;AAAA,EACD,KAAKA,gBAAa;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACd,CAAC;AACL,CAAC;;;AjI4GM,IAAM,OAAO,kBAAkB,QAAQ,EAAE,QAAqB,CAAC;AAC/D,IAAM,UAAU,kBAAkB,WAAW;AAAA,EAChD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AACM,IAAM,gBAAgB,kBAAkB,iBAAiB;AAAA,EAC5D;AAAA,EACA;AACJ,CAAC;AACM,IAAM,WAAW,kBAAkB,YAAY;AAAA,EAClD,UAAUC;AACd,CAAC;AACM,IAAM,UAAU,kBAAkB,WAAW;AAAA,EAChD;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AACM,IAAM,eAAe,kBAAkB,gBAAgB;AAAA,EAC1D;AAAA,EACA;AACJ,CAAC;AACM,IAAM,uBAAuB,kBAAkB,wBAAwB;AAAA,EAC1E;AAAA,EACA,UAAUA;AAAA,EACV,cAAcC;AAClB,CAAC;AACM,IAAM,aAAa,kBAAkB,cAAc;AAAA,EACtD;AACJ,CAAC;AACM,IAAM,WAAW,kBAAkB,YAAY;AAAA,EAClD;AAAA,EACA;AACJ,CAAC;AACM,IAAM,UAAU,kBAAkB,WAAW;AAAA,EAChD,gBAAgBC;AAAA,EAChB;AAAA,EACA,OAAOC;AAAA,EACP;AAAA,EACA,wBAAwBC;AAAA,EACxB;AAAA,EACA;AAAA,EACA,cAAcH;AAClB,CAAC;AACM,IAAM,QAAQ,kBAAkB,SAAS;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AACM,IAAM,YAAY,kBAAkB,aAAa;AAAA,EACpD;AAAA,EACA;AACJ,CAAC;AACM,IAAM,QAAQ,kBAAkB,SAAS;AAAA,EAC5C;AACJ,CAAC;AACM,IAAM,MAAM,kBAAkB,OAAO;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAcA;AAClB,CAAC;AACM,IAAM,WAAW,kBAAkB,YAAY;AAAA,EAClD,gBAAgBI;AAAA,EAChB;AAAA,EACA;AAAA,EACA,SAASC;AACb,CAAC;AACM,IAAM,cAAc,kBAAkB,eAAe;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,kBAAkB,WAAW;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAAA,EACD,UAAU,kBAAkB,YAAY;AAAA,IACpC;AAAA,EACJ,CAAC;AAAA,EACD,UAAU,kBAAkB,YAAY;AAAA,IACpC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACL,CAAC;AACM,IAAM,WAAW,kBAAkB,YAAY;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkBC;AAAA,EAClB,kBAAkBC;AAAA,EAClB,mBAAmBC;AAAA,EACnB,iBAAiBC;AAAA,EACjB,gBAAgBC;AAAA,EAChB;AAAA,EACA,cAAcV;AAClB,CAAC;AACM,IAAM,KAAK,kBAAkB,MAAM;AAAA,EACtC,SAAS,kBAAkB,WAAW;AAAA,IAClC,uBAAuBW;AAAA,IACvB;AAAA,IACA;AAAA,IACA,aAAaC;AAAA,EACjB,CAAC;AAAA,EACD,MAAM,kBAAkB,QAAQ;AAAA,IAC5B;AAAA,IACA;AAAA,EACJ,CAAC;AACL,CAAC;;;AkI9OD,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB;AAC5B,IAAM,kBAAkB;AACxB,IAAM,8BAA8B;AACpC,IAAM,kCAAkC;AACxC,IAAM,sBAAsB,CAAC,QAAQ,WAAW,OAAO,YAAY;AACnE,IAAM,4BAA4B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,8BAA8B,CAAC,WAAW,IAAI,cAAc,QAAQ,eAAe,4BAA4B;AAC9G,SAAS,aAAa,mBAAmB,gBAAgB,6BAA6B;AACzF,EAAAC,QAAO,kBAAkB;AACzB,EAAAA,QAAO,aAAa,OAAO,OAAO,EAAE,kBAAkBA,QAAO,iBAAiB,MAAM,QAAQ,WAAW,UAAU,OAAO,MAAM,YAAY,MAAM,GAAG,oCAAoC,CAAC;AACxL,EAAAA,QAAO,iBAAiB;AACxB,EAAAA,QAAO,YAAY;AACnB,EAAAA,QAAO,aAAa;AACpB,EAAAA,QAAO,qBAAqB;AAC5B,EAAAA,QAAO,iBAAiB;AACxB,EAAAA,QAAO,WAAW,eAAe,iBAAiB;AAClD,WAASA,QAAO,KAAK,SAAS,CAAC,GAAG;AAC9B,QAAI,EAAE,gBAAgBA,UAAS;AAC3B,aAAO,IAAIA,QAAO,KAAK,MAAM;AAAA,IACjC;AACA,UAAM,QAAQ,KAAK,oBAAoB,MAAM;AAC7C,SAAK,qBAAqB;AAC1B,WAAO,eAAe,MAAM,YAAY;AAAA,MACpC,OAAO,KAAK,mBAAmB,cAAc;AAAA,MAC7C,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AACD,SAAK,UAAUA,QAAO;AACtB,SAAK,KAAK,KAAK,SAAS,GAAG,KAAK,KAAK,QAAQ;AAC7C,SAAK,OAAO,KAAK,SAAS,KAAK,KAAK,KAAK,QAAQ;AACjD,SAAK,MAAM,KAAK,SAAS,eAAe,KAAK,KAAK,QAAQ;AAC1D,UAAM,QAAQ,MAAM,aAAa;AACjC,SAAK,OAAO;AAAA,MACR,MAAM,MAAM,QAAQ;AAAA,MACpB,MAAM,MAAM,QAAQ;AAAA,MACpB,UAAU,MAAM,YAAY;AAAA,MAC5B,UAAU;AAAA,MACV,SAAS,MAAM,cAAc;AAAA,MAC7B,SAAS,gBAAgB,WAAW,MAAM,SAAS,eAAe;AAAA,MAClE,mBAAmB,gBAAgB,qBAAqB,MAAM,mBAAmB,CAAC;AAAA,MAClF;AAAA,MACA,YAAY,MAAM,eACb,QACK,KAAK,mBAAmB,qBAAqB,KAAK,IAClD,KAAK,mBAAmB,wBAAwB;AAAA,MAC1D,KAAK;AAAA,MACL,eAAe,MAAM,iBAAiB;AAAA,MACtC,eAAe,MAAM,iBAAiB;AAAA,IAC1C;AACA,UAAM,aAAa,MAAM,cAAc;AACvC,QAAI,eAAeA,QAAO,WAAW,YAAY;AAK7C,MAAAA,QAAO,WAAW,aAAa;AAAA,IACnC;AACA,QAAI,MAAM,SAAS;AACf,WAAK,YAAY,MAAM,OAAO;AAAA,IAClC;AACA,SAAK,eAAe;AACpB,SAAK,kBAAkB,KAAK,MAAM,aAAa;AAC/C,SAAK,SAAS;AACd,SAAK,WAAWA,QAAO;AACvB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,mBAAmB,MAAM,cAAc;AAC5C,SAAK,iBAAiB,cAAc,IAAI;AAGxC,SAAK,iBAAiBA,QAAO;AAAA,EACjC;AACA,EAAAA,QAAO,SAAS;AAChB,EAAAA,QAAO,uBAAuB,kBAAkB;AAQhD,EAAAA,QAAO,wBAAwB,kBAAkB;AAKjD,EAAAA,QAAO,2BAA2B,kBAAkB;AASpD,EAAAA,QAAO,6BACH,kBAAkB;AACtB,EAAAA,QAAO,YAAY;AAAA;AAAA,IAEf,UAAU;AAAA,IACV,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,WAAW,QAAQ,MAAM,QAAQ,SAAS;AACtC,aAAO,KAAK,eAAe,YAAY,QAAQ,MAAM,QAAQ,OAAO;AAAA,IACxE;AAAA;AAAA;AAAA;AAAA,IAIA,kBAAkB,KAAK,eAAe;AAClC,UAAI,OAAO,eAAe;AACtB,cAAM,IAAI,MAAM,6CAA6C;AAAA,MACjE;AACA,UAAI,CAAC,OAAO,CAAC,eAAe;AACxB,cAAM,IAAI,MAAM,kDAAkD;AAAA,MACtE;AACA,WAAK,iBAAiB,MAChB,0BAA0B,GAAG,IAC7B;AAAA,IACV;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY,MAAM;AACd,UAAI,QAAQ,OAAO,SAAS,UAAU;AAClC,cAAM,IAAI,MAAM,4BAA4B;AAAA,MAChD;AACA,UAAI,QAAQ,CAAC,KAAK,MAAM;AACpB,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC9C;AACA,aAAO,QAAQ,CAAC;AAChB,WAAK,WAAW,oBAAoB,OAAO,CAAC,OAAO,SAAS;AACxD,YAAI,OAAO,KAAK,IAAI,KAAK,UAAU;AAC/B,kBAAQ,SAAS,CAAC;AAClB,gBAAM,IAAI,IAAI,KAAK,IAAI;AAAA,QAC3B;AACA,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,aAAa,KAAK,OAAO;AACrB,WAAK,KAAK,GAAG,IAAI;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,YAAY,KAAK;AACb,aAAO,KAAK,KAAK,GAAG;AAAA,IACxB;AAAA,IACA,YAAY,UAAU;AAClB,WAAK,YAAY;AAAA,IACrB;AAAA,IACA,cAAc;AACV,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,aAAa,CAAC,MAAM;AAChB,cAAQ,GAAG;AAAA,QACP,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,MACf;AACA,aAAOA,QAAO,CAAC;AAAA,IACnB;AAAA,IACA,uBAAuB;AACnB,aAAO,KAAK,YAAY,mBAAmB;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,mBAAmB,MAAM,GAAG,YAAY;AACpC,YAAM,MAAM,gBAAgB,MAAM,GAAG,UAAU;AAC/C,WAAK,aAAa,MAAM,GAAG;AAAA,IAC/B;AAAA,IACA,0BAA0B;AACtB,aAAO;AAAA,IACX;AAAA,IACA,8BAA8B;AAC1B,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,mBAAmB,IAAI;AACnB,aAAO,KAAK,yBAAyBA,QAAO,YAAY,EAAE;AAAA,IAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,yBAAyB,MAAM,IAAI;AAC/B,WAAK,mBAAmB,SAAS,EAAE,KAAK,CAAC,UAAU;AAC/C,YAAI;AACJ,cAAM,YAAY,CAAC;AACnB,mBAAW,SAAS,MAAM;AACtB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,KAAK,GAAG;AACpD;AAAA,UACJ;AACA,oBAAU,KAAK,IAAI,oBAAoB,KAAK,KAAK,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,MAAM;AAAA,QACpG;AAEA,kBAAU,QAAQ,mBAAmB,SAAS,SAAS;AACvD,cAAM,SAAS,KAAK,YAAY,YAAY;AAC5C,YAAI,QAAQ;AACR,oBAAU,UAAU,mBAAmB,OAAO,cAAc,CAAC;AAAA,QACjE;AACA,YAAI,KAAK,UAAU;AACf,oBAAU,cAAc,KAAK;AAAA,QACjC;AACA,WAAG,KAAK,UAAU,SAAS,CAAC;AAAA,MAChC,CAAC;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,qBAAqB;AACjB,UAAI,CAAC,KAAK,UAAU;AAChB,eAAO;AAAA,MACX;AACA,UAAI,YAAY,KAAK,SAAS;AAC9B,UAAI,KAAK,SAAS,SAAS;AACvB,qBAAa,IAAI,KAAK,SAAS,OAAO;AAAA,MAC1C;AACA,UAAI,KAAK,SAAS,KAAK;AACnB,qBAAa,KAAK,KAAK,SAAS,GAAG;AAAA,MACvC;AACA,aAAO;AAAA,IACX;AAAA,IACA,sBAAsB;AAClB,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB;AACb,iBAAW,QAAQ,mBAAW;AAC1B,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,mBAAW,IAAI,GAAG;AACxD;AAAA,QACJ;AAEA,aAAK,kBAAkB,IAAI,CAAC,IAAI,IAAI,kBAAU,IAAI,EAAE,IAAI;AAAA,MAC5D;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,oBAAoB,QAAQ;AAExB,UAAI,CAAC,QAAQ;AACT,eAAO,CAAC;AAAA,MACZ;AAEA,YAAM,WAAW,OAAO,WAAW;AACnC,YAAMC,YAAW,WAAW,OAAO,MAAM,KAAK,CAAC,MAAM,QAAQ,MAAM;AACnE,UAAI,CAACA,aAAY,CAAC,UAAU;AACxB,cAAM,IAAI,MAAM,6CAA6C;AAAA,MACjE;AAEA,UAAI,UAAU;AACV,eAAO;AAAA,UACH,YAAY;AAAA,QAChB;AAAA,MACJ;AAEA,YAAM,SAAS,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC,0BAA0B,SAAS,KAAK,CAAC;AAC/F,UAAI,OAAO,SAAS,GAAG;AACnB,cAAM,IAAI,MAAM,iDAAiD,0BAA0B,KAAK,IAAI,CAAC,EAAE;AAAA,MAC3G;AACA,aAAO;AAAA,IACX;AAAA,IACA,eAAe,SAAS,QAAQ,QAAQ,WAAW,gBAAgB,YAAY;AAE3E,aAAO,KAAK,SAAS,eAAe,SAAS,QAAQ,QAAQ,WAAW,gBAAgB,UAAU;AAAA,IACtG;AAAA,EACJ;AACA,SAAOD;AACX;;;ACpWO,IAAM,SAAS,aAAa,IAAI,qBAAqB,CAAC;AAC7D,IAAO,4BAAQ;", "names": ["isNaN", "concatty", "slicy", "Empty", "undefined", "<PERSON><PERSON><PERSON>", "stringToPath", "getBaseIntrinsic", "compactQueue", "arrayToObject", "merge", "encode", "compact", "isRegExp", "<PERSON><PERSON><PERSON><PERSON>", "combine", "maybeMap", "isNonNullishPrimitive", "stringify", "value", "normalizeStringifyOptions", "normalizeParseOptions", "stringify", "headers", "authenticator", "data", "result", "data", "urlData", "signature", "Accounts", "ConfirmationTokens", "Customers", "Disputes", "Events", "Products", "Refunds", "Tokens", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Authorizations", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Cards", "stripeMethod", "stripeMethod", "Configurations", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "InboundTransfers", "stripeMethod", "stripeMethod", "stripeMethod", "MeterEventAdjustments", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "MeterEvents", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "OutboundPayments", "stripeMethod", "stripeMethod", "OutboundTransfers", "stripeMethod", "stripeMethod", "PersonalizationDesigns", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Readers", "stripeMethod", "stripeMethod", "ReceivedCredits", "stripeMethod", "stripeMethod", "ReceivedDebits", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Sessions", "stripeMethod", "Sessions", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Transactions", "stripeMethod", "Transactions", "stripeMethod", "Transactions", "stripeMethod", "Transactions", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Accounts", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "ConfirmationTokens", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Customers", "stripeMethod", "Disputes", "stripeMethod", "stripeMethod", "Events", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Products", "stripeMethod", "stripeMethod", "stripeMethod", "Refunds", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "stripeMethod", "Tokens", "stripeMethod", "stripeMethod", "stripeMethod", "Sessions", "Transactions", "Authorizations", "Cards", "PersonalizationDesigns", "Configurations", "Readers", "InboundTransfers", "OutboundPayments", "OutboundTransfers", "ReceivedCredits", "ReceivedDebits", "MeterEventAdjustments", "MeterEvents", "Stripe", "isObject"]}