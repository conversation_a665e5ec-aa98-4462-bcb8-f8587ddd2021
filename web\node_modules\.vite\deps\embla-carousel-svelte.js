import {
  EmblaCarousel
} from "./chunk-YTIHJDK3.js";
import {
  areOptionsEqual,
  arePluginsEqual,
  canUseDOM
} from "./chunk-5JAIRJ3V.js";
import "./chunk-UQOTJTBP.js";

// node_modules/embla-carousel-svelte/esm/embla-carousel-svelte.esm.js
function emblaCarouselSvelte(emblaNode, emblaConfig = {
  options: {},
  plugins: []
}) {
  let storedEmblaConfig = emblaConfig;
  let emblaApi;
  if (canUseDOM()) {
    EmblaCarousel.globalOptions = emblaCarouselSvelte.globalOptions;
    emblaApi = EmblaCarousel(emblaNode, storedEmblaConfig.options, storedEmblaConfig.plugins);
    emblaApi.on("init", () => emblaNode.dispatchEvent(new CustomEvent("emblaInit", {
      detail: emblaApi
    })));
  }
  return {
    destroy: () => {
      if (emblaApi) emblaApi.destroy();
    },
    update: (newEmblaConfig) => {
      const optionsChanged = !areOptionsEqual(storedEmblaConfig.options, newEmblaConfig.options);
      const pluginsChanged = !arePluginsEqual(storedEmblaConfig.plugins, newEmblaConfig.plugins);
      if (!optionsChanged && !pluginsChanged) return;
      storedEmblaConfig = newEmblaConfig;
      if (emblaApi) {
        emblaApi.reInit(storedEmblaConfig.options, storedEmblaConfig.plugins);
      }
    }
  };
}
emblaCarouselSvelte.globalOptions = void 0;
export {
  emblaCarouselSvelte as default
};
//# sourceMappingURL=embla-carousel-svelte.js.map
