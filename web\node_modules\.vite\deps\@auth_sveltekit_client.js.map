{"version": 3, "sources": ["../../@auth/sveltekit/dist/client.js"], "sourcesContent": ["import { base } from \"$app/paths\";\nexport async function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = base ?? \"\";\n    const signInUrl = `${baseUrl}/auth/${provider === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\",\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            callbackUrl: redirectTo,\n        }),\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\"))\n            window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url,\n    };\n}\nexport async function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href, } = options ?? {};\n    const baseUrl = base ?? \"\";\n    const res = await fetch(`${baseUrl}/auth/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\",\n        },\n        body: new URLSearchParams({\n            callbackUrl: redirectTo,\n        }),\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\"))\n            window.location.reload();\n        return;\n    }\n    return data;\n}\n"], "mappings": ";;;AAAA,SAAS,YAAY;AACrB,eAAsB,OAAO,UAAU,SAAS,qBAAqB;AACjE,QAAM,EAAE,aAAa,GAAG,KAAK,IAAI,WAAW,CAAC;AAC7C,QAAM,EAAE,WAAW,MAAM,aAAa,eAAe,OAAO,SAAS,MAAM,GAAG,aAAa,IAAI;AAC/F,QAAM,UAAU,QAAQ;AACxB,QAAM,YAAY,GAAG,OAAO,SAAS,aAAa,gBAAgB,aAAa,QAAQ,IAAI,QAAQ;AACnG,QAAM,MAAM,MAAM,MAAM,GAAG,SAAS,IAAI,IAAI,gBAAgB,mBAAmB,CAAC,IAAI;AAAA,IAChF,QAAQ;AAAA,IACR,SAAS;AAAA,MACL,gBAAgB;AAAA,MAChB,0BAA0B;AAAA,IAC9B;AAAA,IACA,MAAM,IAAI,gBAAgB;AAAA,MACtB,GAAG;AAAA,MACH,aAAa;AAAA,IACjB,CAAC;AAAA,EACL,CAAC;AACD,QAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,MAAI,UAAU;AACV,UAAM,MAAM,KAAK,OAAO;AACxB,WAAO,SAAS,OAAO;AAEvB,QAAI,IAAI,SAAS,GAAG;AAChB,aAAO,SAAS,OAAO;AAC3B;AAAA,EACJ;AACA,QAAM,QAAQ,IAAI,IAAI,KAAK,GAAG,EAAE,aAAa,IAAI,OAAO,KAAK;AAC7D,QAAM,OAAO,IAAI,IAAI,KAAK,GAAG,EAAE,aAAa,IAAI,MAAM,KAAK;AAC3D,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,QAAQ,IAAI;AAAA,IACZ,IAAI,IAAI;AAAA,IACR,KAAK,QAAQ,OAAO,KAAK;AAAA,EAC7B;AACJ;AACA,eAAsB,QAAQ,SAAS;AACnC,QAAM,EAAE,WAAW,MAAM,cAAa,mCAAS,gBAAe,OAAO,SAAS,KAAM,IAAI,WAAW,CAAC;AACpG,QAAM,UAAU,QAAQ;AACxB,QAAM,MAAM,MAAM,MAAM,GAAG,OAAO,iBAAiB;AAAA,IAC/C,QAAQ;AAAA,IACR,SAAS;AAAA,MACL,gBAAgB;AAAA,MAChB,0BAA0B;AAAA,IAC9B;AAAA,IACA,MAAM,IAAI,gBAAgB;AAAA,MACtB,aAAa;AAAA,IACjB,CAAC;AAAA,EACL,CAAC;AACD,QAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,MAAI,UAAU;AACV,UAAM,MAAM,KAAK,OAAO;AACxB,WAAO,SAAS,OAAO;AAEvB,QAAI,IAAI,SAAS,GAAG;AAChB,aAAO,SAAS,OAAO;AAC3B;AAAA,EACJ;AACA,SAAO;AACX;", "names": []}