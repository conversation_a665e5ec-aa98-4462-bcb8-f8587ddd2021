{"version": 3, "sources": ["../../@lucide/svelte/dist/defaultAttributes.js", "../../@lucide/svelte/dist/Icon.svelte"], "sourcesContent": ["/**\n * @license @lucide/svelte v0.482.0 - ISC\n *\n * ISC License\n * \n * Copyright (c) for portions of Lucide are held by <PERSON> 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.\n * \n * Permission to use, copy, modify, and/or distribute this software for any\n * purpose with or without fee is hereby granted, provided that the above\n * copyright notice and this permission notice appear in all copies.\n * \n * THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n * \n */\nconst defaultAttributes = {\n    xmlns: 'http://www.w3.org/2000/svg',\n    width: 24,\n    height: 24,\n    viewBox: '0 0 24 24',\n    fill: 'none',\n    stroke: 'currentColor',\n    'stroke-width': 2,\n    'stroke-linecap': 'round',\n    'stroke-linejoin': 'round',\n};\nexport default defaultAttributes;\n", null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,oBAAoB;AAAA,EACtB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,mBAAmB;AACvB;AACA,IAAO,4BAAQ;;;;;;;;QC9BD,QAAK,KAAA,SAAA,SAAA,GAAG,cAAc,GAAE,OAAI,KAAA,SAAA,QAAA,GAAG,EAAE,GAAE,cAAW,KAAA,SAAA,eAAA,GAAG,CAAC,GAAE,sBAAmB,KAAA,SAAA,uBAAA,GAAG,KAAK,GAAE,WAAQ,KAAA,SAAA,YAAA,IAAA,MAAA,CAAA,CAAA,GAAoB,QAAK;;;;;;;;;;;;;;;;;;;;SAI1H;SACA;aACG,KAAI;cACH,KAAI;cACJ,MAAK;;;QAEL;;gBAAsD;;;;YADhD,oBAAmB,IAAI,OAAO,YAAW,CAAA,IAAI,KAAM,OAAO,KAAI,CAAA,IAAI,YAAW;;;;iBAGpF,UAAQ,OAAA,CAAAA,WAAA,WAAA;;QAAK,MAAI,MAAA,IAAA,OAAA,EAAA,CAAA;;QAAC,QAAM,MAAA,IAAA,OAAA,EAAA,CAAA;;;;iCAErB,GAAG;;;MAAH;;;gDACF,MAAK,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;", "names": ["$$anchor"]}