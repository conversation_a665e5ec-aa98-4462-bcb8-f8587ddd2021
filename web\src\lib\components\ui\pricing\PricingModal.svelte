<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Check, Loader2 } from 'lucide-svelte';
  import { writable } from 'svelte/store';

  import type { PlanTier } from '$lib/models/features/types';
  import PlanFeaturesList from './PlanFeaturesList.svelte';

  export let isOpen = false;
  export let onClose = () => {};
  export let onSelectPlan = (planId: string, billingCycle: 'monthly' | 'annual') => {};
  export let currentPlanId: string | null = null;
  export let isLoading = false;
  export let section: 'pro' | 'teams' = 'pro';

  // Billing cycle state
  let billingCycleStore = writable<'monthly' | 'annual'>('monthly');
  $: billingCycle = $billingCycleStore;
  let isAnnual = false;

  // Update billing cycle when isAnnual changes
  $: {
    billingCycleStore.set(isAnnual ? 'annual' : 'monthly');
  }

  // Plans state
  let plans: PlanTier[] = [];

  // Load plans from database only when modal is open
  async function loadPlans() {
    if (!isOpen) return; // Don't load plans if modal is not open

    try {
      const response = await fetch('/api/admin/plans');
      if (response.ok) {
        plans = await response.json();
      } else {
        console.error('Error loading plans from API');
        // Create default plans as fallback
        plans = [
          {
            id: 'casual',
            name: 'Casual',
            description: 'For occasional job seekers',
            section: 'pro' as const,
            monthlyPrice: 999,
            annualPrice: 9990,
            features: [],
          },
          {
            id: 'active',
            name: 'Active',
            description: 'For active job seekers',
            section: 'pro' as const,
            monthlyPrice: 1999,
            annualPrice: 19990,
            features: [],
          },
        ];
      }
    } catch (error) {
      console.error('Error loading plans:', error);
    }
  }

  // Load plans when modal opens (only if not already loaded)
  $: if (isOpen && plans.length === 0) {
    loadPlans();
  }

  // Filter plans by section
  $: filteredPlans = plans.filter((plan) => plan.section === section);

  // Helper function to get feature limits from plan
  function getFeatureLimit(plan: PlanTier, featureId: string, limitId: string): number | null {
    const feature = plan.features.find((f) => f.featureId === featureId);
    if (!feature || !feature.limits) return null;

    const limit = feature.limits.find((l) => l.limitId === limitId);
    return limit ? (typeof limit.value === 'number' ? limit.value : null) : null;
  }

  // Helper function to get current plan
  function getCurrentPlan(): PlanTier | undefined {
    return currentPlanId ? plans.find((p) => p.id === currentPlanId) : undefined;
  }

  // Format price
  function formatPrice(cents: number) {
    return (cents / 100).toFixed(0);
  }

  // Calculate savings percentage
  function calculateSavings(monthlyPrice: number, annualPrice: number) {
    const monthlyCost = monthlyPrice * 12;
    const savings = monthlyCost - annualPrice;
    return Math.round((savings / monthlyCost) * 100);
  }
</script>

<Dialog.Root bind:open={isOpen} onOpenChange={onClose}>
  <Dialog.Content class="sm:max-w-[900px]">
    <Dialog.Header>
      <Dialog.Title>Choose a Plan</Dialog.Title>
      <Dialog.Description>Select the plan that best fits your needs.</Dialog.Description>
    </Dialog.Header>

    <div class="flex justify-center py-4">
      <div class="bg-muted flex items-center gap-2 rounded-lg p-1">
        <button
          class={`rounded-md px-3 py-1 text-sm font-medium ${
            !isAnnual ? 'bg-background text-foreground' : 'text-muted-foreground'
          }`}
          on:click={() => (isAnnual = false)}>
          Monthly
        </button>
        <button
          class={`rounded-md px-3 py-1 text-sm font-medium ${
            isAnnual ? 'bg-background text-foreground' : 'text-muted-foreground'
          }`}
          on:click={() => (isAnnual = true)}>
          Annual
          {#if isAnnual}
            <span class="ml-1 text-xs text-green-500">Save 20%</span>
          {/if}
        </button>
      </div>
    </div>

    <div class="grid gap-6 md:grid-cols-3">
      {#each filteredPlans as plan}
        {#if plan.id !== 'free' || section === 'pro'}
          <div
            class={`rounded-lg border p-6 ${
              plan.id === 'power' || plan.id === 'startup' ? 'border-primary relative' : ''
            }`}>
            {#if plan.id === 'power' || plan.id === 'startup'}
              <div
                class="bg-primary absolute -top-3 left-1/2 -translate-x-1/2 rounded-full px-3 py-1 text-xs font-medium text-white">
                Popular
              </div>
            {/if}
            <h3 class="mb-2 text-lg font-medium">{plan.name}</h3>
            <p class="text-3xl font-bold">
              ${formatPrice(isAnnual ? plan.annualPrice / 12 : plan.monthlyPrice)}
            </p>
            <p class="text-muted-foreground">
              per month{isAnnual ? ', billed annually' : ''}
            </p>
            {#if isAnnual && plan.annualPrice > 0}
              <p class="mt-1 text-xs text-green-500">
                Save {calculateSavings(plan.monthlyPrice, plan.annualPrice)}% with annual billing
              </p>
            {/if}

            <p class="text-muted-foreground mt-4 text-sm">{plan.description}</p>

            <div class="mt-4">
              <ul class="space-y-2 text-sm">
                <li class="flex items-center">
                  <Check class="mr-2 h-4 w-4 text-green-500" />
                  {getFeatureLimit(plan, 'resume_scanner', 'resume_scans_per_month') || 10} resumes per
                  month
                </li>
                {#if getFeatureLimit(plan, 'team', 'seats')}
                  <li class="flex items-center">
                    <Check class="mr-2 h-4 w-4 text-green-500" />
                    {getFeatureLimit(plan, 'team', 'seats')}
                    {getFeatureLimit(plan, 'team', 'seats') === 1 ? 'seat' : 'seats'}
                  </li>
                {/if}
              </ul>

              <div class="mt-2">
                <PlanFeaturesList {plan} compact={true} />
              </div>
            </div>

            <Button
              variant={plan.id === 'power' || plan.id === 'startup' ? 'default' : 'outline'}
              class="mt-6 w-full"
              disabled={currentPlanId === plan.id || isLoading}
              onclick={() => onSelectPlan(plan.id, billingCycle)}>
              {#if isLoading}
                <Loader2 class="mr-2 h-4 w-4 animate-spin" />
                Loading...
              {:else if currentPlanId === plan.id}
                Current Plan
              {:else if plan.id === 'free' || (currentPlanId && getCurrentPlan() && plan.monthlyPrice < getCurrentPlan()?.monthlyPrice)}
                Downgrade
              {:else}
                Upgrade
              {/if}
            </Button>
          </div>
        {/if}
      {/each}
    </div>

    <Dialog.Footer>
      <Button variant="outline" onclick={onClose}>Cancel</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
