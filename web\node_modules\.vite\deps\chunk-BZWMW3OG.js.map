{"version": 3, "sources": ["../../@internationalized/date/dist/packages/@internationalized/date/src/utils.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/GregorianCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/weekStartData.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/queries.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/conversion.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/manipulation.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/string.ts", "../../@swc/helpers/esm/_check_private_redeclaration.js", "../../@swc/helpers/esm/_class_private_field_init.js", "../../@internationalized/date/dist/packages/@internationalized/date/src/CalendarDate.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/JapaneseCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/BuddhistCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/TaiwanCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/PersianCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/IndianCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/IslamicCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/HebrewCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/EthiopicCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/createCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/DateFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport type Mutable<T> = {\n  -readonly[P in keyof T]: T[P]\n};\n\nexport function mod(amount: number, numerator: number): number {\n  return amount - numerator * Math.floor(amount / numerator);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst EPOCH = 1721426; // 001/01/03 Julian C.E.\nexport function gregorianToJulianDay(era: string, year: number, month: number, day: number): number {\n  year = getExtendedYear(era, year);\n\n  let y1 = year - 1;\n  let monthOffset = -2;\n  if (month <= 2) {\n    monthOffset = 0;\n  } else if (isLeapYear(year)) {\n    monthOffset = -1;\n  }\n\n  return (\n    EPOCH -\n    1 +\n    365 * y1 +\n    Math.floor(y1 / 4) -\n    Math.floor(y1 / 100) +\n    Math.floor(y1 / 400) +\n    Math.floor((367 * month - 362) / 12 + monthOffset + day)\n  );\n}\n\nexport function isLeapYear(year: number): boolean {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function getExtendedYear(era: string, year: number): number {\n  return era === 'BC' ? 1 - year : year;\n}\n\nexport function fromExtendedYear(year: number): [string, number] {\n  let era = 'AD';\n  if (year <= 0) {\n    era = 'BC';\n    year = 1 - year;\n  }\n\n  return [era, year];\n}\n\nconst daysInMonth = {\n  standard: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\n  leapyear: [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]\n};\n\n/**\n * The Gregorian calendar is the most commonly used calendar system in the world. It supports two eras: BC, and AD.\n * Years always contain 12 months, and 365 or 366 days depending on whether it is a leap year.\n */\nexport class GregorianCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'gregory';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let jd0 = jd;\n    let depoch = jd0 - EPOCH;\n    let quadricent = Math.floor(depoch / 146097);\n    let dqc = mod(depoch, 146097);\n    let cent = Math.floor(dqc / 36524);\n    let dcent = mod(dqc, 36524);\n    let quad = Math.floor(dcent / 1461);\n    let dquad = mod(dcent, 1461);\n    let yindex = Math.floor(dquad / 365);\n\n    let extendedYear = quadricent * 400 + cent * 100 + quad * 4 + yindex + (cent !== 4 && yindex !== 4 ? 1 : 0);\n    let [era, year] = fromExtendedYear(extendedYear);\n    let yearDay = jd0 - gregorianToJulianDay(era, year, 1, 1);\n    let leapAdj = 2;\n    if (jd0 < gregorianToJulianDay(era, year, 3, 1)) {\n      leapAdj = 0;\n    } else if (isLeapYear(year)) {\n      leapAdj = 1;\n    }\n    let month = Math.floor(((yearDay + leapAdj) * 12 + 373) / 367);\n    let day = jd0 - gregorianToJulianDay(era, year, month, 1) + 1;\n\n    return new CalendarDate(era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return gregorianToJulianDay(date.era, date.year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return daysInMonth[isLeapYear(date.year) ? 'leapyear' : 'standard'][date.month - 1];\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 366 : 365;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getYearsInEra(date: AnyCalendarDate): number {\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['BC', 'AD'];\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'BC';\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    if (date.year <= 0) {\n      date.era = date.era === 'BC' ? 'AD' : 'BC';\n      date.year = 1 - date.year;\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Data from https://github.com/unicode-cldr/cldr-core/blob/master/supplemental/weekData.json\n// Locales starting on Sunday have been removed for compression.\nexport const weekStartData = {\n  '001': 1,\n  AD: 1,\n  AE: 6,\n  AF: 6,\n  AI: 1,\n  AL: 1,\n  AM: 1,\n  AN: 1,\n  AR: 1,\n  AT: 1,\n  AU: 1,\n  AX: 1,\n  AZ: 1,\n  BA: 1,\n  BE: 1,\n  BG: 1,\n  BH: 6,\n  BM: 1,\n  BN: 1,\n  BY: 1,\n  CH: 1,\n  CL: 1,\n  CM: 1,\n  CN: 1,\n  CR: 1,\n  CY: 1,\n  CZ: 1,\n  DE: 1,\n  DJ: 6,\n  DK: 1,\n  DZ: 6,\n  EC: 1,\n  EE: 1,\n  EG: 6,\n  ES: 1,\n  FI: 1,\n  FJ: 1,\n  FO: 1,\n  FR: 1,\n  GB: 1,\n  GE: 1,\n  GF: 1,\n  GP: 1,\n  GR: 1,\n  HR: 1,\n  HU: 1,\n  IE: 1,\n  IQ: 6,\n  IR: 6,\n  IS: 1,\n  IT: 1,\n  JO: 6,\n  KG: 1,\n  KW: 6,\n  KZ: 1,\n  LB: 1,\n  LI: 1,\n  LK: 1,\n  LT: 1,\n  LU: 1,\n  LV: 1,\n  LY: 6,\n  MC: 1,\n  MD: 1,\n  ME: 1,\n  MK: 1,\n  MN: 1,\n  MQ: 1,\n  MV: 5,\n  MY: 1,\n  NL: 1,\n  NO: 1,\n  NZ: 1,\n  OM: 6,\n  PL: 1,\n  QA: 6,\n  RE: 1,\n  RO: 1,\n  RS: 1,\n  RU: 1,\n  SD: 6,\n  SE: 1,\n  SI: 1,\n  SK: 1,\n  SM: 1,\n  SY: 6,\n  TJ: 1,\n  TM: 1,\n  TR: 1,\n  UA: 1,\n  UY: 1,\n  UZ: 1,\n  VA: 1,\n  VN: 1,\n  XK: 1\n};\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyCalendarDate, AnyTime, Calendar} from './types';\nimport {CalendarDate, CalendarDateTime, ZonedDateTime} from './CalendarDate';\nimport {fromAbsolute, toAbsolute, toCalendar, toCalendarDate} from './conversion';\nimport {weekStartData} from './weekStartData';\n\ntype DateValue = CalendarDate | CalendarDateTime | ZonedDateTime;\n\n/** Returns whether the given dates occur on the same day, regardless of the time or calendar system. */\nexport function isSameDay(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  return a.era === b.era && a.year === b.year && a.month === b.month && a.day === b.day;\n}\n\n/** Returns whether the given dates occur in the same month, using the calendar system of the first date. */\nexport function isSameMonth(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  // In the Japanese calendar, months can span multiple eras/years, so only compare the first of the month.\n  a = startOfMonth(a);\n  b = startOfMonth(b);\n  return a.era === b.era && a.year === b.year && a.month === b.month;\n}\n\n/** Returns whether the given dates occur in the same year, using the calendar system of the first date. */\nexport function isSameYear(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  a = startOfYear(a);\n  b = startOfYear(b);\n  return a.era === b.era && a.year === b.year;\n}\n\n/** Returns whether the given dates occur on the same day, and are of the same calendar system. */\nexport function isEqualDay(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameDay(a, b);\n}\n\n/** Returns whether the given dates occur in the same month, and are of the same calendar system. */\nexport function isEqualMonth(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameMonth(a, b);\n}\n\n/** Returns whether the given dates occur in the same year, and are of the same calendar system. */\nexport function isEqualYear(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameYear(a, b);\n}\n\n/** Returns whether two calendars are the same. */\nexport function isEqualCalendar(a: Calendar, b: Calendar): boolean {\n  return a.isEqual?.(b) ?? b.isEqual?.(a) ?? a.identifier === b.identifier;\n}\n\n/** Returns whether the date is today in the given time zone. */\nexport function isToday(date: DateValue, timeZone: string): boolean {\n  return isSameDay(date, today(timeZone));\n}\n\nconst DAY_MAP = {\n  sun: 0,\n  mon: 1,\n  tue: 2,\n  wed: 3,\n  thu: 4,\n  fri: 5,\n  sat: 6\n};\n\ntype DayOfWeek = 'sun' | 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat';\n\n/**\n * Returns the day of week for the given date and locale. Days are numbered from zero to six,\n * where zero is the first day of the week in the given locale. For example, in the United States,\n * the first day of the week is Sunday, but in France it is Monday.\n */\nexport function getDayOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): number {\n  let julian = date.calendar.toJulianDay(date);\n\n  // If julian is negative, then julian % 7 will be negative, so we adjust\n  // accordingly.  Julian day 0 is Monday.\n  let weekStart = firstDayOfWeek ? DAY_MAP[firstDayOfWeek] : getWeekStart(locale);\n  let dayOfWeek = Math.ceil(julian + 1 - weekStart) % 7;\n  if (dayOfWeek < 0) {\n    dayOfWeek += 7;\n  }\n\n  return dayOfWeek;\n}\n\n/** Returns the current time in the given time zone. */\nexport function now(timeZone: string): ZonedDateTime {\n  return fromAbsolute(Date.now(), timeZone);\n}\n\n/** Returns today's date in the given time zone. */\nexport function today(timeZone: string): CalendarDate {\n  return toCalendarDate(now(timeZone));\n}\n\nexport function compareDate(a: AnyCalendarDate, b: AnyCalendarDate): number {\n  return a.calendar.toJulianDay(a) - b.calendar.toJulianDay(b);\n}\n\nexport function compareTime(a: AnyTime, b: AnyTime): number {\n  return timeToMs(a) - timeToMs(b);\n}\n\nfunction timeToMs(a: AnyTime): number {\n  return a.hour * 60 * 60 * 1000 + a.minute * 60 * 1000 + a.second * 1000 + a.millisecond;\n}\n\n/**\n * Returns the number of hours in the given date and time zone.\n * Usually this is 24, but it could be 23 or 25 if the date is on a daylight saving transition.\n */\nexport function getHoursInDay(a: CalendarDate, timeZone: string): number {\n  let ms = toAbsolute(a, timeZone);\n  let tomorrow = a.add({days: 1});\n  let tomorrowMs = toAbsolute(tomorrow, timeZone);\n  return (tomorrowMs - ms) / 3600000;\n}\n\nlet localTimeZone: string | null = null;\n\n/** Returns the time zone identifier for the current user. */\nexport function getLocalTimeZone(): string {\n  // TODO: invalidate this somehow?\n  if (localTimeZone == null) {\n    localTimeZone = new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  return localTimeZone!;\n}\n\n/** Returns the first date of the month for the given date. */\nexport function startOfMonth(date: ZonedDateTime): ZonedDateTime;\nexport function startOfMonth(date: CalendarDateTime): CalendarDateTime;\nexport function startOfMonth(date: CalendarDate): CalendarDate;\nexport function startOfMonth(date: DateValue): DateValue;\nexport function startOfMonth(date: DateValue): DateValue {\n  // Use `subtract` instead of `set` so we don't get constrained in an era.\n  return date.subtract({days: date.day - 1});\n}\n\n/** Returns the last date of the month for the given date. */\nexport function endOfMonth(date: ZonedDateTime): ZonedDateTime;\nexport function endOfMonth(date: CalendarDateTime): CalendarDateTime;\nexport function endOfMonth(date: CalendarDate): CalendarDate;\nexport function endOfMonth(date: DateValue): DateValue;\nexport function endOfMonth(date: DateValue): DateValue {\n  return date.add({days: date.calendar.getDaysInMonth(date) - date.day});\n}\n\n/** Returns the first day of the year for the given date. */\nexport function startOfYear(date: ZonedDateTime): ZonedDateTime;\nexport function startOfYear(date: CalendarDateTime): CalendarDateTime;\nexport function startOfYear(date: CalendarDate): CalendarDate;\nexport function startOfYear(date: DateValue): DateValue;\nexport function startOfYear(date: DateValue): DateValue {\n  return startOfMonth(date.subtract({months: date.month - 1}));\n}\n\n/** Returns the last day of the year for the given date. */\nexport function endOfYear(date: ZonedDateTime): ZonedDateTime;\nexport function endOfYear(date: CalendarDateTime): CalendarDateTime;\nexport function endOfYear(date: CalendarDate): CalendarDate;\nexport function endOfYear(date: DateValue): DateValue;\nexport function endOfYear(date: DateValue): DateValue {\n  return endOfMonth(date.add({months: date.calendar.getMonthsInYear(date) - date.month}));\n}\n\nexport function getMinimumMonthInYear(date: AnyCalendarDate): number {\n  if (date.calendar.getMinimumMonthInYear) {\n    return date.calendar.getMinimumMonthInYear(date);\n  }\n\n  return 1;\n}\n\nexport function getMinimumDayInMonth(date: AnyCalendarDate): number {\n  if (date.calendar.getMinimumDayInMonth) {\n    return date.calendar.getMinimumDayInMonth(date);\n  }\n\n  return 1;\n}\n\n/** Returns the first date of the week for the given date and locale. */\nexport function startOfWeek(date: ZonedDateTime, locale: string, firstDayOfWeek?: DayOfWeek): ZonedDateTime;\nexport function startOfWeek(date: CalendarDateTime, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDateTime;\nexport function startOfWeek(date: CalendarDate, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDate;\nexport function startOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue;\nexport function startOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue {\n  let dayOfWeek = getDayOfWeek(date, locale, firstDayOfWeek);\n  return date.subtract({days: dayOfWeek});\n}\n\n/** Returns the last date of the week for the given date and locale. */\nexport function endOfWeek(date: ZonedDateTime, locale: string, firstDayOfWeek?: DayOfWeek): ZonedDateTime;\nexport function endOfWeek(date: CalendarDateTime, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDateTime;\nexport function endOfWeek(date: CalendarDate, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDate;\nexport function endOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue;\nexport function endOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue {\n  return startOfWeek(date, locale, firstDayOfWeek).add({days: 6});\n}\n\nconst cachedRegions = new Map<string, string>();\n\nfunction getRegion(locale: string): string | undefined {\n  // If the Intl.Locale API is available, use it to get the region for the locale.\n  // @ts-ignore\n  if (Intl.Locale) {\n    // Constructing an Intl.Locale is expensive, so cache the result.\n    let region = cachedRegions.get(locale);\n    if (!region) {\n      // @ts-ignore\n      region = new Intl.Locale(locale).maximize().region;\n      if (region) {\n        cachedRegions.set(locale, region);\n      }\n    }\n    return region;\n  }\n\n  // If not, just try splitting the string.\n  // If the second part of the locale string is 'u',\n  // then this is a unicode extension, so ignore it.\n  // Otherwise, it should be the region.\n  let part = locale.split('-')[1];\n  return part === 'u' ? undefined : part;\n}\n\nfunction getWeekStart(locale: string): number {\n  // TODO: use Intl.Locale for this once browsers support the weekInfo property\n  // https://github.com/tc39/proposal-intl-locale-info\n  let region = getRegion(locale);\n  return region ? weekStartData[region] || 0 : 0;\n}\n\n/** Returns the number of weeks in the given month and locale. */\nexport function getWeeksInMonth(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): number {\n  let days = date.calendar.getDaysInMonth(date);\n  return Math.ceil((getDayOfWeek(startOfMonth(date), locale, firstDayOfWeek) + days) / 7);\n}\n\n/** Returns the lesser of the two provider dates. */\nexport function minDate<A extends DateValue, B extends DateValue>(a?: A | null, b?: B | null): A | B | null | undefined {\n  if (a && b) {\n    return a.compare(b) <= 0 ? a : b;\n  }\n\n  return a || b;\n}\n\n/** Returns the greater of the two provider dates. */\nexport function maxDate<A extends DateValue, B extends DateValue>(a?: A | null, b?: B | null): A | B | null | undefined {\n  if (a && b) {\n    return a.compare(b) >= 0 ? a : b;\n  }\n\n  return a || b;\n}\n\nconst WEEKEND_DATA = {\n  AF: [4, 5],\n  AE: [5, 6],\n  BH: [5, 6],\n  DZ: [5, 6],\n  EG: [5, 6],\n  IL: [5, 6],\n  IQ: [5, 6],\n  IR: [5, 5],\n  JO: [5, 6],\n  KW: [5, 6],\n  LY: [5, 6],\n  OM: [5, 6],\n  QA: [5, 6],\n  SA: [5, 6],\n  SD: [5, 6],\n  SY: [5, 6],\n  YE: [5, 6]\n};\n\n/** Returns whether the given date is on a weekend in the given locale. */\nexport function isWeekend(date: DateValue, locale: string): boolean {\n  let julian = date.calendar.toJulianDay(date);\n\n  // If julian is negative, then julian % 7 will be negative, so we adjust\n  // accordingly.  Julian day 0 is Monday.\n  let dayOfWeek = Math.ceil(julian + 1) % 7;\n  if (dayOfWeek < 0) {\n    dayOfWeek += 7;\n  }\n\n  let region = getRegion(locale);\n  // Use Intl.Locale for this once weekInfo is supported.\n  // https://github.com/tc39/proposal-intl-locale-info\n  let [start, end] = WEEKEND_DATA[region!] || [6, 0];\n  return dayOfWeek === start || dayOfWeek === end;\n}\n\n/** Returns whether the given date is on a weekday in the given locale. */\nexport function isWeekday(date: DateValue, locale: string): boolean {\n  return !isWeekend(date, locale);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from the TC39 Temporal proposal.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, AnyDateTime, AnyTime, Calendar, DateFields, Disambiguation, TimeFields} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {constrain} from './manipulation';\nimport {getExtendedYear, GregorianCalendar} from './calendars/GregorianCalendar';\nimport {getLocalTimeZone, isEqualCalendar} from './queries';\nimport {Mutable} from './utils';\n\nexport function epochFromDate(date: AnyDateTime): number {\n  date = toCalendar(date, new GregorianCalendar());\n  let year = getExtendedYear(date.era, date.year);\n  return epochFromParts(year, date.month, date.day, date.hour, date.minute, date.second, date.millisecond);\n}\n\nfunction epochFromParts(year: number, month: number, day: number, hour: number, minute: number, second: number, millisecond: number): number {\n  // Note: Date.UTC() interprets one and two-digit years as being in the\n  // 20th century, so don't use it\n  let date = new Date();\n  date.setUTCHours(hour, minute, second, millisecond);\n  date.setUTCFullYear(year, month - 1, day);\n  return date.getTime();\n}\n\nexport function getTimeZoneOffset(ms: number, timeZone: string): number {\n  // Fast path for UTC.\n  if (timeZone === 'UTC') {\n    return 0;\n  }\n\n  // Fast path: for local timezone after 1970, use native Date.\n  if (ms > 0 && timeZone === getLocalTimeZone()) {\n    return new Date(ms).getTimezoneOffset() * -60 * 1000;\n  }\n\n  let {year, month, day, hour, minute, second} = getTimeZoneParts(ms, timeZone);\n  let utc = epochFromParts(year, month, day, hour, minute, second, 0);\n  return utc - Math.floor(ms / 1000) * 1000;\n}\n\nconst formattersByTimeZone = new Map<string, Intl.DateTimeFormat>();\n\nfunction getTimeZoneParts(ms: number, timeZone: string) {\n  let formatter = formattersByTimeZone.get(timeZone);\n  if (!formatter) {\n    formatter = new Intl.DateTimeFormat('en-US', {\n      timeZone,\n      hour12: false,\n      era: 'short',\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric',\n      hour: 'numeric',\n      minute: 'numeric',\n      second: 'numeric'\n    });\n\n    formattersByTimeZone.set(timeZone, formatter);\n  }\n\n  let parts = formatter.formatToParts(new Date(ms));\n  let namedParts: {[name: string]: string} = {};\n  for (let part of parts) {\n    if (part.type !== 'literal') {\n      namedParts[part.type] = part.value;\n    }\n  }\n\n\n  return {\n    // Firefox returns B instead of BC... https://bugzilla.mozilla.org/show_bug.cgi?id=1752253\n    year: namedParts.era === 'BC' || namedParts.era === 'B' ? -namedParts.year + 1 : +namedParts.year,\n    month: +namedParts.month,\n    day: +namedParts.day,\n    hour: namedParts.hour === '24' ? 0 : +namedParts.hour, // bugs.chromium.org/p/chromium/issues/detail?id=1045791\n    minute: +namedParts.minute,\n    second: +namedParts.second\n  };\n}\n\nconst DAYMILLIS = 86400000;\n\nexport function possibleAbsolutes(date: CalendarDateTime, timeZone: string): number[] {\n  let ms = epochFromDate(date);\n  let earlier = ms - getTimeZoneOffset(ms - DAYMILLIS, timeZone);\n  let later = ms - getTimeZoneOffset(ms + DAYMILLIS, timeZone);\n  return getValidWallTimes(date, timeZone, earlier, later);\n}\n\nfunction getValidWallTimes(date: CalendarDateTime, timeZone: string, earlier: number, later: number): number[] {\n  let found = earlier === later ? [earlier] : [earlier, later];\n  return found.filter(absolute => isValidWallTime(date, timeZone, absolute));\n}\n\nfunction isValidWallTime(date: CalendarDateTime, timeZone: string, absolute: number) {\n  let parts = getTimeZoneParts(absolute, timeZone);\n  return date.year === parts.year\n    && date.month === parts.month\n    && date.day === parts.day\n    && date.hour === parts.hour\n    && date.minute === parts.minute\n    && date.second === parts.second;\n}\n\nexport function toAbsolute(date: CalendarDate | CalendarDateTime, timeZone: string, disambiguation: Disambiguation = 'compatible'): number {\n  let dateTime = toCalendarDateTime(date);\n\n  // Fast path: if the time zone is UTC, use native Date.\n  if (timeZone === 'UTC') {\n    return epochFromDate(dateTime);\n  }\n\n  // Fast path: if the time zone is the local timezone and disambiguation is compatible, use native Date.\n  if (timeZone === getLocalTimeZone() && disambiguation === 'compatible') {\n    dateTime = toCalendar(dateTime, new GregorianCalendar());\n\n    // Don't use Date constructor here because two-digit years are interpreted in the 20th century.\n    let date = new Date();\n    let year = getExtendedYear(dateTime.era, dateTime.year);\n    date.setFullYear(year, dateTime.month - 1, dateTime.day);\n    date.setHours(dateTime.hour, dateTime.minute, dateTime.second, dateTime.millisecond);\n    return date.getTime();\n  }\n\n  let ms = epochFromDate(dateTime);\n  let offsetBefore = getTimeZoneOffset(ms - DAYMILLIS, timeZone);\n  let offsetAfter = getTimeZoneOffset(ms + DAYMILLIS, timeZone);\n  let valid = getValidWallTimes(dateTime, timeZone, ms - offsetBefore, ms - offsetAfter);\n\n  if (valid.length === 1) {\n    return valid[0];\n  }\n\n  if (valid.length > 1) {\n    switch (disambiguation) {\n      // 'compatible' means 'earlier' for \"fall back\" transitions\n      case 'compatible':\n      case 'earlier':\n        return valid[0];\n      case 'later':\n        return valid[valid.length - 1];\n      case 'reject':\n        throw new RangeError('Multiple possible absolute times found');\n    }\n  }\n\n  switch (disambiguation) {\n    case 'earlier':\n      return Math.min(ms - offsetBefore, ms - offsetAfter);\n    // 'compatible' means 'later' for \"spring forward\" transitions\n    case 'compatible':\n    case 'later':\n      return Math.max(ms - offsetBefore, ms - offsetAfter);\n    case 'reject':\n      throw new RangeError('No such absolute time found');\n  }\n}\n\nexport function toDate(dateTime: CalendarDate | CalendarDateTime, timeZone: string, disambiguation: Disambiguation = 'compatible'): Date {\n  return new Date(toAbsolute(dateTime, timeZone, disambiguation));\n}\n\n/**\n * Takes a Unix epoch (milliseconds since 1970) and converts it to the provided time zone.\n */\nexport function fromAbsolute(ms: number, timeZone: string): ZonedDateTime {\n  let offset = getTimeZoneOffset(ms, timeZone);\n  let date = new Date(ms + offset);\n  let year = date.getUTCFullYear();\n  let month = date.getUTCMonth() + 1;\n  let day = date.getUTCDate();\n  let hour = date.getUTCHours();\n  let minute = date.getUTCMinutes();\n  let second = date.getUTCSeconds();\n  let millisecond = date.getUTCMilliseconds();\n\n  return new ZonedDateTime(year < 1 ? 'BC' : 'AD', year < 1 ? -year + 1 : year, month, day, timeZone, offset, hour, minute, second, millisecond);\n}\n\n/**\n * Takes a `Date` object and converts it to the provided time zone.\n */\nexport function fromDate(date: Date, timeZone: string): ZonedDateTime {\n  return fromAbsolute(date.getTime(), timeZone);\n}\n\nexport function fromDateToLocal(date: Date): ZonedDateTime {\n  return fromDate(date, getLocalTimeZone());\n}\n\n/** Converts a value with date components such as a `CalendarDateTime` or `ZonedDateTime` into a `CalendarDate`. */\nexport function toCalendarDate(dateTime: AnyCalendarDate): CalendarDate {\n  return new CalendarDate(dateTime.calendar, dateTime.era, dateTime.year, dateTime.month, dateTime.day);\n}\n\nexport function toDateFields(date: AnyCalendarDate): DateFields {\n  return {\n    era: date.era,\n    year: date.year,\n    month: date.month,\n    day: date.day\n  };\n}\n\nexport function toTimeFields(date: AnyTime): TimeFields {\n  return {\n    hour: date.hour,\n    minute: date.minute,\n    second: date.second,\n    millisecond: date.millisecond\n  };\n}\n\n/**\n * Converts a date value to a `CalendarDateTime`. An optional `Time` value can be passed to set the time\n * of the resulting value, otherwise it will default to midnight.\n */\nexport function toCalendarDateTime(date: CalendarDate | CalendarDateTime | ZonedDateTime, time?: AnyTime): CalendarDateTime {\n  let hour = 0, minute = 0, second = 0, millisecond = 0;\n  if ('timeZone' in date) {\n    ({hour, minute, second, millisecond} = date);\n  } else if ('hour' in date && !time) {\n    return date;\n  }\n\n  if (time) {\n    ({hour, minute, second, millisecond} = time);\n  }\n\n  return new CalendarDateTime(\n    date.calendar,\n    date.era,\n    date.year,\n    date.month,\n    date.day,\n    hour,\n    minute,\n    second,\n    millisecond\n  );\n}\n\n/** Extracts the time components from a value containing a date and time. */\nexport function toTime(dateTime: CalendarDateTime | ZonedDateTime): Time {\n  return new Time(dateTime.hour, dateTime.minute, dateTime.second, dateTime.millisecond);\n}\n\n/** Converts a date from one calendar system to another. */\nexport function toCalendar<T extends AnyCalendarDate>(date: T, calendar: Calendar): T {\n  if (isEqualCalendar(date.calendar, calendar)) {\n    return date;\n  }\n\n  let calendarDate = calendar.fromJulianDay(date.calendar.toJulianDay(date));\n  let copy: Mutable<T> = date.copy();\n  copy.calendar = calendar;\n  copy.era = calendarDate.era;\n  copy.year = calendarDate.year;\n  copy.month = calendarDate.month;\n  copy.day = calendarDate.day;\n  constrain(copy);\n  return copy;\n}\n\n/**\n * Converts a date value to a `ZonedDateTime` in the provided time zone. The `disambiguation` option can be set\n * to control how values that fall on daylight saving time changes are interpreted.\n */\nexport function toZoned(date: CalendarDate | CalendarDateTime | ZonedDateTime, timeZone: string, disambiguation?: Disambiguation): ZonedDateTime {\n  if (date instanceof ZonedDateTime) {\n    if (date.timeZone === timeZone) {\n      return date;\n    }\n\n    return toTimeZone(date, timeZone);\n  }\n\n  let ms = toAbsolute(date, timeZone, disambiguation);\n  return fromAbsolute(ms, timeZone);\n}\n\nexport function zonedToDate(date: ZonedDateTime): Date {\n  let ms = epochFromDate(date) - date.offset;\n  return new Date(ms);\n}\n\n/** Converts a `ZonedDateTime` from one time zone to another. */\nexport function toTimeZone(date: ZonedDateTime, timeZone: string): ZonedDateTime {\n  let ms = epochFromDate(date) - date.offset;\n  return toCalendar(fromAbsolute(ms, timeZone), date.calendar);\n}\n\n/** Converts the given `ZonedDateTime` into the user's local time zone. */\nexport function toLocalTimeZone(date: ZonedDateTime): ZonedDateTime {\n  return toTimeZone(date, getLocalTimeZone());\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyCalendarDate, AnyDateTime, AnyTime, CycleOptions, CycleTimeOptions, DateDuration, DateField, DateFields, DateTimeDuration, Disambiguation, TimeDuration, TimeField, TimeFields} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, toAbsolute, toCalendar, toCalendarDateTime} from './conversion';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mutable} from './utils';\n\nconst ONE_HOUR = 3600000;\n\nexport function add(date: CalendarDateTime, duration: DateTimeDuration): CalendarDateTime;\nexport function add(date: CalendarDate, duration: DateDuration): CalendarDate;\nexport function add(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): CalendarDate | CalendarDateTime;\nexport function add(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): Mutable<AnyCalendarDate | AnyDateTime> {\n  let mutableDate: Mutable<AnyCalendarDate | AnyDateTime> = date.copy();\n  let days = 'hour' in mutableDate ? addTimeFields(mutableDate, duration) : 0;\n\n  addYears(mutableDate, duration.years || 0);\n  if (mutableDate.calendar.balanceYearMonth) {\n    mutableDate.calendar.balanceYearMonth(mutableDate, date);\n  }\n\n  mutableDate.month += duration.months || 0;\n\n  balanceYearMonth(mutableDate);\n  constrainMonthDay(mutableDate);\n\n  mutableDate.day += (duration.weeks || 0) * 7;\n  mutableDate.day += duration.days || 0;\n  mutableDate.day += days;\n\n  balanceDay(mutableDate);\n\n  if (mutableDate.calendar.balanceDate) {\n    mutableDate.calendar.balanceDate(mutableDate);\n  }\n\n  // Constrain in case adding ended up with a date outside the valid range for the calendar system.\n  // The behavior here is slightly different than when constraining in the `set` function in that\n  // we adjust smaller fields to their minimum/maximum values rather than constraining each field\n  // individually. This matches the general behavior of `add` vs `set` regarding how fields are balanced.\n  if (mutableDate.year < 1) {\n    mutableDate.year = 1;\n    mutableDate.month = 1;\n    mutableDate.day = 1;\n  }\n\n  let maxYear = mutableDate.calendar.getYearsInEra(mutableDate);\n  if (mutableDate.year > maxYear) {\n    let isInverseEra = mutableDate.calendar.isInverseEra?.(mutableDate);\n    mutableDate.year = maxYear;\n    mutableDate.month = isInverseEra ? 1 : mutableDate.calendar.getMonthsInYear(mutableDate);\n    mutableDate.day = isInverseEra ? 1 : mutableDate.calendar.getDaysInMonth(mutableDate);\n  }\n\n  if (mutableDate.month < 1) {\n    mutableDate.month = 1;\n    mutableDate.day = 1;\n  }\n\n  let maxMonth = mutableDate.calendar.getMonthsInYear(mutableDate);\n  if (mutableDate.month > maxMonth) {\n    mutableDate.month = maxMonth;\n    mutableDate.day = mutableDate.calendar.getDaysInMonth(mutableDate);\n  }\n\n  mutableDate.day = Math.max(1, Math.min(mutableDate.calendar.getDaysInMonth(mutableDate), mutableDate.day));\n  return mutableDate;\n}\n\nfunction addYears(date: Mutable<AnyCalendarDate>, years: number) {\n  if (date.calendar.isInverseEra?.(date)) {\n    years = -years;\n  }\n\n  date.year += years;\n}\n\nfunction balanceYearMonth(date: Mutable<AnyCalendarDate>) {\n  while (date.month < 1) {\n    addYears(date, -1);\n    date.month += date.calendar.getMonthsInYear(date);\n  }\n\n  let monthsInYear = 0;\n  while (date.month > (monthsInYear = date.calendar.getMonthsInYear(date))) {\n    date.month -= monthsInYear;\n    addYears(date, 1);\n  }\n}\n\nfunction balanceDay(date: Mutable<AnyCalendarDate>) {\n  while (date.day < 1) {\n    date.month--;\n    balanceYearMonth(date);\n    date.day += date.calendar.getDaysInMonth(date);\n  }\n\n  while (date.day > date.calendar.getDaysInMonth(date)) {\n    date.day -= date.calendar.getDaysInMonth(date);\n    date.month++;\n    balanceYearMonth(date);\n  }\n}\n\nfunction constrainMonthDay(date: Mutable<AnyCalendarDate>) {\n  date.month = Math.max(1, Math.min(date.calendar.getMonthsInYear(date), date.month));\n  date.day = Math.max(1, Math.min(date.calendar.getDaysInMonth(date), date.day));\n}\n\nexport function constrain(date: Mutable<AnyCalendarDate>): void {\n  if (date.calendar.constrainDate) {\n    date.calendar.constrainDate(date);\n  }\n\n  date.year = Math.max(1, Math.min(date.calendar.getYearsInEra(date), date.year));\n  constrainMonthDay(date);\n}\n\nexport function invertDuration(duration: DateTimeDuration): DateTimeDuration {\n  let inverseDuration = {};\n  for (let key in duration) {\n    if (typeof duration[key] === 'number') {\n      inverseDuration[key] = -duration[key];\n    }\n  }\n\n  return inverseDuration;\n}\n\nexport function subtract(date: CalendarDateTime, duration: DateTimeDuration): CalendarDateTime;\nexport function subtract(date: CalendarDate, duration: DateDuration): CalendarDate;\nexport function subtract(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): CalendarDate | CalendarDateTime {\n  return add(date, invertDuration(duration));\n}\n\nexport function set(date: CalendarDateTime, fields: DateFields): CalendarDateTime;\nexport function set(date: CalendarDate, fields: DateFields): CalendarDate;\nexport function set(date: CalendarDate | CalendarDateTime, fields: DateFields): Mutable<AnyCalendarDate> {\n  let mutableDate: Mutable<AnyCalendarDate> = date.copy();\n\n  if (fields.era != null) {\n    mutableDate.era = fields.era;\n  }\n\n  if (fields.year != null) {\n    mutableDate.year = fields.year;\n  }\n\n  if (fields.month != null) {\n    mutableDate.month = fields.month;\n  }\n\n  if (fields.day != null) {\n    mutableDate.day = fields.day;\n  }\n\n  constrain(mutableDate);\n  return mutableDate;\n}\n\nexport function setTime(value: CalendarDateTime, fields: TimeFields): CalendarDateTime;\nexport function setTime(value: Time, fields: TimeFields): Time;\nexport function setTime(value: Time | CalendarDateTime, fields: TimeFields): Mutable<Time | CalendarDateTime> {\n  let mutableValue: Mutable<Time | CalendarDateTime> = value.copy();\n\n  if (fields.hour != null) {\n    mutableValue.hour = fields.hour;\n  }\n\n  if (fields.minute != null) {\n    mutableValue.minute = fields.minute;\n  }\n\n  if (fields.second != null) {\n    mutableValue.second = fields.second;\n  }\n\n  if (fields.millisecond != null) {\n    mutableValue.millisecond = fields.millisecond;\n  }\n\n  constrainTime(mutableValue);\n  return mutableValue;\n}\n\nfunction balanceTime(time: Mutable<AnyTime>): number {\n  time.second += Math.floor(time.millisecond / 1000);\n  time.millisecond = nonNegativeMod(time.millisecond, 1000);\n\n  time.minute += Math.floor(time.second / 60);\n  time.second = nonNegativeMod(time.second, 60);\n\n  time.hour += Math.floor(time.minute / 60);\n  time.minute = nonNegativeMod(time.minute, 60);\n\n  let days = Math.floor(time.hour / 24);\n  time.hour = nonNegativeMod(time.hour, 24);\n\n  return days;\n}\n\nexport function constrainTime(time: Mutable<AnyTime>): void {\n  time.millisecond = Math.max(0, Math.min(time.millisecond, 1000));\n  time.second = Math.max(0, Math.min(time.second, 59));\n  time.minute = Math.max(0, Math.min(time.minute, 59));\n  time.hour = Math.max(0, Math.min(time.hour, 23));\n}\n\nfunction nonNegativeMod(a: number, b: number) {\n  let result = a % b;\n  if (result < 0) {\n    result += b;\n  }\n  return result;\n}\n\nfunction addTimeFields(time: Mutable<AnyTime>, duration: TimeDuration): number {\n  time.hour += duration.hours || 0;\n  time.minute += duration.minutes || 0;\n  time.second += duration.seconds || 0;\n  time.millisecond += duration.milliseconds || 0;\n  return balanceTime(time);\n}\n\nexport function addTime(time: Time, duration: TimeDuration): Time {\n  let res = time.copy();\n  addTimeFields(res, duration);\n  return res;\n}\n\nexport function subtractTime(time: Time, duration: TimeDuration): Time {\n  return addTime(time, invertDuration(duration));\n}\n\nexport function cycleDate(value: CalendarDateTime, field: DateField, amount: number, options?: CycleOptions): CalendarDateTime;\nexport function cycleDate(value: CalendarDate, field: DateField, amount: number, options?: CycleOptions): CalendarDate;\nexport function cycleDate(value: CalendarDate | CalendarDateTime, field: DateField, amount: number, options?: CycleOptions): Mutable<CalendarDate | CalendarDateTime> {\n  let mutable: Mutable<CalendarDate | CalendarDateTime> = value.copy();\n\n  switch (field) {\n    case 'era': {\n      let eras = value.calendar.getEras();\n      let eraIndex = eras.indexOf(value.era);\n      if (eraIndex < 0) {\n        throw new Error('Invalid era: ' + value.era);\n      }\n      eraIndex = cycleValue(eraIndex, amount, 0, eras.length - 1, options?.round);\n      mutable.era = eras[eraIndex];\n\n      // Constrain the year and other fields within the era, so the era doesn't change when we balance below.\n      constrain(mutable);\n      break;\n    }\n    case 'year': {\n      if (mutable.calendar.isInverseEra?.(mutable)) {\n        amount = -amount;\n      }\n\n      // The year field should not cycle within the era as that can cause weird behavior affecting other fields.\n      // We need to also allow values < 1 so that decrementing goes to the previous era. If we get -Infinity back\n      // we know we wrapped around after reaching 9999 (the maximum), so set the year back to 1.\n      mutable.year = cycleValue(value.year, amount, -Infinity, 9999, options?.round);\n      if (mutable.year === -Infinity) {\n        mutable.year = 1;\n      }\n\n      if (mutable.calendar.balanceYearMonth) {\n        mutable.calendar.balanceYearMonth(mutable, value);\n      }\n      break;\n    }\n    case 'month':\n      mutable.month = cycleValue(value.month, amount, 1, value.calendar.getMonthsInYear(value), options?.round);\n      break;\n    case 'day':\n      mutable.day = cycleValue(value.day, amount, 1, value.calendar.getDaysInMonth(value), options?.round);\n      break;\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n\n  if (value.calendar.balanceDate) {\n    value.calendar.balanceDate(mutable);\n  }\n\n  constrain(mutable);\n  return mutable;\n}\n\nexport function cycleTime(value: CalendarDateTime, field: TimeField, amount: number, options?: CycleTimeOptions): CalendarDateTime;\nexport function cycleTime(value: Time, field: TimeField, amount: number, options?: CycleTimeOptions): Time;\nexport function cycleTime(value: Time | CalendarDateTime, field: TimeField, amount: number, options?: CycleTimeOptions): Mutable<Time | CalendarDateTime> {\n  let mutable: Mutable<Time | CalendarDateTime> = value.copy();\n\n  switch (field) {\n    case 'hour': {\n      let hours = value.hour;\n      let min = 0;\n      let max = 23;\n      if (options?.hourCycle === 12) {\n        let isPM = hours >= 12;\n        min = isPM ? 12 : 0;\n        max = isPM ? 23 : 11;\n      }\n      mutable.hour = cycleValue(hours, amount, min, max, options?.round);\n      break;\n    }\n    case 'minute':\n      mutable.minute = cycleValue(value.minute, amount, 0, 59, options?.round);\n      break;\n    case 'second':\n      mutable.second = cycleValue(value.second, amount, 0, 59, options?.round);\n      break;\n    case 'millisecond':\n      mutable.millisecond = cycleValue(value.millisecond, amount, 0, 999, options?.round);\n      break;\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n\n  return mutable;\n}\n\nfunction cycleValue(value: number, amount: number, min: number, max: number, round = false) {\n  if (round) {\n    value += Math.sign(amount);\n\n    if (value < min) {\n      value = max;\n    }\n\n    let div = Math.abs(amount);\n    if (amount > 0) {\n      value = Math.ceil(value / div) * div;\n    } else {\n      value = Math.floor(value / div) * div;\n    }\n\n    if (value > max) {\n      value = min;\n    }\n  } else {\n    value += amount;\n    if (value < min) {\n      value = max - (min - value - 1);\n    } else if (value > max) {\n      value = min + (value - max - 1);\n    }\n  }\n\n  return value;\n}\n\nexport function addZoned(dateTime: ZonedDateTime, duration: DateTimeDuration): ZonedDateTime {\n  let ms: number;\n  if ((duration.years != null && duration.years !== 0) || (duration.months != null && duration.months !== 0) || (duration.weeks != null && duration.weeks !== 0) || (duration.days != null && duration.days !== 0)) {\n    let res = add(toCalendarDateTime(dateTime), {\n      years: duration.years,\n      months: duration.months,\n      weeks: duration.weeks,\n      days: duration.days\n    });\n\n    // Changing the date may change the timezone offset, so we need to recompute\n    // using the 'compatible' disambiguation.\n    ms = toAbsolute(res, dateTime.timeZone);\n  } else {\n    // Otherwise, preserve the offset of the original date.\n    ms = epochFromDate(dateTime) - dateTime.offset;\n  }\n\n  // Perform time manipulation in milliseconds rather than on the original time fields to account for DST.\n  // For example, adding one hour during a DST transition may result in the hour field staying the same or\n  // skipping an hour. This results in the offset field changing value instead of the specified field.\n  ms += duration.milliseconds || 0;\n  ms += (duration.seconds || 0) * 1000;\n  ms += (duration.minutes || 0) * 60 * 1000;\n  ms += (duration.hours || 0) * 60 * 60 * 1000;\n\n  let res = fromAbsolute(ms, dateTime.timeZone);\n  return toCalendar(res, dateTime.calendar);\n}\n\nexport function subtractZoned(dateTime: ZonedDateTime, duration: DateTimeDuration): ZonedDateTime {\n  return addZoned(dateTime, invertDuration(duration));\n}\n\nexport function cycleZoned(dateTime: ZonedDateTime, field: DateField | TimeField, amount: number, options?: CycleTimeOptions): ZonedDateTime {\n  // For date fields, we want the time to remain consistent and the UTC offset to potentially change to account for DST changes.\n  // For time fields, we want the time to change by the amount given. This may result in the hour field staying the same, but the UTC\n  // offset changing in the case of a backward DST transition, or skipping an hour in the case of a forward DST transition.\n  switch (field) {\n    case 'hour': {\n      let min = 0;\n      let max = 23;\n      if (options?.hourCycle === 12) {\n        let isPM = dateTime.hour >= 12;\n        min = isPM ? 12 : 0;\n        max = isPM ? 23 : 11;\n      }\n\n      // The minimum and maximum hour may be affected by daylight saving time.\n      // For example, it might jump forward at midnight, and skip 1am.\n      // Or it might end at midnight and repeat the 11pm hour. To handle this, we get\n      // the possible absolute times for the min and max, and find the maximum range\n      // that is within the current day.\n      let plainDateTime = toCalendarDateTime(dateTime);\n      let minDate = toCalendar(setTime(plainDateTime, {hour: min}), new GregorianCalendar());\n      let minAbsolute = [toAbsolute(minDate, dateTime.timeZone, 'earlier'), toAbsolute(minDate, dateTime.timeZone, 'later')]\n        .filter(ms => fromAbsolute(ms, dateTime.timeZone).day === minDate.day)[0];\n\n      let maxDate = toCalendar(setTime(plainDateTime, {hour: max}), new GregorianCalendar());\n      let maxAbsolute = [toAbsolute(maxDate, dateTime.timeZone, 'earlier'), toAbsolute(maxDate, dateTime.timeZone, 'later')]\n        .filter(ms => fromAbsolute(ms, dateTime.timeZone).day === maxDate.day).pop()!;\n\n      // Since hours may repeat, we need to operate on the absolute time in milliseconds.\n      // This is done in hours from the Unix epoch so that cycleValue works correctly,\n      // and then converted back to milliseconds.\n      let ms = epochFromDate(dateTime) - dateTime.offset;\n      let hours = Math.floor(ms / ONE_HOUR);\n      let remainder = ms % ONE_HOUR;\n      ms = cycleValue(\n        hours,\n        amount,\n        Math.floor(minAbsolute / ONE_HOUR),\n        Math.floor(maxAbsolute / ONE_HOUR),\n        options?.round\n      ) * ONE_HOUR + remainder;\n\n      // Now compute the new timezone offset, and convert the absolute time back to local time.\n      return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n    }\n    case 'minute':\n    case 'second':\n    case 'millisecond':\n      // @ts-ignore\n      return cycleTime(dateTime, field, amount, options);\n    case 'era':\n    case 'year':\n    case 'month':\n    case 'day': {\n      let res = cycleDate(toCalendarDateTime(dateTime), field, amount, options);\n      let ms = toAbsolute(res, dateTime.timeZone);\n      return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n    }\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n}\n\nexport function setZoned(dateTime: ZonedDateTime, fields: DateFields & TimeFields, disambiguation?: Disambiguation): ZonedDateTime {\n  // Set the date/time fields, and recompute the UTC offset to account for DST changes.\n  // We also need to validate by converting back to a local time in case hours are skipped during forward DST transitions.\n  let plainDateTime = toCalendarDateTime(dateTime);\n  let res = setTime(set(plainDateTime, fields), fields);\n\n  // If the resulting plain date time values are equal, return the original time.\n  // We don't want to change the offset when setting the time to the same value.\n  if (res.compare(plainDateTime) === 0) {\n    return dateTime;\n  }\n\n  let ms = toAbsolute(res, dateTime.timeZone, disambiguation);\n  return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyDateTime, DateTimeDuration, Disambiguation} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, possibleAbsolutes, toAbsolute, toCalendar, toCalendarDateTime, toTimeZone} from './conversion';\nimport {getLocalTimeZone} from './queries';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mu<PERSON>} from './utils';\n\nconst TIME_RE = /^(\\d{2})(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst DATE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})$/;\nconst DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst ZONED_DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:([+-]\\d{2})(?::?(\\d{2}))?)?\\[(.*?)\\]$/;\nconst ABSOLUTE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:(?:([+-]\\d{2})(?::?(\\d{2}))?)|Z)$/;\nconst DATE_TIME_DURATION_RE =\n    /^((?<negative>-)|\\+)?P((?<years>\\d*)Y)?((?<months>\\d*)M)?((?<weeks>\\d*)W)?((?<days>\\d*)D)?((?<time>T)((?<hours>\\d*[.,]?\\d{1,9})H)?((?<minutes>\\d*[.,]?\\d{1,9})M)?((?<seconds>\\d*[.,]?\\d{1,9})S)?)?$/;\nconst requiredDurationTimeGroups = ['hours', 'minutes', 'seconds'];\nconst requiredDurationGroups = ['years', 'months', 'weeks', 'days', ...requiredDurationTimeGroups];\n\n/** Parses an ISO 8601 time string. */\nexport function parseTime(value: string): Time {\n  let m = value.match(TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 time string: ' + value);\n  }\n\n  return new Time(\n    parseNumber(m[1], 0, 23),\n    m[2] ? parseNumber(m[2], 0, 59) : 0,\n    m[3] ? parseNumber(m[3], 0, 59) : 0,\n    m[4] ? parseNumber(m[4], 0, Infinity) * 1000 : 0\n  );\n}\n\n/** Parses an ISO 8601 date string, with no time components. */\nexport function parseDate(value: string): CalendarDate {\n  let m = value.match(DATE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date string: ' + value);\n  }\n\n  let date: Mutable<CalendarDate> = new CalendarDate(\n    parseNumber(m[1], 0, 9999),\n    parseNumber(m[2], 1, 12),\n    1\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n  return date as CalendarDate;\n}\n\n/** Parses an ISO 8601 date and time string, with no time zone. */\nexport function parseDateTime(value: string): CalendarDateTime {\n  let m = value.match(DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<CalendarDateTime> = new CalendarDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n  return date as CalendarDateTime;\n}\n\n/**\n * Parses an ISO 8601 date and time string with a time zone extension and optional UTC offset\n * (e.g. \"2021-11-07T00:45[America/Los_Angeles]\" or \"2021-11-07T00:45-07:00[America/Los_Angeles]\").\n * Ambiguous times due to daylight saving time transitions are resolved according to the `disambiguation`\n * parameter.\n */\nexport function parseZonedDateTime(value: string, disambiguation?: Disambiguation): ZonedDateTime {\n  let m = value.match(ZONED_DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[10],\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  let plainDateTime = toCalendarDateTime(date as ZonedDateTime);\n\n  let ms: number;\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n    ms = epochFromDate(date as ZonedDateTime) - date.offset;\n\n    // Validate offset against parsed date.\n    let absolutes = possibleAbsolutes(plainDateTime, date.timeZone);\n    if (!absolutes.includes(ms)) {\n      throw new Error(`Offset ${offsetToString(date.offset)} is invalid for ${dateTimeToString(date)} in ${date.timeZone}`);\n    }\n  } else {\n    // Convert to absolute and back to fix invalid times due to DST.\n    ms = toAbsolute(toCalendarDateTime(plainDateTime), date.timeZone, disambiguation);\n  }\n\n  return fromAbsolute(ms, date.timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the provided time zone.\n */\nexport function parseAbsolute(value: string, timeZone: string): ZonedDateTime {\n  let m = value.match(ABSOLUTE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    timeZone,\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n  }\n\n  return toTimeZone(date as ZonedDateTime, timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the user's local time zone.\n */\nexport function parseAbsoluteToLocal(value: string): ZonedDateTime {\n  return parseAbsolute(value, getLocalTimeZone());\n}\n\nfunction parseNumber(value: string, min: number, max: number) {\n  let val = Number(value);\n  if (val < min || val > max) {\n    throw new RangeError(`Value out of range: ${min} <= ${val} <= ${max}`);\n  }\n\n  return val;\n}\n\nexport function timeToString(time: Time): string {\n  return `${String(time.hour).padStart(2, '0')}:${String(time.minute).padStart(2, '0')}:${String(time.second).padStart(2, '0')}${time.millisecond ? String(time.millisecond / 1000).slice(1) : ''}`;\n}\n\nexport function dateToString(date: CalendarDate): string {\n  let gregorianDate = toCalendar(date, new GregorianCalendar());\n  let year: string;\n  if (gregorianDate.era === 'BC') {\n    year = gregorianDate.year === 1 \n      ? '0000'\n      : '-' + String(Math.abs(1 - gregorianDate.year)).padStart(6, '00');\n  } else {\n    year = String(gregorianDate.year).padStart(4, '0');\n  }\n  return `${year}-${String(gregorianDate.month).padStart(2, '0')}-${String(gregorianDate.day).padStart(2, '0')}`;\n}\n\nexport function dateTimeToString(date: AnyDateTime): string {\n  // @ts-ignore\n  return `${dateToString(date)}T${timeToString(date)}`;\n}\n\nfunction offsetToString(offset: number) {\n  let sign = Math.sign(offset) < 0 ? '-' : '+';\n  offset = Math.abs(offset);\n  let offsetHours = Math.floor(offset / (60 * 60 * 1000));\n  let offsetMinutes = (offset % (60 * 60 * 1000)) / (60 * 1000);\n  return `${sign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`;\n}\n\nexport function zonedDateTimeToString(date: ZonedDateTime): string {\n  return `${dateTimeToString(date)}${offsetToString(date.offset)}[${date.timeZone}]`;\n}\n\n/**\n * Parses an ISO 8601 duration string (e.g. \"P3Y6M6W4DT12H30M5S\").\n * @param value An ISO 8601 duration string.\n * @returns A DateTimeDuration object.\n */\nexport function parseDuration(value: string): Required<DateTimeDuration> {\n  const match = value.match(DATE_TIME_DURATION_RE);\n\n  if (!match) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const parseDurationGroup = (\n    group: string | undefined,\n    isNegative: boolean\n  ): number => {\n    if (!group) {\n      return 0;\n    }\n    try {\n      const sign = isNegative ? -1 : 1;\n      return sign * Number(group.replace(',', '.'));\n    } catch {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  };\n\n  const isNegative = !!match.groups?.negative;\n\n  const hasRequiredGroups = requiredDurationGroups.some(group => match.groups?.[group]);\n\n  if (!hasRequiredGroups) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const durationStringIncludesTime = match.groups?.time;\n\n  if (durationStringIncludesTime) {\n    const hasRequiredDurationTimeGroups = requiredDurationTimeGroups.some(group => match.groups?.[group]);\n    if (!hasRequiredDurationTimeGroups) {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  }\n\n  const duration: Mutable<DateTimeDuration> = {\n    years: parseDurationGroup(match.groups?.years, isNegative),\n    months: parseDurationGroup(match.groups?.months, isNegative),\n    weeks: parseDurationGroup(match.groups?.weeks, isNegative),\n    days: parseDurationGroup(match.groups?.days, isNegative),\n    hours: parseDurationGroup(match.groups?.hours, isNegative),\n    minutes: parseDurationGroup(match.groups?.minutes, isNegative),\n    seconds: parseDurationGroup(match.groups?.seconds, isNegative)\n  };\n\n  if (duration.hours !== undefined && ((duration.hours % 1) !== 0) && (duration.minutes || duration.seconds)) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  if (duration.minutes !== undefined && ((duration.minutes % 1) !== 0) && duration.seconds) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  return duration as Required<DateTimeDuration>;\n}\n", "function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n", "import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {add, addTime, addZoned, constrain, constrainTime, cycleDate, cycleTime, cycleZoned, set, setTime, setZoned, subtract, subtractTime, subtractZoned} from './manipulation';\nimport {AnyCalendarDate, AnyTime, Calendar, CycleOptions, CycleTimeOptions, DateDuration, DateField, DateFields, DateTimeDuration, Disambiguation, TimeDuration, TimeField, TimeFields} from './types';\nimport {compareDate, compareTime} from './queries';\nimport {dateTimeToString, dateToString, timeToString, zonedDateTimeToString} from './string';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {toCalendarDateTime, toDate, toZoned, zonedToDate} from './conversion';\n\nfunction shiftArgs(args: any[]) {\n  let calendar: Calendar = typeof args[0] === 'object'\n    ? args.shift()\n    : new GregorianCalendar();\n\n  let era: string;\n  if (typeof args[0] === 'string') {\n    era = args.shift();\n  } else {\n    let eras = calendar.getEras();\n    era = eras[eras.length - 1];\n  }\n\n  let year = args.shift();\n  let month = args.shift();\n  let day = args.shift();\n\n  return [calendar, era, year, month, day];\n}\n\n/** A CalendarDate represents a date without any time components in a specific calendar system. */\nexport class CalendarDate {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // i.e. a ZonedDateTime should not be be passable to a parameter that expects CalendarDate.\n  // If that behavior is desired, use the AnyCalendarDate interface instead.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n\n  constructor(year: number, month: number, day: number);\n  constructor(era: string, year: number, month: number, day: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): CalendarDate {\n    if (this.era) {\n      return new CalendarDate(this.calendar, this.era, this.year, this.month, this.day);\n    } else {\n      return new CalendarDate(this.calendar, this.year, this.month, this.day);\n    }\n  }\n\n  /** Returns a new `CalendarDate` with the given duration added to it. */\n  add(duration: DateDuration): CalendarDate {\n    return add(this, duration);\n  }\n\n  /** Returns a new `CalendarDate` with the given duration subtracted from it. */\n  subtract(duration: DateDuration): CalendarDate {\n    return subtract(this, duration);\n  }\n\n  /** Returns a new `CalendarDate` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields): CalendarDate {\n    return set(this, fields);\n  }\n\n  /**\n   * Returns a new `CalendarDate` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField, amount: number, options?: CycleOptions): CalendarDate {\n    return cycleDate(this, field, amount, options);\n  }\n\n  /** Converts the date to a native JavaScript Date object, with the time set to midnight in the given time zone. */\n  toDate(timeZone: string): Date {\n    return toDate(this, timeZone);\n  }\n\n  /** Converts the date to an ISO 8601 formatted string. */\n  toString(): string {\n    return dateToString(this);\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: AnyCalendarDate): number {\n    return compareDate(this, b);\n  }\n}\n\n/** A Time represents a clock time without any date components. */\nexport class Time {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The hour, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n\n  constructor(\n    hour: number = 0,\n    minute: number = 0,\n    second: number = 0,\n    millisecond: number = 0\n  ) {\n    this.hour = hour;\n    this.minute = minute;\n    this.second = second;\n    this.millisecond = millisecond;\n    constrainTime(this);\n  }\n\n  /** Returns a copy of this time. */\n  copy(): Time {\n    return new Time(this.hour, this.minute, this.second, this.millisecond);\n  }\n\n  /** Returns a new `Time` with the given duration added to it. */\n  add(duration: TimeDuration): Time {\n    return addTime(this, duration);\n  }\n\n  /** Returns a new `Time` with the given duration subtracted from it. */\n  subtract(duration: TimeDuration): Time {\n    return subtractTime(this, duration);\n  }\n\n  /** Returns a new `Time` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: TimeFields): Time {\n    return setTime(this, fields);\n  }\n\n  /**\n   * Returns a new `Time` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: TimeField, amount: number, options?: CycleTimeOptions): Time {\n    return cycleTime(this, field, amount, options);\n  }\n\n  /** Converts the time to an ISO 8601 formatted string. */\n  toString(): string {\n    return timeToString(this);\n  }\n\n  /** Compares this time with another. A negative result indicates that this time is before the given one, and a positive time indicates that it is after. */\n  compare(b: AnyTime): number {\n    return compareTime(this, b);\n  }\n}\n\n/** A CalendarDateTime represents a date and time without a time zone, in a specific calendar system. */\nexport class CalendarDateTime {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n  /** The hour in the day, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n\n  constructor(year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(era: string, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n    this.hour = args.shift() || 0;\n    this.minute = args.shift() || 0;\n    this.second = args.shift() || 0;\n    this.millisecond = args.shift() || 0;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): CalendarDateTime {\n    if (this.era) {\n      return new CalendarDateTime(this.calendar, this.era, this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n    } else {\n      return new CalendarDateTime(this.calendar, this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n    }\n  }\n\n  /** Returns a new `CalendarDateTime` with the given duration added to it. */\n  add(duration: DateTimeDuration): CalendarDateTime {\n    return add(this, duration);\n  }\n\n  /** Returns a new `CalendarDateTime` with the given duration subtracted from it. */\n  subtract(duration: DateTimeDuration): CalendarDateTime {\n    return subtract(this, duration);\n  }\n\n  /** Returns a new `CalendarDateTime` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields & TimeFields): CalendarDateTime {\n    return set(setTime(this, fields), fields);\n  }\n\n  /**\n   * Returns a new `CalendarDateTime` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField | TimeField, amount: number, options?: CycleTimeOptions): CalendarDateTime {\n    switch (field) {\n      case 'era':\n      case 'year':\n      case 'month':\n      case 'day':\n        return cycleDate(this, field, amount, options);\n      default:\n        return cycleTime(this, field, amount, options);\n    }\n  }\n\n  /** Converts the date to a native JavaScript Date object in the given time zone. */\n  toDate(timeZone: string, disambiguation?: Disambiguation): Date {\n    return toDate(this, timeZone, disambiguation);\n  }\n\n  /** Converts the date to an ISO 8601 formatted string. */\n  toString(): string {\n    return dateTimeToString(this);\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: CalendarDate | CalendarDateTime | ZonedDateTime): number {\n    let res = compareDate(this, b);\n    if (res === 0) {\n      return compareTime(this, toCalendarDateTime(b));\n    }\n\n    return res;\n  }\n}\n\n/** A ZonedDateTime represents a date and time in a specific time zone and calendar system. */\nexport class ZonedDateTime {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n  /** The hour in the day, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n  /** The IANA time zone identifier that this date and time is represented in. */\n  public readonly timeZone: string;\n  /** The UTC offset for this time, in milliseconds. */\n  public readonly offset: number;\n\n  constructor(year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(era: string, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    let timeZone = args.shift();\n    let offset = args.shift();\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n    this.timeZone = timeZone;\n    this.offset = offset;\n    this.hour = args.shift() || 0;\n    this.minute = args.shift() || 0;\n    this.second = args.shift() || 0;\n    this.millisecond = args.shift() || 0;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): ZonedDateTime {\n    if (this.era) {\n      return new ZonedDateTime(this.calendar, this.era, this.year, this.month, this.day, this.timeZone, this.offset, this.hour, this.minute, this.second, this.millisecond);\n    } else {\n      return new ZonedDateTime(this.calendar, this.year, this.month, this.day, this.timeZone, this.offset, this.hour, this.minute, this.second, this.millisecond);\n    }\n  }\n\n  /** Returns a new `ZonedDateTime` with the given duration added to it. */\n  add(duration: DateTimeDuration): ZonedDateTime {\n    return addZoned(this, duration);\n  }\n\n  /** Returns a new `ZonedDateTime` with the given duration subtracted from it. */\n  subtract(duration: DateTimeDuration): ZonedDateTime {\n    return subtractZoned(this, duration);\n  }\n\n  /** Returns a new `ZonedDateTime` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields & TimeFields, disambiguation?: Disambiguation): ZonedDateTime {\n    return setZoned(this, fields, disambiguation);\n  }\n\n  /**\n   * Returns a new `ZonedDateTime` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField | TimeField, amount: number, options?: CycleTimeOptions): ZonedDateTime {\n    return cycleZoned(this, field, amount, options);\n  }\n\n  /** Converts the date to a native JavaScript Date object. */\n  toDate(): Date {\n    return zonedToDate(this);\n  }\n\n   /** Converts the date to an ISO 8601 formatted string, including the UTC offset and time zone identifier. */\n  toString(): string {\n    return zonedDateTimeToString(this);\n  }\n\n   /** Converts the date to an ISO 8601 formatted string in UTC. */\n  toAbsoluteString(): string {\n    return this.toDate().toISOString();\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: CalendarDate | CalendarDateTime | ZonedDateTime): number {\n    // TODO: Is this a bad idea??\n    return this.toDate().getTime() - toZoned(b, this.timeZone).toDate().getTime();\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from the TC39 Temporal proposal.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {GregorianCalendar} from './GregorianCalendar';\nimport {Mutable} from '../utils';\n\nconst ERA_START_DATES = [[1868, 9, 8], [1912, 7, 30], [1926, 12, 25], [1989, 1, 8], [2019, 5, 1]];\nconst ERA_END_DATES = [[1912, 7, 29], [1926, 12, 24], [1989, 1, 7], [2019, 4, 30]];\nconst ERA_ADDENDS = [1867, 1911, 1925, 1988, 2018];\nconst ERA_NAMES = ['meiji', 'taisho', 'showa', 'heisei', 'reiwa'];\n\nfunction findEraFromGregorianDate(date: AnyCalendarDate) {\n  const idx = ERA_START_DATES.findIndex(([year, month, day]) => {\n    if (date.year < year) {\n      return true;\n    }\n\n    if (date.year === year && date.month < month) {\n      return true;\n    }\n\n    if (date.year === year && date.month === month && date.day < day) {\n      return true;\n    }\n\n    return false;\n  });\n\n  if (idx === -1) {\n    return ERA_START_DATES.length - 1;\n  }\n\n  if (idx === 0) {\n    return 0;\n  }\n\n  return idx - 1;\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let eraAddend = ERA_ADDENDS[ERA_NAMES.indexOf(date.era)];\n  if (!eraAddend) {\n    throw new Error('Unknown era: ' + date.era);\n  }\n\n  return new CalendarDate(\n    date.year + eraAddend,\n    date.month,\n    date.day\n  );\n}\n\n/**\n * The Japanese calendar is based on the Gregorian calendar, but with eras for the reign of each Japanese emperor.\n * Whenever a new emperor ascends to the throne, a new era begins and the year starts again from 1.\n * Note that eras before 1868 (Gregorian) are not currently supported by this implementation.\n */\nexport class JapaneseCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'japanese';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let date = super.fromJulianDay(jd);\n    let era = findEraFromGregorianDate(date);\n\n    return new CalendarDate(\n      this,\n      ERA_NAMES[era],\n      date.year - ERA_ADDENDS[era],\n      date.month,\n      date.day\n    );\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    let gregorianDate = toGregorian(date);\n    let era = findEraFromGregorianDate(gregorianDate);\n\n    if (ERA_NAMES[era] !== date.era) {\n      date.era = ERA_NAMES[era];\n      date.year = gregorianDate.year - ERA_ADDENDS[era];\n    }\n\n    // Constrain in case we went before the first supported era.\n    this.constrainDate(date);\n  }\n\n  constrainDate(date: Mutable<AnyCalendarDate>): void {\n    let idx = ERA_NAMES.indexOf(date.era);\n    let end = ERA_END_DATES[idx];\n    if (end != null) {\n      let [endYear, endMonth, endDay] = end;\n\n      // Constrain the year to the maximum possible value in the era.\n      // Then constrain the month and day fields within that.\n      let maxYear = endYear - ERA_ADDENDS[idx];\n      date.year = Math.max(1, Math.min(maxYear, date.year));\n      if (date.year === maxYear) {\n        date.month = Math.min(endMonth, date.month);\n\n        if (date.month === endMonth) {\n          date.day = Math.min(endDay, date.day);\n        }\n      }\n    }\n\n    if (date.year === 1 && idx >= 0) {\n      let [, startMonth, startDay] = ERA_START_DATES[idx];\n      date.month = Math.max(startMonth, date.month);\n\n      if (date.month === startMonth) {\n        date.day = Math.max(startDay, date.day);\n      }\n    }\n  }\n\n  getEras(): string[] {\n    return ERA_NAMES;\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // Get the number of years in the era, taking into account the date's month and day fields.\n    let era = ERA_NAMES.indexOf(date.era);\n    let cur = ERA_START_DATES[era];\n    let next = ERA_START_DATES[era + 1];\n    if (next == null) {\n      // 9999 gregorian is the maximum year allowed.\n      return 9999 - cur[0] + 1;\n    }\n\n    let years = next[0] - cur[0];\n\n    if (date.month < next[1] || (date.month === next[1] && date.day < next[2])) {\n      years++;\n    }\n\n    return years;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  getMinimumMonthInYear(date: AnyCalendarDate): number {\n    let start = getMinimums(date);\n    return start ? start[1] : 1;\n  }\n\n  getMinimumDayInMonth(date: AnyCalendarDate): number {\n    let start = getMinimums(date);\n    return start && date.month === start[1] ? start[2] : 1;\n  }\n}\n\nfunction getMinimums(date: AnyCalendarDate) {\n  if (date.year === 1) {\n    let idx = ERA_NAMES.indexOf(date.era);\n    return ERA_START_DATES[idx];\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, getExtendedYear, GregorianCalendar} from './GregorianCalendar';\n\nconst BUDDHIST_ERA_START = -543;\n\n/**\n * The Buddhist calendar is the same as the Gregorian calendar, but counts years\n * starting from the birth of Buddha in 543 BC (Gregorian). It supports only one\n * era, identified as 'BE'.\n */\nexport class BuddhistCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'buddhist';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let gregorianDate = super.fromJulianDay(jd);\n    let year = getExtendedYear(gregorianDate.era, gregorianDate.year);\n    return new CalendarDate(\n      this,\n      year - BUDDHIST_ERA_START,\n      gregorianDate.month,\n      gregorianDate.day\n    );\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  getEras(): string[] {\n    return ['BE'];\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  balanceDate(): void {}\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let [era, year] = fromExtendedYear(date.year + BUDDHIST_ERA_START);\n  return new CalendarDate(\n    era,\n    year,\n    date.month,\n    date.day\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, getExtendedYear, GregorianCalendar} from './GregorianCalendar';\nimport {Mutable} from '../utils';\n\nconst TAIWAN_ERA_START = 1911;\n\nfunction gregorianYear(date: AnyCalendarDate) {\n  return date.era === 'minguo'\n    ? date.year + TAIWAN_ERA_START\n    : 1 - date.year + TAIWAN_ERA_START;\n}\n\nfunction gregorianToTaiwan(year: number): [string, number] {\n  let y = year - TAIWAN_ERA_START;\n  if (y > 0) {\n    return ['minguo', y];\n  } else {\n    return ['before_minguo', 1 - y];\n  }\n}\n\n/**\n * The Taiwanese calendar is the same as the Gregorian calendar, but years\n * are numbered starting from 1912 (Gregorian). Two eras are supported:\n * 'before_minguo' and 'minguo'.\n */\nexport class TaiwanCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'roc'; // Republic of China\n\n  fromJulianDay(jd: number): CalendarDate {\n    let date = super.fromJulianDay(jd);\n    let extendedYear = getExtendedYear(date.era, date.year);\n    let [era, year] = gregorianToTaiwan(extendedYear);\n    return new CalendarDate(this, era, year, date.month, date.day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  getEras(): string[] {\n    return ['before_minguo', 'minguo'];\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    let [era, year] = gregorianToTaiwan(gregorianYear(date));\n    date.era = era;\n    date.year = year;\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'before_minguo';\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    return date.era === 'before_minguo' ? 9999 : 9999 - TAIWAN_ERA_START;\n  }\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let [era, year] = fromExtendedYear(gregorianYear(date));\n  return new CalendarDate(\n    era,\n    year,\n    date.month,\n    date.day\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod} from '../utils';\n\nconst PERSIAN_EPOCH = 1948320;\n\n// Number of days from the start of the year to the start of each month.\nconst MONTH_START = [\n  0, // Farvardin\n  31, // Ordibehesht\n  62, // Khordad\n  93, // Tir\n  124, // <PERSON><PERSON><PERSON>\n  155, // <PERSON><PERSON><PERSON>\n  186, // Mehr\n  216, // <PERSON><PERSON>\n  246, // Azar\n  276, // Dey\n  306, // Bahman\n  336  // Esfand\n];\n\n/**\n * The Persian calendar is the main calendar used in Iran and Afghanistan. It has 12 months\n * in each year, the first 6 of which have 31 days, and the next 5 have 30 days. The 12th month\n * has either 29 or 30 days depending on whether it is a leap year. The Persian year starts\n * around the March equinox.\n */\nexport class PersianCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'persian';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let daysSinceEpoch = jd - PERSIAN_EPOCH;\n    let year = 1 + Math.floor((33 * daysSinceEpoch + 3) / 12053);\n    let farvardin1 = 365 * (year - 1) + Math.floor((8 * year + 21) / 33);\n    let dayOfYear = daysSinceEpoch - farvardin1;\n    let month = dayOfYear < 216\n      ? Math.floor(dayOfYear / 31)\n      : Math.floor((dayOfYear - 6) / 30);\n    let day = dayOfYear - MONTH_START[month] + 1;\n    return new CalendarDate(this, year, month + 1, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = PERSIAN_EPOCH - 1 + 365 * (date.year - 1) + Math.floor((8 * date.year + 21) / 33);\n    jd += MONTH_START[date.month - 1];\n    jd += date.day;\n    return jd;\n  }\n\n  getMonthsInYear(): number {\n    return 12;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.month <= 6) {\n      return 31;\n    }\n\n    if (date.month <= 11) {\n      return 30;\n    }\n\n    let isLeapYear = mod(25 * date.year + 11, 33) < 8;\n    return isLeapYear ? 30 : 29;\n  }\n\n  getEras(): string[] {\n    return ['AP'];\n  }\n\n  getYearsInEra(): number {\n    // 9378-10-10 persian is 9999-12-31 gregorian.\n    // Round down to 9377 to set the maximum full year.\n    return 9377;\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, GregorianCalendar, gregorianToJulianDay, isLeapYear} from './GregorianCalendar';\n\n// Starts in 78 AD,\nconst INDIAN_ERA_START = 78;\n\n// The Indian year starts 80 days later than the Gregorian year.\nconst INDIAN_YEAR_START = 80;\n\n/**\n * The Indian National Calendar is similar to the Gregorian calendar, but with\n * years numbered since the Saka era in 78 AD (Gregorian). There are 12 months\n * in each year, with either 30 or 31 days. Only one era identifier is supported: 'saka'.\n */\nexport class IndianCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'indian';\n\n  fromJulianDay(jd: number): CalendarDate {\n    // Gregorian date for Julian day\n    let date = super.fromJulianDay(jd);\n\n    // Year in Saka era\n    let indianYear = date.year - INDIAN_ERA_START;\n\n    // Day number in Gregorian year (starting from 0)\n    let yDay = jd - gregorianToJulianDay(date.era, date.year, 1, 1);\n\n    let leapMonth: number;\n    if (yDay < INDIAN_YEAR_START) {\n      //  Day is at the end of the preceding Saka year\n      indianYear--;\n\n      // Days in leapMonth this year, previous Gregorian year\n      leapMonth = isLeapYear(date.year - 1) ? 31 : 30;\n      yDay += leapMonth + (31 * 5) + (30 * 3) + 10;\n    } else {\n      // Days in leapMonth this year\n      leapMonth = isLeapYear(date.year) ? 31 : 30;\n      yDay -= INDIAN_YEAR_START;\n    }\n\n    let indianMonth: number;\n    let indianDay: number;\n    if (yDay < leapMonth) {\n      indianMonth = 1;\n      indianDay = yDay + 1;\n    } else {\n      let mDay = yDay - leapMonth;\n      if (mDay < (31 * 5)) {\n        indianMonth = Math.floor(mDay / 31) + 2;\n        indianDay = (mDay % 31) + 1;\n      } else {\n        mDay -= 31 * 5;\n        indianMonth = Math.floor(mDay / 30) + 7;\n        indianDay = (mDay % 30) + 1;\n      }\n    }\n\n    return new CalendarDate(this, indianYear, indianMonth, indianDay);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let extendedYear = date.year + INDIAN_ERA_START;\n    let [era, year] = fromExtendedYear(extendedYear);\n\n    let leapMonth: number;\n    let jd: number;\n    if (isLeapYear(year)) {\n      leapMonth = 31;\n      jd = gregorianToJulianDay(era, year, 3, 21);\n    } else {\n      leapMonth = 30;\n      jd = gregorianToJulianDay(era, year, 3, 22);\n    }\n\n    if (date.month === 1) {\n      return jd + date.day - 1;\n    }\n\n    jd += leapMonth + Math.min(date.month - 2, 5) * 31;\n\n    if (date.month >= 8) {\n      jd += (date.month - 7) * 30;\n    }\n\n    jd += date.day - 1;\n    return jd;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.month === 1 && isLeapYear(date.year + INDIAN_ERA_START)) {\n      return 31;\n    }\n\n    if (date.month >= 2 && date.month <= 6) {\n      return 31;\n    }\n\n    return 30;\n  }\n\n  getYearsInEra(): number {\n    // 9999-12-31 gregorian is 9920-10-10 indian.\n    // Round down to 9919 for the last full year.\n    return 9919;\n  }\n\n  getEras(): string[] {\n    return ['saka'];\n  }\n\n  balanceDate(): void {}\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\n\nconst CIVIL_EPOC = 1948440; // CE 622 July 16 Friday (Julian calendar) / CE 622 July 19 (Gregorian calendar)\nconst ASTRONOMICAL_EPOC = 1948439; // CE 622 July 15 Thursday (Julian calendar)\nconst UMALQURA_YEAR_START = 1300;\nconst UMALQURA_YEAR_END = 1600;\nconst UMALQURA_START_DAYS = 460322;\n\nfunction islamicToJulianDay(epoch: number, year: number, month: number, day: number): number {\n  return day +\n    Math.ceil(29.5 * (month - 1)) +\n    (year - 1) * 354 +\n    Math.floor((3 + 11 * year) / 30) +\n    epoch - 1;\n}\n\nfunction julianDayToIslamic(calendar: Calendar, epoch: number, jd: number) {\n  let year = Math.floor((30 * (jd - epoch) + 10646) / 10631);\n  let month = Math.min(12, Math.ceil((jd - (29 + islamicToJulianDay(epoch, year, 1, 1))) / 29.5) + 1);\n  let day = jd - islamicToJulianDay(epoch, year, month, 1) + 1;\n\n  return new CalendarDate(calendar, year, month, day);\n}\n\nfunction isLeapYear(year: number): boolean {\n  return (14 + 11 * year) % 30 < 11;\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The civil variant uses simple arithmetic rules rather than astronomical calculations to approximate\n * the traditional calendar, which is based on sighting of the crescent moon. It uses Friday, July 16 622 CE (Julian) as the epoch.\n * Each year has 12 months, with either 354 or 355 days depending on whether it is a leap year.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicCivilCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'islamic-civil';\n\n  fromJulianDay(jd: number): CalendarDate {\n    return julianDayToIslamic(this, CIVIL_EPOC, jd);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return islamicToJulianDay(CIVIL_EPOC, date.year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    let length = 29 + date.month % 2;\n    if (date.month === 12 && isLeapYear(date.year)) {\n      length++;\n    }\n\n    return length;\n  }\n\n  getMonthsInYear(): number {\n    return 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 355 : 354;\n  }\n\n  getYearsInEra(): number {\n    // 9999 gregorian\n    return 9665;\n  }\n\n  getEras(): string[] {\n    return ['AH'];\n  }\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The tabular variant uses simple arithmetic rules rather than astronomical calculations to approximate\n * the traditional calendar, which is based on sighting of the crescent moon. It uses Thursday, July 15 622 CE (Julian) as the epoch.\n * Each year has 12 months, with either 354 or 355 days depending on whether it is a leap year.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicTabularCalendar extends IslamicCivilCalendar {\n  identifier: CalendarIdentifier = 'islamic-tbla';\n\n  fromJulianDay(jd: number): CalendarDate {\n    return julianDayToIslamic(this, ASTRONOMICAL_EPOC, jd);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return islamicToJulianDay(ASTRONOMICAL_EPOC, date.year, date.month, date.day);\n  }\n}\n\n// Generated by scripts/generate-umalqura.js\nconst UMALQURA_DATA = 'qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=';\nlet UMALQURA_MONTHLENGTH: Uint16Array;\nlet UMALQURA_YEAR_START_TABLE: Uint32Array;\n\nfunction umalquraYearStart(year: number): number {\n  return UMALQURA_START_DAYS + UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START];\n}\n\nfunction umalquraMonthLength(year: number, month: number): number {\n  let idx = (year - UMALQURA_YEAR_START);\n  let mask = (0x01 << (11 - (month - 1)));\n  if ((UMALQURA_MONTHLENGTH[idx] & mask) === 0) {\n    return 29;\n  } else {\n    return 30;\n  }\n}\n\nfunction umalquraMonthStart(year: number, month: number): number {\n  let day = umalquraYearStart(year);\n  for (let i = 1; i < month; i++) {\n    day += umalquraMonthLength(year, i);\n  }\n  return day;\n}\n\nfunction umalquraYearLength(year: number): number {\n  return UMALQURA_YEAR_START_TABLE[year + 1 - UMALQURA_YEAR_START] - UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START];\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The Umalqura variant is primarily used in Saudi Arabia. It is a lunar calendar, based on astronomical\n * calculations that predict the sighting of a crescent moon. Month and year lengths vary between years\n * depending on these calculations.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicUmalquraCalendar extends IslamicCivilCalendar {\n  identifier: CalendarIdentifier = 'islamic-umalqura';\n\n  constructor() {\n    super();\n    if (!UMALQURA_MONTHLENGTH) {\n      UMALQURA_MONTHLENGTH = new Uint16Array(Uint8Array.from(atob(UMALQURA_DATA), c => c.charCodeAt(0)).buffer);\n    }\n\n    if (!UMALQURA_YEAR_START_TABLE) {\n      UMALQURA_YEAR_START_TABLE = new Uint32Array(UMALQURA_YEAR_END - UMALQURA_YEAR_START + 1);\n\n      let yearStart = 0;\n      for (let year = UMALQURA_YEAR_START; year <= UMALQURA_YEAR_END; year++) {\n        UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START] = yearStart;\n        for (let i = 1; i <= 12; i++) {\n          yearStart += umalquraMonthLength(year, i);\n        }\n      }\n    }\n  }\n\n  fromJulianDay(jd: number): CalendarDate {\n    let days = jd - CIVIL_EPOC;\n    let startDays = umalquraYearStart(UMALQURA_YEAR_START);\n    let endDays = umalquraYearStart(UMALQURA_YEAR_END);\n    if (days < startDays || days > endDays) {\n      return super.fromJulianDay(jd);\n    } else {\n      let y = UMALQURA_YEAR_START - 1;\n      let m = 1;\n      let d = 1;\n      while (d > 0) {\n        y++;\n        d = days - umalquraYearStart(y) + 1;\n        let yearLength = umalquraYearLength(y);\n        if (d === yearLength) {\n          m = 12;\n          break;\n        } else if (d < yearLength) {\n          let monthLength = umalquraMonthLength(y, m);\n          m = 1;\n          while (d > monthLength) {\n            d -= monthLength;\n            m++;\n            monthLength = umalquraMonthLength(y, m);\n          }\n          break;\n        }\n      }\n\n      return new CalendarDate(this, y, m, (days - umalquraMonthStart(y, m) + 1));\n    }\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.toJulianDay(date);\n    }\n\n    return CIVIL_EPOC + umalquraMonthStart(date.year, date.month) + (date.day - 1);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.getDaysInMonth(date);\n    }\n\n    return umalquraMonthLength(date.year, date.month);\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.getDaysInYear(date);\n    }\n\n    return umalquraYearLength(date.year);\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst HEBREW_EPOCH = 347997;\n\n// Hebrew date calculations are performed in terms of days, hours, and\n// \"parts\" (or halakim), which are 1/1080 of an hour, or 3 1/3 seconds.\nconst HOUR_PARTS = 1080;\nconst DAY_PARTS  = 24 * HOUR_PARTS;\n\n// An approximate value for the length of a lunar month.\n// It is used to calculate the approximate year and month of a given\n// absolute date.\nconst MONTH_DAYS = 29;\nconst MONTH_FRACT = 12 * HOUR_PARTS + 793;\nconst MONTH_PARTS = MONTH_DAYS * DAY_PARTS + MONTH_FRACT;\n\nfunction isLeapYear(year: number) {\n  return mod(year * 7 + 1, 19) < 7;\n}\n\n// Test for delay of start of new year and to avoid\n// Sunday, Wednesday, and Friday as start of the new year.\nfunction hebrewDelay1(year: number) {\n  let months = Math.floor((235 * year - 234) / 19);\n  let parts = 12084 + 13753 * months;\n  let day = months * 29 + Math.floor(parts / 25920);\n\n  if (mod(3 * (day + 1), 7) < 3) {\n    day += 1;\n  }\n\n  return day;\n}\n\n// Check for delay in start of new year due to length of adjacent years\nfunction hebrewDelay2(year: number) {\n  let last = hebrewDelay1(year - 1);\n  let present = hebrewDelay1(year);\n  let next = hebrewDelay1(year + 1);\n\n  if (next - present === 356) {\n    return 2;\n  }\n\n  if (present - last === 382) {\n    return 1;\n  }\n\n  return 0;\n}\n\nfunction startOfYear(year: number) {\n  return hebrewDelay1(year) + hebrewDelay2(year);\n}\n\nfunction getDaysInYear(year: number) {\n  return startOfYear(year + 1) - startOfYear(year);\n}\n\nfunction getYearType(year: number) {\n  let yearLength = getDaysInYear(year);\n\n  if (yearLength > 380) {\n    yearLength -= 30; // Subtract length of leap month.\n  }\n\n  switch (yearLength) {\n    case 353:\n      return 0; // deficient\n    case 354:\n      return 1; // normal\n    case 355:\n      return 2; // complete\n  }\n}\n\nfunction getDaysInMonth(year: number, month: number): number {\n  // Normalize month numbers from 1 - 13, even on non-leap years\n  if (month >= 6 && !isLeapYear(year)) {\n    month++;\n  }\n\n  // First of all, dispose of fixed-length 29 day months\n  if (month === 4 || month === 7 || month === 9 || month === 11 || month === 13) {\n    return 29;\n  }\n\n  let yearType = getYearType(year);\n\n  // If it's Heshvan, days depend on length of year\n  if (month === 2) {\n    return yearType === 2 ? 30 : 29;\n  }\n\n  // Similarly, Kislev varies with the length of year\n  if (month === 3) {\n    return yearType === 0 ? 29 : 30;\n  }\n\n  // Adar I only exists in leap years\n  if (month === 6) {\n    return isLeapYear(year) ? 30 : 0;\n  }\n\n  return 30;\n}\n\n/**\n * The Hebrew calendar is used in Israel and around the world by the Jewish faith.\n * Years include either 12 or 13 months depending on whether it is a leap year.\n * In leap years, an extra month is inserted at month 6.\n */\nexport class HebrewCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'hebrew';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let d = jd - HEBREW_EPOCH;\n    let m = (d * DAY_PARTS) / MONTH_PARTS;           // Months (approx)\n    let year = Math.floor((19 * m + 234) / 235) + 1; // Years (approx)\n    let ys = startOfYear(year);                      // 1st day of year\n    let dayOfYear = Math.floor(d - ys);\n\n    // Because of the postponement rules, it's possible to guess wrong.  Fix it.\n    while (dayOfYear < 1) {\n      year--;\n      ys = startOfYear(year);\n      dayOfYear = Math.floor(d - ys);\n    }\n\n    // Now figure out which month we're in, and the date within that month\n    let month = 1;\n    let monthStart = 0;\n    while (monthStart < dayOfYear) {\n      monthStart += getDaysInMonth(year, month);\n      month++;\n    }\n\n    month--;\n    monthStart -= getDaysInMonth(year, month);\n\n    let day = dayOfYear - monthStart;\n    return new CalendarDate(this, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = startOfYear(date.year);\n    for (let month = 1; month < date.month; month++) {\n      jd += getDaysInMonth(date.year, month);\n    }\n\n    return jd + date.day + HEBREW_EPOCH;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 13 : 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return getDaysInYear(date.year);\n  }\n\n  getYearsInEra(): number {\n    // 6239 gregorian\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['AM'];\n  }\n\n  balanceYearMonth(date: Mutable<AnyCalendarDate>, previousDate: AnyCalendarDate): void {\n    // Keep date in the same month when switching between leap years and non leap years\n    if (previousDate.year !== date.year) {\n      if (isLeapYear(previousDate.year) && !isLeapYear(date.year) && previousDate.month > 6) {\n        date.month--;\n      } else if (!isLeapYear(previousDate.year) && isLeapYear(date.year) && previousDate.month > 6) {\n        date.month++;\n      }\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {Mutable} from '../utils';\n\nconst ETHIOPIC_EPOCH = 1723856;\nconst COPTIC_EPOCH = 1824665;\n\n// The delta between Amete Alem 1 and Amete Mihret 1\n// AA 5501 = AM 1\nconst AMETE_MIHRET_DELTA = 5500;\n\nfunction ceToJulianDay(epoch: number, year: number, month: number, day: number): number {\n  return (\n    epoch                   // difference from Julian epoch to 1,1,1\n    + 365 * year            // number of days from years\n    + Math.floor(year / 4)  // extra day of leap year\n    + 30 * (month - 1)      // number of days from months (1 based)\n    + day - 1               // number of days for present month (1 based)\n  );\n}\n\nfunction julianDayToCE(epoch: number, jd: number) {\n  let year = Math.floor((4 * (jd - epoch)) / 1461);\n  let month = 1 + Math.floor((jd - ceToJulianDay(epoch, year, 1, 1)) / 30);\n  let day = jd + 1 - ceToJulianDay(epoch, year, month, 1);\n  return [year, month, day];\n}\n\nfunction getLeapDay(year: number) {\n  return Math.floor((year % 4) / 3);\n}\n\nfunction getDaysInMonth(year: number, month: number) {\n  // The Ethiopian and Coptic calendars have 13 months, 12 of 30 days each and\n  // an intercalary month at the end of the year of 5 or 6 days, depending whether\n  // the year is a leap year or not. The Leap Year follows the same rules as the\n  // Julian Calendar so that the extra month always has six days in the year before\n  // a Julian Leap Year.\n  if (month % 13 !== 0) {\n    // not intercalary month\n    return 30;\n  } else {\n    // intercalary month 5 days + possible leap day\n    return getLeapDay(year) + 5;\n  }\n}\n\n/**\n * The Ethiopic calendar system is the official calendar used in Ethiopia.\n * It includes 12 months of 30 days each, plus 5 or 6 intercalary days depending\n * on whether it is a leap year. Two eras are supported: 'AA' and 'AM'.\n */\nexport class EthiopicCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'ethiopic';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(ETHIOPIC_EPOCH, jd);\n    let era = 'AM';\n    if (year <= 0) {\n      era = 'AA';\n      year += AMETE_MIHRET_DELTA;\n    }\n\n    return new CalendarDate(this, era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'AA') {\n      year -= AMETE_MIHRET_DELTA;\n    }\n\n    return ceToJulianDay(ETHIOPIC_EPOCH, year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(): number {\n    return 13;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return 365 + getLeapDay(date.year);\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // 9999-12-31 gregorian is 9992-20-02 ethiopic.\n    // Round down to 9991 for the last full year.\n    // AA 9999-01-01 ethiopic is 4506-09-30 gregorian.\n    return date.era === 'AA' ? 9999 : 9991;\n  }\n\n  getEras(): string[] {\n    return ['AA', 'AM'];\n  }\n}\n\n/**\n * The Ethiopic (Amete Alem) calendar is the same as the modern Ethiopic calendar,\n * except years were measured from a different epoch. Only one era is supported: 'AA'.\n */\nexport class EthiopicAmeteAlemCalendar extends EthiopicCalendar {\n  identifier: CalendarIdentifier = 'ethioaa'; // also known as 'ethiopic-amete-alem' in ICU\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(ETHIOPIC_EPOCH, jd);\n    year += AMETE_MIHRET_DELTA;\n    return new CalendarDate(this, 'AA', year, month, day);\n  }\n\n  getEras(): string[] {\n    return ['AA'];\n  }\n\n  getYearsInEra(): number {\n    // 9999-13-04 ethioaa is the maximum date, which is equivalent to 4506-09-29 gregorian.\n    return 9999;\n  }\n}\n\n/**\n * The Coptic calendar is similar to the Ethiopic calendar.\n * It includes 12 months of 30 days each, plus 5 or 6 intercalary days depending\n * on whether it is a leap year. Two eras are supported: 'BCE' and 'CE'.\n */\nexport class CopticCalendar extends EthiopicCalendar {\n  identifier: CalendarIdentifier = 'coptic';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(COPTIC_EPOCH, jd);\n    let era = 'CE';\n    if (year <= 0) {\n      era = 'BCE';\n      year = 1 - year;\n    }\n\n    return new CalendarDate(this, era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'BCE') {\n      year = 1 - year;\n    }\n\n    return ceToJulianDay(COPTIC_EPOCH, year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'BCE') {\n      year = 1 - year;\n    }\n\n    return getDaysInMonth(year, date.month);\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'BCE';\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    if (date.year <= 0) {\n      date.era = date.era === 'BCE' ? 'CE' : 'BCE';\n      date.year = 1 - date.year;\n    }\n  }\n\n  getEras(): string[] {\n    return ['BCE', 'CE'];\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // 9999-12-30 gregorian is 9716-02-20 coptic.\n    // Round down to 9715 for the last full year.\n    // BCE 9999-01-01 coptic is BC 9716-06-15 gregorian.\n    return date.era === 'BCE' ? 9999 : 9715;\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {BuddhistCalendar} from './calendars/BuddhistCalendar';\nimport {Calendar, CalendarIdentifier} from './types';\nimport {CopticCalendar, EthiopicAmeteAlemCalendar, EthiopicCalendar} from './calendars/EthiopicCalendar';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {HebrewCalendar} from './calendars/HebrewCalendar';\nimport {IndianCalendar} from './calendars/IndianCalendar';\nimport {IslamicCivilCalendar, IslamicTabularCalendar, IslamicUmalquraCalendar} from './calendars/IslamicCalendar';\nimport {JapaneseCalendar} from './calendars/JapaneseCalendar';\nimport {PersianCalendar} from './calendars/PersianCalendar';\nimport {TaiwanCalendar} from './calendars/TaiwanCalendar';\n\n/** Creates a `Calendar` instance from a Unicode calendar identifier string. */\nexport function createCalendar(name: CalendarIdentifier): Calendar {\n  switch (name) {\n    case 'buddhist':\n      return new BuddhistCalendar();\n    case 'ethiopic':\n      return new EthiopicCalendar();\n    case 'ethioaa':\n      return new EthiopicAmeteAlemCalendar();\n    case 'coptic':\n      return new CopticCalendar();\n    case 'hebrew':\n      return new HebrewCalendar();\n    case 'indian':\n      return new IndianCalendar();\n    case 'islamic-civil':\n      return new IslamicCivilCalendar();\n    case 'islamic-tbla':\n      return new IslamicTabularCalendar();\n    case 'islamic-umalqura':\n      return new IslamicUmalquraCalendar();\n    case 'japanese':\n      return new JapaneseCalendar();\n    case 'persian':\n      return new PersianCalendar();\n    case 'roc':\n      return new TaiwanCalendar();\n    case 'gregory':\n    default:\n      return new GregorianCalendar();\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet formatterCache = new Map<string, Intl.DateTimeFormat>();\n\ninterface DateRangeFormatPart extends Intl.DateTimeFormatPart {\n  source: 'startRange' | 'endRange' | 'shared'\n}\n\n/** A wrapper around Intl.DateTimeFormat that fixes various browser bugs, and polyfills new features. */\nexport class DateFormatter implements Intl.DateTimeFormat {\n  private formatter: Intl.DateTimeFormat;\n  private options: Intl.DateTimeFormatOptions;\n  private resolvedHourCycle: Intl.DateTimeFormatOptions['hourCycle'];\n\n  constructor(locale: string, options: Intl.DateTimeFormatOptions = {}) {\n    this.formatter = getCachedDateFormatter(locale, options);\n    this.options = options;\n  }\n\n  /** Formats a date as a string according to the locale and format options passed to the constructor. */\n  format(value: Date): string {\n    return this.formatter.format(value);\n  }\n\n  /** Formats a date to an array of parts such as separators, numbers, punctuation, and more. */\n  formatToParts(value: Date): Intl.DateTimeFormatPart[] {\n    return this.formatter.formatToParts(value);\n  }\n\n  /** Formats a date range as a string. */\n  formatRange(start: Date, end: Date): string {\n    // @ts-ignore\n    if (typeof this.formatter.formatRange === 'function') {\n      // @ts-ignore\n      return this.formatter.formatRange(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    // Very basic fallback for old browsers.\n    return `${this.formatter.format(start)} – ${this.formatter.format(end)}`;\n  }\n\n  /** Formats a date range as an array of parts. */\n  formatRangeToParts(start: Date, end: Date): DateRangeFormatPart[] {\n    // @ts-ignore\n    if (typeof this.formatter.formatRangeToParts === 'function') {\n      // @ts-ignore\n      return this.formatter.formatRangeToParts(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    let startParts = this.formatter.formatToParts(start);\n    let endParts = this.formatter.formatToParts(end);\n    return [\n      ...startParts.map(p => ({...p, source: 'startRange'} as DateRangeFormatPart)),\n      {type: 'literal', value: ' – ', source: 'shared'},\n      ...endParts.map(p => ({...p, source: 'endRange'} as DateRangeFormatPart))\n    ];\n  }\n\n  /** Returns the resolved formatting options based on the values passed to the constructor. */\n  resolvedOptions(): Intl.ResolvedDateTimeFormatOptions {\n    let resolvedOptions = this.formatter.resolvedOptions();\n    if (hasBuggyResolvedHourCycle()) {\n      if (!this.resolvedHourCycle) {\n        this.resolvedHourCycle = getResolvedHourCycle(resolvedOptions.locale, this.options);\n      }\n      resolvedOptions.hourCycle = this.resolvedHourCycle;\n      resolvedOptions.hour12 = this.resolvedHourCycle === 'h11' || this.resolvedHourCycle === 'h12';\n    }\n\n    // Safari uses a different name for the Ethiopic (Amete Alem) calendar.\n    // https://bugs.webkit.org/show_bug.cgi?id=241564\n    if (resolvedOptions.calendar === 'ethiopic-amete-alem') {\n      resolvedOptions.calendar = 'ethioaa';\n    }\n\n    return resolvedOptions;\n  }\n}\n\n// There are multiple bugs involving the hour12 and hourCycle options in various browser engines.\n//   - Chrome [1] (and the ECMA 402 spec [2]) resolve hour12: false in English and other locales to h24 (24:00 - 23:59)\n//     rather than h23 (00:00 - 23:59). Same can happen with hour12: true in French, which Chrome resolves to h11 (00:00 - 11:59)\n//     rather than h12 (12:00 - 11:59).\n//   - WebKit returns an incorrect hourCycle resolved option in the French locale due to incorrect parsing of 'h' literal\n//     in the resolved pattern. It also formats incorrectly when specifying the hourCycle option for the same reason. [3]\n// [1] https://bugs.chromium.org/p/chromium/issues/detail?id=1045791\n// [2] https://github.com/tc39/ecma402/issues/402\n// [3] https://bugs.webkit.org/show_bug.cgi?id=229313\n\n// https://github.com/unicode-org/cldr/blob/018b55eff7ceb389c7e3fc44e2f657eae3b10b38/common/supplemental/supplementalData.xml#L4774-L4802\nconst hour12Preferences = {\n  true: {\n    // Only Japanese uses the h11 style for 12 hour time. All others use h12.\n    ja: 'h11'\n  },\n  false: {\n    // All locales use h23 for 24 hour time. None use h24.\n  }\n};\n\nfunction getCachedDateFormatter(locale: string, options: Intl.DateTimeFormatOptions = {}): Intl.DateTimeFormat {\n  // Work around buggy hour12 behavior in Chrome / ECMA 402 spec by using hourCycle instead.\n  // Only apply the workaround if the issue is detected, because the hourCycle option is buggy in Safari.\n  if (typeof options.hour12 === 'boolean' && hasBuggyHour12Behavior()) {\n    options = {...options};\n    let pref = hour12Preferences[String(options.hour12)][locale.split('-')[0]];\n    let defaultHourCycle = options.hour12 ? 'h12' : 'h23';\n    options.hourCycle = pref ?? defaultHourCycle;\n    delete options.hour12;\n  }\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (formatterCache.has(cacheKey)) {\n    return formatterCache.get(cacheKey)!;\n  }\n\n  let numberFormatter = new Intl.DateTimeFormat(locale, options);\n  formatterCache.set(cacheKey, numberFormatter);\n  return numberFormatter;\n}\n\nlet _hasBuggyHour12Behavior: boolean | null = null;\nfunction hasBuggyHour12Behavior() {\n  if (_hasBuggyHour12Behavior == null) {\n    _hasBuggyHour12Behavior = new Intl.DateTimeFormat('en-US', {\n      hour: 'numeric',\n      hour12: false\n    }).format(new Date(2020, 2, 3, 0)) === '24';\n  }\n\n  return _hasBuggyHour12Behavior;\n}\n\nlet _hasBuggyResolvedHourCycle: boolean | null = null;\nfunction hasBuggyResolvedHourCycle() {\n  if (_hasBuggyResolvedHourCycle == null) {\n    _hasBuggyResolvedHourCycle = new Intl.DateTimeFormat('fr', {\n      hour: 'numeric',\n      hour12: false\n    }).resolvedOptions().hourCycle === 'h12';\n  }\n\n  return _hasBuggyResolvedHourCycle;\n}\n\nfunction getResolvedHourCycle(locale: string, options: Intl.DateTimeFormatOptions) {\n  if (!options.timeStyle && !options.hour) {\n    return undefined;\n  }\n\n  // Work around buggy results in resolved hourCycle and hour12 options in WebKit.\n  // Format the minimum possible hour and maximum possible hour in a day and parse the results.\n  locale = locale.replace(/(-u-)?-nu-[a-zA-Z0-9]+/, '');\n  locale += (locale.includes('-u-') ? '' : '-u') + '-nu-latn';\n  let formatter = getCachedDateFormatter(locale, {\n    ...options,\n    timeZone: undefined // use local timezone\n  });\n\n  let min = parseInt(formatter.formatToParts(new Date(2020, 2, 3, 0)).find(p => p.type === 'hour')!.value, 10);\n  let max = parseInt(formatter.formatToParts(new Date(2020, 2, 3, 23)).find(p => p.type === 'hour')!.value, 10);\n\n  if (min === 0 && max === 23) {\n    return 'h23';\n  }\n\n  if (min === 24 && max === 23) {\n    return 'h24';\n  }\n\n  if (min === 0 && max === 11) {\n    return 'h11';\n  }\n\n  if (min === 12 && max === 11) {\n    return 'h12';\n  }\n\n  throw new Error('Unexpected hour cycle result');\n}\n"], "mappings": ";AAgBO,SAAS,0CAAI,QAAgB,WAAiB;AACnD,SAAO,SAAS,YAAY,KAAK,MAAM,SAAS,SAAA;AAClD;;;ACCA,IAAM,8BAAQ;AACP,SAAS,0CAAqB,KAAa,MAAc,OAAe,KAAW;AACxF,SAAO,0CAAgB,KAAK,IAAA;AAE5B,MAAI,KAAK,OAAO;AAChB,MAAI,cAAc;AAClB,MAAI,SAAS,EACX,eAAc;WACL,0CAAW,IAAA,EACpB,eAAc;AAGhB,SACE,8BACA,IACA,MAAM,KACN,KAAK,MAAM,KAAK,CAAA,IAChB,KAAK,MAAM,KAAK,GAAA,IAChB,KAAK,MAAM,KAAK,GAAA,IAChB,KAAK,OAAO,MAAM,QAAQ,OAAO,KAAK,cAAc,GAAA;AAExD;AAEO,SAAS,0CAAW,MAAY;AACrC,SAAO,OAAO,MAAM,MAAM,OAAO,QAAQ,KAAK,OAAO,QAAQ;AAC/D;AAEO,SAAS,0CAAgB,KAAa,MAAY;AACvD,SAAO,QAAQ,OAAO,IAAI,OAAO;AACnC;AAEO,SAAS,0CAAiB,MAAY;AAC3C,MAAI,MAAM;AACV,MAAI,QAAQ,GAAG;AACb,UAAM;AACN,WAAO,IAAI;EACb;AAEA,SAAO;IAAC;IAAK;;AACf;AAEA,IAAM,oCAAc;EAClB,UAAU;IAAC;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;;EACvD,UAAU;IAAC;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;;AACzD;AAMO,IAAM,4CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,QAAI,MAAM;AACV,QAAI,SAAS,MAAM;AACnB,QAAI,aAAa,KAAK,MAAM,SAAS,MAAA;AACrC,QAAI,OAAM,GAAA,2CAAI,QAAQ,MAAA;AACtB,QAAI,OAAO,KAAK,MAAM,MAAM,KAAA;AAC5B,QAAI,SAAQ,GAAA,2CAAI,KAAK,KAAA;AACrB,QAAI,OAAO,KAAK,MAAM,QAAQ,IAAA;AAC9B,QAAI,SAAQ,GAAA,2CAAI,OAAO,IAAA;AACvB,QAAI,SAAS,KAAK,MAAM,QAAQ,GAAA;AAEhC,QAAI,eAAe,aAAa,MAAM,OAAO,MAAM,OAAO,IAAI,UAAU,SAAS,KAAK,WAAW,IAAI,IAAI;AACzG,QAAI,CAAC,KAAK,IAAA,IAAQ,0CAAiB,YAAA;AACnC,QAAI,UAAU,MAAM,0CAAqB,KAAK,MAAM,GAAG,CAAA;AACvD,QAAI,UAAU;AACd,QAAI,MAAM,0CAAqB,KAAK,MAAM,GAAG,CAAA,EAC3C,WAAU;aACD,0CAAW,IAAA,EACpB,WAAU;AAEZ,QAAI,QAAQ,KAAK,QAAQ,UAAU,WAAW,KAAK,OAAO,GAAA;AAC1D,QAAI,MAAM,MAAM,0CAAqB,KAAK,MAAM,OAAO,CAAA,IAAK;AAE5D,WAAO,KAAI,GAAA,2CAAa,KAAK,MAAM,OAAO,GAAA;EAC5C;EAEA,YAAY,MAA+B;AACzC,WAAO,0CAAqB,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EACvE;EAEA,eAAe,MAA+B;AAC5C,WAAO,kCAAY,0CAAW,KAAK,IAAI,IAAI,aAAa,UAAA,EAAY,KAAK,QAAQ,CAAA;EACnF;;EAGA,gBAAgB,MAA+B;AAC7C,WAAO;EACT;EAEA,cAAc,MAA+B;AAC3C,WAAO,0CAAW,KAAK,IAAI,IAAI,MAAM;EACvC;;EAGA,cAAc,MAA+B;AAC3C,WAAO;EACT;EAEA,UAAoB;AAClB,WAAO;MAAC;MAAM;;EAChB;EAEA,aAAa,MAAgC;AAC3C,WAAO,KAAK,QAAQ;EACtB;EAEA,YAAY,MAAsC;AAChD,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,MAAM,KAAK,QAAQ,OAAO,OAAO;AACtC,WAAK,OAAO,IAAI,KAAK;IACvB;EACF;;SA/DA,aAAiC;;AAgEnC;;;ACxHO,IAAM,4CAAgB;EAC3B,OAAO;EACP,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;AACN;;;AC1FO,SAAS,0CAAU,GAAc,GAAY;AAClD,OAAI,GAAA,2CAAW,GAAG,EAAE,QAAQ;AAC5B,SAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE;AACpF;AAGO,SAAS,0CAAY,GAAc,GAAY;AACpD,OAAI,GAAA,2CAAW,GAAG,EAAE,QAAQ;AAE5B,MAAI,0CAAa,CAAA;AACjB,MAAI,0CAAa,CAAA;AACjB,SAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC/D;AAGO,SAAS,0CAAW,GAAc,GAAY;AACnD,OAAI,GAAA,2CAAW,GAAG,EAAE,QAAQ;AAC5B,MAAI,0CAAY,CAAA;AAChB,MAAI,0CAAY,CAAA;AAChB,SAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE;AACzC;AAGO,SAAS,0CAAW,GAAc,GAAY;AACnD,SAAO,yCAAgB,EAAE,UAAU,EAAE,QAAQ,KAAK,0CAAU,GAAG,CAAA;AACjE;AAGO,SAAS,0CAAa,GAAc,GAAY;AACrD,SAAO,yCAAgB,EAAE,UAAU,EAAE,QAAQ,KAAK,0CAAY,GAAG,CAAA;AACnE;AAGO,SAAS,0CAAY,GAAc,GAAY;AACpD,SAAO,yCAAgB,EAAE,UAAU,EAAE,QAAQ,KAAK,0CAAW,GAAG,CAAA;AAClE;AAGO,SAAS,yCAAgB,GAAa,GAAW;MAC/C,YAAkB;MAAlB,aAAA;AAAP,UAAO,QAAA,eAAA,aAAA,EAAE,aAAO,QAAT,eAAA,SAAA,SAAA,WAAA,KAAA,GAAY,CAAA,OAAA,QAAZ,gBAAA,SAAA,eAAkB,aAAA,EAAE,aAAO,QAAT,eAAA,SAAA,SAAA,WAAA,KAAA,GAAY,CAAA,OAAA,QAA9B,SAAA,SAAA,OAAoC,EAAE,eAAe,EAAE;AAChE;AAGO,SAAS,0CAAQ,MAAiB,UAAgB;AACvD,SAAO,0CAAU,MAAM,0CAAM,QAAA,CAAA;AAC/B;AAEA,IAAM,gCAAU;EACd,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;AACP;AASO,SAAS,0CAAa,MAAiB,QAAgB,gBAA0B;AACtF,MAAI,SAAS,KAAK,SAAS,YAAY,IAAA;AAIvC,MAAI,YAAY,iBAAiB,8BAAQ,cAAA,IAAkB,mCAAa,MAAA;AACxE,MAAI,YAAY,KAAK,KAAK,SAAS,IAAI,SAAA,IAAa;AACpD,MAAI,YAAY,EACd,cAAa;AAGf,SAAO;AACT;AAGO,SAAS,yCAAI,UAAgB;AAClC,UAAO,GAAA,2CAAa,KAAK,IAAG,GAAI,QAAA;AAClC;AAGO,SAAS,0CAAM,UAAgB;AACpC,UAAO,GAAA,2CAAe,yCAAI,QAAA,CAAA;AAC5B;AAEO,SAAS,0CAAY,GAAoB,GAAkB;AAChE,SAAO,EAAE,SAAS,YAAY,CAAA,IAAK,EAAE,SAAS,YAAY,CAAA;AAC5D;AAEO,SAAS,0CAAY,GAAY,GAAU;AAChD,SAAO,+BAAS,CAAA,IAAK,+BAAS,CAAA;AAChC;AAEA,SAAS,+BAAS,GAAU;AAC1B,SAAO,EAAE,OAAF,OAA0B,EAAE,SAAF,MAAuB,EAAE,SAAS,MAAO,EAAE;AAC9E;AAMO,SAAS,wCAAc,GAAiB,UAAgB;AAC7D,MAAI,MAAK,GAAA,2CAAW,GAAG,QAAA;AACvB,MAAI,WAAW,EAAE,IAAI;IAAC,MAAM;EAAC,CAAA;AAC7B,MAAI,cAAa,GAAA,2CAAW,UAAU,QAAA;AACtC,UAAQ,aAAa,MAAM;AAC7B;AAEA,IAAI,sCAA+B;AAG5B,SAAS,4CAAA;AAEd,MAAI,uCAAiB,KACnB,uCAAgB,IAAI,KAAK,eAAc,EAAG,gBAAe,EAAG;AAG9D,SAAO;AACT;AAOO,SAAS,0CAAa,MAAe;AAE1C,SAAO,KAAK,SAAS;IAAC,MAAM,KAAK,MAAM;EAAC,CAAA;AAC1C;AAOO,SAAS,0CAAW,MAAe;AACxC,SAAO,KAAK,IAAI;IAAC,MAAM,KAAK,SAAS,eAAe,IAAA,IAAQ,KAAK;EAAG,CAAA;AACtE;AAOO,SAAS,0CAAY,MAAe;AACzC,SAAO,0CAAa,KAAK,SAAS;IAAC,QAAQ,KAAK,QAAQ;EAAC,CAAA,CAAA;AAC3D;AAOO,SAAS,0CAAU,MAAe;AACvC,SAAO,0CAAW,KAAK,IAAI;IAAC,QAAQ,KAAK,SAAS,gBAAgB,IAAA,IAAQ,KAAK;EAAK,CAAA,CAAA;AACtF;AAEO,SAAS,0CAAsB,MAAqB;AACzD,MAAI,KAAK,SAAS,sBAChB,QAAO,KAAK,SAAS,sBAAsB,IAAA;AAG7C,SAAO;AACT;AAEO,SAAS,0CAAqB,MAAqB;AACxD,MAAI,KAAK,SAAS,qBAChB,QAAO,KAAK,SAAS,qBAAqB,IAAA;AAG5C,SAAO;AACT;AAOO,SAAS,0CAAY,MAAiB,QAAgB,gBAA0B;AACrF,MAAI,YAAY,0CAAa,MAAM,QAAQ,cAAA;AAC3C,SAAO,KAAK,SAAS;IAAC,MAAM;EAAS,CAAA;AACvC;AAOO,SAAS,0CAAU,MAAiB,QAAgB,gBAA0B;AACnF,SAAO,0CAAY,MAAM,QAAQ,cAAA,EAAgB,IAAI;IAAC,MAAM;EAAC,CAAA;AAC/D;AAEA,IAAM,sCAAgB,oBAAI,IAAA;AAE1B,SAAS,gCAAU,QAAc;AAG/B,MAAI,KAAK,QAAQ;AAEf,QAAI,SAAS,oCAAc,IAAI,MAAA;AAC/B,QAAI,CAAC,QAAQ;AAEX,eAAS,IAAI,KAAK,OAAO,MAAA,EAAQ,SAAQ,EAAG;AAC5C,UAAI,OACF,qCAAc,IAAI,QAAQ,MAAA;IAE9B;AACA,WAAO;EACT;AAMA,MAAI,OAAO,OAAO,MAAM,GAAA,EAAK,CAAA;AAC7B,SAAO,SAAS,MAAM,SAAY;AACpC;AAEA,SAAS,mCAAa,QAAc;AAGlC,MAAI,SAAS,gCAAU,MAAA;AACvB,SAAO,UAAS,GAAA,2CAAc,MAAA,KAAW,IAAI;AAC/C;AAGO,SAAS,0CAAgB,MAAiB,QAAgB,gBAA0B;AACzF,MAAI,OAAO,KAAK,SAAS,eAAe,IAAA;AACxC,SAAO,KAAK,MAAM,0CAAa,0CAAa,IAAA,GAAO,QAAQ,cAAA,IAAkB,QAAQ,CAAA;AACvF;AAGO,SAAS,0CAAkD,GAAc,GAAY;AAC1F,MAAI,KAAK,EACP,QAAO,EAAE,QAAQ,CAAA,KAAM,IAAI,IAAI;AAGjC,SAAO,KAAK;AACd;AAGO,SAAS,0CAAkD,GAAc,GAAY;AAC1F,MAAI,KAAK,EACP,QAAO,EAAE,QAAQ,CAAA,KAAM,IAAI,IAAI;AAGjC,SAAO,KAAK;AACd;AAEA,IAAM,qCAAe;EACnB,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;EACR,IAAI;IAAC;IAAG;;AACV;AAGO,SAAS,yCAAU,MAAiB,QAAc;AACvD,MAAI,SAAS,KAAK,SAAS,YAAY,IAAA;AAIvC,MAAI,YAAY,KAAK,KAAK,SAAS,CAAA,IAAK;AACxC,MAAI,YAAY,EACd,cAAa;AAGf,MAAI,SAAS,gCAAU,MAAA;AAGvB,MAAI,CAAC,OAAO,GAAA,IAAO,mCAAa,MAAA,KAAY;IAAC;IAAG;;AAChD,SAAO,cAAc,SAAS,cAAc;AAC9C;AAGO,SAAS,0CAAU,MAAiB,QAAc;AACvD,SAAO,CAAC,yCAAU,MAAM,MAAA;AAC1B;;;ACnSO,SAAS,yCAAc,MAAiB;AAC7C,SAAO,0CAAW,MAAM,KAAI,GAAA,2CAAgB,CAAA;AAC5C,MAAI,QAAO,GAAA,2CAAgB,KAAK,KAAK,KAAK,IAAI;AAC9C,SAAO,qCAAe,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;AACzG;AAEA,SAAS,qCAAe,MAAc,OAAe,KAAa,MAAc,QAAgB,QAAgB,aAAmB;AAGjI,MAAI,OAAO,oBAAI,KAAA;AACf,OAAK,YAAY,MAAM,QAAQ,QAAQ,WAAA;AACvC,OAAK,eAAe,MAAM,QAAQ,GAAG,GAAA;AACrC,SAAO,KAAK,QAAO;AACrB;AAEO,SAAS,0CAAkB,IAAY,UAAgB;AAE5D,MAAI,aAAa,MACf,QAAO;AAIT,MAAI,KAAK,KAAK,cAAa,GAAA,2CAAe,EACxC,QAAO,IAAI,KAAK,EAAA,EAAI,kBAAiB,IAA9B;AAGT,MAAI,EAAA,MAAK,OAAO,KAAK,MAAM,QAAQ,OAAQ,IAAI,uCAAiB,IAAI,QAAA;AACpE,MAAI,MAAM,qCAAe,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,CAAA;AACjE,SAAO,MAAM,KAAK,MAAM,KAAK,GAAA,IAAQ;AACvC;AAEA,IAAM,6CAAuB,oBAAI,IAAA;AAEjC,SAAS,uCAAiB,IAAY,UAAgB;AACpD,MAAI,YAAY,2CAAqB,IAAI,QAAA;AACzC,MAAI,CAAC,WAAW;AACd,gBAAY,IAAI,KAAK,eAAe,SAAS;;MAE3C,QAAQ;MACR,KAAK;MACL,MAAM;MACN,OAAO;MACP,KAAK;MACL,MAAM;MACN,QAAQ;MACR,QAAQ;IACV,CAAA;AAEA,+CAAqB,IAAI,UAAU,SAAA;EACrC;AAEA,MAAI,QAAQ,UAAU,cAAc,IAAI,KAAK,EAAA,CAAA;AAC7C,MAAI,aAAuC,CAAC;AAC5C,WAAS,QAAQ,MACf,KAAI,KAAK,SAAS,UAChB,YAAW,KAAK,IAAI,IAAI,KAAK;AAKjC,SAAO;;IAEL,MAAM,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW;IAC7F,OAAO,CAAC,WAAW;IACnB,KAAK,CAAC,WAAW;IACjB,MAAM,WAAW,SAAS,OAAO,IAAI,CAAC,WAAW;IACjD,QAAQ,CAAC,WAAW;IACpB,QAAQ,CAAC,WAAW;EACtB;AACF;AAEA,IAAM,kCAAY;AAEX,SAAS,0CAAkB,MAAwB,UAAgB;AACxE,MAAI,KAAK,yCAAc,IAAA;AACvB,MAAI,UAAU,KAAK,0CAAkB,KAAK,iCAAW,QAAA;AACrD,MAAI,QAAQ,KAAK,0CAAkB,KAAK,iCAAW,QAAA;AACnD,SAAO,wCAAkB,MAAM,UAAU,SAAS,KAAA;AACpD;AAEA,SAAS,wCAAkB,MAAwB,UAAkB,SAAiB,OAAa;AACjG,MAAI,QAAQ,YAAY,QAAQ;IAAC;MAAW;IAAC;IAAS;;AACtD,SAAO,MAAM,OAAO,CAAA,aAAY,sCAAgB,MAAM,UAAU,QAAA,CAAA;AAClE;AAEA,SAAS,sCAAgB,MAAwB,UAAkB,UAAgB;AACjF,MAAI,QAAQ,uCAAiB,UAAU,QAAA;AACvC,SAAO,KAAK,SAAS,MAAM,QACtB,KAAK,UAAU,MAAM,SACrB,KAAK,QAAQ,MAAM,OACnB,KAAK,SAAS,MAAM,QACpB,KAAK,WAAW,MAAM,UACtB,KAAK,WAAW,MAAM;AAC7B;AAEO,SAAS,0CAAW,MAAuC,UAAkB,iBAAiC,cAAY;AAC/H,MAAI,WAAW,0CAAmB,IAAA;AAGlC,MAAI,aAAa,MACf,QAAO,yCAAc,QAAA;AAIvB,MAAI,cAAa,GAAA,2CAAe,KAAO,mBAAmB,cAAc;AACtE,eAAW,0CAAW,UAAU,KAAI,GAAA,2CAAgB,CAAA;AAGpD,QAAIA,QAAO,oBAAI,KAAA;AACf,QAAI,QAAO,GAAA,2CAAgB,SAAS,KAAK,SAAS,IAAI;AACtD,IAAAA,MAAK,YAAY,MAAM,SAAS,QAAQ,GAAG,SAAS,GAAG;AACvD,IAAAA,MAAK,SAAS,SAAS,MAAM,SAAS,QAAQ,SAAS,QAAQ,SAAS,WAAW;AACnF,WAAOA,MAAK,QAAO;EACrB;AAEA,MAAI,KAAK,yCAAc,QAAA;AACvB,MAAI,eAAe,0CAAkB,KAAK,iCAAW,QAAA;AACrD,MAAI,cAAc,0CAAkB,KAAK,iCAAW,QAAA;AACpD,MAAI,QAAQ,wCAAkB,UAAU,UAAU,KAAK,cAAc,KAAK,WAAA;AAE1E,MAAI,MAAM,WAAW,EACnB,QAAO,MAAM,CAAA;AAGf,MAAI,MAAM,SAAS,EACjB,SAAQ,gBAAA;IAEN,KAAK;IACL,KAAK;AACH,aAAO,MAAM,CAAA;IACf,KAAK;AACH,aAAO,MAAM,MAAM,SAAS,CAAA;IAC9B,KAAK;AACH,YAAM,IAAI,WAAW,wCAAA;EACzB;AAGF,UAAQ,gBAAA;IACN,KAAK;AACH,aAAO,KAAK,IAAI,KAAK,cAAc,KAAK,WAAA;IAE1C,KAAK;IACL,KAAK;AACH,aAAO,KAAK,IAAI,KAAK,cAAc,KAAK,WAAA;IAC1C,KAAK;AACH,YAAM,IAAI,WAAW,6BAAA;EACzB;AACF;AAEO,SAAS,0CAAO,UAA2C,UAAkB,iBAAiC,cAAY;AAC/H,SAAO,IAAI,KAAK,0CAAW,UAAU,UAAU,cAAA,CAAA;AACjD;AAKO,SAAS,0CAAa,IAAY,UAAgB;AACvD,MAAI,SAAS,0CAAkB,IAAI,QAAA;AACnC,MAAI,OAAO,IAAI,KAAK,KAAK,MAAA;AACzB,MAAI,OAAO,KAAK,eAAc;AAC9B,MAAI,QAAQ,KAAK,YAAW,IAAK;AACjC,MAAI,MAAM,KAAK,WAAU;AACzB,MAAI,OAAO,KAAK,YAAW;AAC3B,MAAI,SAAS,KAAK,cAAa;AAC/B,MAAI,SAAS,KAAK,cAAa;AAC/B,MAAI,cAAc,KAAK,mBAAkB;AAEzC,SAAO,KAAI,GAAA,2CAAc,OAAO,IAAI,OAAO,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,MAAM,OAAO,KAAK,UAAU,QAAQ,MAAM,QAAQ,QAAQ,WAAA;AACpI;AAKO,SAAS,0CAAS,MAAY,UAAgB;AACnD,SAAO,0CAAa,KAAK,QAAO,GAAI,QAAA;AACtC;AAOO,SAAS,0CAAe,UAAyB;AACtD,SAAO,KAAI,GAAA,2CAAa,SAAS,UAAU,SAAS,KAAK,SAAS,MAAM,SAAS,OAAO,SAAS,GAAG;AACtG;AAwBO,SAAS,0CAAmB,MAAuD,MAAc;AACtG,MAAI,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG,cAAc;AACpD,MAAI,cAAc,KACf,EAAA,EAAA,MAAK,QAAQ,QAAQ,YAAa,IAAI;WAC9B,UAAU,QAAQ,CAAC,KAC5B,QAAO;AAGT,MAAI,KACD,EAAA,EAAA,MAAK,QAAQ,QAAQ,YAAa,IAAI;AAGzC,SAAO,KAAI,GAAA,2CACT,KAAK,UACL,KAAK,KACL,KAAK,MACL,KAAK,OACL,KAAK,KACL,MACA,QACA,QACA,WAAA;AAEJ;AAGO,SAAS,0CAAO,UAA0C;AAC/D,SAAO,KAAI,GAAA,0CAAK,SAAS,MAAM,SAAS,QAAQ,SAAS,QAAQ,SAAS,WAAW;AACvF;AAGO,SAAS,0CAAsC,MAAS,UAAkB;AAC/E,OAAI,GAAA,0CAAgB,KAAK,UAAU,QAAA,EACjC,QAAO;AAGT,MAAI,eAAe,SAAS,cAAc,KAAK,SAAS,YAAY,IAAA,CAAA;AACpE,MAAI,OAAmB,KAAK,KAAI;AAChC,OAAK,WAAW;AAChB,OAAK,MAAM,aAAa;AACxB,OAAK,OAAO,aAAa;AACzB,OAAK,QAAQ,aAAa;AAC1B,OAAK,MAAM,aAAa;AACxB,GAAA,GAAA,2CAAU,IAAA;AACV,SAAO;AACT;AAMO,SAAS,0CAAQ,MAAuD,UAAkB,gBAA+B;AAC9H,MAAI,iBAAgB,GAAA,4CAAe;AACjC,QAAI,KAAK,aAAa,SACpB,QAAO;AAGT,WAAO,0CAAW,MAAM,QAAA;EAC1B;AAEA,MAAI,KAAK,0CAAW,MAAM,UAAU,cAAA;AACpC,SAAO,0CAAa,IAAI,QAAA;AAC1B;AAEO,SAAS,yCAAY,MAAmB;AAC7C,MAAI,KAAK,yCAAc,IAAA,IAAQ,KAAK;AACpC,SAAO,IAAI,KAAK,EAAA;AAClB;AAGO,SAAS,0CAAW,MAAqB,UAAgB;AAC9D,MAAI,KAAK,yCAAc,IAAA,IAAQ,KAAK;AACpC,SAAO,0CAAW,0CAAa,IAAI,QAAA,GAAW,KAAK,QAAQ;AAC7D;AAGO,SAAS,0CAAgB,MAAmB;AACjD,SAAO,0CAAW,OAAM,GAAA,2CAAe,CAAA;AACzC;;;AClSA,IAAM,iCAAW;AAKV,SAAS,0CAAI,MAAuC,UAA0B;AACnF,MAAI,cAAsD,KAAK,KAAI;AACnE,MAAI,OAAO,UAAU,cAAc,oCAAc,aAAa,QAAA,IAAY;AAE1E,iCAAS,aAAa,SAAS,SAAS,CAAA;AACxC,MAAI,YAAY,SAAS,iBACvB,aAAY,SAAS,iBAAiB,aAAa,IAAA;AAGrD,cAAY,SAAS,SAAS,UAAU;AAExC,yCAAiB,WAAA;AACjB,0CAAkB,WAAA;AAElB,cAAY,QAAQ,SAAS,SAAS,KAAK;AAC3C,cAAY,OAAO,SAAS,QAAQ;AACpC,cAAY,OAAO;AAEnB,mCAAW,WAAA;AAEX,MAAI,YAAY,SAAS,YACvB,aAAY,SAAS,YAAY,WAAA;AAOnC,MAAI,YAAY,OAAO,GAAG;AACxB,gBAAY,OAAO;AACnB,gBAAY,QAAQ;AACpB,gBAAY,MAAM;EACpB;AAEA,MAAI,UAAU,YAAY,SAAS,cAAc,WAAA;AACjD,MAAI,YAAY,OAAO,SAAS;QACX,oCAAA;AAAnB,QAAI,gBAAe,sCAAA,wBAAA,YAAY,UAAS,kBAAY,QAAjC,uCAAA,SAAA,SAAA,mCAAA,KAAA,uBAAoC,WAAA;AACvD,gBAAY,OAAO;AACnB,gBAAY,QAAQ,eAAe,IAAI,YAAY,SAAS,gBAAgB,WAAA;AAC5E,gBAAY,MAAM,eAAe,IAAI,YAAY,SAAS,eAAe,WAAA;EAC3E;AAEA,MAAI,YAAY,QAAQ,GAAG;AACzB,gBAAY,QAAQ;AACpB,gBAAY,MAAM;EACpB;AAEA,MAAI,WAAW,YAAY,SAAS,gBAAgB,WAAA;AACpD,MAAI,YAAY,QAAQ,UAAU;AAChC,gBAAY,QAAQ;AACpB,gBAAY,MAAM,YAAY,SAAS,eAAe,WAAA;EACxD;AAEA,cAAY,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,YAAY,SAAS,eAAe,WAAA,GAAc,YAAY,GAAG,CAAA;AACxG,SAAO;AACT;AAEA,SAAS,+BAAS,MAAgC,OAAa;MACzD,6BAAA;AAAJ,OAAI,+BAAA,iBAAA,KAAK,UAAS,kBAAY,QAA1B,gCAAA,SAAA,SAAA,4BAAA,KAAA,gBAA6B,IAAA,EAC/B,SAAQ,CAAC;AAGX,OAAK,QAAQ;AACf;AAEA,SAAS,uCAAiB,MAA8B;AACtD,SAAO,KAAK,QAAQ,GAAG;AACrB,mCAAS,MAAM,EAAA;AACf,SAAK,SAAS,KAAK,SAAS,gBAAgB,IAAA;EAC9C;AAEA,MAAI,eAAe;AACnB,SAAO,KAAK,SAAS,eAAe,KAAK,SAAS,gBAAgB,IAAA,IAAQ;AACxE,SAAK,SAAS;AACd,mCAAS,MAAM,CAAA;EACjB;AACF;AAEA,SAAS,iCAAW,MAA8B;AAChD,SAAO,KAAK,MAAM,GAAG;AACnB,SAAK;AACL,2CAAiB,IAAA;AACjB,SAAK,OAAO,KAAK,SAAS,eAAe,IAAA;EAC3C;AAEA,SAAO,KAAK,MAAM,KAAK,SAAS,eAAe,IAAA,GAAO;AACpD,SAAK,OAAO,KAAK,SAAS,eAAe,IAAA;AACzC,SAAK;AACL,2CAAiB,IAAA;EACnB;AACF;AAEA,SAAS,wCAAkB,MAA8B;AACvD,OAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,SAAS,gBAAgB,IAAA,GAAO,KAAK,KAAK,CAAA;AACjF,OAAK,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,SAAS,eAAe,IAAA,GAAO,KAAK,GAAG,CAAA;AAC9E;AAEO,SAAS,0CAAU,MAA8B;AACtD,MAAI,KAAK,SAAS,cAChB,MAAK,SAAS,cAAc,IAAA;AAG9B,OAAK,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,SAAS,cAAc,IAAA,GAAO,KAAK,IAAI,CAAA;AAC7E,0CAAkB,IAAA;AACpB;AAEO,SAAS,0CAAe,UAA0B;AACvD,MAAI,kBAAkB,CAAC;AACvB,WAAS,OAAO,SACd,KAAI,OAAO,SAAS,GAAA,MAAS,SAC3B,iBAAgB,GAAA,IAAO,CAAC,SAAS,GAAA;AAIrC,SAAO;AACT;AAIO,SAAS,0CAAS,MAAuC,UAA0B;AACxF,SAAO,0CAAI,MAAM,0CAAe,QAAA,CAAA;AAClC;AAIO,SAAS,0CAAI,MAAuC,QAAkB;AAC3E,MAAI,cAAwC,KAAK,KAAI;AAErD,MAAI,OAAO,OAAO,KAChB,aAAY,MAAM,OAAO;AAG3B,MAAI,OAAO,QAAQ,KACjB,aAAY,OAAO,OAAO;AAG5B,MAAI,OAAO,SAAS,KAClB,aAAY,QAAQ,OAAO;AAG7B,MAAI,OAAO,OAAO,KAChB,aAAY,MAAM,OAAO;AAG3B,4CAAU,WAAA;AACV,SAAO;AACT;AAIO,SAAS,0CAAQ,OAAgC,QAAkB;AACxE,MAAI,eAAiD,MAAM,KAAI;AAE/D,MAAI,OAAO,QAAQ,KACjB,cAAa,OAAO,OAAO;AAG7B,MAAI,OAAO,UAAU,KACnB,cAAa,SAAS,OAAO;AAG/B,MAAI,OAAO,UAAU,KACnB,cAAa,SAAS,OAAO;AAG/B,MAAI,OAAO,eAAe,KACxB,cAAa,cAAc,OAAO;AAGpC,4CAAc,YAAA;AACd,SAAO;AACT;AAEA,SAAS,kCAAY,MAAsB;AACzC,OAAK,UAAU,KAAK,MAAM,KAAK,cAAc,GAAA;AAC7C,OAAK,cAAc,qCAAe,KAAK,aAAa,GAAA;AAEpD,OAAK,UAAU,KAAK,MAAM,KAAK,SAAS,EAAA;AACxC,OAAK,SAAS,qCAAe,KAAK,QAAQ,EAAA;AAE1C,OAAK,QAAQ,KAAK,MAAM,KAAK,SAAS,EAAA;AACtC,OAAK,SAAS,qCAAe,KAAK,QAAQ,EAAA;AAE1C,MAAI,OAAO,KAAK,MAAM,KAAK,OAAO,EAAA;AAClC,OAAK,OAAO,qCAAe,KAAK,MAAM,EAAA;AAEtC,SAAO;AACT;AAEO,SAAS,0CAAc,MAAsB;AAClD,OAAK,cAAc,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,aAAa,GAAA,CAAA;AAC1D,OAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAA,CAAA;AAChD,OAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAA,CAAA;AAChD,OAAK,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,MAAM,EAAA,CAAA;AAC9C;AAEA,SAAS,qCAAe,GAAW,GAAS;AAC1C,MAAI,SAAS,IAAI;AACjB,MAAI,SAAS,EACX,WAAU;AAEZ,SAAO;AACT;AAEA,SAAS,oCAAc,MAAwB,UAAsB;AACnE,OAAK,QAAQ,SAAS,SAAS;AAC/B,OAAK,UAAU,SAAS,WAAW;AACnC,OAAK,UAAU,SAAS,WAAW;AACnC,OAAK,eAAe,SAAS,gBAAgB;AAC7C,SAAO,kCAAY,IAAA;AACrB;AAEO,SAAS,0CAAQ,MAAY,UAAsB;AACxD,MAAI,MAAM,KAAK,KAAI;AACnB,sCAAc,KAAK,QAAA;AACnB,SAAO;AACT;AAEO,SAAS,0CAAa,MAAY,UAAsB;AAC7D,SAAO,0CAAQ,MAAM,0CAAe,QAAA,CAAA;AACtC;AAIO,SAAS,0CAAU,OAAwC,OAAkB,QAAgB,SAAsB;AACxH,MAAI,UAAoD,MAAM,KAAI;AAElE,UAAQ,OAAA;IACN,KAAK,OAAO;AACV,UAAI,OAAO,MAAM,SAAS,QAAO;AACjC,UAAI,WAAW,KAAK,QAAQ,MAAM,GAAG;AACrC,UAAI,WAAW,EACb,OAAM,IAAI,MAAM,kBAAkB,MAAM,GAAG;AAE7C,iBAAW,iCAAW,UAAU,QAAQ,GAAG,KAAK,SAAS,GAAG,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AAC1E,cAAQ,MAAM,KAAK,QAAA;AAGnB,gDAAU,OAAA;AACV;IACF;IACA,KAAK;UACC,gCAAA;AAAJ,WAAI,kCAAA,oBAAA,QAAQ,UAAS,kBAAY,QAA7B,mCAAA,SAAA,SAAA,+BAAA,KAAA,mBAAgC,OAAA,EAClC,UAAS,CAAC;AAMZ,cAAQ,OAAO,iCAAW,MAAM,MAAM,QAAQ,WAAW,MAAM,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AAC7E,UAAI,QAAQ,SAAS,UACnB,SAAQ,OAAO;AAGjB,UAAI,QAAQ,SAAS,iBACnB,SAAQ,SAAS,iBAAiB,SAAS,KAAA;AAE7C;IAEF,KAAK;AACH,cAAQ,QAAQ,iCAAW,MAAM,OAAO,QAAQ,GAAG,MAAM,SAAS,gBAAgB,KAAA,GAAQ,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACxG;IACF,KAAK;AACH,cAAQ,MAAM,iCAAW,MAAM,KAAK,QAAQ,GAAG,MAAM,SAAS,eAAe,KAAA,GAAQ,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACnG;IACF;AACE,YAAM,IAAI,MAAM,uBAAuB,KAAA;EAC3C;AAEA,MAAI,MAAM,SAAS,YACjB,OAAM,SAAS,YAAY,OAAA;AAG7B,4CAAU,OAAA;AACV,SAAO;AACT;AAIO,SAAS,0CAAU,OAAgC,OAAkB,QAAgB,SAA0B;AACpH,MAAI,UAA4C,MAAM,KAAI;AAE1D,UAAQ,OAAA;IACN,KAAK,QAAQ;AACX,UAAI,QAAQ,MAAM;AAClB,UAAI,MAAM;AACV,UAAI,MAAM;AACV,WAAI,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,eAAc,IAAI;AAC7B,YAAI,OAAO,SAAS;AACpB,cAAM,OAAO,KAAK;AAClB,cAAM,OAAO,KAAK;MACpB;AACA,cAAQ,OAAO,iCAAW,OAAO,QAAQ,KAAK,KAAK,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACjE;IACF;IACA,KAAK;AACH,cAAQ,SAAS,iCAAW,MAAM,QAAQ,QAAQ,GAAG,IAAI,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACvE;IACF,KAAK;AACH,cAAQ,SAAS,iCAAW,MAAM,QAAQ,QAAQ,GAAG,IAAI,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACvE;IACF,KAAK;AACH,cAAQ,cAAc,iCAAW,MAAM,aAAa,QAAQ,GAAG,KAAK,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AAClF;IACF;AACE,YAAM,IAAI,MAAM,uBAAuB,KAAA;EAC3C;AAEA,SAAO;AACT;AAEA,SAAS,iCAAW,OAAe,QAAgB,KAAa,KAAa,QAAQ,OAAK;AACxF,MAAI,OAAO;AACT,aAAS,KAAK,KAAK,MAAA;AAEnB,QAAI,QAAQ,IACV,SAAQ;AAGV,QAAI,MAAM,KAAK,IAAI,MAAA;AACnB,QAAI,SAAS,EACX,SAAQ,KAAK,KAAK,QAAQ,GAAA,IAAO;QAEjC,SAAQ,KAAK,MAAM,QAAQ,GAAA,IAAO;AAGpC,QAAI,QAAQ,IACV,SAAQ;EAEZ,OAAO;AACL,aAAS;AACT,QAAI,QAAQ,IACV,SAAQ,OAAO,MAAM,QAAQ;aACpB,QAAQ,IACjB,SAAQ,OAAO,QAAQ,MAAM;EAEjC;AAEA,SAAO;AACT;AAEO,SAAS,0CAAS,UAAyB,UAA0B;AAC1E,MAAI;AACJ,MAAK,SAAS,SAAS,QAAQ,SAAS,UAAU,KAAO,SAAS,UAAU,QAAQ,SAAS,WAAW,KAAO,SAAS,SAAS,QAAQ,SAAS,UAAU,KAAO,SAAS,QAAQ,QAAQ,SAAS,SAAS,GAAI;AAChN,QAAIC,OAAM,2CAAI,GAAA,2CAAmB,QAAA,GAAW;MAC1C,OAAO,SAAS;MAChB,QAAQ,SAAS;MACjB,OAAO,SAAS;MAChB,MAAM,SAAS;IACjB,CAAA;AAIA,UAAK,GAAA,2CAAWA,MAAK,SAAS,QAAQ;EACxC;AAEE,UAAK,GAAA,0CAAc,QAAA,IAAY,SAAS;AAM1C,QAAM,SAAS,gBAAgB;AAC/B,SAAO,SAAS,WAAW,KAAK;AAChC,SAAO,SAAS,WAAW,KAArB;AACN,SAAO,SAAS,SAAS,KAAnB;AAEN,MAAI,OAAM,GAAA,2CAAa,IAAI,SAAS,QAAQ;AAC5C,UAAO,GAAA,2CAAW,KAAK,SAAS,QAAQ;AAC1C;AAEO,SAAS,0CAAc,UAAyB,UAA0B;AAC/E,SAAO,0CAAS,UAAU,0CAAe,QAAA,CAAA;AAC3C;AAEO,SAAS,0CAAW,UAAyB,OAA8B,QAAgB,SAA0B;AAI1H,UAAQ,OAAA;IACN,KAAK,QAAQ;AACX,UAAI,MAAM;AACV,UAAI,MAAM;AACV,WAAI,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,eAAc,IAAI;AAC7B,YAAI,OAAO,SAAS,QAAQ;AAC5B,cAAM,OAAO,KAAK;AAClB,cAAM,OAAO,KAAK;MACpB;AAOA,UAAI,iBAAgB,GAAA,2CAAmB,QAAA;AACvC,UAAI,WAAU,GAAA,2CAAW,0CAAQ,eAAe;QAAC,MAAM;MAAG,CAAA,GAAI,KAAI,GAAA,2CAAgB,CAAA;AAClF,UAAI,cAAc;SAAC,GAAA,2CAAW,SAAS,SAAS,UAAU,SAAA;SAAY,GAAA,2CAAW,SAAS,SAAS,UAAU,OAAA;QAC1G,OAAO,CAAAC,SAAM,GAAA,2CAAaA,KAAI,SAAS,QAAQ,EAAE,QAAQ,QAAQ,GAAG,EAAE,CAAA;AAEzE,UAAI,WAAU,GAAA,2CAAW,0CAAQ,eAAe;QAAC,MAAM;MAAG,CAAA,GAAI,KAAI,GAAA,2CAAgB,CAAA;AAClF,UAAI,cAAc;SAAC,GAAA,2CAAW,SAAS,SAAS,UAAU,SAAA;SAAY,GAAA,2CAAW,SAAS,SAAS,UAAU,OAAA;QAC1G,OAAO,CAAAA,SAAM,GAAA,2CAAaA,KAAI,SAAS,QAAQ,EAAE,QAAQ,QAAQ,GAAG,EAAE,IAAG;AAK5E,UAAI,MAAK,GAAA,0CAAc,QAAA,IAAY,SAAS;AAC5C,UAAI,QAAQ,KAAK,MAAM,KAAK,8BAAA;AAC5B,UAAI,YAAY,KAAK;AACrB,WAAK,iCACH,OACA,QACA,KAAK,MAAM,cAAc,8BAAA,GACzB,KAAK,MAAM,cAAc,8BAAA,GACzB,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK,IACZ,iCAAW;AAGf,cAAO,GAAA,4CAAW,GAAA,2CAAa,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;IAC1E;IACA,KAAK;IACL,KAAK;IACL,KAAK;AAEH,aAAO,0CAAU,UAAU,OAAO,QAAQ,OAAA;IAC5C,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK,OAAO;AACV,UAAI,MAAM,2CAAU,GAAA,2CAAmB,QAAA,GAAW,OAAO,QAAQ,OAAA;AACjE,UAAI,MAAK,GAAA,2CAAW,KAAK,SAAS,QAAQ;AAC1C,cAAO,GAAA,4CAAW,GAAA,2CAAa,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;IAC1E;IACA;AACE,YAAM,IAAI,MAAM,uBAAuB,KAAA;EAC3C;AACF;AAEO,SAAS,0CAAS,UAAyB,QAAiC,gBAA+B;AAGhH,MAAI,iBAAgB,GAAA,2CAAmB,QAAA;AACvC,MAAI,MAAM,0CAAQ,0CAAI,eAAe,MAAA,GAAS,MAAA;AAI9C,MAAI,IAAI,QAAQ,aAAA,MAAmB,EACjC,QAAO;AAGT,MAAI,MAAK,GAAA,2CAAW,KAAK,SAAS,UAAU,cAAA;AAC5C,UAAO,GAAA,4CAAW,GAAA,2CAAa,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;AAC1E;;;ACxcA,IAAM,gCAAU;AAChB,IAAM,gCAAU;AAChB,IAAM,qCAAe;AACrB,IAAM,2CAAqB;AAC3B,IAAM,oCAAc;AACpB,IAAM,8CACF;AACJ,IAAM,mDAA6B;EAAC;EAAS;EAAW;;AACxD,IAAM,+CAAyB;EAAC;EAAS;EAAU;EAAS;KAAW;;AAGhE,SAAS,0CAAU,OAAa;AACrC,MAAI,IAAI,MAAM,MAAM,6BAAA;AACpB,MAAI,CAAC,EACH,OAAM,IAAI,MAAM,mCAAmC,KAAA;AAGrD,SAAO,KAAI,GAAA,0CACT,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,GACrB,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,QAAA,IAAY,MAAO,CAAA;AAEnD;AAGO,SAAS,yCAAU,OAAa;AACrC,MAAI,IAAI,MAAM,MAAM,6BAAA;AACpB,MAAI,CAAC,EACH,OAAM,IAAI,MAAM,mCAAmC,KAAA;AAGrD,MAAI,OAA8B,KAAI,GAAA,2CACpC,kCAAY,EAAE,CAAA,GAAI,GAAG,IAAA,GACrB,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,GACrB,CAAA;AAGF,OAAK,MAAM,kCAAY,EAAE,CAAA,GAAI,GAAG,KAAK,SAAS,eAAe,IAAA,CAAA;AAC7D,SAAO;AACT;AAGO,SAAS,0CAAc,OAAa;AACzC,MAAI,IAAI,MAAM,MAAM,kCAAA;AACpB,MAAI,CAAC,EACH,OAAM,IAAI,MAAM,wCAAwC,KAAA;AAG1D,MAAI,OAAO,kCAAY,EAAE,CAAA,GAAI,OAAO,IAAA;AACpC,MAAI,MAAM,OAAO,IAAI,OAAO;AAE5B,MAAI,OAAkC,KAAI,GAAA,2CACxC,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,GACrB,GACA,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,QAAA,IAAY,MAAO,CAAA;AAGjD,OAAK,MAAM,kCAAY,EAAE,CAAA,GAAI,GAAG,KAAK,SAAS,eAAe,IAAA,CAAA;AAC7D,SAAO;AACT;AAQO,SAAS,0CAAmB,OAAe,gBAA+B;AAC/E,MAAI,IAAI,MAAM,MAAM,wCAAA;AACpB,MAAI,CAAC,EACH,OAAM,IAAI,MAAM,wCAAwC,KAAA;AAG1D,MAAI,OAAO,kCAAY,EAAE,CAAA,GAAI,OAAO,IAAA;AACpC,MAAI,MAAM,OAAO,IAAI,OAAO;AAE5B,MAAI,OAA+B,KAAI,GAAA,2CACrC,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,GACrB,GACA,EAAE,EAAA,GACF,GACA,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,QAAA,IAAY,MAAO,CAAA;AAGjD,OAAK,MAAM,kCAAY,EAAE,CAAA,GAAI,GAAG,KAAK,SAAS,eAAe,IAAA,CAAA;AAE7D,MAAI,iBAAgB,GAAA,2CAAmB,IAAA;AAEvC,MAAI;AACJ,MAAI,EAAE,CAAA,GAAI;QACgE;AAAxE,SAAK,SAAS,kCAAY,EAAE,CAAA,GAAI,KAAK,EAAA,IAAvB,OAA8C,mCAAY,MAAA,EAAE,CAAA,OAAE,QAAJ,QAAA,SAAA,MAAQ,KAAK,GAAG,EAAA,IAA5B;AAC5D,UAAK,GAAA,0CAAc,IAAA,IAAyB,KAAK;AAGjD,QAAI,aAAY,GAAA,2CAAkB,eAAe,KAAK,QAAQ;AAC9D,QAAI,CAAC,UAAU,SAAS,EAAA,EACtB,OAAM,IAAI,MAAM,UAAU,qCAAe,KAAK,MAAM,CAAA,mBAAoB,0CAAiB,IAAA,CAAA,OAAY,KAAK,QAAQ,EAAE;EAExH;AAEE,UAAK,GAAA,4CAAW,GAAA,2CAAmB,aAAA,GAAgB,KAAK,UAAU,cAAA;AAGpE,UAAO,GAAA,2CAAa,IAAI,KAAK,QAAQ;AACvC;AAMO,SAAS,0CAAc,OAAe,UAAgB;AAC3D,MAAI,IAAI,MAAM,MAAM,iCAAA;AACpB,MAAI,CAAC,EACH,OAAM,IAAI,MAAM,wCAAwC,KAAA;AAG1D,MAAI,OAAO,kCAAY,EAAE,CAAA,GAAI,OAAO,IAAA;AACpC,MAAI,MAAM,OAAO,IAAI,OAAO;AAE5B,MAAI,OAA+B,KAAI,GAAA,2CACrC,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,GACrB,GACA,UACA,GACA,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,EAAA,IAAM,GAClC,EAAE,CAAA,IAAK,kCAAY,EAAE,CAAA,GAAI,GAAG,QAAA,IAAY,MAAO,CAAA;AAGjD,OAAK,MAAM,kCAAY,EAAE,CAAA,GAAI,GAAG,KAAK,SAAS,eAAe,IAAA,CAAA;MAGa;AAD1E,MAAI,EAAE,CAAA,EACJ,MAAK,SAAS,kCAAY,EAAE,CAAA,GAAI,KAAK,EAAA,IAAvB,OAA8C,mCAAY,MAAA,EAAE,CAAA,OAAE,QAAJ,QAAA,SAAA,MAAQ,KAAK,GAAG,EAAA,IAA5B;AAG9D,UAAO,GAAA,2CAAW,MAAuB,QAAA;AAC3C;AAMO,SAAS,0CAAqB,OAAa;AAChD,SAAO,0CAAc,QAAO,GAAA,2CAAe,CAAA;AAC7C;AAEA,SAAS,kCAAY,OAAe,KAAa,KAAW;AAC1D,MAAI,MAAM,OAAO,KAAA;AACjB,MAAI,MAAM,OAAO,MAAM,IACrB,OAAM,IAAI,WAAW,uBAAuB,GAAA,OAAU,GAAA,OAAU,GAAA,EAAK;AAGvE,SAAO;AACT;AAEO,SAAS,0CAAa,MAAU;AACrC,SAAO,GAAG,OAAO,KAAK,IAAI,EAAE,SAAS,GAAG,GAAA,CAAA,IAAQ,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG,GAAA,CAAA,IAAQ,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG,GAAA,CAAA,GAAO,KAAK,cAAc,OAAO,KAAK,cAAc,GAAA,EAAM,MAAM,CAAA,IAAK,EAAA;AAC/L;AAEO,SAAS,0CAAa,MAAkB;AAC7C,MAAI,iBAAgB,GAAA,2CAAW,MAAM,KAAI,GAAA,2CAAgB,CAAA;AACzD,MAAI;AACJ,MAAI,cAAc,QAAQ,KACxB,QAAO,cAAc,SAAS,IAC1B,SACA,MAAM,OAAO,KAAK,IAAI,IAAI,cAAc,IAAI,CAAA,EAAG,SAAS,GAAG,IAAA;MAE/D,QAAO,OAAO,cAAc,IAAI,EAAE,SAAS,GAAG,GAAA;AAEhD,SAAO,GAAG,IAAA,IAAQ,OAAO,cAAc,KAAK,EAAE,SAAS,GAAG,GAAA,CAAA,IAAQ,OAAO,cAAc,GAAG,EAAE,SAAS,GAAG,GAAA,CAAA;AAC1G;AAEO,SAAS,0CAAiB,MAAiB;AAEhD,SAAO,GAAG,0CAAa,IAAA,CAAA,IAAS,0CAAa,IAAA,CAAA;AAC/C;AAEA,SAAS,qCAAe,QAAc;AACpC,MAAI,OAAO,KAAK,KAAK,MAAA,IAAU,IAAI,MAAM;AACzC,WAAS,KAAK,IAAI,MAAA;AAClB,MAAI,cAAc,KAAK,MAAM,SAAU,IAAA;AACvC,MAAI,gBAAiB,SAAU,OAAoB;AACnD,SAAO,GAAG,IAAA,GAAO,OAAO,WAAA,EAAa,SAAS,GAAG,GAAA,CAAA,IAAQ,OAAO,aAAA,EAAe,SAAS,GAAG,GAAA,CAAA;AAC7F;AAEO,SAAS,0CAAsB,MAAmB;AACvD,SAAO,GAAG,0CAAiB,IAAA,CAAA,GAAQ,qCAAe,KAAK,MAAM,CAAA,IAAK,KAAK,QAAQ;AACjF;AAOO,SAAS,0CAAc,OAAa;MAsBpB,eAQc,gBAUP,gBACC,gBACD,gBACD,gBACC,gBACE,gBACA;AA7C9B,QAAM,QAAQ,MAAM,MAAM,2CAAA;AAE1B,MAAI,CAAC,MACH,OAAM,IAAI,MAAM,qCAAqC,KAAA,EAAO;AAG9D,QAAM,qBAAqB,CACzB,OACAC,gBAAA;AAEA,QAAI,CAAC,MACH,QAAO;AAET,QAAI;AACF,YAAM,OAAOA,cAAa,KAAK;AAC/B,aAAO,OAAO,OAAO,MAAM,QAAQ,KAAK,GAAA,CAAA;IAC1C,QAAQ;AACN,YAAM,IAAI,MAAM,qCAAqC,KAAA,EAAO;IAC9D;EACF;AAEA,QAAM,aAAa,CAAC,GAAC,gBAAA,MAAM,YAAM,QAAZ,kBAAA,SAAA,SAAA,cAAc;AAEnC,QAAM,oBAAoB,6CAAuB,KAAK,CAAA,UAAA;QAASC;YAAAA,iBAAA,MAAM,YAAM,QAAZA,mBAAA,SAAA,SAAAA,eAAe,KAAA;;AAE9E,MAAI,CAAC,kBACH,OAAM,IAAI,MAAM,qCAAqC,KAAA,EAAO;AAG9D,QAAM,8BAA6B,iBAAA,MAAM,YAAM,QAAZ,mBAAA,SAAA,SAAA,eAAc;AAEjD,MAAI,4BAA4B;AAC9B,UAAM,gCAAgC,iDAA2B,KAAK,CAAA,UAAA;UAASA;cAAAA,iBAAA,MAAM,YAAM,QAAZA,mBAAA,SAAA,SAAAA,eAAe,KAAA;;AAC9F,QAAI,CAAC,8BACH,OAAM,IAAI,MAAM,qCAAqC,KAAA,EAAO;EAEhE;AAEA,QAAM,WAAsC;IAC1C,OAAO,oBAAmB,iBAAA,MAAM,YAAM,QAAZ,mBAAA,SAAA,SAAA,eAAc,OAAO,UAAA;IAC/C,QAAQ,oBAAmB,iBAAA,MAAM,YAAM,QAAZ,mBAAA,SAAA,SAAA,eAAc,QAAQ,UAAA;IACjD,OAAO,oBAAmB,iBAAA,MAAM,YAAM,QAAZ,mBAAA,SAAA,SAAA,eAAc,OAAO,UAAA;IAC/C,MAAM,oBAAmB,iBAAA,MAAM,YAAM,QAAZ,mBAAA,SAAA,SAAA,eAAc,MAAM,UAAA;IAC7C,OAAO,oBAAmB,iBAAA,MAAM,YAAM,QAAZ,mBAAA,SAAA,SAAA,eAAc,OAAO,UAAA;IAC/C,SAAS,oBAAmB,iBAAA,MAAM,YAAM,QAAZ,mBAAA,SAAA,SAAA,eAAc,SAAS,UAAA;IACnD,SAAS,oBAAmB,iBAAA,MAAM,YAAM,QAAZ,mBAAA,SAAA,SAAA,eAAc,SAAS,UAAA;EACrD;AAEA,MAAI,SAAS,UAAU,UAAe,SAAS,QAAQ,MAAO,MAAO,SAAS,WAAW,SAAS,SAChG,OAAM,IAAI,MAAM,qCAAqC,KAAA,6CAAkD;AAGzG,MAAI,SAAS,YAAY,UAAe,SAAS,UAAU,MAAO,KAAM,SAAS,QAC/E,OAAM,IAAI,MAAM,qCAAqC,KAAA,6CAAkD;AAGzG,SAAO;AACT;;;AC7RA,SAAS,6BAA6B,KAAK,mBAAmB;AAC1D,MAAI,kBAAkB,IAAI,GAAG,GAAG;AAC5B,UAAM,IAAI,UAAU,gEAAgE;AAAA,EACxF;AACJ;;;ACFA,SAAS,0BAA0B,KAAK,YAAY,OAAO;AACvD,+BAA6B,KAAK,UAAU;AAC5C,aAAW,IAAI,KAAK,KAAK;AAC7B;;;ACcA,SAAS,gCAAU,MAAW;AAC5B,MAAI,WAAqB,OAAO,KAAK,CAAA,MAAO,WACxC,KAAK,MAAK,IACV,KAAI,GAAA,2CAAgB;AAExB,MAAI;AACJ,MAAI,OAAO,KAAK,CAAA,MAAO,SACrB,OAAM,KAAK,MAAK;OACX;AACL,QAAI,OAAO,SAAS,QAAO;AAC3B,UAAM,KAAK,KAAK,SAAS,CAAA;EAC3B;AAEA,MAAI,OAAO,KAAK,MAAK;AACrB,MAAI,QAAQ,KAAK,MAAK;AACtB,MAAI,MAAM,KAAK,MAAK;AAEpB,SAAO;IAAC;IAAU;IAAK;IAAM;IAAO;;AACtC;IAQE,8BAAA,oBAAA,QAAA;AALK,IAAM,4CAAN,MAAM,2CAAA;;EAqCX,OAAqB;AACnB,QAAI,KAAK,IACP,QAAO,IAAI,2CAAa,KAAK,UAAU,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;QAEhF,QAAO,IAAI,2CAAa,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EAE1E;;EAGA,IAAI,UAAsC;AACxC,YAAO,GAAA,2CAAI,MAAM,QAAA;EACnB;;EAGA,SAAS,UAAsC;AAC7C,YAAO,GAAA,2CAAS,MAAM,QAAA;EACxB;;EAGA,IAAI,QAAkC;AACpC,YAAO,GAAA,2CAAI,MAAM,MAAA;EACnB;;;;;EAMA,MAAM,OAAkB,QAAgB,SAAsC;AAC5E,YAAO,GAAA,2CAAU,MAAM,OAAO,QAAQ,OAAA;EACxC;;EAGA,OAAO,UAAwB;AAC7B,YAAO,GAAA,2CAAO,MAAM,QAAA;EACtB;;EAGA,WAAmB;AACjB,YAAO,GAAA,2CAAa,IAAI;EAC1B;;EAGA,QAAQ,GAA4B;AAClC,YAAO,GAAA,2CAAY,MAAM,CAAA;EAC3B;EAxDA,eAAe,MAAa;AApB5B,KAAA,GAAA,2BAAA,MAAA,6BAAA;;aAAA;;AAqBE,QAAI,CAAC,UAAU,KAAK,MAAM,OAAO,GAAA,IAAO,gCAAU,IAAA;AAClD,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,MAAM;AAEX,KAAA,GAAA,2CAAU,IAAI;EAChB;AAgDF;IAME,+BAAA,oBAAA,QAAA;AAHK,IAAM,2CAAN,MAAM,0CAAA;;EA2BX,OAAa;AACX,WAAO,IAAI,0CAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;EACvE;;EAGA,IAAI,UAA8B;AAChC,YAAO,GAAA,2CAAQ,MAAM,QAAA;EACvB;;EAGA,SAAS,UAA8B;AACrC,YAAO,GAAA,2CAAa,MAAM,QAAA;EAC5B;;EAGA,IAAI,QAA0B;AAC5B,YAAO,GAAA,2CAAQ,MAAM,MAAA;EACvB;;;;;EAMA,MAAM,OAAkB,QAAgB,SAAkC;AACxE,YAAO,GAAA,2CAAU,MAAM,OAAO,QAAQ,OAAA;EACxC;;EAGA,WAAmB;AACjB,YAAO,GAAA,2CAAa,IAAI;EAC1B;;EAGA,QAAQ,GAAoB;AAC1B,YAAO,GAAA,2CAAY,MAAM,CAAA;EAC3B;EAjDA,YACE,OAAe,GACf,SAAiB,GACjB,SAAiB,GACjB,cAAsB,GACtB;AAfF,KAAA,GAAA,2BAAA,MAAA,8BAAA;;aAAA;;AAgBE,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,KAAA,GAAA,2CAAc,IAAI;EACpB;AAuCF;IAME,+BAAA,oBAAA,QAAA;AAHK,IAAM,4CAAN,MAAM,2CAAA;;EA+CX,OAAyB;AACvB,QAAI,KAAK,IACP,QAAO,IAAI,2CAAiB,KAAK,UAAU,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;QAE3I,QAAO,IAAI,2CAAiB,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;EAErI;;EAGA,IAAI,UAA8C;AAChD,YAAO,GAAA,2CAAI,MAAM,QAAA;EACnB;;EAGA,SAAS,UAA8C;AACrD,YAAO,GAAA,2CAAS,MAAM,QAAA;EACxB;;EAGA,IAAI,QAAmD;AACrD,YAAO,GAAA,4CAAI,GAAA,2CAAQ,MAAM,MAAA,GAAS,MAAA;EACpC;;;;;EAMA,MAAM,OAA8B,QAAgB,SAA8C;AAChG,YAAQ,OAAA;MACN,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,gBAAO,GAAA,2CAAU,MAAM,OAAO,QAAQ,OAAA;MACxC;AACE,gBAAO,GAAA,2CAAU,MAAM,OAAO,QAAQ,OAAA;IAC1C;EACF;;EAGA,OAAO,UAAkB,gBAAuC;AAC9D,YAAO,GAAA,2CAAO,MAAM,UAAU,cAAA;EAChC;;EAGA,WAAmB;AACjB,YAAO,GAAA,2CAAiB,IAAI;EAC9B;;EAGA,QAAQ,GAA4D;AAClE,QAAI,OAAM,GAAA,2CAAY,MAAM,CAAA;AAC5B,QAAI,QAAQ,EACV,SAAO,GAAA,2CAAY,OAAM,GAAA,2CAAmB,CAAA,CAAA;AAG9C,WAAO;EACT;EAzEA,eAAe,MAAa;AA5B5B,KAAA,GAAA,2BAAA,MAAA,8BAAA;;aAAA;;AA6BE,QAAI,CAAC,UAAU,KAAK,MAAM,OAAO,GAAA,IAAO,gCAAU,IAAA;AAClD,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,OAAO,KAAK,MAAK,KAAM;AAC5B,SAAK,SAAS,KAAK,MAAK,KAAM;AAC9B,SAAK,SAAS,KAAK,MAAK,KAAM;AAC9B,SAAK,cAAc,KAAK,MAAK,KAAM;AAEnC,KAAA,GAAA,2CAAU,IAAI;EAChB;AA6DF;IAME,+BAAA,oBAAA,QAAA;AAHK,IAAM,4CAAN,MAAM,2CAAA;;EAuDX,OAAsB;AACpB,QAAI,KAAK,IACP,QAAO,IAAI,2CAAc,KAAK,UAAU,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,UAAU,KAAK,QAAQ,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;QAEpK,QAAO,IAAI,2CAAc,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,UAAU,KAAK,QAAQ,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;EAE9J;;EAGA,IAAI,UAA2C;AAC7C,YAAO,GAAA,2CAAS,MAAM,QAAA;EACxB;;EAGA,SAAS,UAA2C;AAClD,YAAO,GAAA,2CAAc,MAAM,QAAA;EAC7B;;EAGA,IAAI,QAAiC,gBAAgD;AACnF,YAAO,GAAA,2CAAS,MAAM,QAAQ,cAAA;EAChC;;;;;EAMA,MAAM,OAA8B,QAAgB,SAA2C;AAC7F,YAAO,GAAA,2CAAW,MAAM,OAAO,QAAQ,OAAA;EACzC;;EAGA,SAAe;AACb,YAAO,GAAA,0CAAY,IAAI;EACzB;;EAGA,WAAmB;AACjB,YAAO,GAAA,2CAAsB,IAAI;EACnC;;EAGA,mBAA2B;AACzB,WAAO,KAAK,OAAM,EAAG,YAAW;EAClC;;EAGA,QAAQ,GAA4D;AAElE,WAAO,KAAK,OAAM,EAAG,QAAO,KAAK,GAAA,2CAAQ,GAAG,KAAK,QAAQ,EAAE,OAAM,EAAG,QAAO;EAC7E;EAtEA,eAAe,MAAa;AAhC5B,KAAA,GAAA,2BAAA,MAAA,8BAAA;;aAAA;;AAiCE,QAAI,CAAC,UAAU,KAAK,MAAM,OAAO,GAAA,IAAO,gCAAU,IAAA;AAClD,QAAI,WAAW,KAAK,MAAK;AACzB,QAAI,SAAS,KAAK,MAAK;AACvB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,OAAO,KAAK,MAAK,KAAM;AAC5B,SAAK,SAAS,KAAK,MAAK,KAAM;AAC9B,SAAK,SAAS,KAAK,MAAK,KAAM;AAC9B,SAAK,cAAc,KAAK,MAAK,KAAM;AAEnC,KAAA,GAAA,2CAAU,IAAI;EAChB;AAsDF;;;ACjYA,IAAM,wCAAkB;EAAC;IAAC;IAAM;IAAG;;EAAI;IAAC;IAAM;IAAG;;EAAK;IAAC;IAAM;IAAI;;EAAK;IAAC;IAAM;IAAG;;EAAI;IAAC;IAAM;IAAG;;;AAC9F,IAAM,sCAAgB;EAAC;IAAC;IAAM;IAAG;;EAAK;IAAC;IAAM;IAAI;;EAAK;IAAC;IAAM;IAAG;;EAAI;IAAC;IAAM;IAAG;;;AAC9E,IAAM,oCAAc;EAAC;EAAM;EAAM;EAAM;EAAM;;AAC7C,IAAM,kCAAY;EAAC;EAAS;EAAU;EAAS;EAAU;;AAEzD,SAAS,+CAAyB,MAAqB;AACrD,QAAM,MAAM,sCAAgB,UAAU,CAAC,CAAC,MAAM,OAAO,GAAA,MAAI;AACvD,QAAI,KAAK,OAAO,KACd,QAAO;AAGT,QAAI,KAAK,SAAS,QAAQ,KAAK,QAAQ,MACrC,QAAO;AAGT,QAAI,KAAK,SAAS,QAAQ,KAAK,UAAU,SAAS,KAAK,MAAM,IAC3D,QAAO;AAGT,WAAO;EACT,CAAA;AAEA,MAAI,QAAQ,GACV,QAAO,sCAAgB,SAAS;AAGlC,MAAI,QAAQ,EACV,QAAO;AAGT,SAAO,MAAM;AACf;AAEA,SAAS,kCAAY,MAAqB;AACxC,MAAI,YAAY,kCAAY,gCAAU,QAAQ,KAAK,GAAG,CAAA;AACtD,MAAI,CAAC,UACH,OAAM,IAAI,MAAM,kBAAkB,KAAK,GAAG;AAG5C,SAAO,KAAI,GAAA,2CACT,KAAK,OAAO,WACZ,KAAK,OACL,KAAK,GAAG;AAEZ;AAOO,IAAM,4CAAN,eAA+B,GAAA,2CAAgB;EAGpD,cAAc,IAA0B;AACtC,QAAI,OAAO,MAAM,cAAc,EAAA;AAC/B,QAAI,MAAM,+CAAyB,IAAA;AAEnC,WAAO,KAAI,GAAA,2CACT,MACA,gCAAU,GAAA,GACV,KAAK,OAAO,kCAAY,GAAA,GACxB,KAAK,OACL,KAAK,GAAG;EAEZ;EAEA,YAAY,MAA+B;AACzC,WAAO,MAAM,YAAY,kCAAY,IAAA,CAAA;EACvC;EAEA,YAAY,MAAsC;AAChD,QAAI,gBAAgB,kCAAY,IAAA;AAChC,QAAI,MAAM,+CAAyB,aAAA;AAEnC,QAAI,gCAAU,GAAA,MAAS,KAAK,KAAK;AAC/B,WAAK,MAAM,gCAAU,GAAA;AACrB,WAAK,OAAO,cAAc,OAAO,kCAAY,GAAA;IAC/C;AAGA,SAAK,cAAc,IAAA;EACrB;EAEA,cAAc,MAAsC;AAClD,QAAI,MAAM,gCAAU,QAAQ,KAAK,GAAG;AACpC,QAAI,MAAM,oCAAc,GAAA;AACxB,QAAI,OAAO,MAAM;AACf,UAAI,CAAC,SAAS,UAAU,MAAA,IAAU;AAIlC,UAAI,UAAU,UAAU,kCAAY,GAAA;AACpC,WAAK,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,SAAS,KAAK,IAAI,CAAA;AACnD,UAAI,KAAK,SAAS,SAAS;AACzB,aAAK,QAAQ,KAAK,IAAI,UAAU,KAAK,KAAK;AAE1C,YAAI,KAAK,UAAU,SACjB,MAAK,MAAM,KAAK,IAAI,QAAQ,KAAK,GAAG;MAExC;IACF;AAEA,QAAI,KAAK,SAAS,KAAK,OAAO,GAAG;AAC/B,UAAI,CAAA,EAAG,YAAY,QAAA,IAAY,sCAAgB,GAAA;AAC/C,WAAK,QAAQ,KAAK,IAAI,YAAY,KAAK,KAAK;AAE5C,UAAI,KAAK,UAAU,WACjB,MAAK,MAAM,KAAK,IAAI,UAAU,KAAK,GAAG;IAE1C;EACF;EAEA,UAAoB;AAClB,WAAO;EACT;EAEA,cAAc,MAA+B;AAE3C,QAAI,MAAM,gCAAU,QAAQ,KAAK,GAAG;AACpC,QAAI,MAAM,sCAAgB,GAAA;AAC1B,QAAI,OAAO,sCAAgB,MAAM,CAAA;AACjC,QAAI,QAAQ;AAEV,aAAO,OAAO,IAAI,CAAA,IAAK;AAGzB,QAAI,QAAQ,KAAK,CAAA,IAAK,IAAI,CAAA;AAE1B,QAAI,KAAK,QAAQ,KAAK,CAAA,KAAO,KAAK,UAAU,KAAK,CAAA,KAAM,KAAK,MAAM,KAAK,CAAA,EACrE;AAGF,WAAO;EACT;EAEA,eAAe,MAA+B;AAC5C,WAAO,MAAM,eAAe,kCAAY,IAAA,CAAA;EAC1C;EAEA,sBAAsB,MAA+B;AACnD,QAAI,QAAQ,kCAAY,IAAA;AACxB,WAAO,QAAQ,MAAM,CAAA,IAAK;EAC5B;EAEA,qBAAqB,MAA+B;AAClD,QAAI,QAAQ,kCAAY,IAAA;AACxB,WAAO,SAAS,KAAK,UAAU,MAAM,CAAA,IAAK,MAAM,CAAA,IAAK;EACvD;;AAjGK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAiGnC;AAEA,SAAS,kCAAY,MAAqB;AACxC,MAAI,KAAK,SAAS,GAAG;AACnB,QAAI,MAAM,gCAAU,QAAQ,KAAK,GAAG;AACpC,WAAO,sCAAgB,GAAA;EACzB;AACF;;;AC7JA,IAAM,2CAAqB;AAOpB,IAAM,4CAAN,eAA+B,GAAA,2CAAgB;EAGpD,cAAc,IAA0B;AACtC,QAAI,gBAAgB,MAAM,cAAc,EAAA;AACxC,QAAI,QAAO,GAAA,2CAAgB,cAAc,KAAK,cAAc,IAAI;AAChE,WAAO,KAAI,GAAA,2CACT,MACA,OAAO,0CACP,cAAc,OACd,cAAc,GAAG;EAErB;EAEA,YAAY,MAA+B;AACzC,WAAO,MAAM,YAAY,kCAAY,IAAA,CAAA;EACvC;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,eAAe,MAA+B;AAC5C,WAAO,MAAM,eAAe,kCAAY,IAAA,CAAA;EAC1C;EAEA,cAAoB;EAAC;;AA1BhB,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AA0BnC;AAEA,SAAS,kCAAY,MAAqB;AACxC,MAAI,CAAC,KAAK,IAAA,KAAQ,GAAA,2CAAiB,KAAK,OAAO,wCAAA;AAC/C,SAAO,KAAI,GAAA,2CACT,KACA,MACA,KAAK,OACL,KAAK,GAAG;AAEZ;;;AC3CA,IAAM,yCAAmB;AAEzB,SAAS,oCAAc,MAAqB;AAC1C,SAAO,KAAK,QAAQ,WAChB,KAAK,OAAO,yCACZ,IAAI,KAAK,OAAO;AACtB;AAEA,SAAS,wCAAkB,MAAY;AACrC,MAAI,IAAI,OAAO;AACf,MAAI,IAAI,EACN,QAAO;IAAC;IAAU;;MAElB,QAAO;IAAC;IAAiB,IAAI;;AAEjC;AAOO,IAAM,4CAAN,eAA6B,GAAA,2CAAgB;EAGlD,cAAc,IAA0B;AACtC,QAAI,OAAO,MAAM,cAAc,EAAA;AAC/B,QAAI,gBAAe,GAAA,2CAAgB,KAAK,KAAK,KAAK,IAAI;AACtD,QAAI,CAAC,KAAK,IAAA,IAAQ,wCAAkB,YAAA;AACpC,WAAO,KAAI,GAAA,2CAAa,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EAC/D;EAEA,YAAY,MAA+B;AACzC,WAAO,MAAM,YAAY,kCAAY,IAAA,CAAA;EACvC;EAEA,UAAoB;AAClB,WAAO;MAAC;MAAiB;;EAC3B;EAEA,YAAY,MAAsC;AAChD,QAAI,CAAC,KAAK,IAAA,IAAQ,wCAAkB,oCAAc,IAAA,CAAA;AAClD,SAAK,MAAM;AACX,SAAK,OAAO;EACd;EAEA,aAAa,MAAgC;AAC3C,WAAO,KAAK,QAAQ;EACtB;EAEA,eAAe,MAA+B;AAC5C,WAAO,MAAM,eAAe,kCAAY,IAAA,CAAA;EAC1C;EAEA,cAAc,MAA+B;AAC3C,WAAO,KAAK,QAAQ,kBAAkB,OAAO,OAAO;EACtD;;AAlCK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAkCnC;AAEA,SAAS,kCAAY,MAAqB;AACxC,MAAI,CAAC,KAAK,IAAA,KAAQ,GAAA,2CAAiB,oCAAc,IAAA,CAAA;AACjD,SAAO,KAAI,GAAA,2CACT,KACA,MACA,KAAK,OACL,KAAK,GAAG;AAEZ;;;ACpEA,IAAM,sCAAgB;AAGtB,IAAM,oCAAc;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AASK,IAAM,4CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,OAAO,IAAI,KAAK,OAAO,KAAK,iBAAiB,KAAK,KAAA;AACtD,QAAI,aAAa,OAAO,OAAO,KAAK,KAAK,OAAO,IAAI,OAAO,MAAM,EAAA;AACjE,QAAI,YAAY,iBAAiB;AACjC,QAAI,QAAQ,YAAY,MACpB,KAAK,MAAM,YAAY,EAAA,IACvB,KAAK,OAAO,YAAY,KAAK,EAAA;AACjC,QAAI,MAAM,YAAY,kCAAY,KAAA,IAAS;AAC3C,WAAO,KAAI,GAAA,2CAAa,MAAM,MAAM,QAAQ,GAAG,GAAA;EACjD;EAEA,YAAY,MAA+B;AACzC,QAAI,KAAK,sCAAgB,IAAI,OAAO,KAAK,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK,OAAO,MAAM,EAAA;AACvF,UAAM,kCAAY,KAAK,QAAQ,CAAA;AAC/B,UAAM,KAAK;AACX,WAAO;EACT;EAEA,kBAA0B;AACxB,WAAO;EACT;EAEA,eAAe,MAA+B;AAC5C,QAAI,KAAK,SAAS,EAChB,QAAO;AAGT,QAAI,KAAK,SAAS,GAChB,QAAO;AAGT,QAAI,cAAa,GAAA,2CAAI,KAAK,KAAK,OAAO,IAAI,EAAA,IAAM;AAChD,WAAO,aAAa,KAAK;EAC3B;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,gBAAwB;AAGtB,WAAO;EACT;;SA9CA,aAAiC;;AA+CnC;;;ACvEA,IAAM,yCAAmB;AAGzB,IAAM,0CAAoB;AAOnB,IAAM,4CAAN,eAA6B,GAAA,2CAAgB;EAGlD,cAAc,IAA0B;AAEtC,QAAI,OAAO,MAAM,cAAc,EAAA;AAG/B,QAAI,aAAa,KAAK,OAAO;AAG7B,QAAI,OAAO,MAAK,GAAA,2CAAqB,KAAK,KAAK,KAAK,MAAM,GAAG,CAAA;AAE7D,QAAI;AACJ,QAAI,OAAO,yCAAmB;AAE5B;AAGA,mBAAY,GAAA,2CAAW,KAAK,OAAO,CAAA,IAAK,KAAK;AAC7C,cAAQ,YAAa,MAAW,KAAU;IAC5C,OAAO;AAEL,mBAAY,GAAA,2CAAW,KAAK,IAAI,IAAI,KAAK;AACzC,cAAQ;IACV;AAEA,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,WAAW;AACpB,oBAAc;AACd,kBAAY,OAAO;IACrB,OAAO;AACL,UAAI,OAAO,OAAO;AAClB,UAAI,OAAQ,KAAS;AACnB,sBAAc,KAAK,MAAM,OAAO,EAAA,IAAM;AACtC,oBAAa,OAAO,KAAM;MAC5B,OAAO;AACL,gBAAQ;AACR,sBAAc,KAAK,MAAM,OAAO,EAAA,IAAM;AACtC,oBAAa,OAAO,KAAM;MAC5B;IACF;AAEA,WAAO,KAAI,GAAA,2CAAa,MAAM,YAAY,aAAa,SAAA;EACzD;EAEA,YAAY,MAA+B;AACzC,QAAI,eAAe,KAAK,OAAO;AAC/B,QAAI,CAAC,KAAK,IAAA,KAAQ,GAAA,2CAAiB,YAAA;AAEnC,QAAI;AACJ,QAAI;AACJ,SAAI,GAAA,2CAAW,IAAA,GAAO;AACpB,kBAAY;AACZ,YAAK,GAAA,2CAAqB,KAAK,MAAM,GAAG,EAAA;IAC1C,OAAO;AACL,kBAAY;AACZ,YAAK,GAAA,2CAAqB,KAAK,MAAM,GAAG,EAAA;IAC1C;AAEA,QAAI,KAAK,UAAU,EACjB,QAAO,KAAK,KAAK,MAAM;AAGzB,UAAM,YAAY,KAAK,IAAI,KAAK,QAAQ,GAAG,CAAA,IAAK;AAEhD,QAAI,KAAK,SAAS,EAChB,QAAO,KAAK,QAAQ,KAAK;AAG3B,UAAM,KAAK,MAAM;AACjB,WAAO;EACT;EAEA,eAAe,MAA+B;AAC5C,QAAI,KAAK,UAAU,MAAK,GAAA,2CAAW,KAAK,OAAO,sCAAA,EAC7C,QAAO;AAGT,QAAI,KAAK,SAAS,KAAK,KAAK,SAAS,EACnC,QAAO;AAGT,WAAO;EACT;EAEA,gBAAwB;AAGtB,WAAO;EACT;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,cAAoB;EAAC;;AAjGhB,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAiGnC;;;AC9GA,IAAM,mCAAa;AACnB,IAAM,0CAAoB;AAC1B,IAAM,4CAAsB;AAC5B,IAAM,0CAAoB;AAC1B,IAAM,4CAAsB;AAE5B,SAAS,yCAAmB,OAAe,MAAc,OAAe,KAAW;AACjF,SAAO,MACL,KAAK,KAAK,QAAQ,QAAQ,EAAA,KACzB,OAAO,KAAK,MACb,KAAK,OAAO,IAAI,KAAK,QAAQ,EAAA,IAC7B,QAAQ;AACZ;AAEA,SAAS,yCAAmB,UAAoB,OAAe,IAAU;AACvE,MAAI,OAAO,KAAK,OAAO,MAAM,KAAK,SAAS,SAAS,KAAA;AACpD,MAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,KAAK,yCAAmB,OAAO,MAAM,GAAG,CAAA,MAAO,IAAA,IAAQ,CAAA;AACjG,MAAI,MAAM,KAAK,yCAAmB,OAAO,MAAM,OAAO,CAAA,IAAK;AAE3D,SAAO,KAAI,GAAA,2CAAa,UAAU,MAAM,OAAO,GAAA;AACjD;AAEA,SAAS,iCAAW,MAAY;AAC9B,UAAQ,KAAK,KAAK,QAAQ,KAAK;AACjC;AASO,IAAM,4CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,WAAO,yCAAmB,MAAM,kCAAY,EAAA;EAC9C;EAEA,YAAY,MAA+B;AACzC,WAAO,yCAAmB,kCAAY,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EACvE;EAEA,eAAe,MAA+B;AAC5C,QAAI,SAAS,KAAK,KAAK,QAAQ;AAC/B,QAAI,KAAK,UAAU,MAAM,iCAAW,KAAK,IAAI,EAC3C;AAGF,WAAO;EACT;EAEA,kBAA0B;AACxB,WAAO;EACT;EAEA,cAAc,MAA+B;AAC3C,WAAO,iCAAW,KAAK,IAAI,IAAI,MAAM;EACvC;EAEA,gBAAwB;AAEtB,WAAO;EACT;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;;SAlCA,aAAiC;;AAmCnC;AASO,IAAM,4CAAN,cAAqC,0CAAA;EAG1C,cAAc,IAA0B;AACtC,WAAO,yCAAmB,MAAM,yCAAmB,EAAA;EACrD;EAEA,YAAY,MAA+B;AACzC,WAAO,yCAAmB,yCAAmB,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EAC9E;;AATK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AASnC;AAGA,IAAM,sCAAgB;AACtB,IAAI;AACJ,IAAI;AAEJ,SAAS,wCAAkB,MAAY;AACrC,SAAO,4CAAsB,gDAA0B,OAAO,yCAAA;AAChE;AAEA,SAAS,0CAAoB,MAAc,OAAa;AACtD,MAAI,MAAO,OAAO;AAClB,MAAI,OAAQ,KAAS,MAAM,QAAQ;AACnC,OAAK,2CAAqB,GAAA,IAAO,UAAU,EACzC,QAAO;MAEP,QAAO;AAEX;AAEA,SAAS,yCAAmB,MAAc,OAAa;AACrD,MAAI,MAAM,wCAAkB,IAAA;AAC5B,WAAS,IAAI,GAAG,IAAI,OAAO,IACzB,QAAO,0CAAoB,MAAM,CAAA;AAEnC,SAAO;AACT;AAEA,SAAS,yCAAmB,MAAY;AACtC,SAAO,gDAA0B,OAAO,IAAI,yCAAA,IAAuB,gDAA0B,OAAO,yCAAA;AACtG;AASO,IAAM,4CAAN,cAAsC,0CAAA;EAsB3C,cAAc,IAA0B;AACtC,QAAI,OAAO,KAAK;AAChB,QAAI,YAAY,wCAAkB,yCAAA;AAClC,QAAI,UAAU,wCAAkB,uCAAA;AAChC,QAAI,OAAO,aAAa,OAAO,QAC7B,QAAO,MAAM,cAAc,EAAA;SACtB;AACL,UAAI,IAAI,4CAAsB;AAC9B,UAAI,IAAI;AACR,UAAI,IAAI;AACR,aAAO,IAAI,GAAG;AACZ;AACA,YAAI,OAAO,wCAAkB,CAAA,IAAK;AAClC,YAAI,aAAa,yCAAmB,CAAA;AACpC,YAAI,MAAM,YAAY;AACpB,cAAI;AACJ;QACF,WAAW,IAAI,YAAY;AACzB,cAAI,cAAc,0CAAoB,GAAG,CAAA;AACzC,cAAI;AACJ,iBAAO,IAAI,aAAa;AACtB,iBAAK;AACL;AACA,0BAAc,0CAAoB,GAAG,CAAA;UACvC;AACA;QACF;MACF;AAEA,aAAO,KAAI,GAAA,2CAAa,MAAM,GAAG,GAAI,OAAO,yCAAmB,GAAG,CAAA,IAAK,CAAA;IACzE;EACF;EAEA,YAAY,MAA+B;AACzC,QAAI,KAAK,OAAO,6CAAuB,KAAK,OAAO,wCACjD,QAAO,MAAM,YAAY,IAAA;AAG3B,WAAO,mCAAa,yCAAmB,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,MAAM;EAC9E;EAEA,eAAe,MAA+B;AAC5C,QAAI,KAAK,OAAO,6CAAuB,KAAK,OAAO,wCACjD,QAAO,MAAM,eAAe,IAAA;AAG9B,WAAO,0CAAoB,KAAK,MAAM,KAAK,KAAK;EAClD;EAEA,cAAc,MAA+B;AAC3C,QAAI,KAAK,OAAO,6CAAuB,KAAK,OAAO,wCACjD,QAAO,MAAM,cAAc,IAAA;AAG7B,WAAO,yCAAmB,KAAK,IAAI;EACrC;EA1EA,cAAc;AACZ,UAAK,GAAA,KAHP,aAAiC;AAI/B,QAAI,CAAC,2CACH,8CAAuB,IAAI,YAAY,WAAW,KAAK,KAAK,mCAAA,GAAgB,CAAA,MAAK,EAAE,WAAW,CAAA,CAAA,EAAI,MAAM;AAG1G,QAAI,CAAC,iDAA2B;AAC9B,wDAA4B,IAAI,YAAY,0CAAoB,4CAAsB,CAAA;AAEtF,UAAI,YAAY;AAChB,eAAS,OAAO,2CAAqB,QAAQ,yCAAmB,QAAQ;AACtE,wDAA0B,OAAO,yCAAA,IAAuB;AACxD,iBAAS,IAAI,GAAG,KAAK,IAAI,IACvB,cAAa,0CAAoB,MAAM,CAAA;MAE3C;IACF;EACF;AA0DF;;;AC7MA,IAAM,qCAAe;AAIrB,IAAM,mCAAa;AACnB,IAAM,kCAAa,KAAK;AAKxB,IAAM,mCAAa;AACnB,IAAM,oCAAc,KAAK,mCAAa;AACtC,IAAM,oCAAc,mCAAa,kCAAY;AAE7C,SAAS,iCAAW,MAAY;AAC9B,UAAO,GAAA,2CAAI,OAAO,IAAI,GAAG,EAAA,IAAM;AACjC;AAIA,SAAS,mCAAa,MAAY;AAChC,MAAI,SAAS,KAAK,OAAO,MAAM,OAAO,OAAO,EAAA;AAC7C,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,MAAM,SAAS,KAAK,KAAK,MAAM,QAAQ,KAAA;AAE3C,OAAI,GAAA,2CAAI,KAAK,MAAM,IAAI,CAAA,IAAK,EAC1B,QAAO;AAGT,SAAO;AACT;AAGA,SAAS,mCAAa,MAAY;AAChC,MAAI,OAAO,mCAAa,OAAO,CAAA;AAC/B,MAAI,UAAU,mCAAa,IAAA;AAC3B,MAAI,OAAO,mCAAa,OAAO,CAAA;AAE/B,MAAI,OAAO,YAAY,IACrB,QAAO;AAGT,MAAI,UAAU,SAAS,IACrB,QAAO;AAGT,SAAO;AACT;AAEA,SAAS,kCAAY,MAAY;AAC/B,SAAO,mCAAa,IAAA,IAAQ,mCAAa,IAAA;AAC3C;AAEA,SAAS,oCAAc,MAAY;AACjC,SAAO,kCAAY,OAAO,CAAA,IAAK,kCAAY,IAAA;AAC7C;AAEA,SAAS,kCAAY,MAAY;AAC/B,MAAI,aAAa,oCAAc,IAAA;AAE/B,MAAI,aAAa,IACf,eAAc;AAGhB,UAAQ,YAAA;IACN,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;EACX;AACF;AAEA,SAAS,qCAAe,MAAc,OAAa;AAEjD,MAAI,SAAS,KAAK,CAAC,iCAAW,IAAA,EAC5B;AAIF,MAAI,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,MAAM,UAAU,GACzE,QAAO;AAGT,MAAI,WAAW,kCAAY,IAAA;AAG3B,MAAI,UAAU,EACZ,QAAO,aAAa,IAAI,KAAK;AAI/B,MAAI,UAAU,EACZ,QAAO,aAAa,IAAI,KAAK;AAI/B,MAAI,UAAU,EACZ,QAAO,iCAAW,IAAA,IAAQ,KAAK;AAGjC,SAAO;AACT;AAOO,IAAM,2CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,QAAI,IAAI,KAAK;AACb,QAAI,IAAK,IAAI,kCAAa;AAC1B,QAAI,OAAO,KAAK,OAAO,KAAK,IAAI,OAAO,GAAA,IAAO;AAC9C,QAAI,KAAK,kCAAY,IAAA;AACrB,QAAI,YAAY,KAAK,MAAM,IAAI,EAAA;AAG/B,WAAO,YAAY,GAAG;AACpB;AACA,WAAK,kCAAY,IAAA;AACjB,kBAAY,KAAK,MAAM,IAAI,EAAA;IAC7B;AAGA,QAAI,QAAQ;AACZ,QAAI,aAAa;AACjB,WAAO,aAAa,WAAW;AAC7B,oBAAc,qCAAe,MAAM,KAAA;AACnC;IACF;AAEA;AACA,kBAAc,qCAAe,MAAM,KAAA;AAEnC,QAAI,MAAM,YAAY;AACtB,WAAO,KAAI,GAAA,2CAAa,MAAM,MAAM,OAAO,GAAA;EAC7C;EAEA,YAAY,MAA+B;AACzC,QAAI,KAAK,kCAAY,KAAK,IAAI;AAC9B,aAAS,QAAQ,GAAG,QAAQ,KAAK,OAAO,QACtC,OAAM,qCAAe,KAAK,MAAM,KAAA;AAGlC,WAAO,KAAK,KAAK,MAAM;EACzB;EAEA,eAAe,MAA+B;AAC5C,WAAO,qCAAe,KAAK,MAAM,KAAK,KAAK;EAC7C;EAEA,gBAAgB,MAA+B;AAC7C,WAAO,iCAAW,KAAK,IAAI,IAAI,KAAK;EACtC;EAEA,cAAc,MAA+B;AAC3C,WAAO,oCAAc,KAAK,IAAI;EAChC;EAEA,gBAAwB;AAEtB,WAAO;EACT;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,iBAAiB,MAAgC,cAAqC;AAEpF,QAAI,aAAa,SAAS,KAAK,MAAM;AACnC,UAAI,iCAAW,aAAa,IAAI,KAAK,CAAC,iCAAW,KAAK,IAAI,KAAK,aAAa,QAAQ,EAClF,MAAK;eACI,CAAC,iCAAW,aAAa,IAAI,KAAK,iCAAW,KAAK,IAAI,KAAK,aAAa,QAAQ,EACzF,MAAK;IAET;EACF;;SAtEA,aAAiC;;AAuEnC;;;ACtLA,IAAM,uCAAiB;AACvB,IAAM,qCAAe;AAIrB,IAAM,2CAAqB;AAE3B,SAAS,oCAAc,OAAe,MAAc,OAAe,KAAW;AAC5E,SACE,QACE,MAAM,OACN,KAAK,MAAM,OAAO,CAAA,IAClB,MAAM,QAAQ,KACd,MAAM;AAEZ;AAEA,SAAS,oCAAc,OAAe,IAAU;AAC9C,MAAI,OAAO,KAAK,MAAO,KAAK,KAAK,SAAU,IAAA;AAC3C,MAAI,QAAQ,IAAI,KAAK,OAAO,KAAK,oCAAc,OAAO,MAAM,GAAG,CAAA,KAAM,EAAA;AACrE,MAAI,MAAM,KAAK,IAAI,oCAAc,OAAO,MAAM,OAAO,CAAA;AACrD,SAAO;IAAC;IAAM;IAAO;;AACvB;AAEA,SAAS,iCAAW,MAAY;AAC9B,SAAO,KAAK,MAAO,OAAO,IAAK,CAAA;AACjC;AAEA,SAAS,qCAAe,MAAc,OAAa;AAMjD,MAAI,QAAQ,OAAO;AAEjB,WAAO;;AAGP,WAAO,iCAAW,IAAA,IAAQ;AAE9B;AAOO,IAAM,4CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,QAAI,CAAC,MAAM,OAAO,GAAA,IAAO,oCAAc,sCAAgB,EAAA;AACvD,QAAI,MAAM;AACV,QAAI,QAAQ,GAAG;AACb,YAAM;AACN,cAAQ;IACV;AAEA,WAAO,KAAI,GAAA,2CAAa,MAAM,KAAK,MAAM,OAAO,GAAA;EAClD;EAEA,YAAY,MAA+B;AACzC,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,QAAQ,KACf,SAAQ;AAGV,WAAO,oCAAc,sCAAgB,MAAM,KAAK,OAAO,KAAK,GAAG;EACjE;EAEA,eAAe,MAA+B;AAC5C,WAAO,qCAAe,KAAK,MAAM,KAAK,KAAK;EAC7C;EAEA,kBAA0B;AACxB,WAAO;EACT;EAEA,cAAc,MAA+B;AAC3C,WAAO,MAAM,iCAAW,KAAK,IAAI;EACnC;EAEA,cAAc,MAA+B;AAI3C,WAAO,KAAK,QAAQ,OAAO,OAAO;EACpC;EAEA,UAAoB;AAClB,WAAO;MAAC;MAAM;;EAChB;;SA3CA,aAAiC;;AA4CnC;AAMO,IAAM,4CAAN,cAAwC,0CAAA;EAG7C,cAAc,IAA0B;AACtC,QAAI,CAAC,MAAM,OAAO,GAAA,IAAO,oCAAc,sCAAgB,EAAA;AACvD,YAAQ;AACR,WAAO,KAAI,GAAA,2CAAa,MAAM,MAAM,MAAM,OAAO,GAAA;EACnD;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,gBAAwB;AAEtB,WAAO;EACT;;AAhBK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAgBnC;AAOO,IAAM,4CAAN,cAA6B,0CAAA;EAGlC,cAAc,IAA0B;AACtC,QAAI,CAAC,MAAM,OAAO,GAAA,IAAO,oCAAc,oCAAc,EAAA;AACrD,QAAI,MAAM;AACV,QAAI,QAAQ,GAAG;AACb,YAAM;AACN,aAAO,IAAI;IACb;AAEA,WAAO,KAAI,GAAA,2CAAa,MAAM,KAAK,MAAM,OAAO,GAAA;EAClD;EAEA,YAAY,MAA+B;AACzC,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,QAAQ,MACf,QAAO,IAAI;AAGb,WAAO,oCAAc,oCAAc,MAAM,KAAK,OAAO,KAAK,GAAG;EAC/D;EAEA,eAAe,MAA+B;AAC5C,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,QAAQ,MACf,QAAO,IAAI;AAGb,WAAO,qCAAe,MAAM,KAAK,KAAK;EACxC;EAEA,aAAa,MAAgC;AAC3C,WAAO,KAAK,QAAQ;EACtB;EAEA,YAAY,MAAsC;AAChD,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,MAAM,KAAK,QAAQ,QAAQ,OAAO;AACvC,WAAK,OAAO,IAAI,KAAK;IACvB;EACF;EAEA,UAAoB;AAClB,WAAO;MAAC;MAAO;;EACjB;EAEA,cAAc,MAA+B;AAI3C,WAAO,KAAK,QAAQ,QAAQ,OAAO;EACrC;;AApDK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAoDnC;;;AC3KO,SAAS,0CAAe,MAAwB;AACrD,UAAQ,MAAA;IACN,KAAK;AACH,aAAO,KAAI,GAAA,2CAAe;IAC5B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAe;IAC5B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAwB;IACrC,KAAK;AACH,aAAO,KAAI,GAAA,2CAAa;IAC1B,KAAK;AACH,aAAO,KAAI,GAAA,0CAAa;IAC1B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAa;IAC1B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAmB;IAChC,KAAK;AACH,aAAO,KAAI,GAAA,2CAAqB;IAClC,KAAK;AACH,aAAO,KAAI,GAAA,2CAAsB;IACnC,KAAK;AACH,aAAO,KAAI,GAAA,2CAAe;IAC5B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAc;IAC3B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAa;IAC1B,KAAK;IACL;AACE,aAAO,KAAI,GAAA,2CAAgB;EAC/B;AACF;;;AC1CA,IAAI,uCAAiB,oBAAI,IAAA;AAOlB,IAAM,4CAAN,MAAM;;EAWX,OAAO,OAAqB;AAC1B,WAAO,KAAK,UAAU,OAAO,KAAA;EAC/B;;EAGA,cAAc,OAAwC;AACpD,WAAO,KAAK,UAAU,cAAc,KAAA;EACtC;;EAGA,YAAY,OAAa,KAAmB;AAE1C,QAAI,OAAO,KAAK,UAAU,gBAAgB;AAExC,aAAO,KAAK,UAAU,YAAY,OAAO,GAAA;AAG3C,QAAI,MAAM,MACR,OAAM,IAAI,WAAW,gCAAA;AAIvB,WAAO,GAAG,KAAK,UAAU,OAAO,KAAA,CAAA,MAAY,KAAK,UAAU,OAAO,GAAA,CAAA;EACpE;;EAGA,mBAAmB,OAAa,KAAkC;AAEhE,QAAI,OAAO,KAAK,UAAU,uBAAuB;AAE/C,aAAO,KAAK,UAAU,mBAAmB,OAAO,GAAA;AAGlD,QAAI,MAAM,MACR,OAAM,IAAI,WAAW,gCAAA;AAGvB,QAAI,aAAa,KAAK,UAAU,cAAc,KAAA;AAC9C,QAAI,WAAW,KAAK,UAAU,cAAc,GAAA;AAC5C,WAAO;SACF,WAAW,IAAI,CAAA,OAAM;QAAC,GAAG;QAAG,QAAQ;MAAY,EAAA;MACnD;QAAC,MAAM;QAAW,OAAO;QAAO,QAAQ;MAAQ;SAC7C,SAAS,IAAI,CAAA,OAAM;QAAC,GAAG;QAAG,QAAQ;MAAU,EAAA;;EAEnD;;EAGA,kBAAsD;AACpD,QAAI,kBAAkB,KAAK,UAAU,gBAAe;AACpD,QAAI,gDAAA,GAA6B;AAC/B,UAAI,CAAC,KAAK,kBACR,MAAK,oBAAoB,2CAAqB,gBAAgB,QAAQ,KAAK,OAAO;AAEpF,sBAAgB,YAAY,KAAK;AACjC,sBAAgB,SAAS,KAAK,sBAAsB,SAAS,KAAK,sBAAsB;IAC1F;AAIA,QAAI,gBAAgB,aAAa,sBAC/B,iBAAgB,WAAW;AAG7B,WAAO;EACT;EAtEA,YAAY,QAAgB,UAAsC,CAAC,GAAG;AACpE,SAAK,YAAY,6CAAuB,QAAQ,OAAA;AAChD,SAAK,UAAU;EACjB;AAoEF;AAaA,IAAM,0CAAoB;EACxB,MAAM;;IAEJ,IAAI;EACN;EACA,OAAO,CAEP;AACF;AAEA,SAAS,6CAAuB,QAAgB,UAAsC,CAAC,GAAC;AAGtF,MAAI,OAAO,QAAQ,WAAW,aAAa,6CAAA,GAA0B;AACnE,cAAU;MAAC,GAAG;IAAO;AACrB,QAAI,OAAO,wCAAkB,OAAO,QAAQ,MAAM,CAAA,EAAG,OAAO,MAAM,GAAA,EAAK,CAAA,CAAE;AACzE,QAAI,mBAAmB,QAAQ,SAAS,QAAQ;AAChD,YAAQ,YAAY,SAAA,QAAA,SAAA,SAAA,OAAQ;AAC5B,WAAO,QAAQ;EACjB;AAEA,MAAI,WAAW,UAAU,UAAU,OAAO,QAAQ,OAAA,EAAS,KAAK,CAAC,GAAG,MAAM,EAAE,CAAA,IAAK,EAAE,CAAA,IAAK,KAAK,CAAA,EAAG,KAAI,IAAK;AACzG,MAAI,qCAAe,IAAI,QAAA,EACrB,QAAO,qCAAe,IAAI,QAAA;AAG5B,MAAI,kBAAkB,IAAI,KAAK,eAAe,QAAQ,OAAA;AACtD,uCAAe,IAAI,UAAU,eAAA;AAC7B,SAAO;AACT;AAEA,IAAI,gDAA0C;AAC9C,SAAS,+CAAA;AACP,MAAI,iDAA2B,KAC7B,iDAA0B,IAAI,KAAK,eAAe,SAAS;IACzD,MAAM;IACN,QAAQ;EACV,CAAA,EAAG,OAAO,IAAI,KAAK,MAAM,GAAG,GAAG,CAAA,CAAA,MAAQ;AAGzC,SAAO;AACT;AAEA,IAAI,mDAA6C;AACjD,SAAS,kDAAA;AACP,MAAI,oDAA8B,KAChC,oDAA6B,IAAI,KAAK,eAAe,MAAM;IACzD,MAAM;IACN,QAAQ;EACV,CAAA,EAAG,gBAAe,EAAG,cAAc;AAGrC,SAAO;AACT;AAEA,SAAS,2CAAqB,QAAgB,SAAmC;AAC/E,MAAI,CAAC,QAAQ,aAAa,CAAC,QAAQ,KACjC,QAAO;AAKT,WAAS,OAAO,QAAQ,0BAA0B,EAAA;AAClD,aAAW,OAAO,SAAS,KAAA,IAAS,KAAK,QAAQ;AACjD,MAAI,YAAY,6CAAuB,QAAQ;IAC7C,GAAG;IACH,UAAU;;EACZ,CAAA;AAEA,MAAI,MAAM,SAAS,UAAU,cAAc,IAAI,KAAK,MAAM,GAAG,GAAG,CAAA,CAAA,EAAI,KAAK,CAAA,MAAK,EAAE,SAAS,MAAA,EAAS,OAAO,EAAA;AACzG,MAAI,MAAM,SAAS,UAAU,cAAc,IAAI,KAAK,MAAM,GAAG,GAAG,EAAA,CAAA,EAAK,KAAK,CAAA,MAAK,EAAE,SAAS,MAAA,EAAS,OAAO,EAAA;AAE1G,MAAI,QAAQ,KAAK,QAAQ,GACvB,QAAO;AAGT,MAAI,QAAQ,MAAM,QAAQ,GACxB,QAAO;AAGT,MAAI,QAAQ,KAAK,QAAQ,GACvB,QAAO;AAGT,MAAI,QAAQ,MAAM,QAAQ,GACxB,QAAO;AAGT,QAAM,IAAI,MAAM,8BAAA;AAClB;", "names": ["date", "res", "ms", "isNegative", "_match_groups"]}