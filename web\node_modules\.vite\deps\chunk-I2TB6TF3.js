import {
  esm_default
} from "./chunk-2VEQPS3P.js";
import {
  createSubscriber
} from "./chunk-O7CJ26HI.js";
import {
  clsx
} from "./chunk-U7P2NEEE.js";
import {
  effect_root,
  get,
  getContext,
  hasContext,
  on,
  proxy,
  set,
  setContext,
  state,
  strict_equals,
  tick,
  untrack,
  user_derived,
  user_effect,
  user_pre_effect
} from "./chunk-2PJMTVJS.js";
import {
  true_default
} from "./chunk-HNWPC2PS.js";
import {
  __privateAdd,
  __privateGet,
  __privateSet
} from "./chunk-UQOTJTBP.js";

// node_modules/svelte-toolbelt/dist/utils/compose-handlers.js
function composeHandlers(...handlers) {
  return function(e) {
    var _a;
    for (const handler of handlers) {
      if (!handler)
        continue;
      if (e.defaultPrevented)
        return;
      if (typeof handler === "function") {
        handler.call(this, e);
      } else {
        (_a = handler.current) == null ? void 0 : _a.call(this, e);
      }
    }
  };
}

// node_modules/svelte-toolbelt/dist/utils/strings.js
var NUMBER_CHAR_RE = /\d/;
var STR_SPLITTERS = ["-", "_", "/", "."];
function isUppercase(char = "") {
  if (NUMBER_CHAR_RE.test(char))
    return void 0;
  return char !== char.toLowerCase();
}
function splitByCase(str) {
  const parts = [];
  let buff = "";
  let previousUpper;
  let previousSplitter;
  for (const char of str) {
    const isSplitter = STR_SPLITTERS.includes(char);
    if (isSplitter === true) {
      parts.push(buff);
      buff = "";
      previousUpper = void 0;
      continue;
    }
    const isUpper = isUppercase(char);
    if (previousSplitter === false) {
      if (previousUpper === false && isUpper === true) {
        parts.push(buff);
        buff = char;
        previousUpper = isUpper;
        continue;
      }
      if (previousUpper === true && isUpper === false && buff.length > 1) {
        const lastChar = buff.at(-1);
        parts.push(buff.slice(0, Math.max(0, buff.length - 1)));
        buff = lastChar + char;
        previousUpper = isUpper;
        continue;
      }
    }
    buff += char;
    previousUpper = isUpper;
    previousSplitter = isSplitter;
  }
  parts.push(buff);
  return parts;
}
function pascalCase(str) {
  if (!str)
    return "";
  return splitByCase(str).map((p) => upperFirst(p)).join("");
}
function camelCase(str) {
  return lowerFirst(pascalCase(str || ""));
}
function upperFirst(str) {
  return str ? str[0].toUpperCase() + str.slice(1) : "";
}
function lowerFirst(str) {
  return str ? str[0].toLowerCase() + str.slice(1) : "";
}

// node_modules/svelte-toolbelt/dist/utils/css-to-style-obj.js
function cssToStyleObj(css) {
  if (!css)
    return {};
  const styleObj = {};
  function iterator(name, value) {
    if (name.startsWith("-moz-") || name.startsWith("-webkit-") || name.startsWith("-ms-") || name.startsWith("-o-")) {
      styleObj[pascalCase(name)] = value;
      return;
    }
    if (name.startsWith("--")) {
      styleObj[name] = value;
      return;
    }
    styleObj[camelCase(name)] = value;
  }
  esm_default(css, iterator);
  return styleObj;
}

// node_modules/svelte-toolbelt/dist/utils/is.js
function isFunction(value) {
  return typeof value === "function";
}
function isObject(value) {
  return value !== null && typeof value === "object";
}
var CLASS_VALUE_PRIMITIVE_TYPES = ["string", "number", "bigint", "boolean"];
function isClassValue(value) {
  if (value === null || value === void 0)
    return true;
  if (CLASS_VALUE_PRIMITIVE_TYPES.includes(typeof value))
    return true;
  if (Array.isArray(value))
    return value.every((item) => isClassValue(item));
  if (typeof value === "object") {
    if (Object.getPrototypeOf(value) !== Object.prototype)
      return false;
    return true;
  }
  return false;
}

// node_modules/svelte-toolbelt/dist/utils/execute-callbacks.js
function executeCallbacks(...callbacks) {
  return (...args) => {
    for (const callback of callbacks) {
      if (typeof callback === "function") {
        callback(...args);
      }
    }
  };
}

// node_modules/svelte-toolbelt/dist/utils/style-to-css.js
function createParser(matcher, replacer) {
  const regex = RegExp(matcher, "g");
  return (str) => {
    if (typeof str !== "string") {
      throw new TypeError(`expected an argument of type string, but got ${typeof str}`);
    }
    if (!str.match(regex))
      return str;
    return str.replace(regex, replacer);
  };
}
var camelToKebab = createParser(/[A-Z]/, (match) => `-${match.toLowerCase()}`);
function styleToCSS(styleObj) {
  if (!styleObj || typeof styleObj !== "object" || Array.isArray(styleObj)) {
    throw new TypeError(`expected an argument of type object, but got ${typeof styleObj}`);
  }
  return Object.keys(styleObj).map((property) => `${camelToKebab(property)}: ${styleObj[property]};`).join("\n");
}

// node_modules/svelte-toolbelt/dist/utils/style.js
function styleToString(style = {}) {
  return styleToCSS(style).replace("\n", " ");
}
var srOnlyStyles = {
  position: "absolute",
  width: "1px",
  height: "1px",
  padding: "0",
  margin: "-1px",
  overflow: "hidden",
  clip: "rect(0, 0, 0, 0)",
  whiteSpace: "nowrap",
  borderWidth: "0",
  transform: "translateX(-100%)"
};
var srOnlyStylesString = styleToString(srOnlyStyles);

// node_modules/svelte-toolbelt/dist/utils/merge-props.js
function isEventHandler(key) {
  var _a;
  return key.length > 2 && key.startsWith("on") && key[2] === ((_a = key[2]) == null ? void 0 : _a.toLowerCase());
}
function mergeProps(...args) {
  const result = { ...args[0] };
  for (let i = 1; i < args.length; i++) {
    const props = args[i];
    for (const key in props) {
      const a = result[key];
      const b = props[key];
      const aIsFunction = typeof a === "function";
      const bIsFunction = typeof b === "function";
      if (aIsFunction && typeof bIsFunction && isEventHandler(key)) {
        const aHandler = a;
        const bHandler = b;
        result[key] = composeHandlers(aHandler, bHandler);
      } else if (aIsFunction && bIsFunction) {
        result[key] = executeCallbacks(a, b);
      } else if (key === "class") {
        const aIsClassValue = isClassValue(a);
        const bIsClassValue = isClassValue(b);
        if (aIsClassValue && bIsClassValue) {
          result[key] = clsx(a, b);
        } else if (aIsClassValue) {
          result[key] = clsx(a);
        } else if (bIsClassValue) {
          result[key] = clsx(b);
        }
      } else if (key === "style") {
        const aIsObject = typeof a === "object";
        const bIsObject = typeof b === "object";
        const aIsString = typeof a === "string";
        const bIsString = typeof b === "string";
        if (aIsObject && bIsObject) {
          result[key] = { ...a, ...b };
        } else if (aIsObject && bIsString) {
          const parsedStyle = cssToStyleObj(b);
          result[key] = { ...a, ...parsedStyle };
        } else if (aIsString && bIsObject) {
          const parsedStyle = cssToStyleObj(a);
          result[key] = { ...parsedStyle, ...b };
        } else if (aIsString && bIsString) {
          const parsedStyleA = cssToStyleObj(a);
          const parsedStyleB = cssToStyleObj(b);
          result[key] = { ...parsedStyleA, ...parsedStyleB };
        } else if (aIsObject) {
          result[key] = a;
        } else if (bIsObject) {
          result[key] = b;
        } else if (aIsString) {
          result[key] = a;
        } else if (bIsString) {
          result[key] = b;
        }
      } else {
        result[key] = b !== void 0 ? b : a;
      }
    }
  }
  if (typeof result.style === "object") {
    result.style = styleToString(result.style).replaceAll("\n", " ");
  }
  if (result.hidden !== true) {
    result.hidden = void 0;
    delete result.hidden;
  }
  if (result.disabled !== true) {
    result.disabled = void 0;
    delete result.disabled;
  }
  return result;
}

// node_modules/svelte-toolbelt/dist/box/box.svelte.js
var BoxSymbol = Symbol("box");
var isWritableSymbol = Symbol("is-writable");
function isBox(value) {
  return isObject(value) && BoxSymbol in value;
}
function isWritableBox(value) {
  return box.isBox(value) && isWritableSymbol in value;
}
function box(initialValue) {
  let current = state(proxy(initialValue));
  return {
    [BoxSymbol]: true,
    [isWritableSymbol]: true,
    get current() {
      return get(current);
    },
    set current(v) {
      set(current, v, true);
    }
  };
}
function boxWith(getter, setter) {
  const derived = user_derived(getter);
  if (setter) {
    return {
      [BoxSymbol]: true,
      [isWritableSymbol]: true,
      get current() {
        return get(derived);
      },
      set current(v) {
        setter(v);
      }
    };
  }
  return {
    [BoxSymbol]: true,
    get current() {
      return getter();
    }
  };
}
function boxFrom(value) {
  if (box.isBox(value)) return value;
  if (isFunction(value)) return box.with(value);
  return box(value);
}
function boxFlatten(boxes) {
  return Object.entries(boxes).reduce(
    (acc, [key, b]) => {
      if (!box.isBox(b)) {
        return Object.assign(acc, { [key]: b });
      }
      if (box.isWritableBox(b)) {
        Object.defineProperty(acc, key, {
          get() {
            return b.current;
          },
          set(v) {
            b.current = v;
          }
        });
      } else {
        Object.defineProperty(acc, key, {
          get() {
            return b.current;
          }
        });
      }
      return acc;
    },
    {}
  );
}
function toReadonlyBox(b) {
  if (!box.isWritableBox(b)) return b;
  return {
    [BoxSymbol]: true,
    get current() {
      return b.current;
    }
  };
}
box.from = boxFrom;
box.with = boxWith;
box.flatten = boxFlatten;
box.readonly = toReadonlyBox;
box.isBox = isBox;
box.isWritableBox = isWritableBox;

// node_modules/runed/dist/utilities/watch/watch.svelte.js
function runEffect(flush, effect) {
  switch (flush) {
    case "post":
      user_effect(effect);
      break;
    case "pre":
      user_pre_effect(effect);
      break;
  }
}
function runWatcher(sources, flush, effect, options = {}) {
  const { lazy = false } = options;
  let active = !lazy;
  let previousValues = Array.isArray(sources) ? [] : void 0;
  runEffect(flush, () => {
    const values = Array.isArray(sources) ? sources.map((source) => source()) : sources();
    if (!active) {
      active = true;
      previousValues = values;
      return;
    }
    const cleanup = untrack(() => effect(values, previousValues));
    previousValues = values;
    return cleanup;
  });
}
function runWatcherOnce(sources, flush, effect) {
  const cleanupRoot = effect_root(() => {
    let stop = false;
    runWatcher(
      sources,
      flush,
      (values, previousValues) => {
        if (stop) {
          cleanupRoot();
          return;
        }
        const cleanup = effect(values, previousValues);
        stop = true;
        return cleanup;
      },
      // Running the effect immediately just once makes no sense at all.
      // That's just `onMount` with extra steps.
      { lazy: true }
    );
  });
  user_effect(() => {
    return cleanupRoot;
  });
}
function watch(sources, effect, options) {
  runWatcher(sources, "post", effect, options);
}
function watchPre(sources, effect, options) {
  runWatcher(sources, "pre", effect, options);
}
watch.pre = watchPre;
function watchOnce(source, effect) {
  runWatcherOnce(source, "post", effect);
}
function watchOncePre(source, effect) {
  runWatcherOnce(source, "pre", effect);
}
watchOnce.pre = watchOncePre;

// node_modules/runed/dist/utilities/previous/previous.svelte.js
var _previous, _curr;
var Previous = class {
  constructor(getter) {
    __privateAdd(this, _previous, state(void 0));
    __privateAdd(this, _curr);
    user_effect(() => {
      set(__privateGet(this, _previous), __privateGet(this, _curr), true);
      __privateSet(this, _curr, getter());
    });
  }
  get current() {
    return get(__privateGet(this, _previous));
  }
};
_previous = new WeakMap();
_curr = new WeakMap();

// node_modules/runed/dist/internal/configurable-globals.js
var defaultWindow = true_default && typeof window !== "undefined" ? window : void 0;
var defaultDocument = true_default && typeof window !== "undefined" ? window.document : void 0;
var defaultNavigator = true_default && typeof window !== "undefined" ? window.navigator : void 0;
var defaultLocation = true_default && typeof window !== "undefined" ? window.location : void 0;

// node_modules/runed/dist/internal/utils/dom.js
function getActiveElement(document2) {
  let activeElement2 = document2.activeElement;
  while (activeElement2 == null ? void 0 : activeElement2.shadowRoot) {
    const node = activeElement2.shadowRoot.activeElement;
    if (node === activeElement2)
      break;
    else
      activeElement2 = node;
  }
  return activeElement2;
}

// node_modules/runed/dist/utilities/active-element/active-element.svelte.js
var _document, _subscribe;
var ActiveElement = class {
  constructor(options = {}) {
    __privateAdd(this, _document);
    __privateAdd(this, _subscribe);
    const {
      window: window2 = defaultWindow,
      document: document2 = window2 == null ? void 0 : window2.document
    } = options;
    if (strict_equals(window2, void 0)) return;
    __privateSet(this, _document, document2);
    __privateSet(this, _subscribe, createSubscriber((update) => {
      const cleanupFocusIn = on(window2, "focusin", update);
      const cleanupFocusOut = on(window2, "focusout", update);
      return () => {
        cleanupFocusIn();
        cleanupFocusOut();
      };
    }));
  }
  get current() {
    var _a;
    (_a = __privateGet(this, _subscribe)) == null ? void 0 : _a.call(this);
    if (!__privateGet(this, _document)) return null;
    return getActiveElement(__privateGet(this, _document));
  }
};
_document = new WeakMap();
_subscribe = new WeakMap();
var activeElement = new ActiveElement();

// node_modules/runed/dist/internal/utils/is.js
function isFunction2(value) {
  return typeof value === "function";
}

// node_modules/runed/dist/utilities/use-debounce/use-debounce.svelte.js
function useDebounce(callback, wait = 250) {
  let context = state(null);
  function debounced(...args) {
    if (get(context)) {
      if (get(context).timeout) {
        clearTimeout(get(context).timeout);
      }
    } else {
      let resolve;
      let reject;
      const promise = new Promise((res, rej) => {
        resolve = res;
        reject = rej;
      });
      set(
        context,
        {
          timeout: null,
          runner: null,
          promise,
          resolve,
          reject
        },
        true
      );
    }
    get(context).runner = async () => {
      if (!get(context)) return;
      const ctx = get(context);
      set(context, null);
      try {
        ctx.resolve(await callback.apply(this, args));
      } catch (error) {
        ctx.reject(error);
      }
    };
    get(context).timeout = setTimeout(get(context).runner, strict_equals(typeof wait, "function") ? wait() : wait);
    return get(context).promise;
  }
  debounced.cancel = async () => {
    if (!get(context) || strict_equals(get(context).timeout, null)) {
      await new Promise((resolve) => setTimeout(resolve, 0));
      if (!get(context) || strict_equals(get(context).timeout, null)) return;
    }
    clearTimeout(get(context).timeout);
    get(context).reject("Cancelled");
    set(context, null);
  };
  debounced.runScheduledNow = async () => {
    var _a, _b;
    if (!get(context) || !get(context).timeout) {
      await new Promise((resolve) => setTimeout(resolve, 0));
      if (!get(context) || !get(context).timeout) return;
    }
    clearTimeout(get(context).timeout);
    get(context).timeout = null;
    await ((_b = (_a = get(context)).runner) == null ? void 0 : _b.call(_a));
  };
  Object.defineProperty(debounced, "pending", {
    enumerable: true,
    get() {
      var _a;
      return !!((_a = get(context)) == null ? void 0 : _a.timeout);
    }
  });
  return debounced;
}

// node_modules/runed/dist/internal/utils/get.js
function get2(value) {
  if (isFunction2(value)) {
    return value();
  }
  return value;
}

// node_modules/runed/dist/utilities/element-size/element-size.svelte.js
var _size;
var ElementSize = class {
  constructor(node, options = { box: "border-box" }) {
    __privateAdd(this, _size, state(proxy({ width: 0, height: 0 })));
    var _a, _b;
    const window2 = options.window ?? defaultWindow;
    set(
      __privateGet(this, _size),
      {
        width: ((_a = options.initialSize) == null ? void 0 : _a.width) ?? 0,
        height: ((_b = options.initialSize) == null ? void 0 : _b.height) ?? 0
      },
      true
    );
    user_effect(() => {
      if (!window2) return;
      const node$ = get2(node);
      if (!node$) return;
      const observer = new window2.ResizeObserver((entries) => {
        for (const entry of entries) {
          const boxSize = strict_equals(options.box, "content-box") ? entry.contentBoxSize : entry.borderBoxSize;
          const boxSizeArr = Array.isArray(boxSize) ? boxSize : [boxSize];
          get(__privateGet(this, _size)).width = boxSizeArr.reduce((acc, size) => Math.max(acc, size.inlineSize), 0);
          get(__privateGet(this, _size)).height = boxSizeArr.reduce((acc, size) => Math.max(acc, size.blockSize), 0);
        }
      });
      observer.observe(node$);
      return () => {
        observer.disconnect();
      };
    });
  }
  get current() {
    return get(__privateGet(this, _size));
  }
  get width() {
    return get(__privateGet(this, _size)).width;
  }
  get height() {
    return get(__privateGet(this, _size)).height;
  }
};
_size = new WeakMap();

// node_modules/runed/dist/utilities/is-mounted/is-mounted.svelte.js
var _isMounted;
var IsMounted = class {
  constructor() {
    __privateAdd(this, _isMounted, state(false));
    user_effect(() => {
      untrack(() => set(__privateGet(this, _isMounted), true));
      return () => {
        set(__privateGet(this, _isMounted), false);
      };
    });
  }
  get current() {
    return get(__privateGet(this, _isMounted));
  }
};
_isMounted = new WeakMap();

// node_modules/runed/dist/utilities/context/context.js
var _name, _key;
var Context = class {
  /**
   * @param name The name of the context.
   * This is used for generating the context key and error messages.
   */
  constructor(name) {
    __privateAdd(this, _name);
    __privateAdd(this, _key);
    __privateSet(this, _name, name);
    __privateSet(this, _key, Symbol(name));
  }
  /**
   * The key used to get and set the context.
   *
   * It is not recommended to use this value directly.
   * Instead, use the methods provided by this class.
   */
  get key() {
    return __privateGet(this, _key);
  }
  /**
   * Checks whether this has been set in the context of a parent component.
   *
   * Must be called during component initialisation.
   */
  exists() {
    return hasContext(__privateGet(this, _key));
  }
  /**
   * Retrieves the context that belongs to the closest parent component.
   *
   * Must be called during component initialisation.
   *
   * @throws An error if the context does not exist.
   */
  get() {
    const context = getContext(__privateGet(this, _key));
    if (context === void 0) {
      throw new Error(`Context "${__privateGet(this, _name)}" not found`);
    }
    return context;
  }
  /**
   * Retrieves the context that belongs to the closest parent component,
   * or the given fallback value if the context does not exist.
   *
   * Must be called during component initialisation.
   */
  getOr(fallback) {
    const context = getContext(__privateGet(this, _key));
    if (context === void 0) {
      return fallback;
    }
    return context;
  }
  /**
   * Associates the given value with the current component and returns it.
   *
   * Must be called during component initialisation.
   */
  set(context) {
    return setContext(__privateGet(this, _key), context);
  }
};
_name = new WeakMap();
_key = new WeakMap();

// node_modules/svelte-toolbelt/dist/utils/on-destroy-effect.svelte.js
function onDestroyEffect(fn) {
  user_effect(() => {
    return () => {
      fn();
    };
  });
}

// node_modules/svelte-toolbelt/dist/utils/use-ref-by-id.svelte.js
function useRefById({
  id,
  ref,
  deps = () => true,
  onRefChange,
  getRootNode
}) {
  watch([() => id.current, deps], ([_id]) => {
    const rootNode = (getRootNode == null ? void 0 : getRootNode()) ?? document;
    const node = rootNode == null ? void 0 : rootNode.getElementById(_id);
    if (node) ref.current = node;
    else ref.current = null;
    onRefChange == null ? void 0 : onRefChange(ref.current);
  });
  onDestroyEffect(() => {
    ref.current = null;
    onRefChange == null ? void 0 : onRefChange(null);
  });
}

// node_modules/svelte-toolbelt/dist/utils/after-sleep.js
function afterSleep(ms, cb) {
  return setTimeout(cb, ms);
}

// node_modules/svelte-toolbelt/dist/utils/after-tick.js
function afterTick(fn) {
  tick().then(fn);
}

// node_modules/svelte-toolbelt/dist/utils/on-mount-effect.svelte.js
function onMountEffect(fn) {
  user_effect(() => {
    const cleanup = untrack(() => fn());
    return cleanup;
  });
}

export {
  box,
  composeHandlers,
  cssToStyleObj,
  executeCallbacks,
  styleToString,
  srOnlyStyles,
  srOnlyStylesString,
  mergeProps,
  useDebounce,
  watch,
  ElementSize,
  IsMounted,
  Previous,
  Context,
  onDestroyEffect,
  useRefById,
  afterSleep,
  afterTick,
  onMountEffect
};
//# sourceMappingURL=chunk-I2TB6TF3.js.map
