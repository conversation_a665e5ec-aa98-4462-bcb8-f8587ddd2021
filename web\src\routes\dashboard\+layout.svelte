<script lang="ts">
  import DashboardMainNav from '$components/layout/DashboardNav.svelte';
  import Logo from '$components/ui/Logo.svelte';
  import GlobalSearch from '$components/search/GlobalSearch.svelte';
  import { initResumeParsingHandler } from '$lib/websocket/resume-parsing-handler';
  import { browser } from '$app/environment';
  import { notifications, unreadCount } from '$lib/stores/notification';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import * as Tooltip from '$lib/components/ui/tooltip';
  import { Button } from '$lib/components/ui/button';
  import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar';
  import { Badge } from '$lib/components/ui/badge';
  import { Gift, ChevronDown, Users, Building, Sun, Moon, Monitor, Bell } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import { mode, setMode } from 'mode-watcher';
  import websocket from '$lib/websocket/websocket-singleton';
  import { KeyboardShortcutsDialog } from '$components/keyboard-shortcuts';
  import { page } from '$app/stores';
  import { toast } from 'svelte-sonner';

  const { data, children } = $props();
  const user = data?.user;
  let showKeyboardShortcutsDialog = $state(false);

  // Initialize all singletons and handlers
  if (browser) {
    // Initialize the WebSocket singleton - this will only connect once
    console.log('Initializing WebSocket singleton...');
    websocket.initialize();

    // Fetch notifications from server
    console.log('Fetching notifications from server...');
    notifications.fetchFromServer();

    // Initialize resume parsing handler
    console.log('Initializing resume parsing handler...');
    initResumeParsingHandler();
  }

  function openKeyboardShortcutsDialog() {
    console.log('Opening keyboard shortcuts dialog');
    showKeyboardShortcutsDialog = !showKeyboardShortcutsDialog;
    console.log('Keyboard shortcuts dialog state:', showKeyboardShortcutsDialog);
  }

  function isActive(href: string, exact = false) {
    if (exact) {
      return $page.url.pathname === href;
    }
    return $page.url.pathname.includes(href);
  }

  // Logout function
  async function logout() {
    try {
      const res = await fetch('/api/auth/logout', { method: 'POST' });
      if (res.ok) {
        toast.success('Logged out successfully');
        window.location.href = '/auth/sign-in';
      } else {
        toast.error('Failed to log out', {
          description: 'Please try again later',
        });
      }
    } catch (error) {
      toast.error('Network error', {
        description: 'Could not connect to the server',
      });
      console.error('Logout error:', error);
    }
  }
</script>

<div class="h-[calc(100vh-65px)]">
  <div class="bg-secondary/80">
    <Tooltip.Provider>
      <div class="flex h-16 items-center px-4">
        <!-- Left Side: Logo dropdown + buttons -->
        <div class="flex items-center gap-4">
          <!-- Logo Dropdown with border and chevron -->
          <DropdownMenu.Root>
            <DropdownMenu.Trigger
              class="border-foreground/10 hover:bg-muted/5  flex h-9 cursor-pointer items-center gap-2 rounded-md border py-1.5 pl-2 pr-1">
              <span class="rounded-sm bg-gradient-to-r from-orange-500 to-purple-600 p-0.5">
                <Logo fill="white" stroke="black" class="bg-secondary h-4 w-4" />
              </span>
              <ChevronDown class="text-muted-foreground h-4 w-4" />
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="start" class="w-60" sideOffset={10}>
              <GlobalSearch className="mb-2 m-1" placeholder="Search..." />
              <DropdownMenu.Item
                onclick={() => goto('/dashboard')}
                class={isActive('/dashboard', true) ? 'bg-accent text-accent-foreground' : ''}>
                Go to dashboard
              </DropdownMenu.Item>
              <DropdownMenu.Separator />
              <DropdownMenu.Item
                onclick={() => goto('/dashboard/settings/profile')}
                class={isActive('/dashboard/settings/profile')
                  ? 'bg-accent text-accent-foreground'
                  : ''}>
                Profiles
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onclick={() => goto('/dashboard/settings/analysis')}
                class={isActive('/dashboard/settings/analysis')
                  ? 'bg-accent text-accent-foreground'
                  : ''}>
                Analysis
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onclick={() => goto('/dashboard/notifications')}
                class={isActive('/dashboard/notifications')
                  ? 'bg-accent text-accent-foreground'
                  : ''}>
                Notifications
              </DropdownMenu.Item>
              <DropdownMenu.Group>
                <DropdownMenu.Sub>
                  <DropdownMenu.SubTrigger>Help & Account</DropdownMenu.SubTrigger>
                  <DropdownMenu.SubContent>
                    <DropdownMenu.Item
                      onclick={() => goto('/help')}
                      class={isActive('/help') ? 'bg-accent text-accent-foreground' : ''}>
                      Help Center
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onclick={openKeyboardShortcutsDialog}>
                      Keyboard Shortcuts
                    </DropdownMenu.Item>
                    <DropdownMenu.Item
                      onclick={() => goto('/resources')}
                      class={isActive('/resources') ? 'bg-accent text-accent-foreground' : ''}>
                      Resources
                    </DropdownMenu.Item>
                    <DropdownMenu.Item
                      onclick={() => goto('https://autoapply.featurebase.app/roadmap')}>
                      Roadmap
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onclick={() => goto('https://autoapply.featurebase.app/')}>
                      Submit Feedback
                    </DropdownMenu.Item>
                    <DropdownMenu.Item
                      onclick={() => goto('/system-status')}
                      class={isActive('/system-status') ? 'bg-accent text-accent-foreground' : ''}>
                      System Status
                    </DropdownMenu.Item>
                    <DropdownMenu.Separator />
                    <DropdownMenu.Item
                      onclick={() => goto('/dashboard/settings')}
                      class={isActive('/dashboard/settings')
                        ? 'bg-accent text-accent-foreground'
                        : ''}>
                      Manage Account
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onclick={logout}>Sign Out</DropdownMenu.Item>
                  </DropdownMenu.SubContent>
                </DropdownMenu.Sub>
              </DropdownMenu.Group>

              <DropdownMenu.Separator />

              <!-- Theme Toggle -->
              <DropdownMenu.Sub>
                <DropdownMenu.SubTrigger>
                  {#if mode.current === 'light'}
                    <Sun class="mr-2 h-4 w-4" />
                    Light Mode
                  {:else if mode.current === 'dark'}
                    <Moon class="mr-2 h-4 w-4" />
                    Dark Mode
                  {:else}
                    <Monitor class="mr-2 h-4 w-4" />
                    System
                  {/if}
                </DropdownMenu.SubTrigger>
                <DropdownMenu.SubContent>
                  <DropdownMenu.Item onclick={() => setMode('light')}>
                    <Sun class="mr-2 h-4 w-4" />
                    Light
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onclick={() => setMode('dark')}>
                    <Moon class="mr-2 h-4 w-4" />
                    Dark
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onclick={() => setMode('system')}>
                    <Monitor class="mr-2 h-4 w-4" />
                    System
                  </DropdownMenu.Item>
                </DropdownMenu.SubContent>
              </DropdownMenu.Sub>

              {#if user && 'teamId' in user && user.teamId}
                <DropdownMenu.Separator />
                <DropdownMenu.Item
                  onclick={() => goto('/dashboard/settings/team')}
                  class={isActive('/dashboard/settings/team')
                    ? 'bg-accent text-accent-foreground'
                    : ''}>
                  <Users class="mr-2 h-4 w-4" />
                  Team Settings
                </DropdownMenu.Item>
                <DropdownMenu.Separator />
                <DropdownMenu.Item
                  onclick={() => goto('/dashboard/settings')}
                  class={isActive('/dashboard/settings') ? 'bg-accent text-accent-foreground' : ''}>
                  <Building class="mr-2 h-4 w-4" />
                  Workspace Settings
                </DropdownMenu.Item>
              {/if}
            </DropdownMenu.Content>
          </DropdownMenu.Root>

          <Button
            variant="ghost"
            onclick={() => goto('/dashboard/settings/referrals')}
            class={`border-foreground/10 gap-2 border ${isActive('/dashboard/settings/referrals') ? 'bg-accent text-accent-foreground' : ''}`}>
            <Gift class="h-4 w-4" />
            Invite & Earn
          </Button>
        </div>

        <!-- Center: Navigation (using flex-1 to center) -->
        <div class="flex flex-1 justify-center">
          <DashboardMainNav />
        </div>

        <!-- Right Side: Notifications + Avatar + Upgrade -->
        <div class="flex items-center gap-4">
          <!-- Notifications Button -->
          <Tooltip.Root>
            <Tooltip.Trigger>
              <Button
                variant="ghost"
                size="sm"
                onclick={() => goto('/dashboard/notifications')}
                class={`relative h-9 w-9 p-0 ${isActive('/dashboard/notifications') ? 'bg-accent text-accent-foreground' : ''}`}>
                <Bell class="h-4 w-4" />
                {#if $unreadCount > 0}
                  <Badge
                    variant="destructive"
                    class="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs">
                    {$unreadCount > 99 ? '99+' : $unreadCount}
                  </Badge>
                {/if}
              </Button>
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>Notifications {$unreadCount > 0 ? `(${$unreadCount} unread)` : ''}</p>
            </Tooltip.Content>
          </Tooltip.Root>

          <Tooltip.Root>
            <Tooltip.Trigger>
              <Avatar class="h-8 w-8">
                <AvatarImage src={user?.image} alt={user?.name || user?.email} />
                <AvatarFallback>
                  {user?.name
                    ? user.name.charAt(0).toUpperCase()
                    : user?.email?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>{user?.name || user?.email}</p>
            </Tooltip.Content>
          </Tooltip.Root>
          <div
            class="inline-block rounded-lg bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 p-0.5">
            <Button size="sm" onclick={() => goto('/pricing')} class="font-semibold"
              >Upgrade</Button>
          </div>
        </div>
      </div>
    </Tooltip.Provider>
  </div>

  <main class="bg-secondary/80 h-full px-4">
    <div
      class="bg-background border-border h-full flex-1 space-y-4 rounded-lg rounded-b-none border border-b-0">
      {@render children()}
    </div>
  </main>
</div>
<!-- <Feedback/> -->

<!-- Keyboard Shortcuts Dialog -->
<KeyboardShortcutsDialog bind:open={showKeyboardShortcutsDialog} />
