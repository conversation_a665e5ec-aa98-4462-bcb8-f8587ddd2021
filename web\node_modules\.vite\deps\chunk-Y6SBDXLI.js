// node_modules/@auth/core/errors.js
var AuthError = class extends Error {
  /** @internal */
  constructor(message, errorOptions) {
    var _a;
    if (message instanceof Error) {
      super(void 0, {
        cause: { err: message, ...message.cause, ...errorOptions }
      });
    } else if (typeof message === "string") {
      if (errorOptions instanceof Error) {
        errorOptions = { err: errorOptions, ...errorOptions.cause };
      }
      super(message, errorOptions);
    } else {
      super(void 0, message);
    }
    this.name = this.constructor.name;
    this.type = this.constructor.type ?? "AuthError";
    this.kind = this.constructor.kind ?? "error";
    (_a = Error.captureStackTrace) == null ? void 0 : _a.call(Error, this, this.constructor);
    const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;
    this.message += `${this.message ? ". " : ""}Read more at ${url}`;
  }
};
var SignInError = class extends AuthError {
};
SignInError.kind = "signIn";
var AdapterError = class extends AuthError {
};
AdapterError.type = "AdapterError";
var AccessDenied = class extends AuthError {
};
AccessDenied.type = "AccessDenied";
var CallbackRouteError = class extends AuthError {
};
CallbackRouteError.type = "CallbackRouteError";
var ErrorPageLoop = class extends AuthError {
};
ErrorPageLoop.type = "ErrorPageLoop";
var EventError = class extends AuthError {
};
EventError.type = "EventError";
var InvalidCallbackUrl = class extends AuthError {
};
InvalidCallbackUrl.type = "InvalidCallbackUrl";
var CredentialsSignin = class extends SignInError {
  constructor() {
    super(...arguments);
    this.code = "credentials";
  }
};
CredentialsSignin.type = "CredentialsSignin";
var InvalidEndpoints = class extends AuthError {
};
InvalidEndpoints.type = "InvalidEndpoints";
var InvalidCheck = class extends AuthError {
};
InvalidCheck.type = "InvalidCheck";
var JWTSessionError = class extends AuthError {
};
JWTSessionError.type = "JWTSessionError";
var MissingAdapter = class extends AuthError {
};
MissingAdapter.type = "MissingAdapter";
var MissingAdapterMethods = class extends AuthError {
};
MissingAdapterMethods.type = "MissingAdapterMethods";
var MissingAuthorize = class extends AuthError {
};
MissingAuthorize.type = "MissingAuthorize";
var MissingSecret = class extends AuthError {
};
MissingSecret.type = "MissingSecret";
var OAuthAccountNotLinked = class extends SignInError {
};
OAuthAccountNotLinked.type = "OAuthAccountNotLinked";
var OAuthCallbackError = class extends SignInError {
};
OAuthCallbackError.type = "OAuthCallbackError";
var OAuthProfileParseError = class extends AuthError {
};
OAuthProfileParseError.type = "OAuthProfileParseError";
var SessionTokenError = class extends AuthError {
};
SessionTokenError.type = "SessionTokenError";
var OAuthSignInError = class extends SignInError {
};
OAuthSignInError.type = "OAuthSignInError";
var EmailSignInError = class extends SignInError {
};
EmailSignInError.type = "EmailSignInError";
var SignOutError = class extends AuthError {
};
SignOutError.type = "SignOutError";
var UnknownAction = class extends AuthError {
};
UnknownAction.type = "UnknownAction";
var UnsupportedStrategy = class extends AuthError {
};
UnsupportedStrategy.type = "UnsupportedStrategy";
var InvalidProvider = class extends AuthError {
};
InvalidProvider.type = "InvalidProvider";
var UntrustedHost = class extends AuthError {
};
UntrustedHost.type = "UntrustedHost";
var Verification = class extends AuthError {
};
Verification.type = "Verification";
var MissingCSRF = class extends SignInError {
};
MissingCSRF.type = "MissingCSRF";
var clientErrors = /* @__PURE__ */ new Set([
  "CredentialsSignin",
  "OAuthAccountNotLinked",
  "OAuthCallbackError",
  "AccessDenied",
  "Verification",
  "MissingCSRF",
  "AccountNotLinked",
  "WebAuthnVerificationError"
]);
function isClientError(error) {
  if (error instanceof AuthError)
    return clientErrors.has(error.type);
  return false;
}
var DuplicateConditionalUI = class extends AuthError {
};
DuplicateConditionalUI.type = "DuplicateConditionalUI";
var MissingWebAuthnAutocomplete = class extends AuthError {
};
MissingWebAuthnAutocomplete.type = "MissingWebAuthnAutocomplete";
var WebAuthnVerificationError = class extends AuthError {
};
WebAuthnVerificationError.type = "WebAuthnVerificationError";
var AccountNotLinked = class extends SignInError {
};
AccountNotLinked.type = "AccountNotLinked";
var ExperimentalFeatureNotEnabled = class extends AuthError {
};
ExperimentalFeatureNotEnabled.type = "ExperimentalFeatureNotEnabled";

export {
  AuthError,
  SignInError,
  AdapterError,
  AccessDenied,
  CallbackRouteError,
  ErrorPageLoop,
  EventError,
  InvalidCallbackUrl,
  CredentialsSignin,
  InvalidEndpoints,
  InvalidCheck,
  JWTSessionError,
  MissingAdapter,
  MissingAdapterMethods,
  MissingAuthorize,
  MissingSecret,
  OAuthAccountNotLinked,
  OAuthCallbackError,
  OAuthProfileParseError,
  SessionTokenError,
  OAuthSignInError,
  EmailSignInError,
  SignOutError,
  UnknownAction,
  UnsupportedStrategy,
  InvalidProvider,
  UntrustedHost,
  Verification,
  MissingCSRF,
  isClientError,
  DuplicateConditionalUI,
  MissingWebAuthnAutocomplete,
  WebAuthnVerificationError,
  AccountNotLinked,
  ExperimentalFeatureNotEnabled
};
//# sourceMappingURL=chunk-Y6SBDXLI.js.map
