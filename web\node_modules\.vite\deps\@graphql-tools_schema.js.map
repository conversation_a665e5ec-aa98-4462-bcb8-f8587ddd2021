{"version": 3, "sources": ["../../@graphql-tools/schema/esm/assertResolversPresent.js", "../../@graphql-tools/utils/esm/helpers.js", "../../@graphql-tools/utils/esm/getDirectiveExtensions.js", "../../cross-inspect/esm/index.js", "../../@graphql-tools/utils/esm/getArgumentValues.js", "../../@graphql-tools/utils/esm/errors.js", "../../@whatwg-node/promise-helpers/esm/index.js", "../../@graphql-tools/utils/esm/jsutils.js", "../../@graphql-tools/utils/esm/memoize.js", "../../@graphql-tools/utils/esm/get-directives.js", "../../@graphql-tools/utils/esm/get-fields-with-directives.js", "../../@graphql-tools/utils/esm/get-arguments-with-directives.js", "../../@graphql-tools/utils/esm/print-schema-with-directives.js", "../../@graphql-tools/utils/esm/astFromType.js", "../../@graphql-tools/utils/esm/astFromValue.js", "../../@graphql-tools/utils/esm/astFromValueUntyped.js", "../../@graphql-tools/utils/esm/descriptionFromObject.js", "../../@graphql-tools/utils/esm/rootTypes.js", "../../@graphql-tools/utils/esm/validate-documents.js", "../../@graphql-tools/utils/esm/parse-graphql-json.js", "../../@graphql-tools/utils/esm/parse-graphql-sdl.js", "../../@graphql-tools/utils/esm/comments.js", "../../@graphql-tools/utils/esm/build-operation-for-field.js", "../../@graphql-tools/utils/esm/types.js", "../../@graphql-tools/utils/esm/filterSchema.js", "../../@graphql-tools/utils/esm/Interfaces.js", "../../@graphql-tools/utils/esm/mapSchema.js", "../../@graphql-tools/utils/esm/getObjectTypeFromTypeMap.js", "../../@graphql-tools/utils/esm/rewire.js", "../../@graphql-tools/utils/esm/stub.js", "../../@graphql-tools/utils/esm/transformInputValue.js", "../../@graphql-tools/utils/esm/heal.js", "../../@graphql-tools/utils/esm/getResolversFromSchema.js", "../../@graphql-tools/utils/esm/forEachField.js", "../../@graphql-tools/utils/esm/forEachDefaultValue.js", "../../@graphql-tools/utils/esm/addTypes.js", "../../@graphql-tools/utils/esm/prune.js", "../../@graphql-tools/utils/esm/mergeDeep.js", "../../@graphql-tools/utils/esm/selectionSets.js", "../../@graphql-tools/utils/esm/fields.js", "../../@graphql-tools/utils/esm/renameType.js", "../../@graphql-tools/utils/esm/updateArgument.js", "../../@graphql-tools/utils/esm/implementsAbstractType.js", "../../@graphql-tools/utils/esm/visitResult.js", "../../@graphql-tools/utils/esm/collectFields.js", "../../@graphql-tools/utils/esm/AccumulatorMap.js", "../../@graphql-tools/utils/esm/directives.js", "../../@graphql-tools/utils/esm/getOperationASTFromRequest.js", "../../@graphql-tools/utils/esm/isDocumentNode.js", "../../@graphql-tools/utils/esm/withCancel.js", "../../@graphql-tools/utils/esm/fixSchemaAst.js", "../../@graphql-tools/utils/esm/extractExtensionsFromSchema.js", "../../@graphql-tools/utils/esm/registerAbortSignalListener.js", "../../@graphql-tools/schema/esm/chainResolvers.js", "../../@graphql-tools/schema/esm/addResolversToSchema.js", "../../@graphql-tools/schema/esm/checkForResolveTypeResolver.js", "../../@graphql-tools/schema/esm/extendResolversFromInterfaces.js", "../../@graphql-tools/schema/esm/makeExecutableSchema.js", "../../@graphql-tools/merge/esm/merge-resolvers.js", "../../@graphql-tools/merge/esm/typedefs-mergers/arguments.js", "../../@graphql-tools/merge/esm/typedefs-mergers/directives.js", "../../@graphql-tools/merge/esm/typedefs-mergers/enum-values.js", "../../@graphql-tools/merge/esm/typedefs-mergers/enum.js", "../../@graphql-tools/merge/esm/typedefs-mergers/utils.js", "../../@graphql-tools/merge/esm/typedefs-mergers/fields.js", "../../@graphql-tools/merge/esm/typedefs-mergers/input-type.js", "../../@graphql-tools/merge/esm/typedefs-mergers/interface.js", "../../@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js", "../../@graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js", "../../@graphql-tools/merge/esm/typedefs-mergers/scalar.js", "../../@graphql-tools/merge/esm/typedefs-mergers/schema-def.js", "../../@graphql-tools/merge/esm/typedefs-mergers/type.js", "../../@graphql-tools/merge/esm/typedefs-mergers/union.js", "../../@graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js", "../../@graphql-tools/merge/esm/extensions.js", "../../@graphql-tools/schema/esm/merge-schemas.js"], "sourcesContent": ["import { getNamedType, isScalarType } from 'graphql';\nimport { forEachField } from '@graphql-tools/utils';\nexport function assertResolversPresent(schema, resolverValidationOptions = {}) {\n    const { requireResolversForArgs, requireResolversForNonScalar, requireResolversForAllFields } = resolverValidationOptions;\n    if (requireResolversForAllFields && (requireResolversForArgs || requireResolversForNonScalar)) {\n        throw new TypeError('requireResolversForAllFields takes precedence over the more specific assertions. ' +\n            'Please configure either requireResolversForAllFields or requireResolversForArgs / ' +\n            'requireResolversForNonScalar, but not a combination of them.');\n    }\n    forEachField(schema, (field, typeName, fieldName) => {\n        // requires a resolver for *every* field.\n        if (requireResolversForAllFields) {\n            expectResolver('requireResolversForAllFields', requireResolversForAllFields, field, typeName, fieldName);\n        }\n        // requires a resolver on every field that has arguments\n        if (requireResolversForArgs && field.args.length > 0) {\n            expectResolver('requireResolversForArgs', requireResolversForArgs, field, typeName, fieldName);\n        }\n        // requires a resolver on every field that returns a non-scalar type\n        if (requireResolversForNonScalar !== 'ignore' && !isScalarType(getNamedType(field.type))) {\n            expectResolver('requireResolversForNonScalar', requireResolversForNonScalar, field, typeName, fieldName);\n        }\n    });\n}\nfunction expectResolver(validator, behavior, field, typeName, fieldName) {\n    if (!field.resolve) {\n        const message = `Resolver missing for \"${typeName}.${fieldName}\".\nTo disable this validator, use:\n  resolverValidationOptions: {\n    ${validator}: 'ignore'\n  }`;\n        if (behavior === 'error') {\n            throw new Error(message);\n        }\n        if (behavior === 'warn') {\n            console.warn(message);\n        }\n        return;\n    }\n    if (typeof field.resolve !== 'function') {\n        throw new Error(`Resolver \"${typeName}.${fieldName}\" must be a function`);\n    }\n}\n", "import { parse } from 'graphql';\nconst URL_REGEXP = /^(https?|wss?|file):\\/\\//;\n/**\n * Checks if the given string is a valid URL.\n *\n * @param str - The string to validate as a URL\n * @returns A boolean indicating whether the string is a valid URL\n *\n * @remarks\n * This function first attempts to use the `URL.canParse` method if available.\n * If not, it falls back to creating a new `URL` object to validate the string.\n */\nexport function isUrl(str) {\n    if (typeof str !== 'string') {\n        return false;\n    }\n    if (!URL_REGEXP.test(str)) {\n        return false;\n    }\n    if (URL.canParse) {\n        return URL.canParse(str);\n    }\n    try {\n        const url = new URL(str);\n        return !!url;\n    }\n    catch (e) {\n        return false;\n    }\n}\nexport const asArray = (fns) => (Array.isArray(fns) ? fns : fns ? [fns] : []);\nconst invalidDocRegex = /\\.[a-z0-9]+$/i;\n/**\n * Determines if a given input is a valid GraphQL document string.\n *\n * @param str - The input to validate as a GraphQL document\n * @returns A boolean indicating whether the input is a valid GraphQL document string\n *\n * @remarks\n * This function performs several validation checks:\n * - Ensures the input is a string\n * - Filters out strings with invalid document extensions\n * - Excludes URLs\n * - Attempts to parse the string as a GraphQL document\n *\n * @throws {Error} If the document fails to parse and is empty except GraphQL comments\n */\nexport function isDocumentString(str) {\n    if (typeof str !== 'string') {\n        return false;\n    }\n    // XXX: is-valid-path or is-glob treat SDL as a valid path\n    // (`scalar Date` for example)\n    // this why checking the extension is fast enough\n    // and prevent from parsing the string in order to find out\n    // if the string is a SDL\n    if (invalidDocRegex.test(str) || isUrl(str)) {\n        return false;\n    }\n    try {\n        parse(str);\n        return true;\n    }\n    catch (e) {\n        if (!e.message.includes('EOF') &&\n            str.replace(/(\\#[^*]*)/g, '').trim() !== '' &&\n            str.includes(' ')) {\n            throw new Error(`Failed to parse the GraphQL document. ${e.message}\\n${str}`);\n        }\n    }\n    return false;\n}\nconst invalidPathRegex = /[‘“!%^<>`\\n]/;\n/**\n * Checkes whether the `str` contains any path illegal characters.\n *\n * A string may sometimes look like a path but is not (like an SDL of a simple\n * GraphQL schema). To make sure we don't yield false-positives in such cases,\n * we disallow new lines in paths (even though most Unix systems support new\n * lines in file names).\n */\nexport function isValidPath(str) {\n    return typeof str === 'string' && !invalidPathRegex.test(str);\n}\nexport function compareStrings(a, b) {\n    if (String(a) < String(b)) {\n        return -1;\n    }\n    if (String(a) > String(b)) {\n        return 1;\n    }\n    return 0;\n}\nexport function nodeToString(a) {\n    let name;\n    if ('alias' in a) {\n        name = a.alias?.value;\n    }\n    if (name == null && 'name' in a) {\n        name = a.name?.value;\n    }\n    if (name == null) {\n        name = a.kind;\n    }\n    return name;\n}\nexport function compareNodes(a, b, customFn) {\n    const aStr = nodeToString(a);\n    const bStr = nodeToString(b);\n    if (typeof customFn === 'function') {\n        return customFn(aStr, bStr);\n    }\n    return compareStrings(aStr, bStr);\n}\nexport function isSome(input) {\n    return input != null;\n}\nexport function assertSome(input, message = 'Value should be something') {\n    if (input == null) {\n        throw new Error(message);\n    }\n}\n", "import { valueFromAST, valueFromASTUntyped } from 'graphql';\nimport { getArgumentValues } from './getArgumentValues.js';\nimport { memoize1 } from './memoize.js';\nexport function getDirectiveExtensions(directableObj, schema, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = {};\n    if (directableObj.extensions) {\n        let directivesInExtensions = directableObj.extensions;\n        for (const pathSegment of pathToDirectivesInExtensions) {\n            directivesInExtensions = directivesInExtensions?.[pathSegment];\n        }\n        if (directivesInExtensions != null) {\n            for (const directiveNameProp in directivesInExtensions) {\n                const directiveObjs = directivesInExtensions[directiveNameProp];\n                const directiveName = directiveNameProp;\n                if (Array.isArray(directiveObjs)) {\n                    for (const directiveObj of directiveObjs) {\n                        let existingDirectiveExtensions = directiveExtensions[directiveName];\n                        if (!existingDirectiveExtensions) {\n                            existingDirectiveExtensions = [];\n                            directiveExtensions[directiveName] = existingDirectiveExtensions;\n                        }\n                        existingDirectiveExtensions.push(directiveObj);\n                    }\n                }\n                else {\n                    let existingDirectiveExtensions = directiveExtensions[directiveName];\n                    if (!existingDirectiveExtensions) {\n                        existingDirectiveExtensions = [];\n                        directiveExtensions[directiveName] = existingDirectiveExtensions;\n                    }\n                    existingDirectiveExtensions.push(directiveObjs);\n                }\n            }\n        }\n    }\n    const memoizedStringify = memoize1(obj => JSON.stringify(obj));\n    const astNodes = [];\n    if (directableObj.astNode) {\n        astNodes.push(directableObj.astNode);\n    }\n    if (directableObj.extensionASTNodes) {\n        astNodes.push(...directableObj.extensionASTNodes);\n    }\n    for (const astNode of astNodes) {\n        if (astNode.directives?.length) {\n            for (const directive of astNode.directives) {\n                const directiveName = directive.name.value;\n                let existingDirectiveExtensions = directiveExtensions[directiveName];\n                if (!existingDirectiveExtensions) {\n                    existingDirectiveExtensions = [];\n                    directiveExtensions[directiveName] = existingDirectiveExtensions;\n                }\n                const directiveInSchema = schema?.getDirective(directiveName);\n                let value = {};\n                if (directiveInSchema) {\n                    value = getArgumentValues(directiveInSchema, directive);\n                }\n                if (directive.arguments) {\n                    for (const argNode of directive.arguments) {\n                        const argName = argNode.name.value;\n                        if (value[argName] == null) {\n                            const argInDirective = directiveInSchema?.args.find(arg => arg.name === argName);\n                            if (argInDirective) {\n                                value[argName] = valueFromAST(argNode.value, argInDirective.type);\n                            }\n                        }\n                        if (value[argName] == null) {\n                            value[argName] = valueFromASTUntyped(argNode.value);\n                        }\n                    }\n                }\n                if (astNodes.length > 0 && existingDirectiveExtensions.length > 0) {\n                    const valStr = memoizedStringify(value);\n                    if (existingDirectiveExtensions.some(val => memoizedStringify(val) === valStr)) {\n                        continue;\n                    }\n                }\n                existingDirectiveExtensions.push(value);\n            }\n        }\n    }\n    return directiveExtensions;\n}\n", "// Taken from graphql-js\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/inspect.ts\nconst MAX_RECURSIVE_DEPTH = 3;\n/**\n * Used to print values in error messages.\n */\nexport function inspect(value) {\n    return formatValue(value, []);\n}\nfunction formatValue(value, seenValues) {\n    switch (typeof value) {\n        case 'string':\n            return JSON.stringify(value);\n        case 'function':\n            return value.name ? `[function ${value.name}]` : '[function]';\n        case 'object':\n            return formatObjectValue(value, seenValues);\n        default:\n            return String(value);\n    }\n}\nfunction formatError(value) {\n    // eslint-disable-next-line no-constant-condition\n    if ((value.name = 'GraphQLError')) {\n        return value.toString();\n    }\n    return `${value.name}: ${value.message};\\n ${value.stack}`;\n}\nfunction formatObjectValue(value, previouslySeenValues) {\n    if (value === null) {\n        return 'null';\n    }\n    if (value instanceof Error) {\n        if (value.name === 'AggregateError') {\n            return (formatError(value) +\n                '\\n' +\n                formatArray(value.errors, previouslySeenValues));\n        }\n        return formatError(value);\n    }\n    if (previouslySeenValues.includes(value)) {\n        return '[Circular]';\n    }\n    const seenValues = [...previouslySeenValues, value];\n    if (isJSONable(value)) {\n        const jsonValue = value.toJSON();\n        // check for infinite recursion\n        if (jsonValue !== value) {\n            return typeof jsonValue === 'string' ? jsonValue : formatValue(jsonValue, seenValues);\n        }\n    }\n    else if (Array.isArray(value)) {\n        return formatArray(value, seenValues);\n    }\n    return formatObject(value, seenValues);\n}\nfunction isJSONable(value) {\n    return typeof value.toJSON === 'function';\n}\nfunction formatObject(object, seenValues) {\n    const entries = Object.entries(object);\n    if (entries.length === 0) {\n        return '{}';\n    }\n    if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n        return '[' + getObjectTag(object) + ']';\n    }\n    const properties = entries.map(([key, value]) => key + ': ' + formatValue(value, seenValues));\n    return '{ ' + properties.join(', ') + ' }';\n}\nfunction formatArray(array, seenValues) {\n    if (array.length === 0) {\n        return '[]';\n    }\n    if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n        return '[Array]';\n    }\n    const len = array.length;\n    const items = [];\n    for (let i = 0; i < len; ++i) {\n        items.push(formatValue(array[i], seenValues));\n    }\n    return '[' + items.join(', ') + ']';\n}\nfunction getObjectTag(object) {\n    const tag = Object.prototype.toString\n        .call(object)\n        .replace(/^\\[object /, '')\n        .replace(/]$/, '');\n    if (tag === 'Object' && typeof object.constructor === 'function') {\n        const name = object.constructor.name;\n        if (typeof name === 'string' && name !== '') {\n            return name;\n        }\n    }\n    return tag;\n}\n", "import { inspect } from 'cross-inspect';\nimport { isNonNullType, Kind, print, valueFromAST, } from 'graphql';\nimport { createGraphQLError } from './errors.js';\nimport { hasOwnProperty } from './jsutils.js';\n/**\n * Prepares an object map of argument values given a list of argument\n * definitions and list of argument AST nodes.\n *\n * Note: The returned value is a plain Object with a prototype, since it is\n * exposed to user code. Care should be taken to not pull values from the\n * Object prototype.\n */\nexport function getArgumentValues(def, node, variableValues = {}) {\n    const coercedValues = {};\n    const argumentNodes = node.arguments ?? [];\n    const argNodeMap = argumentNodes.reduce((prev, arg) => ({\n        ...prev,\n        [arg.name.value]: arg,\n    }), {});\n    for (const { name, type: argType, defaultValue } of def.args) {\n        const argumentNode = argNodeMap[name];\n        if (!argumentNode) {\n            if (defaultValue !== undefined) {\n                coercedValues[name] = defaultValue;\n            }\n            else if (isNonNullType(argType)) {\n                throw createGraphQLError(`Argument \"${name}\" of required type \"${inspect(argType)}\" ` + 'was not provided.', {\n                    nodes: [node],\n                });\n            }\n            continue;\n        }\n        const valueNode = argumentNode.value;\n        let isNull = valueNode.kind === Kind.NULL;\n        if (valueNode.kind === Kind.VARIABLE) {\n            const variableName = valueNode.name.value;\n            if (variableValues == null || !hasOwnProperty(variableValues, variableName)) {\n                if (defaultValue !== undefined) {\n                    coercedValues[name] = defaultValue;\n                }\n                else if (isNonNullType(argType)) {\n                    throw createGraphQLError(`Argument \"${name}\" of required type \"${inspect(argType)}\" ` +\n                        `was provided the variable \"$${variableName}\" which was not provided a runtime value.`, {\n                        nodes: [valueNode],\n                    });\n                }\n                continue;\n            }\n            isNull = variableValues[variableName] == null;\n        }\n        if (isNull && isNonNullType(argType)) {\n            throw createGraphQLError(`Argument \"${name}\" of non-null type \"${inspect(argType)}\" ` + 'must not be null.', {\n                nodes: [valueNode],\n            });\n        }\n        const coercedValue = valueFromAST(valueNode, argType, variableValues);\n        if (coercedValue === undefined) {\n            // Note: ValuesOfCorrectTypeRule validation should catch this before\n            // execution. This is a runtime check to ensure execution does not\n            // continue with an invalid argument value.\n            throw createGraphQLError(`Argument \"${name}\" has invalid value ${print(valueNode)}.`, {\n                nodes: [valueNode],\n            });\n        }\n        coercedValues[name] = coercedValue;\n    }\n    return coercedValues;\n}\n", "import { GraphQLError, versionInfo } from 'graphql';\nconst possibleGraphQLErrorProperties = [\n    'message',\n    'locations',\n    'path',\n    'nodes',\n    'source',\n    'positions',\n    'originalError',\n    'name',\n    'stack',\n    'extensions',\n];\nfunction isGraphQLErrorLike(error) {\n    return (error != null &&\n        typeof error === 'object' &&\n        Object.keys(error).every(key => possibleGraphQLErrorProperties.includes(key)));\n}\nexport function createGraphQLError(message, options) {\n    if (options?.originalError &&\n        !(options.originalError instanceof Error) &&\n        isGraphQLErrorLike(options.originalError)) {\n        options.originalError = createGraphQLError(options.originalError.message, options.originalError);\n    }\n    if (versionInfo.major >= 17) {\n        return new GraphQLError(message, options);\n    }\n    return new GraphQLError(message, options?.nodes, options?.source, options?.positions, options?.path, options?.originalError, options?.extensions);\n}\nexport function relocatedError(originalError, path) {\n    return createGraphQLError(originalError.message, {\n        nodes: originalError.nodes,\n        source: originalError.source,\n        positions: originalError.positions,\n        path: path == null ? originalError.path : path,\n        originalError,\n        extensions: originalError.extensions,\n    });\n}\n", "const kFakePromise = Symbol.for('@whatwg-node/promise-helpers/FakePromise');\nexport function isPromise(value) {\n    return value?.then != null;\n}\nexport function isActualPromise(value) {\n    const maybePromise = value;\n    return maybePromise && maybePromise.then && maybePromise.catch && maybePromise.finally;\n}\nexport function handleMaybePromise(inputFactory, outputSuccessFactory, outputErrorFactory, finallyFactory) {\n    let result$ = fakePromise().then(inputFactory).then(outputSuccessFactory, outputErrorFactory);\n    if (finallyFactory) {\n        result$ = result$.finally(finallyFactory);\n    }\n    return unfakePromise(result$);\n}\nexport function fakePromise(value) {\n    if (value && isActualPromise(value)) {\n        return value;\n    }\n    if (isPromise(value)) {\n        return {\n            then: (resolve, reject) => fakePromise(value.then(resolve, reject)),\n            catch: reject => fakePromise(value.then(res => res, reject)),\n            finally: cb => fakePromise(cb ? promiseLikeFinally(value, cb) : value),\n            [Symbol.toStringTag]: 'Promise',\n        };\n    }\n    // Write a fake promise to avoid the promise constructor\n    // being called with `new Promise` in the browser.\n    return {\n        then(resolve) {\n            if (resolve) {\n                try {\n                    return fakePromise(resolve(value));\n                }\n                catch (err) {\n                    return fakeRejectPromise(err);\n                }\n            }\n            return this;\n        },\n        catch() {\n            return this;\n        },\n        finally(cb) {\n            if (cb) {\n                try {\n                    return fakePromise(cb()).then(() => value, () => value);\n                }\n                catch (err) {\n                    return fakeRejectPromise(err);\n                }\n            }\n            return this;\n        },\n        [Symbol.toStringTag]: 'Promise',\n        __fakePromiseValue: value,\n        [kFakePromise]: 'resolved',\n    };\n}\nexport function createDeferredPromise() {\n    if (Promise.withResolvers) {\n        return Promise.withResolvers();\n    }\n    let resolveFn;\n    let rejectFn;\n    const promise = new Promise(function deferredPromiseExecutor(resolve, reject) {\n        resolveFn = resolve;\n        rejectFn = reject;\n    });\n    return {\n        promise,\n        get resolve() {\n            return resolveFn;\n        },\n        get reject() {\n            return rejectFn;\n        },\n    };\n}\nexport { iterateAsync as iterateAsyncVoid };\nexport function iterateAsync(iterable, callback, results) {\n    if (iterable?.length === 0) {\n        return;\n    }\n    const iterator = iterable[Symbol.iterator]();\n    let index = 0;\n    function iterate() {\n        const { done: endOfIterator, value } = iterator.next();\n        if (endOfIterator) {\n            return;\n        }\n        let endedEarly = false;\n        function endEarly() {\n            endedEarly = true;\n        }\n        return handleMaybePromise(function handleCallback() {\n            return callback(value, endEarly, index++);\n        }, function handleCallbackResult(result) {\n            if (result) {\n                results?.push(result);\n            }\n            if (endedEarly) {\n                return;\n            }\n            return iterate();\n        });\n    }\n    return iterate();\n}\nexport function fakeRejectPromise(error) {\n    return {\n        then(_resolve, reject) {\n            if (reject) {\n                try {\n                    return fakePromise(reject(error));\n                }\n                catch (err) {\n                    return fakeRejectPromise(err);\n                }\n            }\n            return this;\n        },\n        catch(reject) {\n            if (reject) {\n                try {\n                    return fakePromise(reject(error));\n                }\n                catch (err) {\n                    return fakeRejectPromise(err);\n                }\n            }\n            return this;\n        },\n        finally(cb) {\n            if (cb) {\n                try {\n                    cb();\n                }\n                catch (err) {\n                    return fakeRejectPromise(err);\n                }\n            }\n            return this;\n        },\n        __fakeRejectError: error,\n        [Symbol.toStringTag]: 'Promise',\n        [kFakePromise]: 'rejected',\n    };\n}\nexport function mapMaybePromise(input, onSuccess, onError) {\n    return handleMaybePromise(() => input, onSuccess, onError);\n}\n/**\n * Given an AsyncIterable and a callback function, return an AsyncIterator\n * which produces values mapped via calling the callback function.\n */\nexport function mapAsyncIterator(iterator, onNext, onError, onEnd) {\n    if (Symbol.asyncIterator in iterator) {\n        iterator = iterator[Symbol.asyncIterator]();\n    }\n    let $return;\n    let abruptClose;\n    let onEndWithValue;\n    if (onEnd) {\n        let onEndWithValueResult /** R in onEndWithValue */;\n        onEndWithValue = value => {\n            onEndWithValueResult ||= handleMaybePromise(onEnd, () => value, () => value);\n            return onEndWithValueResult;\n        };\n    }\n    if (typeof iterator.return === 'function') {\n        $return = iterator.return;\n        abruptClose = (error) => {\n            const rethrow = () => {\n                throw error;\n            };\n            return $return.call(iterator).then(rethrow, rethrow);\n        };\n    }\n    function mapResult(result) {\n        if (result.done) {\n            return onEndWithValue ? onEndWithValue(result) : result;\n        }\n        return handleMaybePromise(() => result.value, value => handleMaybePromise(() => onNext(value), iteratorResult, abruptClose));\n    }\n    let mapReject;\n    if (onError) {\n        let onErrorResult;\n        // Capture rejectCallback to ensure it cannot be null.\n        const reject = onError;\n        mapReject = (error) => {\n            onErrorResult ||= handleMaybePromise(() => error, error => handleMaybePromise(() => reject(error), iteratorResult, abruptClose));\n            return onErrorResult;\n        };\n    }\n    return {\n        next() {\n            return iterator.next().then(mapResult, mapReject);\n        },\n        return() {\n            const res$ = $return\n                ? $return.call(iterator).then(mapResult, mapReject)\n                : fakePromise({ value: undefined, done: true });\n            return onEndWithValue ? res$.then(onEndWithValue) : res$;\n        },\n        throw(error) {\n            if (typeof iterator.throw === 'function') {\n                return iterator.throw(error).then(mapResult, mapReject);\n            }\n            if (abruptClose) {\n                return abruptClose(error);\n            }\n            return fakeRejectPromise(error);\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n    };\n}\nfunction iteratorResult(value) {\n    return { value, done: false };\n}\nfunction isFakePromise(value) {\n    return value?.[kFakePromise] === 'resolved';\n}\nfunction isFakeRejectPromise(value) {\n    return value?.[kFakePromise] === 'rejected';\n}\nexport function promiseLikeFinally(value, onFinally) {\n    if ('finally' in value) {\n        return value.finally(onFinally);\n    }\n    return value.then(res => {\n        const finallyRes = onFinally();\n        return isPromise(finallyRes) ? finallyRes.then(() => res) : res;\n    }, err => {\n        const finallyRes = onFinally();\n        if (isPromise(finallyRes)) {\n            return finallyRes.then(() => {\n                throw err;\n            });\n        }\n        else {\n            throw err;\n        }\n    });\n}\nexport function unfakePromise(promise) {\n    if (isFakePromise(promise)) {\n        return promise.__fakePromiseValue;\n    }\n    if (isFakeRejectPromise(promise)) {\n        throw promise.__fakeRejectError;\n    }\n    return promise;\n}\n", "import { handleMaybePromise, isPromise } from '@whatwg-node/promise-helpers';\nexport function isIterableObject(value) {\n    return value != null && typeof value === 'object' && Symbol.iterator in value;\n}\nexport function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport { isPromise };\nexport function promiseReduce(values, callbackFn, initialValue) {\n    let accumulator = initialValue;\n    for (const value of values) {\n        accumulator = handleMaybePromise(() => accumulator, resolved => callbackFn(resolved, value));\n    }\n    return accumulator;\n}\nexport function hasOwnProperty(obj, prop) {\n    return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n", "export function memoize1(fn) {\n    const memoize1cache = new WeakMap();\n    return function memoized(a1) {\n        const cachedValue = memoize1cache.get(a1);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1);\n            memoize1cache.set(a1, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize2(fn) {\n    const memoize2cache = new WeakMap();\n    return function memoized(a1, a2) {\n        let cache2 = memoize2cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2cache.set(a1, cache2);\n            const newValue = fn(a1, a2);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize3(fn) {\n    const memoize3Cache = new WeakMap();\n    return function memoized(a1, a2, a3) {\n        let cache2 = memoize3Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize3Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        const cachedValue = cache3.get(a3);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize4(fn) {\n    const memoize4Cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4) {\n        let cache2 = memoize4Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize4Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        const cache4 = cache3.get(a3);\n        if (!cache4) {\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        const cachedValue = cache4.get(a4);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize5(fn) {\n    const memoize5Cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4, a5) {\n        let cache2 = memoize5Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize5Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache4 = cache3.get(a3);\n        if (!cache4) {\n            cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache5 = cache4.get(a4);\n        if (!cache5) {\n            cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        const cachedValue = cache5.get(a5);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize2of4(fn) {\n    const memoize2of4cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4) {\n        let cache2 = memoize2of4cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2of4cache.set(a1, cache2);\n            const newValue = fn(a1, a2, a3, a4);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize2of5(fn) {\n    const memoize2of4cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4, a5) {\n        let cache2 = memoize2of4cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2of4cache.set(a1, cache2);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\n", "import { getDirectiveExtensions } from './getDirectiveExtensions.js';\nexport function getDirectivesInExtensions(node, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = getDirectiveExtensions(node, undefined, pathToDirectivesInExtensions);\n    return Object.entries(directiveExtensions)\n        .map(([directiveName, directiveArgsArr]) => directiveArgsArr?.map(directiveArgs => ({\n        name: directiveName,\n        args: directiveArgs,\n    })))\n        .flat(Infinity)\n        .filter(Boolean);\n}\nexport function getDirectiveInExtensions(node, directiveName, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = getDirectiveExtensions(node, undefined, pathToDirectivesInExtensions);\n    return directiveExtensions[directiveName];\n}\nexport function getDirectives(schema, node, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = getDirectiveExtensions(node, schema, pathToDirectivesInExtensions);\n    return Object.entries(directiveExtensions)\n        .map(([directiveName, directiveArgsArr]) => directiveArgsArr?.map(directiveArgs => ({\n        name: directiveName,\n        args: directiveArgs,\n    })))\n        .flat(Infinity)\n        .filter(Boolean);\n}\nexport function getDirective(schema, node, directiveName, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = getDirectiveExtensions(node, schema, pathToDirectivesInExtensions);\n    return directiveExtensions[directiveName];\n}\n", "import { valueFromASTUntyped, } from 'graphql';\nexport function getFieldsWithDirectives(documentNode, options = {}) {\n    const result = {};\n    let selected = ['ObjectTypeDefinition', 'ObjectTypeExtension'];\n    if (options.includeInputTypes) {\n        selected = [...selected, 'InputObjectTypeDefinition', 'InputObjectTypeExtension'];\n    }\n    const allTypes = documentNode.definitions.filter(obj => selected.includes(obj.kind));\n    for (const type of allTypes) {\n        const typeName = type.name.value;\n        if (type.fields == null) {\n            continue;\n        }\n        for (const field of type.fields) {\n            if (field.directives && field.directives.length > 0) {\n                const fieldName = field.name.value;\n                const key = `${typeName}.${fieldName}`;\n                const directives = field.directives.map(d => ({\n                    name: d.name.value,\n                    args: (d.arguments || []).reduce((prev, arg) => ({ ...prev, [arg.name.value]: valueFromASTUntyped(arg.value) }), {}),\n                }));\n                result[key] = directives;\n            }\n        }\n    }\n    return result;\n}\n", "import { Kind, valueFromASTUntyped, } from 'graphql';\nfunction isTypeWithFields(t) {\n    return t.kind === Kind.OBJECT_TYPE_DEFINITION || t.kind === Kind.OBJECT_TYPE_EXTENSION;\n}\nexport function getArgumentsWithDirectives(documentNode) {\n    const result = {};\n    const allTypes = documentNode.definitions.filter(isTypeWithFields);\n    for (const type of allTypes) {\n        if (type.fields == null) {\n            continue;\n        }\n        for (const field of type.fields) {\n            const argsWithDirectives = field.arguments?.filter(arg => arg.directives?.length);\n            if (!argsWithDirectives?.length) {\n                continue;\n            }\n            const typeFieldResult = (result[`${type.name.value}.${field.name.value}`] = {});\n            for (const arg of argsWithDirectives) {\n                const directives = arg.directives.map(d => ({\n                    name: d.name.value,\n                    args: (d.arguments || []).reduce((prev, dArg) => ({ ...prev, [dArg.name.value]: valueFromASTUntyped(dArg.value) }), {}),\n                }));\n                typeFieldResult[arg.name.value] = directives;\n            }\n        }\n    }\n    return result;\n}\n", "import { GraphQLDeprecatedDirective, isEnumType, isInputObjectType, isInterfaceType, isIntrospectionType, isObjectType, isScalarType, isSpecifiedDirective, isSpecifiedScalarType, isUnionType, Kind, print, } from 'graphql';\nimport { astFromType } from './astFromType.js';\nimport { astFromValue } from './astFromValue.js';\nimport { astFromValueUntyped } from './astFromValueUntyped.js';\nimport { getDescriptionNode } from './descriptionFromObject.js';\nimport { getDirectivesInExtensions, } from './get-directives.js';\nimport { isSome } from './helpers.js';\nimport { getRootTypeMap } from './rootTypes.js';\nexport function getDocumentNodeFromSchema(schema, options = {}) {\n    const pathToDirectivesInExtensions = options.pathToDirectivesInExtensions;\n    const typesMap = schema.getTypeMap();\n    const schemaNode = astFromSchema(schema, pathToDirectivesInExtensions);\n    const definitions = schemaNode != null ? [schemaNode] : [];\n    const directives = schema.getDirectives();\n    for (const directive of directives) {\n        if (isSpecifiedDirective(directive)) {\n            continue;\n        }\n        definitions.push(astFromDirective(directive, schema, pathToDirectivesInExtensions));\n    }\n    for (const typeName in typesMap) {\n        const type = typesMap[typeName];\n        const isPredefinedScalar = isSpecifiedScalarType(type);\n        const isIntrospection = isIntrospectionType(type);\n        if (isPredefinedScalar || isIntrospection) {\n            continue;\n        }\n        if (isObjectType(type)) {\n            definitions.push(astFromObjectType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isInterfaceType(type)) {\n            definitions.push(astFromInterfaceType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isUnionType(type)) {\n            definitions.push(astFromUnionType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isInputObjectType(type)) {\n            definitions.push(astFromInputObjectType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isEnumType(type)) {\n            definitions.push(astFromEnumType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isScalarType(type)) {\n            definitions.push(astFromScalarType(type, schema, pathToDirectivesInExtensions));\n        }\n        else {\n            throw new Error(`Unknown type ${type}.`);\n        }\n    }\n    return {\n        kind: Kind.DOCUMENT,\n        definitions,\n    };\n}\n// this approach uses the default schema printer rather than a custom solution, so may be more backwards compatible\n// currently does not allow customization of printSchema options having to do with comments.\nexport function printSchemaWithDirectives(schema, options = {}) {\n    const documentNode = getDocumentNodeFromSchema(schema, options);\n    return print(documentNode);\n}\nexport function astFromSchema(schema, pathToDirectivesInExtensions) {\n    const operationTypeMap = new Map([\n        ['query', undefined],\n        ['mutation', undefined],\n        ['subscription', undefined],\n    ]);\n    const nodes = [];\n    if (schema.astNode != null) {\n        nodes.push(schema.astNode);\n    }\n    if (schema.extensionASTNodes != null) {\n        for (const extensionASTNode of schema.extensionASTNodes) {\n            nodes.push(extensionASTNode);\n        }\n    }\n    for (const node of nodes) {\n        if (node.operationTypes) {\n            for (const operationTypeDefinitionNode of node.operationTypes) {\n                operationTypeMap.set(operationTypeDefinitionNode.operation, operationTypeDefinitionNode);\n            }\n        }\n    }\n    const rootTypeMap = getRootTypeMap(schema);\n    for (const [operationTypeNode, operationTypeDefinitionNode] of operationTypeMap) {\n        const rootType = rootTypeMap.get(operationTypeNode);\n        if (rootType != null) {\n            const rootTypeAST = astFromType(rootType);\n            if (operationTypeDefinitionNode != null) {\n                operationTypeDefinitionNode.type = rootTypeAST;\n            }\n            else {\n                operationTypeMap.set(operationTypeNode, {\n                    kind: Kind.OPERATION_TYPE_DEFINITION,\n                    operation: operationTypeNode,\n                    type: rootTypeAST,\n                });\n            }\n        }\n    }\n    const operationTypes = [...operationTypeMap.values()].filter(isSome);\n    const directives = getDirectiveNodes(schema, schema, pathToDirectivesInExtensions);\n    if (!operationTypes.length && !directives.length) {\n        return null;\n    }\n    const schemaNode = {\n        kind: operationTypes != null ? Kind.SCHEMA_DEFINITION : Kind.SCHEMA_EXTENSION,\n        operationTypes,\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: directives,\n    };\n    const descriptionNode = getDescriptionNode(schema);\n    if (descriptionNode) {\n        schemaNode.description = descriptionNode;\n    }\n    return schemaNode;\n}\nexport function astFromDirective(directive, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.DIRECTIVE_DEFINITION,\n        description: getDescriptionNode(directive),\n        name: {\n            kind: Kind.NAME,\n            value: directive.name,\n        },\n        arguments: directive.args?.map(arg => astFromArg(arg, schema, pathToDirectivesInExtensions)),\n        repeatable: directive.isRepeatable,\n        locations: directive.locations?.map(location => ({\n            kind: Kind.NAME,\n            value: location,\n        })) || [],\n    };\n}\nexport function getDirectiveNodes(entity, schema, pathToDirectivesInExtensions) {\n    let directiveNodesBesidesDeprecatedAndSpecifiedBy = [];\n    const directivesInExtensions = getDirectivesInExtensions(entity, pathToDirectivesInExtensions);\n    let directives;\n    if (directivesInExtensions != null) {\n        directives = makeDirectiveNodes(schema, directivesInExtensions);\n    }\n    let deprecatedDirectiveNode = null;\n    let specifiedByDirectiveNode = null;\n    if (directives != null) {\n        directiveNodesBesidesDeprecatedAndSpecifiedBy = directives.filter(directive => directive.name.value !== 'deprecated' && directive.name.value !== 'specifiedBy');\n        if (entity.deprecationReason != null) {\n            deprecatedDirectiveNode = directives.filter(directive => directive.name.value === 'deprecated')?.[0];\n        }\n        if (entity.specifiedByUrl != null || entity.specifiedByURL != null) {\n            specifiedByDirectiveNode = directives.filter(directive => directive.name.value === 'specifiedBy')?.[0];\n        }\n    }\n    if (entity.deprecationReason != null && deprecatedDirectiveNode == null) {\n        deprecatedDirectiveNode = makeDeprecatedDirective(entity.deprecationReason);\n    }\n    if (entity.specifiedByUrl != null ||\n        (entity.specifiedByURL != null && specifiedByDirectiveNode == null)) {\n        const specifiedByValue = entity.specifiedByUrl || entity.specifiedByURL;\n        const specifiedByArgs = {\n            url: specifiedByValue,\n        };\n        specifiedByDirectiveNode = makeDirectiveNode('specifiedBy', specifiedByArgs);\n    }\n    if (deprecatedDirectiveNode != null) {\n        directiveNodesBesidesDeprecatedAndSpecifiedBy.push(deprecatedDirectiveNode);\n    }\n    if (specifiedByDirectiveNode != null) {\n        directiveNodesBesidesDeprecatedAndSpecifiedBy.push(specifiedByDirectiveNode);\n    }\n    return directiveNodesBesidesDeprecatedAndSpecifiedBy;\n}\nexport function astFromArg(arg, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.INPUT_VALUE_DEFINITION,\n        description: getDescriptionNode(arg),\n        name: {\n            kind: Kind.NAME,\n            value: arg.name,\n        },\n        type: astFromType(arg.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        defaultValue: arg.defaultValue !== undefined\n            ? (astFromValue(arg.defaultValue, arg.type) ?? undefined)\n            : undefined,\n        directives: getDirectiveNodes(arg, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromObjectType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.OBJECT_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromField(field, schema, pathToDirectivesInExtensions)),\n        interfaces: Object.values(type.getInterfaces()).map(iFace => astFromType(iFace)),\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromInterfaceType(type, schema, pathToDirectivesInExtensions) {\n    const node = {\n        kind: Kind.INTERFACE_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromField(field, schema, pathToDirectivesInExtensions)),\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n    if ('getInterfaces' in type) {\n        node.interfaces = Object.values(type.getInterfaces()).map(iFace => astFromType(iFace));\n    }\n    return node;\n}\nexport function astFromUnionType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.UNION_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n        types: type.getTypes().map(type => astFromType(type)),\n    };\n}\nexport function astFromInputObjectType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromInputField(field, schema, pathToDirectivesInExtensions)),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromEnumType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.ENUM_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        values: Object.values(type.getValues()).map(value => astFromEnumValue(value, schema, pathToDirectivesInExtensions)),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromScalarType(type, schema, pathToDirectivesInExtensions) {\n    const directivesInExtensions = getDirectivesInExtensions(type, pathToDirectivesInExtensions);\n    const directives = makeDirectiveNodes(schema, directivesInExtensions);\n    const specifiedByValue = (type['specifiedByUrl'] ||\n        type['specifiedByURL']);\n    if (specifiedByValue &&\n        !directives.some(directiveNode => directiveNode.name.value === 'specifiedBy')) {\n        const specifiedByArgs = {\n            url: specifiedByValue,\n        };\n        directives.push(makeDirectiveNode('specifiedBy', specifiedByArgs));\n    }\n    return {\n        kind: Kind.SCALAR_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: directives,\n    };\n}\nexport function astFromField(field, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.FIELD_DEFINITION,\n        description: getDescriptionNode(field),\n        name: {\n            kind: Kind.NAME,\n            value: field.name,\n        },\n        arguments: field.args.map(arg => astFromArg(arg, schema, pathToDirectivesInExtensions)),\n        type: astFromType(field.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromInputField(field, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.INPUT_VALUE_DEFINITION,\n        description: getDescriptionNode(field),\n        name: {\n            kind: Kind.NAME,\n            value: field.name,\n        },\n        type: astFromType(field.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),\n        defaultValue: astFromValue(field.defaultValue, field.type) ?? undefined,\n    };\n}\nexport function astFromEnumValue(value, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.ENUM_VALUE_DEFINITION,\n        description: getDescriptionNode(value),\n        name: {\n            kind: Kind.NAME,\n            value: value.name,\n        },\n        directives: getDirectiveNodes(value, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function makeDeprecatedDirective(deprecationReason) {\n    return makeDirectiveNode('deprecated', { reason: deprecationReason }, GraphQLDeprecatedDirective);\n}\nexport function makeDirectiveNode(name, args, directive) {\n    const directiveArguments = [];\n    for (const argName in args) {\n        const argValue = args[argName];\n        let value;\n        if (directive != null) {\n            const arg = directive.args.find(arg => arg.name === argName);\n            if (arg) {\n                value = astFromValue(argValue, arg.type);\n            }\n        }\n        if (value == null) {\n            value = astFromValueUntyped(argValue);\n        }\n        if (value != null) {\n            directiveArguments.push({\n                kind: Kind.ARGUMENT,\n                name: {\n                    kind: Kind.NAME,\n                    value: argName,\n                },\n                value,\n            });\n        }\n    }\n    return {\n        kind: Kind.DIRECTIVE,\n        name: {\n            kind: Kind.NAME,\n            value: name,\n        },\n        arguments: directiveArguments,\n    };\n}\nexport function makeDirectiveNodes(schema, directiveValues) {\n    const directiveNodes = [];\n    for (const { name, args } of directiveValues) {\n        const directive = schema?.getDirective(name);\n        directiveNodes.push(makeDirectiveNode(name, args, directive));\n    }\n    return directiveNodes;\n}\n", "import { inspect } from 'cross-inspect';\nimport { isListType, isNonNullType, Kind } from 'graphql';\nexport function astFromType(type) {\n    if (isNonNullType(type)) {\n        const innerType = astFromType(type.ofType);\n        if (innerType.kind === Kind.NON_NULL_TYPE) {\n            throw new Error(`Invalid type node ${inspect(type)}. Inner type of non-null type cannot be a non-null type.`);\n        }\n        return {\n            kind: Kind.NON_NULL_TYPE,\n            type: innerType,\n        };\n    }\n    else if (isListType(type)) {\n        return {\n            kind: Kind.LIST_TYPE,\n            type: astFromType(type.ofType),\n        };\n    }\n    return {\n        kind: Kind.NAMED_TYPE,\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n    };\n}\n", "import { inspect } from 'cross-inspect';\nimport { isEnumType, isInputObjectType, isLeafType, isListType, isNonNullType, Kind, } from 'graphql';\nimport { astFromValueUntyped } from './astFromValueUntyped.js';\nimport { isIterableObject, isObjectLike } from './jsutils.js';\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using suggested GraphQLInputType. For example:\n *\n *     astFromValue(\"value\", GraphQLString)\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * JavaScript values.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String / Enum Value  |\n * | Number        | Int / Float          |\n * | BigInt        | Int                  |\n * | Unknown       | Enum Value           |\n * | null          | NullValue            |\n *\n */\nexport function astFromValue(value, type) {\n    if (isNonNullType(type)) {\n        const astValue = astFromValue(value, type.ofType);\n        if (astValue?.kind === Kind.NULL) {\n            return null;\n        }\n        return astValue;\n    }\n    // only explicit null, not undefined, NaN\n    if (value === null) {\n        return { kind: Kind.NULL };\n    }\n    // undefined\n    if (value === undefined) {\n        return null;\n    }\n    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n    // the value is not an array, convert the value using the list's item type.\n    if (isListType(type)) {\n        const itemType = type.ofType;\n        if (isIterableObject(value)) {\n            const valuesNodes = [];\n            for (const item of value) {\n                const itemNode = astFromValue(item, itemType);\n                if (itemNode != null) {\n                    valuesNodes.push(itemNode);\n                }\n            }\n            return { kind: Kind.LIST, values: valuesNodes };\n        }\n        return astFromValue(value, itemType);\n    }\n    // Populate the fields of the input object by creating ASTs from each value\n    // in the JavaScript object according to the fields in the input type.\n    if (isInputObjectType(type)) {\n        if (!isObjectLike(value)) {\n            return null;\n        }\n        const fieldNodes = [];\n        for (const field of Object.values(type.getFields())) {\n            const fieldValue = astFromValue(value[field.name], field.type);\n            if (fieldValue) {\n                fieldNodes.push({\n                    kind: Kind.OBJECT_FIELD,\n                    name: { kind: Kind.NAME, value: field.name },\n                    value: fieldValue,\n                });\n            }\n        }\n        return { kind: Kind.OBJECT, fields: fieldNodes };\n    }\n    if (isLeafType(type)) {\n        // Since value is an internally represented value, it must be serialized\n        // to an externally represented value before converting into an AST.\n        const serialized = type.serialize(value);\n        if (serialized == null) {\n            return null;\n        }\n        if (isEnumType(type)) {\n            return { kind: Kind.ENUM, value: serialized };\n        }\n        // ID types can use Int literals.\n        if (type.name === 'ID' &&\n            typeof serialized === 'string' &&\n            integerStringRegExp.test(serialized)) {\n            return { kind: Kind.INT, value: serialized };\n        }\n        return astFromValueUntyped(serialized);\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible types have been considered.\n    console.assert(false, 'Unexpected input type: ' + inspect(type));\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;\n", "import { Kind } from 'graphql';\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using the following mapping.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String               |\n * | Number        | Int / Float          |\n * | BigInt        | Int                  |\n * | null          | NullValue            |\n *\n */\nexport function astFromValueUntyped(value) {\n    // only explicit null, not undefined, NaN\n    if (value === null) {\n        return { kind: Kind.NULL };\n    }\n    // undefined\n    if (value === undefined) {\n        return null;\n    }\n    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n    // the value is not an array, convert the value using the list's item type.\n    if (Array.isArray(value)) {\n        const valuesNodes = [];\n        for (const item of value) {\n            const itemNode = astFromValueUntyped(item);\n            if (itemNode != null) {\n                valuesNodes.push(itemNode);\n            }\n        }\n        return { kind: Kind.LIST, values: valuesNodes };\n    }\n    if (typeof value === 'object') {\n        if (value?.toJSON) {\n            return astFromValueUntyped(value.toJSON());\n        }\n        const fieldNodes = [];\n        for (const fieldName in value) {\n            const fieldValue = value[fieldName];\n            const ast = astFromValueUntyped(fieldValue);\n            if (ast) {\n                fieldNodes.push({\n                    kind: Kind.OBJECT_FIELD,\n                    name: { kind: Kind.NAME, value: fieldName },\n                    value: ast,\n                });\n            }\n        }\n        return { kind: Kind.OBJECT, fields: fieldNodes };\n    }\n    // Others serialize based on their corresponding JavaScript scalar types.\n    if (typeof value === 'boolean') {\n        return { kind: Kind.BOOLEAN, value };\n    }\n    if (typeof value === 'bigint') {\n        return { kind: Kind.INT, value: String(value) };\n    }\n    // JavaScript numbers can be Int or Float values.\n    if (typeof value === 'number' && isFinite(value)) {\n        const stringNum = String(value);\n        return integerStringRegExp.test(stringNum)\n            ? { kind: Kind.INT, value: stringNum }\n            : { kind: Kind.FLOAT, value: stringNum };\n    }\n    if (typeof value === 'string') {\n        return { kind: Kind.STRING, value };\n    }\n    throw new TypeError(`Cannot convert value to AST: ${value}.`);\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;\n", "import { Kind } from 'graphql';\nexport function getDescriptionNode(obj) {\n    if (obj.astNode?.description) {\n        return {\n            ...obj.astNode.description,\n            block: true,\n        };\n    }\n    if (obj.description) {\n        return {\n            kind: Kind.STRING,\n            value: obj.description,\n            block: true,\n        };\n    }\n}\n", "import { createGraphQLError } from './errors.js';\nimport { memoize1 } from './memoize.js';\nexport function getDefinedRootType(schema, operation, nodes) {\n    const rootTypeMap = getRootTypeMap(schema);\n    const rootType = rootTypeMap.get(operation);\n    if (rootType == null) {\n        throw createGraphQLError(`<PERSON>hem<PERSON> is not configured to execute ${operation} operation.`, {\n            nodes,\n        });\n    }\n    return rootType;\n}\nexport const getRootTypeNames = memoize1(function getRootTypeNames(schema) {\n    const rootTypes = getRootTypes(schema);\n    return new Set([...rootTypes].map(type => type.name));\n});\nexport const getRootTypes = memoize1(function getRootTypes(schema) {\n    const rootTypeMap = getRootTypeMap(schema);\n    return new Set(rootTypeMap.values());\n});\nexport const getRootTypeMap = memoize1(function getRootTypeMap(schema) {\n    const rootTypeMap = new Map();\n    const queryType = schema.getQueryType();\n    if (queryType) {\n        rootTypeMap.set('query', queryType);\n    }\n    const mutationType = schema.getMutationType();\n    if (mutationType) {\n        rootTypeMap.set('mutation', mutationType);\n    }\n    const subscriptionType = schema.getSubscriptionType();\n    if (subscriptionType) {\n        rootTypeMap.set('subscription', subscriptionType);\n    }\n    return rootTypeMap;\n});\n", "import { Kind, specifiedRules, validate, versionInfo, } from 'graphql';\nexport function validateGraphQlDocuments(schema, documents, rules = createDefaultRules()) {\n    const definitions = new Set();\n    const fragmentsDefinitionsMap = new Map();\n    for (const document of documents) {\n        for (const docDefinition of document.definitions) {\n            if (docDefinition.kind === Kind.FRAGMENT_DEFINITION) {\n                fragmentsDefinitionsMap.set(docDefinition.name.value, docDefinition);\n            }\n            else {\n                definitions.add(docDefinition);\n            }\n        }\n    }\n    const fullAST = {\n        kind: Kind.DOCUMENT,\n        definitions: Array.from([...definitions, ...fragmentsDefinitionsMap.values()]),\n    };\n    const errors = validate(schema, fullAST, rules);\n    for (const error of errors) {\n        error.stack = error.message;\n        if (error.locations) {\n            for (const location of error.locations) {\n                error.stack += `\\n    at ${error.source?.name}:${location.line}:${location.column}`;\n            }\n        }\n    }\n    return errors;\n}\nexport function createDefaultRules() {\n    let ignored = ['NoUnusedFragmentsRule', 'NoUnusedVariablesRule', 'KnownDirectivesRule'];\n    if (versionInfo.major < 15) {\n        ignored = ignored.map(rule => rule.replace(/Rule$/, ''));\n    }\n    return specifiedRules.filter((f) => !ignored.includes(f.name));\n}\n", "import { buildClientSchema } from 'graphql';\nfunction stripBOM(content) {\n    content = content.toString();\n    // Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n    // because the buffer-to-string conversion in `fs.readFileSync()`\n    // translates it to FEFF, the UTF-16 BOM.\n    if (content.charCodeAt(0) === 0xfeff) {\n        content = content.slice(1);\n    }\n    return content;\n}\nfunction parseBOM(content) {\n    return JSON.parse(stripBOM(content));\n}\nexport function parseGraphQLJSON(location, jsonContent, options) {\n    let parsedJson = parseBOM(jsonContent);\n    if (parsedJson.data) {\n        parsedJson = parsedJson.data;\n    }\n    if (parsedJson.kind === 'Document') {\n        return {\n            location,\n            document: parsedJson,\n        };\n    }\n    else if (parsedJson.__schema) {\n        const schema = buildClientSchema(parsedJson, options);\n        return {\n            location,\n            schema,\n        };\n    }\n    else if (typeof parsedJson === 'string') {\n        return {\n            location,\n            rawSDL: parsedJson,\n        };\n    }\n    throw new Error(`Not valid JSON content`);\n}\n", "import { Source as GraphQLSource, isTypeSystemDefinitionNode, Kind, parse, print, visit, } from 'graphql';\nimport { dedentBlockStringValue, getLeadingCommentBlock } from './comments.js';\nexport function parseGraphQLSDL(location, rawSDL, options = {}) {\n    let document;\n    try {\n        if (options.commentDescriptions && rawSDL.includes('#')) {\n            document = transformCommentsToDescriptions(rawSDL, options);\n            // If noLocation=true, we need to make sure to print and parse it again, to remove locations,\n            // since `transformCommentsToDescriptions` must have locations set in order to transform the comments\n            // into descriptions.\n            if (options.noLocation) {\n                document = parse(print(document), options);\n            }\n        }\n        else {\n            document = parse(new GraphQLSource(rawSDL, location), options);\n        }\n    }\n    catch (e) {\n        if (e.message.includes('EOF') && rawSDL.replace(/(\\#[^*]*)/g, '').trim() === '') {\n            document = {\n                kind: Kind.DOCUMENT,\n                definitions: [],\n            };\n        }\n        else {\n            throw e;\n        }\n    }\n    return {\n        location,\n        document,\n    };\n}\nexport function transformCommentsToDescriptions(sourceSdl, options = {}) {\n    const parsedDoc = parse(sourceSdl, {\n        ...options,\n        noLocation: false,\n    });\n    const modifiedDoc = visit(parsedDoc, {\n        leave: (node) => {\n            if (isDescribable(node)) {\n                const rawValue = getLeadingCommentBlock(node);\n                if (rawValue !== undefined) {\n                    const commentsBlock = dedentBlockStringValue('\\n' + rawValue);\n                    const isBlock = commentsBlock.includes('\\n');\n                    if (!node.description) {\n                        return {\n                            ...node,\n                            description: {\n                                kind: Kind.STRING,\n                                value: commentsBlock,\n                                block: isBlock,\n                            },\n                        };\n                    }\n                    else {\n                        return {\n                            ...node,\n                            description: {\n                                ...node.description,\n                                value: node.description.value + '\\n' + commentsBlock,\n                                block: true,\n                            },\n                        };\n                    }\n                }\n            }\n        },\n    });\n    return modifiedDoc;\n}\nexport function isDescribable(node) {\n    return (isTypeSystemDefinitionNode(node) ||\n        node.kind === Kind.FIELD_DEFINITION ||\n        node.kind === Kind.INPUT_VALUE_DEFINITION ||\n        node.kind === Kind.ENUM_VALUE_DEFINITION);\n}\n", "import { TokenKind, visit, } from 'graphql';\nconst MAX_LINE_LENGTH = 80;\nlet commentsRegistry = {};\nexport function resetComments() {\n    commentsRegistry = {};\n}\nexport function collectComment(node) {\n    const entityName = node.name?.value;\n    if (entityName == null) {\n        return;\n    }\n    pushComment(node, entityName);\n    switch (node.kind) {\n        case 'EnumTypeDefinition':\n            if (node.values) {\n                for (const value of node.values) {\n                    pushComment(value, entityName, value.name.value);\n                }\n            }\n            break;\n        case 'ObjectTypeDefinition':\n        case 'InputObjectTypeDefinition':\n        case 'InterfaceTypeDefinition':\n            if (node.fields) {\n                for (const field of node.fields) {\n                    pushComment(field, entityName, field.name.value);\n                    if (isFieldDefinitionNode(field) && field.arguments) {\n                        for (const arg of field.arguments) {\n                            pushComment(arg, entityName, field.name.value, arg.name.value);\n                        }\n                    }\n                }\n            }\n            break;\n    }\n}\nexport function pushComment(node, entity, field, argument) {\n    const comment = getComment(node);\n    if (typeof comment !== 'string' || comment.length === 0) {\n        return;\n    }\n    const keys = [entity];\n    if (field) {\n        keys.push(field);\n        if (argument) {\n            keys.push(argument);\n        }\n    }\n    const path = keys.join('.');\n    if (!commentsRegistry[path]) {\n        commentsRegistry[path] = [];\n    }\n    commentsRegistry[path].push(comment);\n}\nexport function printComment(comment) {\n    return '\\n# ' + comment.replace(/\\n/g, '\\n# ');\n}\n/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n/**\n * NOTE: ==> This file has been modified just to add comments to the printed AST\n * This is a temp measure, we will move to using the original non modified printer.js ASAP.\n */\n/**\n * Given maybeArray, print an empty string if it is null or empty, otherwise\n * print all items together separated by separator if provided\n */\nfunction join(maybeArray, separator) {\n    return maybeArray ? maybeArray.filter(x => x).join(separator || '') : '';\n}\nfunction hasMultilineItems(maybeArray) {\n    return maybeArray?.some(str => str.includes('\\n')) ?? false;\n}\nfunction addDescription(cb) {\n    return (node, _key, _parent, path, ancestors) => {\n        const keys = [];\n        const parent = path.reduce((prev, key) => {\n            if (['fields', 'arguments', 'values'].includes(key) && prev.name) {\n                keys.push(prev.name.value);\n            }\n            return prev[key];\n        }, ancestors[0]);\n        const key = [...keys, parent?.name?.value].filter(Boolean).join('.');\n        const items = [];\n        if (node.kind.includes('Definition') && commentsRegistry[key]) {\n            items.push(...commentsRegistry[key]);\n        }\n        return join([...items.map(printComment), node.description, cb(node, _key, _parent, path, ancestors)], '\\n');\n    };\n}\nfunction indent(maybeString) {\n    return maybeString && `  ${maybeString.replace(/\\n/g, '\\n  ')}`;\n}\n/**\n * Given array, print each item on its own line, wrapped in an\n * indented \"{ }\" block.\n */\nfunction block(array) {\n    return array && array.length !== 0 ? `{\\n${indent(join(array, '\\n'))}\\n}` : '';\n}\n/**\n * If maybeString is not null or empty, then wrap with start and end, otherwise\n * print an empty string.\n */\nfunction wrap(start, maybeString, end) {\n    return maybeString ? start + maybeString + (end || '') : '';\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n */\nfunction printBlockString(value, isDescription = false) {\n    const escaped = value.replace(/\\\\/g, '\\\\\\\\').replace(/\"\"\"/g, '\\\\\"\"\"');\n    return (value[0] === ' ' || value[0] === '\\t') && value.indexOf('\\n') === -1\n        ? `\"\"\"${escaped.replace(/\"$/, '\"\\n')}\"\"\"`\n        : `\"\"\"\\n${isDescription ? escaped : indent(escaped)}\\n\"\"\"`;\n}\nconst printDocASTReducer = {\n    Name: { leave: node => node.value },\n    Variable: { leave: node => '$' + node.name },\n    // Document\n    Document: {\n        leave: node => join(node.definitions, '\\n\\n'),\n    },\n    OperationDefinition: {\n        leave: node => {\n            const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');\n            const prefix = join([node.operation, join([node.name, varDefs]), join(node.directives, ' ')], ' ');\n            // the query short form.\n            return prefix + ' ' + node.selectionSet;\n        },\n    },\n    VariableDefinition: {\n        leave: ({ variable, type, defaultValue, directives }) => variable + ': ' + type + wrap(' = ', defaultValue) + wrap(' ', join(directives, ' ')),\n    },\n    SelectionSet: { leave: ({ selections }) => block(selections) },\n    Field: {\n        leave({ alias, name, arguments: args, directives, selectionSet }) {\n            const prefix = wrap('', alias, ': ') + name;\n            let argsLine = prefix + wrap('(', join(args, ', '), ')');\n            if (argsLine.length > MAX_LINE_LENGTH) {\n                argsLine = prefix + wrap('(\\n', indent(join(args, '\\n')), '\\n)');\n            }\n            return join([argsLine, join(directives, ' '), selectionSet], ' ');\n        },\n    },\n    Argument: { leave: ({ name, value }) => name + ': ' + value },\n    // Fragments\n    FragmentSpread: {\n        leave: ({ name, directives }) => '...' + name + wrap(' ', join(directives, ' ')),\n    },\n    InlineFragment: {\n        leave: ({ typeCondition, directives, selectionSet }) => join(['...', wrap('on ', typeCondition), join(directives, ' '), selectionSet], ' '),\n    },\n    FragmentDefinition: {\n        leave: ({ name, typeCondition, variableDefinitions, directives, selectionSet }) => \n        // Note: fragment variable definitions are experimental and may be changed\n        // or removed in the future.\n        `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` +\n            `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` +\n            selectionSet,\n    },\n    // Value\n    IntValue: { leave: ({ value }) => value },\n    FloatValue: { leave: ({ value }) => value },\n    StringValue: {\n        leave: ({ value, block: isBlockString }) => {\n            if (isBlockString) {\n                return printBlockString(value);\n            }\n            return JSON.stringify(value);\n        },\n    },\n    BooleanValue: { leave: ({ value }) => (value ? 'true' : 'false') },\n    NullValue: { leave: () => 'null' },\n    EnumValue: { leave: ({ value }) => value },\n    ListValue: { leave: ({ values }) => '[' + join(values, ', ') + ']' },\n    ObjectValue: { leave: ({ fields }) => '{' + join(fields, ', ') + '}' },\n    ObjectField: { leave: ({ name, value }) => name + ': ' + value },\n    // Directive\n    Directive: {\n        leave: ({ name, arguments: args }) => '@' + name + wrap('(', join(args, ', '), ')'),\n    },\n    // Type\n    NamedType: { leave: ({ name }) => name },\n    ListType: { leave: ({ type }) => '[' + type + ']' },\n    NonNullType: { leave: ({ type }) => type + '!' },\n    // Type System Definitions\n    SchemaDefinition: {\n        leave: ({ directives, operationTypes }) => join(['schema', join(directives, ' '), block(operationTypes)], ' '),\n    },\n    OperationTypeDefinition: {\n        leave: ({ operation, type }) => operation + ': ' + type,\n    },\n    ScalarTypeDefinition: {\n        leave: ({ name, directives }) => join(['scalar', name, join(directives, ' ')], ' '),\n    },\n    ObjectTypeDefinition: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'type',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    FieldDefinition: {\n        leave: ({ name, arguments: args, type, directives }) => name +\n            (hasMultilineItems(args)\n                ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n                : wrap('(', join(args, ', '), ')')) +\n            ': ' +\n            type +\n            wrap(' ', join(directives, ' ')),\n    },\n    InputValueDefinition: {\n        leave: ({ name, type, defaultValue, directives }) => join([name + ': ' + type, wrap('= ', defaultValue), join(directives, ' ')], ' '),\n    },\n    InterfaceTypeDefinition: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'interface',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    UnionTypeDefinition: {\n        leave: ({ name, directives, types }) => join(['union', name, join(directives, ' '), wrap('= ', join(types, ' | '))], ' '),\n    },\n    EnumTypeDefinition: {\n        leave: ({ name, directives, values }) => join(['enum', name, join(directives, ' '), block(values)], ' '),\n    },\n    EnumValueDefinition: {\n        leave: ({ name, directives }) => join([name, join(directives, ' ')], ' '),\n    },\n    InputObjectTypeDefinition: {\n        leave: ({ name, directives, fields }) => join(['input', name, join(directives, ' '), block(fields)], ' '),\n    },\n    DirectiveDefinition: {\n        leave: ({ name, arguments: args, repeatable, locations }) => 'directive @' +\n            name +\n            (hasMultilineItems(args)\n                ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n                : wrap('(', join(args, ', '), ')')) +\n            (repeatable ? ' repeatable' : '') +\n            ' on ' +\n            join(locations, ' | '),\n    },\n    SchemaExtension: {\n        leave: ({ directives, operationTypes }) => join(['extend schema', join(directives, ' '), block(operationTypes)], ' '),\n    },\n    ScalarTypeExtension: {\n        leave: ({ name, directives }) => join(['extend scalar', name, join(directives, ' ')], ' '),\n    },\n    ObjectTypeExtension: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'extend type',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    InterfaceTypeExtension: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'extend interface',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    UnionTypeExtension: {\n        leave: ({ name, directives, types }) => join(['extend union', name, join(directives, ' '), wrap('= ', join(types, ' | '))], ' '),\n    },\n    EnumTypeExtension: {\n        leave: ({ name, directives, values }) => join(['extend enum', name, join(directives, ' '), block(values)], ' '),\n    },\n    InputObjectTypeExtension: {\n        leave: ({ name, directives, fields }) => join(['extend input', name, join(directives, ' '), block(fields)], ' '),\n    },\n};\nconst printDocASTReducerWithComments = Object.keys(printDocASTReducer).reduce((prev, key) => ({\n    ...prev,\n    [key]: {\n        leave: addDescription(printDocASTReducer[key].leave),\n    },\n}), {});\n/**\n * Converts an AST into a string, using one set of reasonable\n * formatting rules.\n */\nexport function printWithComments(ast) {\n    return visit(ast, printDocASTReducerWithComments);\n}\nfunction isFieldDefinitionNode(node) {\n    return node.kind === 'FieldDefinition';\n}\n// graphql < v13 and > v15 does not export getDescription\nexport function getDescription(node, options) {\n    if (node.description != null) {\n        return node.description.value;\n    }\n    if (options?.commentDescriptions) {\n        return getComment(node);\n    }\n}\nexport function getComment(node) {\n    const rawValue = getLeadingCommentBlock(node);\n    if (rawValue !== undefined) {\n        return dedentBlockStringValue(`\\n${rawValue}`);\n    }\n}\nexport function getLeadingCommentBlock(node) {\n    const loc = node.loc;\n    if (!loc) {\n        return;\n    }\n    const comments = [];\n    let token = loc.startToken.prev;\n    while (token != null &&\n        token.kind === TokenKind.COMMENT &&\n        token.next != null &&\n        token.prev != null &&\n        token.line + 1 === token.next.line &&\n        token.line !== token.prev.line) {\n        const value = String(token.value);\n        comments.push(value);\n        token = token.prev;\n    }\n    return comments.length > 0 ? comments.reverse().join('\\n') : undefined;\n}\nexport function dedentBlockStringValue(rawString) {\n    // Expand a block string's raw value into independent lines.\n    const lines = rawString.split(/\\r\\n|[\\n\\r]/g);\n    // Remove common indentation from all lines but first.\n    const commonIndent = getBlockStringIndentation(lines);\n    if (commonIndent !== 0) {\n        for (let i = 1; i < lines.length; i++) {\n            lines[i] = lines[i].slice(commonIndent);\n        }\n    }\n    // Remove leading and trailing blank lines.\n    while (lines.length > 0 && isBlank(lines[0])) {\n        lines.shift();\n    }\n    while (lines.length > 0 && isBlank(lines[lines.length - 1])) {\n        lines.pop();\n    }\n    // Return a string of the lines joined with U+000A.\n    return lines.join('\\n');\n}\n/**\n * @internal\n */\nexport function getBlockStringIndentation(lines) {\n    let commonIndent = null;\n    for (let i = 1; i < lines.length; i++) {\n        const line = lines[i];\n        const indent = leadingWhitespace(line);\n        if (indent === line.length) {\n            continue; // skip empty lines\n        }\n        if (commonIndent === null || indent < commonIndent) {\n            commonIndent = indent;\n            if (commonIndent === 0) {\n                break;\n            }\n        }\n    }\n    return commonIndent === null ? 0 : commonIndent;\n}\nfunction leadingWhitespace(str) {\n    let i = 0;\n    while (i < str.length && (str[i] === ' ' || str[i] === '\\t')) {\n        i++;\n    }\n    return i;\n}\nfunction isBlank(str) {\n    return leadingWhitespace(str) === str.length;\n}\n", "import { getNamedType, isEnumType, isInterfaceType, isListType, isNonNullType, isObjectType, isScalarType, isUnionType, Kind, } from 'graphql';\nimport { getDefinedRootType, getRootTypeNames } from './rootTypes.js';\nlet operationVariables = [];\nlet fieldTypeMap = new Map();\nfunction addOperationVariable(variable) {\n    operationVariables.push(variable);\n}\nfunction resetOperationVariables() {\n    operationVariables = [];\n}\nfunction resetFieldMap() {\n    fieldTypeMap = new Map();\n}\nexport function buildOperationNodeForField({ schema, kind, field, models, ignore = [], depthLimit, circularReferenceDepth, argNames, selectedFields = true, }) {\n    resetOperationVariables();\n    resetFieldMap();\n    const rootTypeNames = getRootTypeNames(schema);\n    const operationNode = buildOperationAndCollectVariables({\n        schema,\n        fieldName: field,\n        kind,\n        models: models || [],\n        ignore,\n        depthLimit: depthLimit || Infinity,\n        circularReferenceDepth: circularReferenceDepth || 1,\n        argNames,\n        selectedFields,\n        rootTypeNames,\n    });\n    // attach variables\n    operationNode.variableDefinitions = [...operationVariables];\n    resetOperationVariables();\n    resetFieldMap();\n    return operationNode;\n}\nfunction buildOperationAndCollectVariables({ schema, fieldName, kind, models, ignore, depthLimit, circularReferenceDepth, argNames, selectedFields, rootTypeNames, }) {\n    const type = getDefinedRootType(schema, kind);\n    const field = type.getFields()[fieldName];\n    const operationName = `${fieldName}_${kind}`;\n    if (field.args) {\n        for (const arg of field.args) {\n            const argName = arg.name;\n            if (!argNames || argNames.includes(argName)) {\n                addOperationVariable(resolveVariable(arg, argName));\n            }\n        }\n    }\n    return {\n        kind: Kind.OPERATION_DEFINITION,\n        operation: kind,\n        name: {\n            kind: Kind.NAME,\n            value: operationName,\n        },\n        variableDefinitions: [],\n        selectionSet: {\n            kind: Kind.SELECTION_SET,\n            selections: [\n                resolveField({\n                    type,\n                    field,\n                    models,\n                    firstCall: true,\n                    path: [],\n                    ancestors: [],\n                    ignore,\n                    depthLimit,\n                    circularReferenceDepth,\n                    schema,\n                    depth: 0,\n                    argNames,\n                    selectedFields,\n                    rootTypeNames,\n                }),\n            ],\n        },\n    };\n}\nfunction resolveSelectionSet({ parent, type, models, firstCall, path, ancestors, ignore, depthLimit, circularReferenceDepth, schema, depth, argNames, selectedFields, rootTypeNames, }) {\n    if (typeof selectedFields === 'boolean' && depth > depthLimit) {\n        return;\n    }\n    if (isUnionType(type)) {\n        const types = type.getTypes();\n        return {\n            kind: Kind.SELECTION_SET,\n            selections: types\n                .filter(t => !hasCircularRef([...ancestors, t], {\n                depth: circularReferenceDepth,\n            }))\n                .map(t => {\n                return {\n                    kind: Kind.INLINE_FRAGMENT,\n                    typeCondition: {\n                        kind: Kind.NAMED_TYPE,\n                        name: {\n                            kind: Kind.NAME,\n                            value: t.name,\n                        },\n                    },\n                    selectionSet: resolveSelectionSet({\n                        parent: type,\n                        type: t,\n                        models,\n                        path,\n                        ancestors,\n                        ignore,\n                        depthLimit,\n                        circularReferenceDepth,\n                        schema,\n                        depth,\n                        argNames,\n                        selectedFields,\n                        rootTypeNames,\n                    }),\n                };\n            })\n                .filter(fragmentNode => fragmentNode?.selectionSet?.selections?.length > 0),\n        };\n    }\n    if (isInterfaceType(type)) {\n        const types = Object.values(schema.getTypeMap()).filter((t) => isObjectType(t) && t.getInterfaces().includes(type));\n        return {\n            kind: Kind.SELECTION_SET,\n            selections: types\n                .filter(t => !hasCircularRef([...ancestors, t], {\n                depth: circularReferenceDepth,\n            }))\n                .map(t => {\n                return {\n                    kind: Kind.INLINE_FRAGMENT,\n                    typeCondition: {\n                        kind: Kind.NAMED_TYPE,\n                        name: {\n                            kind: Kind.NAME,\n                            value: t.name,\n                        },\n                    },\n                    selectionSet: resolveSelectionSet({\n                        parent: type,\n                        type: t,\n                        models,\n                        path,\n                        ancestors,\n                        ignore,\n                        depthLimit,\n                        circularReferenceDepth,\n                        schema,\n                        depth,\n                        argNames,\n                        selectedFields,\n                        rootTypeNames,\n                    }),\n                };\n            })\n                .filter(fragmentNode => fragmentNode?.selectionSet?.selections?.length > 0),\n        };\n    }\n    if (isObjectType(type) && !rootTypeNames.has(type.name)) {\n        const isIgnored = ignore.includes(type.name) || ignore.includes(`${parent.name}.${path[path.length - 1]}`);\n        const isModel = models.includes(type.name);\n        if (!firstCall && isModel && !isIgnored) {\n            return {\n                kind: Kind.SELECTION_SET,\n                selections: [\n                    {\n                        kind: Kind.FIELD,\n                        name: {\n                            kind: Kind.NAME,\n                            value: 'id',\n                        },\n                    },\n                ],\n            };\n        }\n        const fields = type.getFields();\n        return {\n            kind: Kind.SELECTION_SET,\n            selections: Object.keys(fields)\n                .filter(fieldName => {\n                return !hasCircularRef([...ancestors, getNamedType(fields[fieldName].type)], {\n                    depth: circularReferenceDepth,\n                });\n            })\n                .map(fieldName => {\n                const selectedSubFields = typeof selectedFields === 'object' ? selectedFields[fieldName] : true;\n                if (selectedSubFields) {\n                    return resolveField({\n                        type,\n                        field: fields[fieldName],\n                        models,\n                        path: [...path, fieldName],\n                        ancestors,\n                        ignore,\n                        depthLimit,\n                        circularReferenceDepth,\n                        schema,\n                        depth,\n                        argNames,\n                        selectedFields: selectedSubFields,\n                        rootTypeNames,\n                    });\n                }\n                return null;\n            })\n                .filter((f) => {\n                if (f == null) {\n                    return false;\n                }\n                else if ('selectionSet' in f) {\n                    return !!f.selectionSet?.selections?.length;\n                }\n                return true;\n            }),\n        };\n    }\n}\nfunction resolveVariable(arg, name) {\n    function resolveVariableType(type) {\n        if (isListType(type)) {\n            return {\n                kind: Kind.LIST_TYPE,\n                type: resolveVariableType(type.ofType),\n            };\n        }\n        if (isNonNullType(type)) {\n            return {\n                kind: Kind.NON_NULL_TYPE,\n                // for v16 compatibility\n                type: resolveVariableType(type.ofType),\n            };\n        }\n        return {\n            kind: Kind.NAMED_TYPE,\n            name: {\n                kind: Kind.NAME,\n                value: type.name,\n            },\n        };\n    }\n    return {\n        kind: Kind.VARIABLE_DEFINITION,\n        variable: {\n            kind: Kind.VARIABLE,\n            name: {\n                kind: Kind.NAME,\n                value: name || arg.name,\n            },\n        },\n        type: resolveVariableType(arg.type),\n    };\n}\nfunction getArgumentName(name, path) {\n    return [...path, name].join('_');\n}\nfunction resolveField({ type, field, models, firstCall, path, ancestors, ignore, depthLimit, circularReferenceDepth, schema, depth, argNames, selectedFields, rootTypeNames, }) {\n    const namedType = getNamedType(field.type);\n    let args = [];\n    let removeField = false;\n    if (field.args && field.args.length) {\n        args = field.args\n            .map(arg => {\n            const argumentName = getArgumentName(arg.name, path);\n            if (argNames && !argNames.includes(argumentName)) {\n                if (isNonNullType(arg.type)) {\n                    removeField = true;\n                }\n                return null;\n            }\n            if (!firstCall) {\n                addOperationVariable(resolveVariable(arg, argumentName));\n            }\n            return {\n                kind: Kind.ARGUMENT,\n                name: {\n                    kind: Kind.NAME,\n                    value: arg.name,\n                },\n                value: {\n                    kind: Kind.VARIABLE,\n                    name: {\n                        kind: Kind.NAME,\n                        value: getArgumentName(arg.name, path),\n                    },\n                },\n            };\n        })\n            .filter(Boolean);\n    }\n    if (removeField) {\n        return null;\n    }\n    const fieldPath = [...path, field.name];\n    const fieldPathStr = fieldPath.join('.');\n    let fieldName = field.name;\n    if (fieldTypeMap.has(fieldPathStr) && fieldTypeMap.get(fieldPathStr) !== field.type.toString()) {\n        fieldName += field.type\n            .toString()\n            .replace(/!/g, 'NonNull')\n            .replace(/\\[/g, 'List')\n            .replace(/\\]/g, '');\n    }\n    fieldTypeMap.set(fieldPathStr, field.type.toString());\n    if (!isScalarType(namedType) && !isEnumType(namedType)) {\n        return {\n            kind: Kind.FIELD,\n            name: {\n                kind: Kind.NAME,\n                value: field.name,\n            },\n            ...(fieldName !== field.name && { alias: { kind: Kind.NAME, value: fieldName } }),\n            selectionSet: resolveSelectionSet({\n                parent: type,\n                type: namedType,\n                models,\n                firstCall,\n                path: fieldPath,\n                ancestors: [...ancestors, type],\n                ignore,\n                depthLimit,\n                circularReferenceDepth,\n                schema,\n                depth: depth + 1,\n                argNames,\n                selectedFields,\n                rootTypeNames,\n            }) || undefined,\n            arguments: args,\n        };\n    }\n    return {\n        kind: Kind.FIELD,\n        name: {\n            kind: Kind.NAME,\n            value: field.name,\n        },\n        ...(fieldName !== field.name && { alias: { kind: Kind.NAME, value: fieldName } }),\n        arguments: args,\n    };\n}\nfunction hasCircularRef(types, config = {\n    depth: 1,\n}) {\n    const type = types[types.length - 1];\n    if (isScalarType(type)) {\n        return false;\n    }\n    const size = types.filter(t => t.name === type.name).length;\n    return size > config.depth;\n}\n", "export var DirectiveLocation;\n(function (DirectiveLocation) {\n    /** Request Definitions */\n    DirectiveLocation[\"QUERY\"] = \"QUERY\";\n    DirectiveLocation[\"MUTATION\"] = \"MUTATION\";\n    DirectiveLocation[\"SUBSCRIPTION\"] = \"SUBSCRIPTION\";\n    DirectiveLocation[\"FIELD\"] = \"FIELD\";\n    DirectiveLocation[\"FRAGMENT_DEFINITION\"] = \"FRAGMENT_DEFINITION\";\n    DirectiveLocation[\"FRAGMENT_SPREAD\"] = \"FRAGMENT_SPREAD\";\n    DirectiveLocation[\"INLINE_FRAGMENT\"] = \"INLINE_FRAGMENT\";\n    DirectiveLocation[\"VARIABLE_DEFINITION\"] = \"VARIABLE_DEFINITION\";\n    /** Type System Definitions */\n    DirectiveLocation[\"SCHEMA\"] = \"SCHEMA\";\n    DirectiveLocation[\"SCALAR\"] = \"SCALAR\";\n    DirectiveLocation[\"OBJECT\"] = \"OBJECT\";\n    DirectiveLocation[\"FIELD_DEFINITION\"] = \"FIELD_DEFINITION\";\n    DirectiveLocation[\"ARGUMENT_DEFINITION\"] = \"ARGUMENT_DEFINITION\";\n    DirectiveLocation[\"INTERFACE\"] = \"INTERFACE\";\n    DirectiveLocation[\"UNION\"] = \"UNION\";\n    DirectiveLocation[\"ENUM\"] = \"ENUM\";\n    DirectiveLocation[\"ENUM_VALUE\"] = \"ENUM_VALUE\";\n    DirectiveLocation[\"INPUT_OBJECT\"] = \"INPUT_OBJECT\";\n    DirectiveLocation[\"INPUT_FIELD_DEFINITION\"] = \"INPUT_FIELD_DEFINITION\";\n})(DirectiveLocation || (DirectiveLocation = {}));\n", "import { GraphQLInputObjectType, GraphQLInterfaceType, GraphQLObjectType, } from 'graphql';\nimport { MapperKind, } from './Interfaces.js';\nimport { mapSchema } from './mapSchema.js';\nexport function filterSchema({ schema, typeFilter = () => true, fieldFilter = undefined, rootFieldFilter = undefined, objectFieldFilter = undefined, interfaceFieldFilter = undefined, inputObjectFieldFilter = undefined, argumentFilter = undefined, directiveFilter = undefined, enumValueFilter = undefined, }) {\n    const filteredSchema = mapSchema(schema, {\n        [MapperKind.QUERY]: (type) => filterRootFields(type, 'Query', rootFieldFilter, argumentFilter),\n        [MapperKind.MUTATION]: (type) => filterRootFields(type, 'Mutation', rootFieldFilter, argumentFilter),\n        [MapperKind.SUBSCRIPTION]: (type) => filterRootFields(type, 'Subscription', rootFieldFilter, argumentFilter),\n        [MapperKind.OBJECT_TYPE]: (type) => typeFilter(type.name, type)\n            ? filterElementFields(GraphQLObjectType, type, objectFieldFilter || fieldFilter, argumentFilter)\n            : null,\n        [MapperKind.INTERFACE_TYPE]: (type) => typeFilter(type.name, type)\n            ? filterElementFields(GraphQLInterfaceType, type, interfaceFieldFilter || fieldFilter, argumentFilter)\n            : null,\n        [MapperKind.INPUT_OBJECT_TYPE]: (type) => typeFilter(type.name, type)\n            ? filterElementFields(GraphQLInputObjectType, type, inputObjectFieldFilter || fieldFilter)\n            : null,\n        [MapperKind.UNION_TYPE]: (type) => typeFilter(type.name, type) ? undefined : null,\n        [MapperKind.ENUM_TYPE]: (type) => typeFilter(type.name, type) ? undefined : null,\n        [MapperKind.SCALAR_TYPE]: (type) => typeFilter(type.name, type) ? undefined : null,\n        [MapperKind.DIRECTIVE]: directive => directiveFilter && !directiveFilter(directive.name, directive) ? null : undefined,\n        [MapperKind.ENUM_VALUE]: (valueConfig, typeName, _schema, externalValue) => enumValueFilter && !enumValueFilter(typeName, externalValue, valueConfig) ? null : undefined,\n    });\n    return filteredSchema;\n}\nfunction filterRootFields(type, operation, rootFieldFilter, argumentFilter) {\n    if (rootFieldFilter || argumentFilter) {\n        const config = type.toConfig();\n        for (const fieldName in config.fields) {\n            const field = config.fields[fieldName];\n            if (rootFieldFilter && !rootFieldFilter(operation, fieldName, config.fields[fieldName])) {\n                delete config.fields[fieldName];\n            }\n            else if (argumentFilter && field.args) {\n                for (const argName in field.args) {\n                    if (!argumentFilter(type.name, fieldName, argName, field.args[argName])) {\n                        delete field.args[argName];\n                    }\n                }\n            }\n        }\n        return new GraphQLObjectType(config);\n    }\n    return type;\n}\nfunction filterElementFields(ElementConstructor, type, fieldFilter, argumentFilter) {\n    if (fieldFilter || argumentFilter) {\n        const config = type.toConfig();\n        for (const fieldName in config.fields) {\n            const field = config.fields[fieldName];\n            if (fieldFilter && !fieldFilter(type.name, fieldName, config.fields[fieldName])) {\n                delete config.fields[fieldName];\n            }\n            else if (argumentFilter && 'args' in field) {\n                for (const argName in field.args) {\n                    if (!argumentFilter(type.name, fieldName, argName, field.args[argName])) {\n                        delete field.args[argName];\n                    }\n                }\n            }\n        }\n        return new ElementConstructor(config);\n    }\n}\n", "export var MapperKind;\n(function (MapperKind) {\n    MapperKind[\"TYPE\"] = \"MapperKind.TYPE\";\n    MapperKind[\"SCALAR_TYPE\"] = \"MapperKind.SCALAR_TYPE\";\n    MapperKind[\"ENUM_TYPE\"] = \"MapperKind.ENUM_TYPE\";\n    MapperKind[\"COMPOSITE_TYPE\"] = \"MapperKind.COMPOSITE_TYPE\";\n    MapperKind[\"OBJECT_TYPE\"] = \"MapperKind.OBJECT_TYPE\";\n    MapperKind[\"INPUT_OBJECT_TYPE\"] = \"MapperKind.INPUT_OBJECT_TYPE\";\n    MapperKind[\"ABSTRACT_TYPE\"] = \"MapperKind.ABSTRACT_TYPE\";\n    MapperKind[\"UNION_TYPE\"] = \"MapperKind.UNION_TYPE\";\n    MapperKind[\"INTERFACE_TYPE\"] = \"MapperKind.INTERFACE_TYPE\";\n    MapperKind[\"ROOT_OBJECT\"] = \"MapperKind.ROOT_OBJECT\";\n    MapperKind[\"QUERY\"] = \"MapperKind.QUERY\";\n    MapperKind[\"MUTATION\"] = \"MapperKind.MUTATION\";\n    MapperKind[\"SUBSCRIPTION\"] = \"MapperKind.SUBSCRIPTION\";\n    MapperKind[\"DIRECTIVE\"] = \"MapperKind.DIRECTIVE\";\n    MapperKind[\"FIELD\"] = \"MapperKind.FIELD\";\n    MapperKind[\"COMPOSITE_FIELD\"] = \"MapperKind.COMPOSITE_FIELD\";\n    MapperKind[\"OBJECT_FIELD\"] = \"MapperKind.OBJECT_FIELD\";\n    MapperKind[\"ROOT_FIELD\"] = \"MapperKind.ROOT_FIELD\";\n    MapperKind[\"QUERY_ROOT_FIELD\"] = \"MapperKind.QUERY_ROOT_FIELD\";\n    MapperKind[\"MUTATION_ROOT_FIELD\"] = \"MapperKind.MUTATION_ROOT_FIELD\";\n    MapperKind[\"SUBSCRIPTION_ROOT_FIELD\"] = \"MapperKind.SUBSCRIPTION_ROOT_FIELD\";\n    MapperKind[\"INTERFACE_FIELD\"] = \"MapperKind.INTERFACE_FIELD\";\n    MapperKind[\"INPUT_OBJECT_FIELD\"] = \"MapperKind.INPUT_OBJECT_FIELD\";\n    MapperKind[\"ARGUMENT\"] = \"MapperKind.ARGUMENT\";\n    MapperKind[\"ENUM_VALUE\"] = \"MapperKind.ENUM_VALUE\";\n})(MapperKind || (MapperKind = {}));\n", "import { GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLSchema, isEnumType, isInputObjectType, isInterfaceType, isLeafType, isListType, isNamedType, isNonNullType, isObjectType, isScalarType, isUnionType, Kind, } from 'graphql';\nimport { getObjectTypeFromTypeMap } from './getObjectTypeFromTypeMap.js';\nimport { MapperKind, } from './Interfaces.js';\nimport { rewireTypes } from './rewire.js';\nimport { parseInputValue, serializeInputValue } from './transformInputValue.js';\nexport function mapSchema(schema, schemaMapper = {}) {\n    const newTypeMap = mapArguments(mapFields(mapTypes(mapDefaultValues(mapEnumValues(mapTypes(mapDefaultValues(schema.getTypeMap(), schema, serializeInputValue), schema, schemaMapper, type => isLeafType(type)), schema, schemaMapper), schema, parseInputValue), schema, schemaMapper, type => !isLeafType(type)), schema, schemaMapper), schema, schemaMapper);\n    const originalDirectives = schema.getDirectives();\n    const newDirectives = mapDirectives(originalDirectives, schema, schemaMapper);\n    const { typeMap, directives } = rewireTypes(newTypeMap, newDirectives);\n    return new GraphQLSchema({\n        ...schema.toConfig(),\n        query: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getQueryType())),\n        mutation: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getMutationType())),\n        subscription: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getSubscriptionType())),\n        types: Object.values(typeMap),\n        directives,\n    });\n}\nfunction mapTypes(originalTypeMap, schema, schemaMapper, testFn = () => true) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (originalType == null || !testFn(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const typeMapper = getTypeMapper(schema, schemaMapper, typeName);\n            if (typeMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const maybeNewType = typeMapper(originalType, schema);\n            if (maybeNewType === undefined) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            newTypeMap[typeName] = maybeNewType;\n        }\n    }\n    return newTypeMap;\n}\nfunction mapEnumValues(originalTypeMap, schema, schemaMapper) {\n    const enumValueMapper = getEnumValueMapper(schemaMapper);\n    if (!enumValueMapper) {\n        return originalTypeMap;\n    }\n    return mapTypes(originalTypeMap, schema, {\n        [MapperKind.ENUM_TYPE]: type => {\n            const config = type.toConfig();\n            const originalEnumValueConfigMap = config.values;\n            const newEnumValueConfigMap = {};\n            for (const externalValue in originalEnumValueConfigMap) {\n                const originalEnumValueConfig = originalEnumValueConfigMap[externalValue];\n                const mappedEnumValue = enumValueMapper(originalEnumValueConfig, type.name, schema, externalValue);\n                if (mappedEnumValue === undefined) {\n                    newEnumValueConfigMap[externalValue] = originalEnumValueConfig;\n                }\n                else if (Array.isArray(mappedEnumValue)) {\n                    const [newExternalValue, newEnumValueConfig] = mappedEnumValue;\n                    newEnumValueConfigMap[newExternalValue] =\n                        newEnumValueConfig === undefined ? originalEnumValueConfig : newEnumValueConfig;\n                }\n                else if (mappedEnumValue !== null) {\n                    newEnumValueConfigMap[externalValue] = mappedEnumValue;\n                }\n            }\n            return correctASTNodes(new GraphQLEnumType({\n                ...config,\n                values: newEnumValueConfigMap,\n            }));\n        },\n    }, type => isEnumType(type));\n}\nfunction mapDefaultValues(originalTypeMap, schema, fn) {\n    const newTypeMap = mapArguments(originalTypeMap, schema, {\n        [MapperKind.ARGUMENT]: argumentConfig => {\n            if (argumentConfig.defaultValue === undefined) {\n                return argumentConfig;\n            }\n            const maybeNewType = getNewType(originalTypeMap, argumentConfig.type);\n            if (maybeNewType != null) {\n                return {\n                    ...argumentConfig,\n                    defaultValue: fn(maybeNewType, argumentConfig.defaultValue),\n                };\n            }\n        },\n    });\n    return mapFields(newTypeMap, schema, {\n        [MapperKind.INPUT_OBJECT_FIELD]: inputFieldConfig => {\n            if (inputFieldConfig.defaultValue === undefined) {\n                return inputFieldConfig;\n            }\n            const maybeNewType = getNewType(newTypeMap, inputFieldConfig.type);\n            if (maybeNewType != null) {\n                return {\n                    ...inputFieldConfig,\n                    defaultValue: fn(maybeNewType, inputFieldConfig.defaultValue),\n                };\n            }\n        },\n    });\n}\nfunction getNewType(newTypeMap, type) {\n    if (isListType(type)) {\n        const newType = getNewType(newTypeMap, type.ofType);\n        return newType != null ? new GraphQLList(newType) : null;\n    }\n    else if (isNonNullType(type)) {\n        const newType = getNewType(newTypeMap, type.ofType);\n        return newType != null ? new GraphQLNonNull(newType) : null;\n    }\n    else if (isNamedType(type)) {\n        const newType = newTypeMap[type.name];\n        return newType != null ? newType : null;\n    }\n    return null;\n}\nfunction mapFields(originalTypeMap, schema, schemaMapper) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (!isObjectType(originalType) &&\n                !isInterfaceType(originalType) &&\n                !isInputObjectType(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const fieldMapper = getFieldMapper(schema, schemaMapper, typeName);\n            if (fieldMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const config = originalType.toConfig();\n            const originalFieldConfigMap = config.fields;\n            const newFieldConfigMap = {};\n            for (const fieldName in originalFieldConfigMap) {\n                const originalFieldConfig = originalFieldConfigMap[fieldName];\n                const mappedField = fieldMapper(originalFieldConfig, fieldName, typeName, schema);\n                if (mappedField === undefined) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                }\n                else if (Array.isArray(mappedField)) {\n                    const [newFieldName, newFieldConfig] = mappedField;\n                    if (newFieldConfig.astNode != null) {\n                        newFieldConfig.astNode = {\n                            ...newFieldConfig.astNode,\n                            name: {\n                                ...newFieldConfig.astNode.name,\n                                value: newFieldName,\n                            },\n                        };\n                    }\n                    newFieldConfigMap[newFieldName] =\n                        newFieldConfig === undefined ? originalFieldConfig : newFieldConfig;\n                }\n                else if (mappedField !== null) {\n                    newFieldConfigMap[fieldName] = mappedField;\n                }\n            }\n            if (isObjectType(originalType)) {\n                newTypeMap[typeName] = correctASTNodes(new GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n            else if (isInterfaceType(originalType)) {\n                newTypeMap[typeName] = correctASTNodes(new GraphQLInterfaceType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n            else {\n                newTypeMap[typeName] = correctASTNodes(new GraphQLInputObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        }\n    }\n    return newTypeMap;\n}\nfunction mapArguments(originalTypeMap, schema, schemaMapper) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (!isObjectType(originalType) && !isInterfaceType(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const argumentMapper = getArgumentMapper(schemaMapper);\n            if (argumentMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const config = originalType.toConfig();\n            const originalFieldConfigMap = config.fields;\n            const newFieldConfigMap = {};\n            for (const fieldName in originalFieldConfigMap) {\n                const originalFieldConfig = originalFieldConfigMap[fieldName];\n                const originalArgumentConfigMap = originalFieldConfig.args;\n                if (originalArgumentConfigMap == null) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                    continue;\n                }\n                const argumentNames = Object.keys(originalArgumentConfigMap);\n                if (!argumentNames.length) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                    continue;\n                }\n                const newArgumentConfigMap = {};\n                for (const argumentName of argumentNames) {\n                    const originalArgumentConfig = originalArgumentConfigMap[argumentName];\n                    const mappedArgument = argumentMapper(originalArgumentConfig, fieldName, typeName, schema);\n                    if (mappedArgument === undefined) {\n                        newArgumentConfigMap[argumentName] = originalArgumentConfig;\n                    }\n                    else if (Array.isArray(mappedArgument)) {\n                        const [newArgumentName, newArgumentConfig] = mappedArgument;\n                        newArgumentConfigMap[newArgumentName] = newArgumentConfig;\n                    }\n                    else if (mappedArgument !== null) {\n                        newArgumentConfigMap[argumentName] = mappedArgument;\n                    }\n                }\n                newFieldConfigMap[fieldName] = {\n                    ...originalFieldConfig,\n                    args: newArgumentConfigMap,\n                };\n            }\n            if (isObjectType(originalType)) {\n                newTypeMap[typeName] = new GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n            else if (isInterfaceType(originalType)) {\n                newTypeMap[typeName] = new GraphQLInterfaceType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n            else {\n                newTypeMap[typeName] = new GraphQLInputObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n        }\n    }\n    return newTypeMap;\n}\nfunction mapDirectives(originalDirectives, schema, schemaMapper) {\n    const directiveMapper = getDirectiveMapper(schemaMapper);\n    if (directiveMapper == null) {\n        return originalDirectives.slice();\n    }\n    const newDirectives = [];\n    for (const directive of originalDirectives) {\n        const mappedDirective = directiveMapper(directive, schema);\n        if (mappedDirective === undefined) {\n            newDirectives.push(directive);\n        }\n        else if (mappedDirective !== null) {\n            newDirectives.push(mappedDirective);\n        }\n    }\n    return newDirectives;\n}\nfunction getTypeSpecifiers(schema, typeName) {\n    const type = schema.getType(typeName);\n    const specifiers = [MapperKind.TYPE];\n    if (isObjectType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.OBJECT_TYPE);\n        if (typeName === schema.getQueryType()?.name) {\n            specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.QUERY);\n        }\n        else if (typeName === schema.getMutationType()?.name) {\n            specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.MUTATION);\n        }\n        else if (typeName === schema.getSubscriptionType()?.name) {\n            specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.SUBSCRIPTION);\n        }\n    }\n    else if (isInputObjectType(type)) {\n        specifiers.push(MapperKind.INPUT_OBJECT_TYPE);\n    }\n    else if (isInterfaceType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.ABSTRACT_TYPE, MapperKind.INTERFACE_TYPE);\n    }\n    else if (isUnionType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.ABSTRACT_TYPE, MapperKind.UNION_TYPE);\n    }\n    else if (isEnumType(type)) {\n        specifiers.push(MapperKind.ENUM_TYPE);\n    }\n    else if (isScalarType(type)) {\n        specifiers.push(MapperKind.SCALAR_TYPE);\n    }\n    return specifiers;\n}\nfunction getTypeMapper(schema, schemaMapper, typeName) {\n    const specifiers = getTypeSpecifiers(schema, typeName);\n    let typeMapper;\n    const stack = [...specifiers];\n    while (!typeMapper && stack.length > 0) {\n        // It is safe to use the ! operator here as we check the length.\n        const next = stack.pop();\n        typeMapper = schemaMapper[next];\n    }\n    return typeMapper != null ? typeMapper : null;\n}\nfunction getFieldSpecifiers(schema, typeName) {\n    const type = schema.getType(typeName);\n    const specifiers = [MapperKind.FIELD];\n    if (isObjectType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_FIELD, MapperKind.OBJECT_FIELD);\n        if (typeName === schema.getQueryType()?.name) {\n            specifiers.push(MapperKind.ROOT_FIELD, MapperKind.QUERY_ROOT_FIELD);\n        }\n        else if (typeName === schema.getMutationType()?.name) {\n            specifiers.push(MapperKind.ROOT_FIELD, MapperKind.MUTATION_ROOT_FIELD);\n        }\n        else if (typeName === schema.getSubscriptionType()?.name) {\n            specifiers.push(MapperKind.ROOT_FIELD, MapperKind.SUBSCRIPTION_ROOT_FIELD);\n        }\n    }\n    else if (isInterfaceType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_FIELD, MapperKind.INTERFACE_FIELD);\n    }\n    else if (isInputObjectType(type)) {\n        specifiers.push(MapperKind.INPUT_OBJECT_FIELD);\n    }\n    return specifiers;\n}\nfunction getFieldMapper(schema, schemaMapper, typeName) {\n    const specifiers = getFieldSpecifiers(schema, typeName);\n    let fieldMapper;\n    const stack = [...specifiers];\n    while (!fieldMapper && stack.length > 0) {\n        // It is safe to use the ! operator here as we check the length.\n        const next = stack.pop();\n        // TODO: fix this as unknown cast\n        fieldMapper = schemaMapper[next];\n    }\n    return fieldMapper ?? null;\n}\nfunction getArgumentMapper(schemaMapper) {\n    const argumentMapper = schemaMapper[MapperKind.ARGUMENT];\n    return argumentMapper != null ? argumentMapper : null;\n}\nfunction getDirectiveMapper(schemaMapper) {\n    const directiveMapper = schemaMapper[MapperKind.DIRECTIVE];\n    return directiveMapper != null ? directiveMapper : null;\n}\nfunction getEnumValueMapper(schemaMapper) {\n    const enumValueMapper = schemaMapper[MapperKind.ENUM_VALUE];\n    return enumValueMapper != null ? enumValueMapper : null;\n}\nexport function correctASTNodes(type) {\n    if (isObjectType(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: Kind.OBJECT_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: Kind.OBJECT_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new GraphQLObjectType(config);\n    }\n    else if (isInterfaceType(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: Kind.INTERFACE_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: Kind.INTERFACE_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new GraphQLInterfaceType(config);\n    }\n    else if (isInputObjectType(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: Kind.INPUT_OBJECT_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new GraphQLInputObjectType(config);\n    }\n    else if (isEnumType(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const values = [];\n            for (const enumKey in config.values) {\n                const enumValueConfig = config.values[enumKey];\n                if (enumValueConfig.astNode != null) {\n                    values.push(enumValueConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                values,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                values: undefined,\n            }));\n        }\n        return new GraphQLEnumType(config);\n    }\n    else {\n        return type;\n    }\n}\n", "import { isObjectType } from 'graphql';\nexport function getObjectTypeFromTypeMap(typeMap, type) {\n    if (type) {\n        const maybeObjectType = typeMap[type.name];\n        if (isObjectType(maybeObjectType)) {\n            return maybeObjectType;\n        }\n    }\n}\n", "import { GraphQLDirective, GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLScalarType, GraphQLUnionType, isEnumType, isInputObjectType, isInterfaceType, isListType, isNamedType, isNonNullType, isObjectType, isScalarType, isSpecifiedDirective, isSpecifiedScalarType, isUnionType, } from 'graphql';\nimport { getBuiltInForStub, isNamedStub } from './stub.js';\nexport function rewireTypes(originalTypeMap, directives) {\n    const referenceTypeMap = Object.create(null);\n    for (const typeName in originalTypeMap) {\n        referenceTypeMap[typeName] = originalTypeMap[typeName];\n    }\n    const newTypeMap = Object.create(null);\n    for (const typeName in referenceTypeMap) {\n        const namedType = referenceTypeMap[typeName];\n        if (namedType == null || typeName.startsWith('__')) {\n            continue;\n        }\n        const newName = namedType.name;\n        if (newName.startsWith('__')) {\n            continue;\n        }\n        if (newTypeMap[newName] != null) {\n            console.warn(`Duplicate schema type name ${newName} found; keeping the existing one found in the schema`);\n            continue;\n        }\n        newTypeMap[newName] = namedType;\n    }\n    for (const typeName in newTypeMap) {\n        newTypeMap[typeName] = rewireNamedType(newTypeMap[typeName]);\n    }\n    const newDirectives = directives.map(directive => rewireDirective(directive));\n    return {\n        typeMap: newTypeMap,\n        directives: newDirectives,\n    };\n    function rewireDirective(directive) {\n        if (isSpecifiedDirective(directive)) {\n            return directive;\n        }\n        const directiveConfig = directive.toConfig();\n        directiveConfig.args = rewireArgs(directiveConfig.args);\n        return new GraphQLDirective(directiveConfig);\n    }\n    function rewireArgs(args) {\n        const rewiredArgs = {};\n        for (const argName in args) {\n            const arg = args[argName];\n            const rewiredArgType = rewireType(arg.type);\n            if (rewiredArgType != null) {\n                arg.type = rewiredArgType;\n                rewiredArgs[argName] = arg;\n            }\n        }\n        return rewiredArgs;\n    }\n    function rewireNamedType(type) {\n        if (isObjectType(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireFields(config.fields),\n                interfaces: () => rewireNamedTypes(config.interfaces),\n            };\n            return new GraphQLObjectType(newConfig);\n        }\n        else if (isInterfaceType(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireFields(config.fields),\n            };\n            if ('interfaces' in newConfig) {\n                newConfig.interfaces = () => rewireNamedTypes(config.interfaces);\n            }\n            return new GraphQLInterfaceType(newConfig);\n        }\n        else if (isUnionType(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                types: () => rewireNamedTypes(config.types),\n            };\n            return new GraphQLUnionType(newConfig);\n        }\n        else if (isInputObjectType(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireInputFields(config.fields),\n            };\n            return new GraphQLInputObjectType(newConfig);\n        }\n        else if (isEnumType(type)) {\n            const enumConfig = type.toConfig();\n            return new GraphQLEnumType(enumConfig);\n        }\n        else if (isScalarType(type)) {\n            if (isSpecifiedScalarType(type)) {\n                return type;\n            }\n            const scalarConfig = type.toConfig();\n            return new GraphQLScalarType(scalarConfig);\n        }\n        throw new Error(`Unexpected schema type: ${type}`);\n    }\n    function rewireFields(fields) {\n        const rewiredFields = {};\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            const rewiredFieldType = rewireType(field.type);\n            if (rewiredFieldType != null && field.args) {\n                field.type = rewiredFieldType;\n                field.args = rewireArgs(field.args);\n                rewiredFields[fieldName] = field;\n            }\n        }\n        return rewiredFields;\n    }\n    function rewireInputFields(fields) {\n        const rewiredFields = {};\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            const rewiredFieldType = rewireType(field.type);\n            if (rewiredFieldType != null) {\n                field.type = rewiredFieldType;\n                rewiredFields[fieldName] = field;\n            }\n        }\n        return rewiredFields;\n    }\n    function rewireNamedTypes(namedTypes) {\n        const rewiredTypes = [];\n        for (const namedType of namedTypes) {\n            const rewiredType = rewireType(namedType);\n            if (rewiredType != null) {\n                rewiredTypes.push(rewiredType);\n            }\n        }\n        return rewiredTypes;\n    }\n    function rewireType(type) {\n        if (isListType(type)) {\n            const rewiredType = rewireType(type.ofType);\n            return rewiredType != null ? new GraphQLList(rewiredType) : null;\n        }\n        else if (isNonNullType(type)) {\n            const rewiredType = rewireType(type.ofType);\n            return rewiredType != null ? new GraphQLNonNull(rewiredType) : null;\n        }\n        else if (isNamedType(type)) {\n            let rewiredType = referenceTypeMap[type.name];\n            if (rewiredType === undefined) {\n                rewiredType = isNamedStub(type) ? getBuiltInForStub(type) : rewireNamedType(type);\n                newTypeMap[rewiredType.name] = referenceTypeMap[type.name] = rewiredType;\n            }\n            return rewiredType != null ? newTypeMap[rewiredType.name] : null;\n        }\n        return null;\n    }\n}\n", "import { GraphQLBoolean, GraphQLFloat, GraphQLID, GraphQLInputObjectType, GraphQLInt, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLString, Kind, } from 'graphql';\nexport function createNamedStub(name, type) {\n    let constructor;\n    if (type === 'object') {\n        constructor = GraphQLObjectType;\n    }\n    else if (type === 'interface') {\n        constructor = GraphQLInterfaceType;\n    }\n    else {\n        constructor = GraphQLInputObjectType;\n    }\n    return new constructor({\n        name,\n        fields: {\n            _fake: {\n                type: GraphQLString,\n            },\n        },\n    });\n}\nexport function createStub(node, type) {\n    switch (node.kind) {\n        case Kind.LIST_TYPE:\n            return new GraphQLList(createStub(node.type, type));\n        case Kind.NON_NULL_TYPE:\n            return new GraphQLNonNull(createStub(node.type, type));\n        default:\n            if (type === 'output') {\n                return createNamedStub(node.name.value, 'object');\n            }\n            return createNamedStub(node.name.value, 'input');\n    }\n}\nexport function isNamedStub(type) {\n    if ('getFields' in type) {\n        const fields = type.getFields();\n        // eslint-disable-next-line no-unreachable-loop\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            return field.name === '_fake';\n        }\n    }\n    return false;\n}\nexport function getBuiltInForStub(type) {\n    switch (type.name) {\n        case GraphQLInt.name:\n            return GraphQLInt;\n        case GraphQLFloat.name:\n            return GraphQLFloat;\n        case GraphQLString.name:\n            return GraphQLString;\n        case GraphQLBoolean.name:\n            return GraphQLBoolean;\n        case GraphQLID.name:\n            return GraphQLID;\n        default:\n            return type;\n    }\n}\n", "import { getNullableType, isInputObjectType, isLeafType, isListType, } from 'graphql';\nimport { asArray } from './helpers.js';\nexport function transformInputValue(type, value, inputLeafValueTransformer = null, inputObjectValueTransformer = null) {\n    if (value == null) {\n        return value;\n    }\n    const nullableType = getNullableType(type);\n    if (isLeafType(nullableType)) {\n        return inputLeafValueTransformer != null\n            ? inputLeafValueTransformer(nullableType, value)\n            : value;\n    }\n    else if (isListType(nullableType)) {\n        return asArray(value).map((listMember) => transformInputValue(nullableType.ofType, listMember, inputLeafValueTransformer, inputObjectValueTransformer));\n    }\n    else if (isInputObjectType(nullableType)) {\n        const fields = nullableType.getFields();\n        const newValue = {};\n        for (const key in value) {\n            const field = fields[key];\n            if (field != null) {\n                newValue[key] = transformInputValue(field.type, value[key], inputLeafValueTransformer, inputObjectValueTransformer);\n            }\n        }\n        return inputObjectValueTransformer != null\n            ? inputObjectValueTransformer(nullableType, newValue)\n            : newValue;\n    }\n    // unreachable, no other possible return value\n}\nexport function serializeInputValue(type, value) {\n    return transformInputValue(type, value, (t, v) => {\n        try {\n            return t.serialize(v);\n        }\n        catch {\n            return v;\n        }\n    });\n}\nexport function parseInputValue(type, value) {\n    return transformInputValue(type, value, (t, v) => {\n        try {\n            return t.parseValue(v);\n        }\n        catch {\n            return v;\n        }\n    });\n}\nexport function parseInputValueLiteral(type, value) {\n    return transformInputValue(type, value, (t, v) => t.parseLiteral(v, {}));\n}\n", "import { Graph<PERSON><PERSON><PERSON>, GraphQL<PERSON>on<PERSON>ull, isInputObjectType, isInterfaceType, isLeafType, isListType, isNamedType, isNonNullType, isObjectType, isUnionType, } from 'graphql';\n// Update any references to named schema types that disagree with the named\n// types found in schema.getTypeMap().\n//\n// healSchema and its callers (visitSchema/visitSchemaDirectives) all modify the schema in place.\n// Therefore, private variables (such as the stored implementation map and the proper root types)\n// are not updated.\n//\n// If this causes issues, the schema could be more aggressively healed as follows:\n//\n// healSchema(schema);\n// const config = schema.toConfig()\n// const healedSchema = new GraphQLSchema({\n//   ...config,\n//   query: schema.getType('<desired new root query type name>'),\n//   mutation: schema.getType('<desired new root mutation type name>'),\n//   subscription: schema.getType('<desired new root subscription type name>'),\n// });\n//\n// One can then also -- if necessary --  assign the correct private variables to the initial schema\n// as follows:\n// Object.assign(schema, healedSchema);\n//\n// These steps are not taken automatically to preserve backwards compatibility with graphql-tools v4.\n// See https://github.com/ardatan/graphql-tools/issues/1462\n//\n// They were briefly taken in v5, but can now be phased out as they were only required when other\n// areas of the codebase were using healSchema and visitSchema more extensively.\n//\nexport function healSchema(schema) {\n    healTypes(schema.getTypeMap(), schema.getDirectives());\n    return schema;\n}\nexport function healTypes(originalTypeMap, directives) {\n    const actualNamedTypeMap = Object.create(null);\n    // If any of the .name properties of the GraphQLNamedType objects in\n    // schema.getTypeMap() have changed, the keys of the type map need to\n    // be updated accordingly.\n    for (const typeName in originalTypeMap) {\n        const namedType = originalTypeMap[typeName];\n        if (namedType == null || typeName.startsWith('__')) {\n            continue;\n        }\n        const actualName = namedType.name;\n        if (actualName.startsWith('__')) {\n            continue;\n        }\n        if (actualNamedTypeMap[actualName] != null) {\n            console.warn(`Duplicate schema type name ${actualName} found; keeping the existing one found in the schema`);\n            continue;\n        }\n        actualNamedTypeMap[actualName] = namedType;\n        // Note: we are deliberately leaving namedType in the schema by its\n        // original name (which might be different from actualName), so that\n        // references by that name can be healed.\n    }\n    // Now add back every named type by its actual name.\n    for (const typeName in actualNamedTypeMap) {\n        const namedType = actualNamedTypeMap[typeName];\n        originalTypeMap[typeName] = namedType;\n    }\n    // Directive declaration argument types can refer to named types.\n    for (const decl of directives) {\n        decl.args = decl.args.filter(arg => {\n            arg.type = healType(arg.type);\n            return arg.type !== null;\n        });\n    }\n    for (const typeName in originalTypeMap) {\n        const namedType = originalTypeMap[typeName];\n        // Heal all named types, except for dangling references, kept only to redirect.\n        if (!typeName.startsWith('__') && typeName in actualNamedTypeMap) {\n            if (namedType != null) {\n                healNamedType(namedType);\n            }\n        }\n    }\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__') && !(typeName in actualNamedTypeMap)) {\n            delete originalTypeMap[typeName];\n        }\n    }\n    function healNamedType(type) {\n        if (isObjectType(type)) {\n            healFields(type);\n            healInterfaces(type);\n            return;\n        }\n        else if (isInterfaceType(type)) {\n            healFields(type);\n            if ('getInterfaces' in type) {\n                healInterfaces(type);\n            }\n            return;\n        }\n        else if (isUnionType(type)) {\n            healUnderlyingTypes(type);\n            return;\n        }\n        else if (isInputObjectType(type)) {\n            healInputFields(type);\n            return;\n        }\n        else if (isLeafType(type)) {\n            return;\n        }\n        throw new Error(`Unexpected schema type: ${type}`);\n    }\n    function healFields(type) {\n        const fieldMap = type.getFields();\n        for (const [key, field] of Object.entries(fieldMap)) {\n            field.args\n                .map(arg => {\n                arg.type = healType(arg.type);\n                return arg.type === null ? null : arg;\n            })\n                .filter(Boolean);\n            field.type = healType(field.type);\n            if (field.type === null) {\n                delete fieldMap[key];\n            }\n        }\n    }\n    function healInterfaces(type) {\n        if ('getInterfaces' in type) {\n            const interfaces = type.getInterfaces();\n            interfaces.push(...interfaces\n                .splice(0)\n                .map(iface => healType(iface))\n                .filter(Boolean));\n        }\n    }\n    function healInputFields(type) {\n        const fieldMap = type.getFields();\n        for (const [key, field] of Object.entries(fieldMap)) {\n            field.type = healType(field.type);\n            if (field.type === null) {\n                delete fieldMap[key];\n            }\n        }\n    }\n    function healUnderlyingTypes(type) {\n        const types = type.getTypes();\n        types.push(...types\n            .splice(0)\n            .map(t => healType(t))\n            .filter(Boolean));\n    }\n    function healType(type) {\n        // Unwrap the two known wrapper types\n        if (isListType(type)) {\n            const healedType = healType(type.ofType);\n            return healedType != null ? new GraphQLList(healedType) : null;\n        }\n        else if (isNonNullType(type)) {\n            const healedType = healType(type.ofType);\n            return healedType != null ? new GraphQLNonNull(healedType) : null;\n        }\n        else if (isNamedType(type)) {\n            // If a type annotation on a field or an argument or a union member is\n            // any `GraphQLNamedType` with a `name`, then it must end up identical\n            // to `schema.getType(name)`, since `schema.getTypeMap()` is the source\n            // of truth for all named schema types.\n            // Note that new types can still be simply added by adding a field, as\n            // the official type will be undefined, not null.\n            const officialType = originalTypeMap[type.name];\n            if (officialType && type !== officialType) {\n                return officialType;\n            }\n        }\n        return type;\n    }\n}\n", "import { GraphQLScalarType, isEnumType, isInterfaceType, isObjectType, isScalarType, isSpecifiedScalarType, isUnionType, } from 'graphql';\nexport function getResolversFromSchema(schema, \n// Include default merged resolvers\nincludeDefaultMergedResolver) {\n    const resolvers = Object.create(null);\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        if (!typeName.startsWith('__')) {\n            const type = typeMap[typeName];\n            if (isScalarType(type)) {\n                if (!isSpecifiedScalarType(type)) {\n                    const config = type.toConfig();\n                    delete config.astNode; // avoid AST duplication elsewhere\n                    resolvers[typeName] = new GraphQLScalarType(config);\n                }\n            }\n            else if (isEnumType(type)) {\n                resolvers[typeName] = {};\n                const values = type.getValues();\n                for (const value of values) {\n                    resolvers[typeName][value.name] = value.value;\n                }\n            }\n            else if (isInterfaceType(type)) {\n                if (type.resolveType != null) {\n                    resolvers[typeName] = {\n                        __resolveType: type.resolveType,\n                    };\n                }\n            }\n            else if (isUnionType(type)) {\n                if (type.resolveType != null) {\n                    resolvers[typeName] = {\n                        __resolveType: type.resolveType,\n                    };\n                }\n            }\n            else if (isObjectType(type)) {\n                resolvers[typeName] = {};\n                if (type.isTypeOf != null) {\n                    resolvers[typeName].__isTypeOf = type.isTypeOf;\n                }\n                const fields = type.getFields();\n                for (const fieldName in fields) {\n                    const field = fields[fieldName];\n                    if (field.subscribe != null) {\n                        resolvers[typeName][fieldName] = resolvers[typeName][fieldName] || {};\n                        resolvers[typeName][fieldName].subscribe = field.subscribe;\n                    }\n                    if (field.resolve != null && field.resolve?.name !== 'defaultFieldResolver') {\n                        switch (field.resolve?.name) {\n                            case 'defaultMergedResolver':\n                                if (!includeDefaultMergedResolver) {\n                                    continue;\n                                }\n                                break;\n                            case 'defaultFieldResolver':\n                                continue;\n                        }\n                        resolvers[typeName][fieldName] = resolvers[typeName][fieldName] || {};\n                        resolvers[typeName][fieldName].resolve = field.resolve;\n                    }\n                }\n            }\n        }\n    }\n    return resolvers;\n}\n", "import { getNamedType, isObjectType } from 'graphql';\nexport function forEachField(schema, fn) {\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        const type = typeMap[typeName];\n        // TODO: maybe have an option to include these?\n        if (!getNamedType(type).name.startsWith('__') && isObjectType(type)) {\n            const fields = type.getFields();\n            for (const fieldName in fields) {\n                const field = fields[fieldName];\n                fn(field, typeName, fieldName);\n            }\n        }\n    }\n}\n", "import { getNamedType, isInputObjectType, isObjectType } from 'graphql';\nexport function forEachDefaultValue(schema, fn) {\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        const type = typeMap[typeName];\n        if (!getNamedType(type).name.startsWith('__')) {\n            if (isObjectType(type)) {\n                const fields = type.getFields();\n                for (const fieldName in fields) {\n                    const field = fields[fieldName];\n                    for (const arg of field.args) {\n                        arg.defaultValue = fn(arg.type, arg.defaultValue);\n                    }\n                }\n            }\n            else if (isInputObjectType(type)) {\n                const fields = type.getFields();\n                for (const fieldName in fields) {\n                    const field = fields[fieldName];\n                    field.defaultValue = fn(field.type, field.defaultValue);\n                }\n            }\n        }\n    }\n}\n", "// addTypes uses toConfig to create a new schema with a new or replaced\n// type or directive. Rewiring is employed so that the replaced type can be\n// reconnected with the existing types.\n//\n// Rewiring is employed even for new types or directives as a convenience, so\n// that type references within the new type or directive do not have to be to\n// the identical objects within the original schema.\n//\n// In fact, the type references could even be stub types with entirely different\n// fields, as long as the type references share the same name as the desired\n// type within the original schema's type map.\n//\n// This makes it easy to perform simple schema operations (e.g. adding a new\n// type with a fiew fields removed from an existing type) that could normally be\n// performed by using toConfig directly, but is blocked if any intervening\n// more advanced schema operations have caused the types to be recreated via\n// rewiring.\n//\n// Type recreation happens, for example, with every use of mapSchema, as the\n// types are always rewired. If fields are selected and removed using\n// mapSchema, adding those fields to a new type can no longer be simply done\n// by toConfig, as the types are not the identical JavaScript objects, and\n// schema creation will fail with errors referencing multiple types with the\n// same names.\n//\n// enhanceSchema can fill this gap by adding an additional round of rewiring.\n//\nimport { GraphQLSchema, isDirective, isNamedType, } from 'graphql';\nimport { getObjectTypeFromTypeMap } from './getObjectTypeFromTypeMap.js';\nimport { rewireTypes } from './rewire.js';\nexport function addTypes(schema, newTypesOrDirectives) {\n    const config = schema.toConfig();\n    const originalTypeMap = {};\n    for (const type of config.types) {\n        originalTypeMap[type.name] = type;\n    }\n    const originalDirectiveMap = {};\n    for (const directive of config.directives) {\n        originalDirectiveMap[directive.name] = directive;\n    }\n    for (const newTypeOrDirective of newTypesOrDirectives) {\n        if (isNamedType(newTypeOrDirective)) {\n            originalTypeMap[newTypeOrDirective.name] = newTypeOrDirective;\n        }\n        else if (isDirective(newTypeOrDirective)) {\n            originalDirectiveMap[newTypeOrDirective.name] = newTypeOrDirective;\n        }\n    }\n    const { typeMap, directives } = rewireTypes(originalTypeMap, Object.values(originalDirectiveMap));\n    return new GraphQLSchema({\n        ...config,\n        query: getObjectTypeFromTypeMap(typeMap, schema.getQueryType()),\n        mutation: getObjectTypeFromTypeMap(typeMap, schema.getMutationType()),\n        subscription: getObjectTypeFromTypeMap(typeMap, schema.getSubscriptionType()),\n        types: Object.values(typeMap),\n        directives,\n    });\n}\n", "import { getNamedType, isEnumType, isInputObjectType, isInterfaceType, isObjectType, isScalarType, isSpecifiedScalarType, isUnionType, } from 'graphql';\nimport { getImplementingTypes } from './get-implementing-types.js';\nimport { MapperKind } from './Interfaces.js';\nimport { mapSchema } from './mapSchema.js';\nimport { getRootTypes } from './rootTypes.js';\n/**\n * Prunes the provided schema, removing unused and empty types\n * @param schema The schema to prune\n * @param options Additional options for removing unused types from the schema\n */\nexport function pruneSchema(schema, options = {}) {\n    const { skipEmptyCompositeTypePruning, skipEmptyUnionPruning, skipPruning, skipUnimplementedInterfacesPruning, skipUnusedTypesPruning, } = options;\n    let prunedTypes = []; // Pruned types during mapping\n    let prunedSchema = schema;\n    do {\n        let visited = visitSchema(prunedSchema);\n        // Custom pruning  was defined, so we need to pre-emptively revisit the schema accounting for this\n        if (skipPruning) {\n            const revisit = [];\n            for (const typeName in prunedSchema.getTypeMap()) {\n                if (typeName.startsWith('__')) {\n                    continue;\n                }\n                const type = prunedSchema.getType(typeName);\n                // if we want to skip pruning for this type, add it to the list of types to revisit\n                if (type && skipPruning(type)) {\n                    revisit.push(typeName);\n                }\n            }\n            visited = visitQueue(revisit, prunedSchema, visited); // visit again\n        }\n        prunedTypes = [];\n        prunedSchema = mapSchema(prunedSchema, {\n            [MapperKind.TYPE]: type => {\n                if (!visited.has(type.name) && !isSpecifiedScalarType(type)) {\n                    if (isUnionType(type) ||\n                        isInputObjectType(type) ||\n                        isInterfaceType(type) ||\n                        isObjectType(type) ||\n                        isScalarType(type)) {\n                        // skipUnusedTypesPruning: skip pruning unused types\n                        if (skipUnusedTypesPruning) {\n                            return type;\n                        }\n                        // skipEmptyUnionPruning: skip pruning empty unions\n                        if (isUnionType(type) &&\n                            skipEmptyUnionPruning &&\n                            !Object.keys(type.getTypes()).length) {\n                            return type;\n                        }\n                        if (isInputObjectType(type) || isInterfaceType(type) || isObjectType(type)) {\n                            // skipEmptyCompositeTypePruning: skip pruning object types or interfaces with no fields\n                            if (skipEmptyCompositeTypePruning && !Object.keys(type.getFields()).length) {\n                                return type;\n                            }\n                        }\n                        // skipUnimplementedInterfacesPruning: skip pruning interfaces that are not implemented by any other types\n                        if (isInterfaceType(type) && skipUnimplementedInterfacesPruning) {\n                            return type;\n                        }\n                    }\n                    prunedTypes.push(type.name);\n                    visited.delete(type.name);\n                    return null;\n                }\n                return type;\n            },\n        });\n    } while (prunedTypes.length); // Might have empty types and need to prune again\n    return prunedSchema;\n}\nfunction visitSchema(schema) {\n    const queue = []; // queue of nodes to visit\n    // Grab the root types and start there\n    for (const type of getRootTypes(schema)) {\n        queue.push(type.name);\n    }\n    return visitQueue(queue, schema);\n}\nfunction visitQueue(queue, schema, visited = new Set()) {\n    // Interfaces encountered that are field return types need to be revisited to add their implementations\n    const revisit = new Map();\n    // Navigate all types starting with pre-queued types (root types)\n    while (queue.length) {\n        const typeName = queue.pop();\n        // Skip types we already visited unless it is an interface type that needs revisiting\n        if (visited.has(typeName) && revisit[typeName] !== true) {\n            continue;\n        }\n        const type = schema.getType(typeName);\n        if (type) {\n            // Get types for union\n            if (isUnionType(type)) {\n                queue.push(...type.getTypes().map(type => type.name));\n            }\n            // If it is an interface and it is a returned type, grab all implementations so we can use proper __typename in fragments\n            if (isInterfaceType(type) && revisit[typeName] === true) {\n                queue.push(...getImplementingTypes(type.name, schema));\n                // No need to revisit this interface again\n                revisit[typeName] = false;\n            }\n            if (isEnumType(type)) {\n                // Visit enum values directives argument types\n                queue.push(...type.getValues().flatMap(value => getDirectivesArgumentsTypeNames(schema, value)));\n            }\n            // Visit interfaces this type is implementing if they haven't been visited yet\n            if ('getInterfaces' in type) {\n                // Only pushes to queue to visit but not return types\n                queue.push(...type.getInterfaces().map(iface => iface.name));\n            }\n            // If the type has fields visit those field types\n            if ('getFields' in type) {\n                const fields = type.getFields();\n                const entries = Object.entries(fields);\n                if (!entries.length) {\n                    continue;\n                }\n                for (const [, field] of entries) {\n                    if (isObjectType(type)) {\n                        // Visit arg types and arg directives arguments types\n                        queue.push(...field.args.flatMap(arg => {\n                            const typeNames = [getNamedType(arg.type).name];\n                            typeNames.push(...getDirectivesArgumentsTypeNames(schema, arg));\n                            return typeNames;\n                        }));\n                    }\n                    const namedType = getNamedType(field.type);\n                    queue.push(namedType.name);\n                    queue.push(...getDirectivesArgumentsTypeNames(schema, field));\n                    // Interfaces returned on fields need to be revisited to add their implementations\n                    if (isInterfaceType(namedType) && !(namedType.name in revisit)) {\n                        revisit[namedType.name] = true;\n                    }\n                }\n            }\n            queue.push(...getDirectivesArgumentsTypeNames(schema, type));\n            visited.add(typeName); // Mark as visited (and therefore it is used and should be kept)\n        }\n    }\n    return visited;\n}\nfunction getDirectivesArgumentsTypeNames(schema, directableObj) {\n    const argTypeNames = new Set();\n    if (directableObj.astNode?.directives) {\n        for (const directiveNode of directableObj.astNode.directives) {\n            const directive = schema.getDirective(directiveNode.name.value);\n            if (directive?.args) {\n                for (const arg of directive.args) {\n                    const argType = getNamedType(arg.type);\n                    argTypeNames.add(argType.name);\n                }\n            }\n        }\n    }\n    if (directableObj.extensions?.['directives']) {\n        for (const directiveName in directableObj.extensions['directives']) {\n            const directive = schema.getDirective(directiveName);\n            if (directive?.args) {\n                for (const arg of directive.args) {\n                    const argType = getNamedType(arg.type);\n                    argTypeNames.add(argType.name);\n                }\n            }\n        }\n    }\n    return [...argTypeNames];\n}\n", "import { isSome } from './helpers.js';\nexport function mergeDeep(sources, respectPrototype = false, respectArrays = false, respectArrayLength = false) {\n    let expectedLength;\n    let allArrays = true;\n    const areArraysInTheSameLength = sources.every(source => {\n        if (Array.isArray(source)) {\n            if (expectedLength === undefined) {\n                expectedLength = source.length;\n                return true;\n            }\n            else if (expectedLength === source.length) {\n                return true;\n            }\n        }\n        else {\n            allArrays = false;\n        }\n        return false;\n    });\n    if (respectArrayLength && areArraysInTheSameLength) {\n        return new Array(expectedLength).fill(null).map((_, index) => mergeDeep(sources.map(source => source[index]), respectPrototype, respectArrays, respectArrayLength));\n    }\n    if (allArrays) {\n        return sources.flat(1);\n    }\n    let output;\n    let firstObjectSource;\n    if (respectPrototype) {\n        firstObjectSource = sources.find(source => isObject(source));\n        if (output == null) {\n            output = {};\n        }\n        if (firstObjectSource) {\n            Object.setPrototypeOf(output, Object.create(Object.getPrototypeOf(firstObjectSource)));\n        }\n    }\n    for (const source of sources) {\n        if (isObject(source)) {\n            if (firstObjectSource) {\n                const outputPrototype = Object.getPrototypeOf(output);\n                const sourcePrototype = Object.getPrototypeOf(source);\n                if (sourcePrototype) {\n                    for (const key of Object.getOwnPropertyNames(sourcePrototype)) {\n                        const descriptor = Object.getOwnPropertyDescriptor(sourcePrototype, key);\n                        if (isSome(descriptor)) {\n                            Object.defineProperty(outputPrototype, key, descriptor);\n                        }\n                    }\n                }\n            }\n            for (const key in source) {\n                if (output == null) {\n                    output = {};\n                }\n                if (key in output) {\n                    output[key] = mergeDeep([output[key], source[key]], respectPrototype, respectArrays, respectArrayLength);\n                }\n                else {\n                    output[key] = source[key];\n                }\n            }\n        }\n        else if (Array.isArray(source)) {\n            if (!Array.isArray(output)) {\n                output = source;\n            }\n            else {\n                output = mergeDeep([output, source], respectPrototype, respectArrays, respectArrayLength);\n            }\n        }\n        else {\n            output = source;\n        }\n    }\n    return output;\n}\nfunction isObject(item) {\n    return item && typeof item === 'object' && !Array.isArray(item);\n}\n", "import { parse } from 'graphql';\nexport function parseSelectionSet(selectionSet, options) {\n    const query = parse(selectionSet, options).definitions[0];\n    return query.selectionSet;\n}\n", "import { GraphQLObjectType, } from 'graphql';\nimport { addTypes } from './addTypes.js';\nimport { MapperKind } from './Interfaces.js';\nimport { correctASTNodes, mapSchema } from './mapSchema.js';\nexport function appendObjectFields(schema, typeName, additionalFields) {\n    if (schema.getType(typeName) == null) {\n        return addTypes(schema, [\n            new GraphQLObjectType({\n                name: typeName,\n                fields: additionalFields,\n            }),\n        ]);\n    }\n    return mapSchema(schema, {\n        [MapperKind.OBJECT_TYPE]: type => {\n            if (type.name === typeName) {\n                const config = type.toConfig();\n                const originalFieldConfigMap = config.fields;\n                const newFieldConfigMap = {};\n                for (const fieldName in originalFieldConfigMap) {\n                    newFieldConfigMap[fieldName] = originalFieldConfigMap[fieldName];\n                }\n                for (const fieldName in additionalFields) {\n                    newFieldConfigMap[fieldName] = additionalFields[fieldName];\n                }\n                return correctASTNodes(new GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        },\n    });\n}\nexport function removeObjectFields(schema, typeName, testFn) {\n    const removedFields = {};\n    const newSchema = mapSchema(schema, {\n        [MapperKind.OBJECT_TYPE]: type => {\n            if (type.name === typeName) {\n                const config = type.toConfig();\n                const originalFieldConfigMap = config.fields;\n                const newFieldConfigMap = {};\n                for (const fieldName in originalFieldConfigMap) {\n                    const originalFieldConfig = originalFieldConfigMap[fieldName];\n                    if (testFn(fieldName, originalFieldConfig)) {\n                        removedFields[fieldName] = originalFieldConfig;\n                    }\n                    else {\n                        newFieldConfigMap[fieldName] = originalFieldConfig;\n                    }\n                }\n                return correctASTNodes(new GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        },\n    });\n    return [newSchema, removedFields];\n}\nexport function selectObjectFields(schema, typeName, testFn) {\n    const selectedFields = {};\n    mapSchema(schema, {\n        [MapperKind.OBJECT_TYPE]: type => {\n            if (type.name === typeName) {\n                const config = type.toConfig();\n                const originalFieldConfigMap = config.fields;\n                for (const fieldName in originalFieldConfigMap) {\n                    const originalFieldConfig = originalFieldConfigMap[fieldName];\n                    if (testFn(fieldName, originalFieldConfig)) {\n                        selectedFields[fieldName] = originalFieldConfig;\n                    }\n                }\n            }\n            return undefined;\n        },\n    });\n    return selectedFields;\n}\nexport function modifyObjectFields(schema, typeName, testFn, newFields) {\n    const removedFields = {};\n    const newSchema = mapSchema(schema, {\n        [MapperKind.OBJECT_TYPE]: type => {\n            if (type.name === typeName) {\n                const config = type.toConfig();\n                const originalFieldConfigMap = config.fields;\n                const newFieldConfigMap = {};\n                for (const fieldName in originalFieldConfigMap) {\n                    const originalFieldConfig = originalFieldConfigMap[fieldName];\n                    if (testFn(fieldName, originalFieldConfig)) {\n                        removedFields[fieldName] = originalFieldConfig;\n                    }\n                    else {\n                        newFieldConfigMap[fieldName] = originalFieldConfig;\n                    }\n                }\n                for (const fieldName in newFields) {\n                    const fieldConfig = newFields[fieldName];\n                    newFieldConfigMap[fieldName] = fieldConfig;\n                }\n                return correctASTNodes(new GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        },\n    });\n    return [newSchema, removedFields];\n}\n", "import { GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLObjectType, GraphQLScalarType, GraphQLUnionType, isEnumType, isInputObjectType, isInterfaceType, isObjectType, isScalarType, isUnionType, } from 'graphql';\nexport function renameType(type, newTypeName) {\n    if (isObjectType(type)) {\n        return new GraphQLObjectType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if (isInterfaceType(type)) {\n        return new GraphQLInterfaceType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if (isUnionType(type)) {\n        return new GraphQLUnionType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if (isInputObjectType(type)) {\n        return new GraphQLInputObjectType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if (isEnumType(type)) {\n        return new GraphQLEnumType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if (isScalarType(type)) {\n        return new GraphQLScalarType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    throw new Error(`Unknown type ${type}.`);\n}\n", "import { Kind } from 'graphql';\nimport { astFromType } from './astFromType.js';\nexport function updateArgument(argumentNodes, variableDefinitionsMap, variableValues, argName, varName, type, value) {\n    argumentNodes[argName] = {\n        kind: Kind.ARGUMENT,\n        name: {\n            kind: Kind.NAME,\n            value: argName,\n        },\n        value: {\n            kind: Kind.VARIABLE,\n            name: {\n                kind: Kind.NAME,\n                value: varName,\n            },\n        },\n    };\n    variableDefinitionsMap[varName] = {\n        kind: Kind.VARIABLE_DEFINITION,\n        variable: {\n            kind: Kind.VARIABLE,\n            name: {\n                kind: Kind.NAME,\n                value: varName,\n            },\n        },\n        type: astFromType(type),\n    };\n    if (value !== undefined) {\n        variableValues[varName] = value;\n        return;\n    }\n    // including the variable in the map with value of `undefined`\n    // will actually be translated by graphql-js into `null`\n    // see https://github.com/graphql/graphql-js/issues/2533\n    if (varName in variableValues) {\n        delete variableValues[varName];\n    }\n}\nexport function createVariableNameGenerator(variableDefinitionMap) {\n    let varCounter = 0;\n    return (argName) => {\n        let varName;\n        do {\n            varName = varCounter === 0 ? argName : `_v${varCounter.toString()}_${argName}`;\n            varCounter++;\n        } while (varName in variableDefinitionMap);\n        return varName;\n    };\n}\n", "import { doTypesOverlap, isCompositeType } from 'graphql';\nexport function implementsAbstractType(schema, typeA, typeB) {\n    if (typeB == null || typeA == null) {\n        return false;\n    }\n    else if (typeA === typeB) {\n        return true;\n    }\n    else if (isCompositeType(typeA) && isCompositeType(typeB)) {\n        return doTypesOverlap(schema, typeA, typeB);\n    }\n    return false;\n}\n", "import { getNullableType, isAbstractType, isListType, isObjectType, Kind, SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef, } from 'graphql';\nimport { collectFields, collectSubFields } from './collectFields.js';\nimport { getOperationASTFromRequest } from './getOperationASTFromRequest.js';\nexport function visitData(data, enter, leave) {\n    if (Array.isArray(data)) {\n        return data.map(value => visitData(value, enter, leave));\n    }\n    else if (typeof data === 'object') {\n        const newData = enter != null ? enter(data) : data;\n        if (newData != null) {\n            for (const key in newData) {\n                const value = newData[key];\n                Object.defineProperty(newData, key, {\n                    value: visitData(value, enter, leave),\n                });\n            }\n        }\n        return leave != null ? leave(newData) : newData;\n    }\n    return data;\n}\nexport function visitErrors(errors, visitor) {\n    return errors.map(error => visitor(error));\n}\nexport function visitResult(result, request, schema, resultVisitorMap, errorVisitorMap) {\n    const fragments = request.document.definitions.reduce((acc, def) => {\n        if (def.kind === Kind.FRAGMENT_DEFINITION) {\n            acc[def.name.value] = def;\n        }\n        return acc;\n    }, {});\n    const variableValues = request.variables || {};\n    const errorInfo = {\n        segmentInfoMap: new Map(),\n        unpathedErrors: new Set(),\n    };\n    const data = result.data;\n    const errors = result.errors;\n    const visitingErrors = errors != null && errorVisitorMap != null;\n    const operationDocumentNode = getOperationASTFromRequest(request);\n    if (data != null && operationDocumentNode != null) {\n        result.data = visitRoot(data, operationDocumentNode, schema, fragments, variableValues, resultVisitorMap, visitingErrors ? errors : undefined, errorInfo);\n    }\n    if (errors != null && errorVisitorMap) {\n        result.errors = visitErrorsByType(errors, errorVisitorMap, errorInfo);\n    }\n    return result;\n}\nfunction visitErrorsByType(errors, errorVisitorMap, errorInfo) {\n    const segmentInfoMap = errorInfo.segmentInfoMap;\n    const unpathedErrors = errorInfo.unpathedErrors;\n    const unpathedErrorVisitor = errorVisitorMap['__unpathed'];\n    return errors.map(originalError => {\n        const pathSegmentsInfo = segmentInfoMap.get(originalError);\n        const newError = pathSegmentsInfo == null\n            ? originalError\n            : pathSegmentsInfo.reduceRight((acc, segmentInfo) => {\n                const typeName = segmentInfo.type.name;\n                const typeVisitorMap = errorVisitorMap[typeName];\n                if (typeVisitorMap == null) {\n                    return acc;\n                }\n                const errorVisitor = typeVisitorMap[segmentInfo.fieldName];\n                return errorVisitor == null ? acc : errorVisitor(acc, segmentInfo.pathIndex);\n            }, originalError);\n        if (unpathedErrorVisitor && unpathedErrors.has(originalError)) {\n            return unpathedErrorVisitor(newError);\n        }\n        return newError;\n    });\n}\nfunction getOperationRootType(schema, operationDef) {\n    switch (operationDef.operation) {\n        case 'query':\n            return schema.getQueryType();\n        case 'mutation':\n            return schema.getMutationType();\n        case 'subscription':\n            return schema.getSubscriptionType();\n    }\n}\nfunction visitRoot(root, operation, schema, fragments, variableValues, resultVisitorMap, errors, errorInfo) {\n    const operationRootType = getOperationRootType(schema, operation);\n    const { fields: collectedFields } = collectFields(schema, fragments, variableValues, operationRootType, operation.selectionSet);\n    return visitObjectValue(root, operationRootType, collectedFields, schema, fragments, variableValues, resultVisitorMap, 0, errors, errorInfo);\n}\nfunction visitObjectValue(object, type, fieldNodeMap, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo) {\n    const fieldMap = type.getFields();\n    const typeVisitorMap = resultVisitorMap?.[type.name];\n    const enterObject = typeVisitorMap?.__enter;\n    const newObject = enterObject != null ? enterObject(object) : object;\n    let sortedErrors;\n    let errorMap = null;\n    if (errors != null) {\n        sortedErrors = sortErrorsByPathSegment(errors, pathIndex);\n        errorMap = sortedErrors.errorMap;\n        for (const error of sortedErrors.unpathedErrors) {\n            errorInfo.unpathedErrors.add(error);\n        }\n    }\n    for (const [responseKey, subFieldNodes] of fieldNodeMap) {\n        const fieldName = subFieldNodes[0].name.value;\n        let fieldType = fieldMap[fieldName]?.type;\n        if (fieldType == null) {\n            switch (fieldName) {\n                case '__typename':\n                    fieldType = TypeNameMetaFieldDef.type;\n                    break;\n                case '__schema':\n                    fieldType = SchemaMetaFieldDef.type;\n                    break;\n                case '__type':\n                    fieldType = TypeMetaFieldDef.type;\n                    break;\n            }\n        }\n        const newPathIndex = pathIndex + 1;\n        let fieldErrors;\n        if (errorMap) {\n            fieldErrors = errorMap[responseKey];\n            if (fieldErrors != null) {\n                delete errorMap[responseKey];\n            }\n            addPathSegmentInfo(type, fieldName, newPathIndex, fieldErrors, errorInfo);\n        }\n        const newValue = visitFieldValue(object[responseKey], fieldType, subFieldNodes, schema, fragments, variableValues, resultVisitorMap, newPathIndex, fieldErrors, errorInfo);\n        updateObject(newObject, responseKey, newValue, typeVisitorMap, fieldName);\n    }\n    const oldTypename = newObject.__typename;\n    if (oldTypename != null) {\n        updateObject(newObject, '__typename', oldTypename, typeVisitorMap, '__typename');\n    }\n    if (errorMap) {\n        for (const errorsKey in errorMap) {\n            const errors = errorMap[errorsKey];\n            for (const error of errors) {\n                errorInfo.unpathedErrors.add(error);\n            }\n        }\n    }\n    const leaveObject = typeVisitorMap?.__leave;\n    return leaveObject != null ? leaveObject(newObject) : newObject;\n}\nfunction updateObject(object, responseKey, newValue, typeVisitorMap, fieldName) {\n    if (typeVisitorMap == null) {\n        object[responseKey] = newValue;\n        return;\n    }\n    const fieldVisitor = typeVisitorMap[fieldName];\n    if (fieldVisitor == null) {\n        object[responseKey] = newValue;\n        return;\n    }\n    const visitedValue = fieldVisitor(newValue);\n    if (visitedValue === undefined) {\n        delete object[responseKey];\n        return;\n    }\n    object[responseKey] = visitedValue;\n}\nfunction visitListValue(list, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo) {\n    return list.map(listMember => visitFieldValue(listMember, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex + 1, errors, errorInfo));\n}\nfunction visitFieldValue(value, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors = [], errorInfo) {\n    if (value == null) {\n        return value;\n    }\n    const nullableType = getNullableType(returnType);\n    if (isListType(nullableType)) {\n        return visitListValue(value, nullableType.ofType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);\n    }\n    else if (isAbstractType(nullableType)) {\n        const finalType = schema.getType(value.__typename);\n        let { fields: collectedFields, patches } = collectSubFields(schema, fragments, variableValues, finalType, fieldNodes);\n        if (patches.length) {\n            collectedFields = new Map(collectedFields);\n            for (const patch of patches) {\n                for (const [responseKey, fields] of patch.fields) {\n                    const existingFields = collectedFields.get(responseKey);\n                    if (existingFields) {\n                        existingFields.push(...fields);\n                    }\n                    else {\n                        collectedFields.set(responseKey, fields);\n                    }\n                }\n            }\n        }\n        return visitObjectValue(value, finalType, collectedFields, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);\n    }\n    else if (isObjectType(nullableType)) {\n        let { fields: collectedFields, patches } = collectSubFields(schema, fragments, variableValues, nullableType, fieldNodes);\n        if (patches.length) {\n            collectedFields = new Map(collectedFields);\n            for (const patch of patches) {\n                for (const [responseKey, fields] of patch.fields) {\n                    const existingFields = collectedFields.get(responseKey);\n                    if (existingFields) {\n                        existingFields.push(...fields);\n                    }\n                    else {\n                        collectedFields.set(responseKey, fields);\n                    }\n                }\n            }\n        }\n        return visitObjectValue(value, nullableType, collectedFields, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);\n    }\n    const typeVisitorMap = resultVisitorMap?.[nullableType.name];\n    if (typeVisitorMap == null) {\n        return value;\n    }\n    const visitedValue = typeVisitorMap(value);\n    return visitedValue === undefined ? value : visitedValue;\n}\nfunction sortErrorsByPathSegment(errors, pathIndex) {\n    const errorMap = Object.create(null);\n    const unpathedErrors = new Set();\n    for (const error of errors) {\n        const pathSegment = error.path?.[pathIndex];\n        if (pathSegment == null) {\n            unpathedErrors.add(error);\n            continue;\n        }\n        if (pathSegment in errorMap) {\n            errorMap[pathSegment].push(error);\n        }\n        else {\n            errorMap[pathSegment] = [error];\n        }\n    }\n    return {\n        errorMap,\n        unpathedErrors,\n    };\n}\nfunction addPathSegmentInfo(type, fieldName, pathIndex, errors = [], errorInfo) {\n    for (const error of errors) {\n        const segmentInfo = {\n            type,\n            fieldName,\n            pathIndex,\n        };\n        const pathSegmentsInfo = errorInfo.segmentInfoMap.get(error);\n        if (pathSegmentsInfo == null) {\n            errorInfo.segmentInfoMap.set(error, [segmentInfo]);\n        }\n        else {\n            pathSegmentsInfo.push(segmentInfo);\n        }\n    }\n}\n", "import { getDirectiveValues, GraphQLIncludeDirective, GraphQLSkipDirective, isAbstractType, Kind, typeFromAST, } from 'graphql';\nimport { AccumulatorMap } from './AccumulatorMap.js';\nimport { GraphQLDeferDirective } from './directives.js';\nimport { memoize5 } from './memoize.js';\nfunction collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, patches, visitedFragmentNames) {\n    for (const selection of selectionSet.selections) {\n        switch (selection.kind) {\n            case Kind.FIELD: {\n                if (!shouldIncludeNode(variableValues, selection)) {\n                    continue;\n                }\n                fields.add(getFieldEntryKey(selection), selection);\n                break;\n            }\n            case Kind.INLINE_FRAGMENT: {\n                if (!shouldIncludeNode(variableValues, selection) ||\n                    !doesFragmentConditionMatch(schema, selection, runtimeType)) {\n                    continue;\n                }\n                const defer = getDeferValues(variableValues, selection);\n                if (defer) {\n                    const patchFields = new AccumulatorMap();\n                    collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, patchFields, patches, visitedFragmentNames);\n                    patches.push({\n                        label: defer.label,\n                        fields: patchFields,\n                    });\n                }\n                else {\n                    collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, fields, patches, visitedFragmentNames);\n                }\n                break;\n            }\n            case Kind.FRAGMENT_SPREAD: {\n                const fragName = selection.name.value;\n                if (!shouldIncludeNode(variableValues, selection)) {\n                    continue;\n                }\n                const defer = getDeferValues(variableValues, selection);\n                if (visitedFragmentNames.has(fragName) && !defer) {\n                    continue;\n                }\n                const fragment = fragments[fragName];\n                if (!fragment || !doesFragmentConditionMatch(schema, fragment, runtimeType)) {\n                    continue;\n                }\n                if (!defer) {\n                    visitedFragmentNames.add(fragName);\n                }\n                if (defer) {\n                    const patchFields = new AccumulatorMap();\n                    collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, patchFields, patches, visitedFragmentNames);\n                    patches.push({\n                        label: defer.label,\n                        fields: patchFields,\n                    });\n                }\n                else {\n                    collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, fields, patches, visitedFragmentNames);\n                }\n                break;\n            }\n        }\n    }\n}\n/**\n * Given a selectionSet, collects all of the fields and returns them.\n *\n * CollectFields requires the \"runtime type\" of an object. For a field that\n * returns an Interface or Union type, the \"runtime type\" will be the actual\n * object type returned by that field.\n *\n */\nexport function collectFields(schema, fragments, variableValues, runtimeType, selectionSet) {\n    const fields = new AccumulatorMap();\n    const patches = [];\n    collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, patches, new Set());\n    return { fields, patches };\n}\n/**\n * Determines if a field should be included based on the `@include` and `@skip`\n * directives, where `@skip` has higher precedence than `@include`.\n */\nexport function shouldIncludeNode(variableValues, node) {\n    const skip = getDirectiveValues(GraphQLSkipDirective, node, variableValues);\n    if (skip?.['if'] === true) {\n        return false;\n    }\n    const include = getDirectiveValues(GraphQLIncludeDirective, node, variableValues);\n    if (include?.['if'] === false) {\n        return false;\n    }\n    return true;\n}\n/**\n * Determines if a fragment is applicable to the given type.\n */\nexport function doesFragmentConditionMatch(schema, fragment, type) {\n    const typeConditionNode = fragment.typeCondition;\n    if (!typeConditionNode) {\n        return true;\n    }\n    const conditionalType = typeFromAST(schema, typeConditionNode);\n    if (conditionalType === type) {\n        return true;\n    }\n    if (isAbstractType(conditionalType)) {\n        const possibleTypes = schema.getPossibleTypes(conditionalType);\n        return possibleTypes.includes(type);\n    }\n    return false;\n}\n/**\n * Implements the logic to compute the key of a given field's entry\n */\nexport function getFieldEntryKey(node) {\n    return node.alias ? node.alias.value : node.name.value;\n}\n/**\n * Returns an object containing the `@defer` arguments if a field should be\n * deferred based on the experimental flag, defer directive present and\n * not disabled by the \"if\" argument.\n */\nexport function getDeferValues(variableValues, node) {\n    const defer = getDirectiveValues(GraphQLDeferDirective, node, variableValues);\n    if (!defer) {\n        return;\n    }\n    if (defer['if'] === false) {\n        return;\n    }\n    return {\n        label: typeof defer['label'] === 'string' ? defer['label'] : undefined,\n    };\n}\n/**\n * Given an array of field nodes, collects all of the subfields of the passed\n * in fields, and returns them at the end.\n *\n * CollectSubFields requires the \"return type\" of an object. For a field that\n * returns an Interface or Union type, the \"return type\" will be the actual\n * object type returned by that field.\n *\n */\nexport const collectSubFields = memoize5(function collectSubfields(schema, fragments, variableValues, returnType, fieldNodes) {\n    const subFieldNodes = new AccumulatorMap();\n    const visitedFragmentNames = new Set();\n    const subPatches = [];\n    const subFieldsAndPatches = {\n        fields: subFieldNodes,\n        patches: subPatches,\n    };\n    for (const node of fieldNodes) {\n        if (node.selectionSet) {\n            collectFieldsImpl(schema, fragments, variableValues, returnType, node.selectionSet, subFieldNodes, subPatches, visitedFragmentNames);\n        }\n    }\n    return subFieldsAndPatches;\n});\n", "/**\n * ES6 Map with additional `add` method to accumulate items.\n */\nexport class AccumulatorMap extends Map {\n    get [Symbol.toStringTag]() {\n        return 'AccumulatorMap';\n    }\n    add(key, item) {\n        const group = this.get(key);\n        if (group === undefined) {\n            this.set(key, [item]);\n        }\n        else {\n            group.push(item);\n        }\n    }\n}\n", "import { DirectiveLocation, GraphQLBoolean, GraphQLDirective, GraphQLInt, GraphQLNonNull, GraphQLString, } from 'graphql';\n/**\n * Used to conditionally defer fragments.\n */\nexport const GraphQLDeferDirective = new GraphQLDirective({\n    name: 'defer',\n    description: 'Directs the executor to defer this fragment when the `if` argument is true or undefined.',\n    locations: [DirectiveLocation.FRAGMENT_SPREAD, DirectiveLocation.INLINE_FRAGMENT],\n    args: {\n        if: {\n            type: new GraphQLNonNull(GraphQLBoolean),\n            description: 'Deferred when true or undefined.',\n            defaultValue: true,\n        },\n        label: {\n            type: GraphQLString,\n            description: 'Unique name',\n        },\n    },\n});\n/**\n * Used to conditionally stream list fields.\n */\nexport const GraphQLStreamDirective = new GraphQLDirective({\n    name: 'stream',\n    description: 'Directs the executor to stream plural fields when the `if` argument is true or undefined.',\n    locations: [DirectiveLocation.FIELD],\n    args: {\n        if: {\n            type: new GraphQLNonNull(GraphQLBoolean),\n            description: 'Stream when true or undefined.',\n            defaultValue: true,\n        },\n        label: {\n            type: GraphQLString,\n            description: 'Unique name',\n        },\n        initialCount: {\n            defaultValue: 0,\n            type: GraphQLInt,\n            description: 'Number of items to return immediately',\n        },\n    },\n});\n", "import { getOperationAST } from 'graphql';\nimport { memoize1 } from './memoize.js';\nexport function getOperationASTFromDocument(documentNode, operationName) {\n    const doc = getOperationAST(documentNode, operationName);\n    if (!doc) {\n        throw new Error(`Cannot infer operation ${operationName || ''}`);\n    }\n    return doc;\n}\nexport const getOperationASTFromRequest = memoize1(function getOperationASTFromRequest(request) {\n    return getOperationASTFromDocument(request.document, request.operationName);\n});\n", "import { Kind } from 'graphql';\nexport function isDocumentNode(object) {\n    return object && typeof object === 'object' && 'kind' in object && object.kind === Kind.DOCUMENT;\n}\n", "import { memoize2 } from './memoize.js';\nasync function defaultAsyncIteratorReturn(value) {\n    return { value, done: true };\n}\nconst proxyMethodFactory = memoize2(function proxyMethodFactory(target, targetMethod) {\n    return function proxyMethod(...args) {\n        return Reflect.apply(targetMethod, target, args);\n    };\n});\nexport function getAsyncIteratorWithCancel(asyncIterator, onCancel) {\n    return new Proxy(asyncIterator, {\n        has(asyncIterator, prop) {\n            if (prop === 'return') {\n                return true;\n            }\n            return Reflect.has(asyncIterator, prop);\n        },\n        get(asyncIterator, prop, receiver) {\n            const existingPropValue = Reflect.get(asyncIterator, prop, receiver);\n            if (prop === 'return') {\n                const existingReturn = existingPropValue || defaultAsyncIteratorReturn;\n                return async function returnWithCancel(value) {\n                    const returnValue = await onCancel(value);\n                    return Reflect.apply(existingReturn, asyncIterator, [returnValue]);\n                };\n            }\n            else if (typeof existingPropValue === 'function') {\n                return proxyMethodFactory(asyncIterator, existingPropValue);\n            }\n            return existingPropValue;\n        },\n    });\n}\nexport function getAsyncIterableWithCancel(asyncIterable, onCancel) {\n    return new Proxy(asyncIterable, {\n        get(asyncIterable, prop, receiver) {\n            const existingPropValue = Reflect.get(asyncIterable, prop, receiver);\n            if (Symbol.asyncIterator === prop) {\n                return function asyncIteratorFactory() {\n                    const asyncIterator = Reflect.apply(existingPropValue, asyncIterable, []);\n                    return getAsyncIteratorWithCancel(asyncIterator, onCancel);\n                };\n            }\n            else if (typeof existingPropValue === 'function') {\n                return proxyMethodFactory(asyncIterable, existingPropValue);\n            }\n            return existingPropValue;\n        },\n    });\n}\nexport { getAsyncIterableWithCancel as withCancel };\n", "import { buildASTSchema } from 'graphql';\nimport { getDocumentNodeFromSchema } from './print-schema-with-directives.js';\nfunction buildFixedSchema(schema, options) {\n    const document = getDocumentNodeFromSchema(schema);\n    return buildASTSchema(document, {\n        ...(options || {}),\n    });\n}\nexport function fixSchemaAst(schema, options) {\n    // eslint-disable-next-line no-undef-init\n    let schemaWithValidAst = undefined;\n    if (!schema.astNode || !schema.extensionASTNodes) {\n        schemaWithValidAst = buildFixedSchema(schema, options);\n    }\n    if (!schema.astNode && schemaWithValidAst?.astNode) {\n        schema.astNode = schemaWithValidAst.astNode;\n    }\n    if (!schema.extensionASTNodes && schemaWithValidAst?.astNode) {\n        schema.extensionASTNodes = schemaWithValidAst.extensionASTNodes;\n    }\n    return schema;\n}\n", "import { asArray } from './helpers.js';\nimport { MapperKind } from './Interfaces.js';\nimport { mapSchema } from './mapSchema.js';\nfunction handleDirectiveExtensions(extensions, removeDirectives) {\n    extensions = extensions || {};\n    const { directives: existingDirectives, ...rest } = extensions;\n    const finalExtensions = {\n        ...rest,\n    };\n    if (!removeDirectives) {\n        if (existingDirectives != null) {\n            const directives = {};\n            for (const directiveName in existingDirectives) {\n                directives[directiveName] = [...asArray(existingDirectives[directiveName])];\n            }\n            finalExtensions.directives = directives;\n        }\n    }\n    return finalExtensions;\n}\nexport function extractExtensionsFromSchema(schema, removeDirectives = false) {\n    const result = {\n        schemaExtensions: handleDirectiveExtensions(schema.extensions, removeDirectives),\n        types: {},\n    };\n    mapSchema(schema, {\n        [MapperKind.OBJECT_TYPE]: type => {\n            result.types[type.name] = {\n                fields: {},\n                type: 'object',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [MapperKind.INTERFACE_TYPE]: type => {\n            result.types[type.name] = {\n                fields: {},\n                type: 'interface',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [MapperKind.FIELD]: (field, fieldName, typeName) => {\n            result.types[typeName].fields[fieldName] = {\n                arguments: {},\n                extensions: handleDirectiveExtensions(field.extensions, removeDirectives),\n            };\n            const args = field.args;\n            if (args != null) {\n                for (const argName in args) {\n                    result.types[typeName].fields[fieldName].arguments[argName] =\n                        handleDirectiveExtensions(args[argName].extensions, removeDirectives);\n                }\n            }\n            return field;\n        },\n        [MapperKind.ENUM_TYPE]: type => {\n            result.types[type.name] = {\n                values: {},\n                type: 'enum',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [MapperKind.ENUM_VALUE]: (value, typeName, _schema, valueName) => {\n            result.types[typeName].values[valueName] = handleDirectiveExtensions(value.extensions, removeDirectives);\n            return value;\n        },\n        [MapperKind.SCALAR_TYPE]: type => {\n            result.types[type.name] = {\n                type: 'scalar',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [MapperKind.UNION_TYPE]: type => {\n            result.types[type.name] = {\n                type: 'union',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [MapperKind.INPUT_OBJECT_TYPE]: type => {\n            result.types[type.name] = {\n                fields: {},\n                type: 'input',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [MapperKind.INPUT_OBJECT_FIELD]: (field, fieldName, typeName) => {\n            result.types[typeName].fields[fieldName] = {\n                extensions: handleDirectiveExtensions(field.extensions, removeDirectives),\n            };\n            return field;\n        },\n    });\n    return result;\n}\n", "import { fakeRejectPromise } from '@whatwg-node/promise-helpers';\nimport { memoize1 } from './memoize.js';\n// AbortSignal handler cache to avoid the \"possible EventEmitter memory leak detected\"\n// on Node.js\nconst getListenersOfAbortSignal = memoize1(function getListenersOfAbortSignal(signal) {\n    const listeners = new Set();\n    signal.addEventListener('abort', e => {\n        for (const listener of listeners) {\n            listener(e);\n        }\n    }, { once: true });\n    return listeners;\n});\n/**\n * Register an AbortSignal handler for a signal.\n * This helper function mainly exists to work around the\n * \"possible EventEmitter memory leak detected. 11 listeners added. Use emitter.setMaxListeners() to increase limit.\"\n * warning occuring on Node.js\n */\nexport function registerAbortSignalListener(signal, listener) {\n    // If the signal is already aborted, call the listener immediately\n    if (signal.aborted) {\n        listener();\n        return;\n    }\n    getListenersOfAbortSignal(signal).add(listener);\n}\nexport const getAbortPromise = memoize1(function getAbortPromise(signal) {\n    // If the signal is already aborted, return a rejected promise\n    if (signal.aborted) {\n        return fakeRejectPromise(signal.reason);\n    }\n    return new Promise((_resolve, reject) => {\n        if (signal.aborted) {\n            reject(signal.reason);\n            return;\n        }\n        registerAbortSignalListener(signal, () => {\n            reject(signal.reason);\n        });\n    });\n});\n", "import { defaultFieldResolver } from 'graphql';\nexport function chainResolvers(resolvers) {\n    return (root, args, ctx, info) => resolvers.reduce((prev, curResolver) => {\n        if (curResolver != null) {\n            return curResolver(prev, args, ctx, info);\n        }\n        return defaultFieldResolver(prev, args, ctx, info);\n    }, root);\n}\n", "import { GraphQLEnumType, GraphQLInterfaceType, GraphQLObjectType, GraphQLScalarType, GraphQLUnionType, isEnumType, isInterfaceType, isObjectType, isScalarType, isSpecifiedScalarType, isUnionType, } from 'graphql';\nimport { forEachDefaultValue, forEachField, healSchema, MapperKind, mapSchema, parseInputValue, serializeInputValue, } from '@graphql-tools/utils';\nimport { checkForResolveTypeResolver } from './checkForResolveTypeResolver.js';\nimport { extendResolversFromInterfaces } from './extendResolversFromInterfaces.js';\nexport function addResolversToSchema({ schema, resolvers: inputResolvers, defaultFieldResolver, resolverValidationOptions = {}, inheritResolversFromInterfaces = false, updateResolversInPlace = false, }) {\n    const { requireResolversToMatchSchema = 'error', requireResolversForResolveType } = resolverValidationOptions;\n    const resolvers = inheritResolversFromInterfaces\n        ? extendResolversFromInterfaces(schema, inputResolvers)\n        : inputResolvers;\n    for (const typeName in resolvers) {\n        const resolverValue = resolvers[typeName];\n        const resolverType = typeof resolverValue;\n        if (resolverType !== 'object') {\n            throw new Error(`\"${typeName}\" defined in resolvers, but has invalid value \"${resolverValue}\". The resolver's value must be of type object.`);\n        }\n        const type = schema.getType(typeName);\n        if (type == null) {\n            const msg = `\"${typeName}\" defined in resolvers, but not in schema`;\n            if (requireResolversToMatchSchema && requireResolversToMatchSchema !== 'error') {\n                if (requireResolversToMatchSchema === 'warn') {\n                    console.warn(msg);\n                }\n                continue;\n            }\n            throw new Error(msg);\n        }\n        else if (isSpecifiedScalarType(type)) {\n            // allow -- without recommending -- overriding of specified scalar types\n            for (const fieldName in resolverValue) {\n                if (fieldName.startsWith('__')) {\n                    type[fieldName.substring(2)] = resolverValue[fieldName];\n                }\n                else {\n                    type[fieldName] = resolverValue[fieldName];\n                }\n            }\n        }\n        else if (isEnumType(type)) {\n            const values = type.getValues();\n            for (const fieldName in resolverValue) {\n                if (!fieldName.startsWith('__') &&\n                    !values.some(value => value.name === fieldName) &&\n                    requireResolversToMatchSchema &&\n                    requireResolversToMatchSchema !== 'ignore') {\n                    const msg = `${type.name}.${fieldName} was defined in resolvers, but not present within ${type.name}`;\n                    if (requireResolversToMatchSchema === 'error') {\n                        throw new Error(msg);\n                    }\n                    else {\n                        console.warn(msg);\n                    }\n                }\n            }\n        }\n        else if (isUnionType(type)) {\n            for (const fieldName in resolverValue) {\n                if (!fieldName.startsWith('__') &&\n                    requireResolversToMatchSchema &&\n                    requireResolversToMatchSchema !== 'ignore') {\n                    const msg = `${type.name}.${fieldName} was defined in resolvers, but ${type.name} is not an object or interface type`;\n                    if (requireResolversToMatchSchema === 'error') {\n                        throw new Error(msg);\n                    }\n                    else {\n                        console.warn(msg);\n                    }\n                }\n            }\n        }\n        else if (isObjectType(type) || isInterfaceType(type)) {\n            for (const fieldName in resolverValue) {\n                if (!fieldName.startsWith('__')) {\n                    const fields = type.getFields();\n                    const field = fields[fieldName];\n                    if (field == null) {\n                        // Field present in resolver but not in schema\n                        if (requireResolversToMatchSchema && requireResolversToMatchSchema !== 'ignore') {\n                            const msg = `${typeName}.${fieldName} defined in resolvers, but not in schema`;\n                            if (requireResolversToMatchSchema === 'error') {\n                                throw new Error(msg);\n                            }\n                            else {\n                                console.error(msg);\n                            }\n                        }\n                    }\n                    else {\n                        // Field present in both the resolver and schema\n                        const fieldResolve = resolverValue[fieldName];\n                        if (typeof fieldResolve !== 'function' && typeof fieldResolve !== 'object') {\n                            throw new Error(`Resolver ${typeName}.${fieldName} must be object or function`);\n                        }\n                    }\n                }\n            }\n        }\n    }\n    schema = updateResolversInPlace\n        ? addResolversToExistingSchema(schema, resolvers, defaultFieldResolver)\n        : createNewSchemaWithResolvers(schema, resolvers, defaultFieldResolver);\n    if (requireResolversForResolveType && requireResolversForResolveType !== 'ignore') {\n        checkForResolveTypeResolver(schema, requireResolversForResolveType);\n    }\n    return schema;\n}\nfunction addResolversToExistingSchema(schema, resolvers, defaultFieldResolver) {\n    const typeMap = schema.getTypeMap();\n    for (const typeName in resolvers) {\n        const type = schema.getType(typeName);\n        const resolverValue = resolvers[typeName];\n        if (isScalarType(type)) {\n            for (const fieldName in resolverValue) {\n                if (fieldName.startsWith('__')) {\n                    type[fieldName.substring(2)] = resolverValue[fieldName];\n                }\n                else if (fieldName === 'astNode' && type.astNode != null) {\n                    type.astNode = {\n                        ...type.astNode,\n                        description: resolverValue?.astNode?.description ??\n                            type.astNode.description,\n                        directives: (type.astNode.directives ?? []).concat(resolverValue?.astNode?.directives ?? []),\n                    };\n                }\n                else if (fieldName === 'extensionASTNodes' && type.extensionASTNodes != null) {\n                    type.extensionASTNodes = type.extensionASTNodes.concat(resolverValue?.extensionASTNodes ?? []);\n                }\n                else if (fieldName === 'extensions' &&\n                    type.extensions != null &&\n                    resolverValue.extensions != null) {\n                    type.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);\n                }\n                else {\n                    type[fieldName] = resolverValue[fieldName];\n                }\n            }\n        }\n        else if (isEnumType(type)) {\n            const config = type.toConfig();\n            const enumValueConfigMap = config.values;\n            for (const fieldName in resolverValue) {\n                if (fieldName.startsWith('__')) {\n                    config[fieldName.substring(2)] = resolverValue[fieldName];\n                }\n                else if (fieldName === 'astNode' && config.astNode != null) {\n                    config.astNode = {\n                        ...config.astNode,\n                        description: resolverValue?.astNode?.description ??\n                            config.astNode.description,\n                        directives: (config.astNode.directives ?? []).concat(resolverValue?.astNode?.directives ?? []),\n                    };\n                }\n                else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {\n                    config.extensionASTNodes = config.extensionASTNodes.concat(resolverValue?.extensionASTNodes ?? []);\n                }\n                else if (fieldName === 'extensions' &&\n                    type.extensions != null &&\n                    resolverValue.extensions != null) {\n                    type.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);\n                }\n                else if (enumValueConfigMap[fieldName]) {\n                    enumValueConfigMap[fieldName].value = resolverValue[fieldName];\n                }\n            }\n            typeMap[typeName] = new GraphQLEnumType(config);\n        }\n        else if (isUnionType(type)) {\n            for (const fieldName in resolverValue) {\n                if (fieldName.startsWith('__')) {\n                    type[fieldName.substring(2)] = resolverValue[fieldName];\n                }\n            }\n        }\n        else if (isObjectType(type) || isInterfaceType(type)) {\n            for (const fieldName in resolverValue) {\n                if (fieldName.startsWith('__')) {\n                    // this is for isTypeOf and resolveType and all the other stuff.\n                    type[fieldName.substring(2)] = resolverValue[fieldName];\n                    continue;\n                }\n                const fields = type.getFields();\n                const field = fields[fieldName];\n                if (field != null) {\n                    const fieldResolve = resolverValue[fieldName];\n                    if (typeof fieldResolve === 'function') {\n                        // for convenience. Allows shorter syntax in resolver definition file\n                        field.resolve = fieldResolve.bind(resolverValue);\n                    }\n                    else {\n                        setFieldProperties(field, fieldResolve);\n                    }\n                }\n            }\n        }\n    }\n    // serialize all default values prior to healing fields with new scalar/enum types.\n    forEachDefaultValue(schema, serializeInputValue);\n    // schema may have new scalar/enum types that require healing\n    healSchema(schema);\n    // reparse all default values with new parsing functions.\n    forEachDefaultValue(schema, parseInputValue);\n    if (defaultFieldResolver != null) {\n        forEachField(schema, field => {\n            if (!field.resolve) {\n                field.resolve = defaultFieldResolver;\n            }\n        });\n    }\n    return schema;\n}\nfunction createNewSchemaWithResolvers(schema, resolvers, defaultFieldResolver) {\n    schema = mapSchema(schema, {\n        [MapperKind.SCALAR_TYPE]: type => {\n            const config = type.toConfig();\n            const resolverValue = resolvers[type.name];\n            if (!isSpecifiedScalarType(type) && resolverValue != null) {\n                for (const fieldName in resolverValue) {\n                    if (fieldName.startsWith('__')) {\n                        config[fieldName.substring(2)] = resolverValue[fieldName];\n                    }\n                    else if (fieldName === 'astNode' && config.astNode != null) {\n                        config.astNode = {\n                            ...config.astNode,\n                            description: resolverValue?.astNode?.description ??\n                                config.astNode.description,\n                            directives: (config.astNode.directives ?? []).concat(resolverValue?.astNode?.directives ?? []),\n                        };\n                    }\n                    else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {\n                        config.extensionASTNodes = config.extensionASTNodes.concat(resolverValue?.extensionASTNodes ?? []);\n                    }\n                    else if (fieldName === 'extensions' &&\n                        config.extensions != null &&\n                        resolverValue.extensions != null) {\n                        config.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);\n                    }\n                    else {\n                        config[fieldName] = resolverValue[fieldName];\n                    }\n                }\n                return new GraphQLScalarType(config);\n            }\n        },\n        [MapperKind.ENUM_TYPE]: type => {\n            const resolverValue = resolvers[type.name];\n            const config = type.toConfig();\n            const enumValueConfigMap = config.values;\n            if (resolverValue != null) {\n                for (const fieldName in resolverValue) {\n                    if (fieldName.startsWith('__')) {\n                        config[fieldName.substring(2)] = resolverValue[fieldName];\n                    }\n                    else if (fieldName === 'astNode' && config.astNode != null) {\n                        config.astNode = {\n                            ...config.astNode,\n                            description: resolverValue?.astNode?.description ??\n                                config.astNode.description,\n                            directives: (config.astNode.directives ?? []).concat(resolverValue?.astNode?.directives ?? []),\n                        };\n                    }\n                    else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {\n                        config.extensionASTNodes = config.extensionASTNodes.concat(resolverValue?.extensionASTNodes ?? []);\n                    }\n                    else if (fieldName === 'extensions' &&\n                        config.extensions != null &&\n                        resolverValue.extensions != null) {\n                        config.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);\n                    }\n                    else if (enumValueConfigMap[fieldName]) {\n                        enumValueConfigMap[fieldName].value = resolverValue[fieldName];\n                    }\n                }\n                return new GraphQLEnumType(config);\n            }\n        },\n        [MapperKind.UNION_TYPE]: type => {\n            const resolverValue = resolvers[type.name];\n            if (resolverValue != null) {\n                const config = type.toConfig();\n                if (resolverValue['__resolveType']) {\n                    config.resolveType = resolverValue['__resolveType'];\n                }\n                return new GraphQLUnionType(config);\n            }\n        },\n        [MapperKind.OBJECT_TYPE]: type => {\n            const resolverValue = resolvers[type.name];\n            if (resolverValue != null) {\n                const config = type.toConfig();\n                if (resolverValue['__isTypeOf']) {\n                    config.isTypeOf = resolverValue['__isTypeOf'];\n                }\n                return new GraphQLObjectType(config);\n            }\n        },\n        [MapperKind.INTERFACE_TYPE]: type => {\n            const resolverValue = resolvers[type.name];\n            if (resolverValue != null) {\n                const config = type.toConfig();\n                if (resolverValue['__resolveType']) {\n                    config.resolveType = resolverValue['__resolveType'];\n                }\n                return new GraphQLInterfaceType(config);\n            }\n        },\n        [MapperKind.COMPOSITE_FIELD]: (fieldConfig, fieldName, typeName) => {\n            const resolverValue = resolvers[typeName];\n            if (resolverValue != null) {\n                const fieldResolve = resolverValue[fieldName];\n                if (fieldResolve != null) {\n                    const newFieldConfig = { ...fieldConfig };\n                    if (typeof fieldResolve === 'function') {\n                        // for convenience. Allows shorter syntax in resolver definition file\n                        newFieldConfig.resolve = fieldResolve.bind(resolverValue);\n                    }\n                    else {\n                        setFieldProperties(newFieldConfig, fieldResolve);\n                    }\n                    return newFieldConfig;\n                }\n            }\n        },\n    });\n    if (defaultFieldResolver != null) {\n        schema = mapSchema(schema, {\n            [MapperKind.OBJECT_FIELD]: fieldConfig => ({\n                ...fieldConfig,\n                resolve: fieldConfig.resolve != null ? fieldConfig.resolve : defaultFieldResolver,\n            }),\n        });\n    }\n    return schema;\n}\nfunction setFieldProperties(field, propertiesObj) {\n    for (const propertyName in propertiesObj) {\n        field[propertyName] = propertiesObj[propertyName];\n    }\n}\n", "import { MapperKind, mapSchema } from '@graphql-tools/utils';\n// If we have any union or interface types throw if no there is no resolveType resolver\nexport function checkForResolveTypeResolver(schema, requireResolversForResolveType) {\n    mapSchema(schema, {\n        [MapperKind.ABSTRACT_TYPE]: type => {\n            if (!type.resolveType) {\n                const message = `Type \"${type.name}\" is missing a \"__resolveType\" resolver. Pass 'ignore' into ` +\n                    '\"resolverValidationOptions.requireResolversForResolveType\" to disable this error.';\n                if (requireResolversForResolveType === 'error') {\n                    throw new Error(message);\n                }\n                if (requireResolversForResolveType === 'warn') {\n                    console.warn(message);\n                }\n            }\n            return undefined;\n        },\n    });\n}\n", "export function extendResolversFromInterfaces(schema, resolvers) {\n    const extendedResolvers = {};\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        const type = typeMap[typeName];\n        if ('getInterfaces' in type) {\n            extendedResolvers[typeName] = {};\n            for (const iFace of type.getInterfaces()) {\n                if (resolvers[iFace.name]) {\n                    for (const fieldName in resolvers[iFace.name]) {\n                        if (fieldName === '__isTypeOf' || !fieldName.startsWith('__')) {\n                            extendedResolvers[typeName][fieldName] = resolvers[iFace.name][fieldName];\n                        }\n                    }\n                }\n            }\n            const typeResolvers = resolvers[typeName];\n            extendedResolvers[typeName] = {\n                ...extendedResolvers[typeName],\n                ...typeResolvers,\n            };\n        }\n        else {\n            const typeResolvers = resolvers[typeName];\n            if (typeResolvers != null) {\n                extendedResolvers[typeName] = typeResolvers;\n            }\n        }\n    }\n    return extendedResolvers;\n}\n", "import { buildASTSchema, buildSchema, isSchema } from 'graphql';\nimport { applyExtensions, mergeResolvers, mergeTypeDefs } from '@graphql-tools/merge';\nimport { asArray } from '@graphql-tools/utils';\nimport { addResolversToSchema } from './addResolversToSchema.js';\nimport { assertResolversPresent } from './assertResolversPresent.js';\n/**\n * Builds a schema from the provided type definitions and resolvers.\n *\n * The type definitions are written using Schema Definition Language (SDL). They\n * can be provided as a string, a `DocumentNode`, a function, or an array of any\n * of these. If a function is provided, it will be passed no arguments and\n * should return an array of strings or `DocumentNode`s.\n *\n * Note: You can use GraphQL magic comment provide additional syntax\n * highlighting in your editor (with the appropriate editor plugin).\n *\n * ```js\n * const typeDefs = /* GraphQL *\\/ `\n *   type Query {\n *     posts: [Post]\n *     author(id: Int!): Author\n *   }\n * `;\n * ```\n *\n * The `resolvers` object should be a map of type names to nested object, which\n * themselves map the type's fields to their appropriate resolvers.\n * See the [Resolvers](/docs/resolvers) section of the documentation for more details.\n *\n * ```js\n * const resolvers = {\n *   Query: {\n *     posts: (obj, args, ctx, info) => getAllPosts(),\n *     author: (obj, args, ctx, info) => getAuthorById(args.id)\n *   }\n * };\n * ```\n *\n * Once you've defined both the `typeDefs` and `resolvers`, you can create your\n * schema:\n *\n * ```js\n * const schema = makeExecutableSchema({\n *   typeDefs,\n *   resolvers,\n * })\n * ```\n */\nexport function makeExecutableSchema({ typeDefs, resolvers = {}, resolverValidationOptions = {}, inheritResolversFromInterfaces = false, updateResolversInPlace = false, schemaExtensions, defaultFieldResolver, ...otherOptions }) {\n    // Validate and clean up arguments\n    if (typeof resolverValidationOptions !== 'object') {\n        throw new Error('Expected `resolverValidationOptions` to be an object');\n    }\n    if (!typeDefs) {\n        throw new Error('Must provide typeDefs');\n    }\n    let schema;\n    if (isSchema(typeDefs)) {\n        schema = typeDefs;\n    }\n    else if (otherOptions?.commentDescriptions) {\n        const mergedTypeDefs = mergeTypeDefs(typeDefs, {\n            ...otherOptions,\n            commentDescriptions: true,\n        });\n        schema = buildSchema(mergedTypeDefs, otherOptions);\n    }\n    else {\n        const mergedTypeDefs = mergeTypeDefs(typeDefs, otherOptions);\n        schema = buildASTSchema(mergedTypeDefs, otherOptions);\n    }\n    // We allow passing in an array of resolver maps, in which case we merge them\n    schema = addResolversToSchema({\n        schema,\n        resolvers: mergeResolvers(resolvers),\n        resolverValidationOptions,\n        inheritResolversFromInterfaces,\n        updateResolversInPlace,\n        defaultFieldResolver,\n    });\n    if (Object.keys(resolverValidationOptions).length > 0) {\n        assertResolversPresent(schema, resolverValidationOptions);\n    }\n    if (schemaExtensions) {\n        for (const schemaExtension of asArray(schemaExtensions)) {\n            applyExtensions(schema, schemaExtension);\n        }\n    }\n    return schema;\n}\n", "import { mergeDeep } from '@graphql-tools/utils';\n/**\n * Deep merges multiple resolver definition objects into a single definition.\n * @param resolversDefinitions Resolver definitions to be merged\n * @param options Additional options\n *\n * ```js\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const clientResolver = require('./clientResolver');\n * const productResolver = require('./productResolver');\n *\n * const resolvers = mergeResolvers([\n *  clientResolver,\n *  productResolver,\n * ]);\n * ```\n *\n * If you don't want to manually create the array of resolver objects, you can\n * also use this function along with loadFiles:\n *\n * ```js\n * const path = require('path');\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const { loadFilesSync } = require('@graphql-tools/load-files');\n *\n * const resolversArray = loadFilesSync(path.join(__dirname, './resolvers'));\n *\n * const resolvers = mergeResolvers(resolversArray)\n * ```\n */\nexport function mergeResolvers(resolversDefinitions, options) {\n    if (!resolversDefinitions ||\n        (Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0)) {\n        return {};\n    }\n    if (!Array.isArray(resolversDefinitions)) {\n        return resolversDefinitions;\n    }\n    if (resolversDefinitions.length === 1) {\n        return resolversDefinitions[0] || {};\n    }\n    const resolvers = new Array();\n    for (let resolversDefinition of resolversDefinitions) {\n        if (Array.isArray(resolversDefinition)) {\n            resolversDefinition = mergeResolvers(resolversDefinition);\n        }\n        if (typeof resolversDefinition === 'object' && resolversDefinition) {\n            resolvers.push(resolversDefinition);\n        }\n    }\n    const result = mergeDeep(resolvers, true);\n    if (options?.exclusions) {\n        for (const exclusion of options.exclusions) {\n            const [typeName, fieldName] = exclusion.split('.');\n            if (['__proto__', 'constructor', 'prototype'].includes(typeName) ||\n                ['__proto__', 'constructor', 'prototype'].includes(fieldName)) {\n                continue;\n            }\n            if (!fieldName || fieldName === '*') {\n                delete result[typeName];\n            }\n            else if (result[typeName]) {\n                delete result[typeName][fieldName];\n            }\n        }\n    }\n    return result;\n}\n", "import { compareNodes, isSome } from '@graphql-tools/utils';\nexport function mergeArguments(args1, args2, config) {\n    const result = deduplicateArguments([...args2, ...args1].filter(isSome), config);\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\nfunction deduplicateArguments(args, config) {\n    return args.reduce((acc, current) => {\n        const dupIndex = acc.findIndex(arg => arg.name.value === current.name.value);\n        if (dupIndex === -1) {\n            return acc.concat([current]);\n        }\n        else if (!config?.reverseArguments) {\n            acc[dupIndex] = current;\n        }\n        return acc;\n    }, []);\n}\n", "import { isSome } from '@graphql-tools/utils';\nfunction directiveAlreadyExists(directivesArr, otherDirective) {\n    return !!directivesArr.find(directive => directive.name.value === otherDirective.name.value);\n}\nfunction isRepeatableDirective(directive, directives) {\n    return !!directives?.[directive.name.value]?.repeatable;\n}\nfunction nameAlreadyExists(name, namesArr) {\n    return namesArr.some(({ value }) => value === name.value);\n}\nfunction mergeArguments(a1, a2) {\n    const result = [...a2];\n    for (const argument of a1) {\n        const existingIndex = result.findIndex(a => a.name.value === argument.name.value);\n        if (existingIndex > -1) {\n            const existingArg = result[existingIndex];\n            if (existingArg.value.kind === 'ListValue') {\n                const source = existingArg.value.values;\n                const target = argument.value.values;\n                // merge values of two lists\n                existingArg.value.values = deduplicateLists(source, target, (targetVal, source) => {\n                    const value = targetVal.value;\n                    return !value || !source.some((sourceVal) => sourceVal.value === value);\n                });\n            }\n            else {\n                existingArg.value = argument.value;\n            }\n        }\n        else {\n            result.push(argument);\n        }\n    }\n    return result;\n}\nfunction deduplicateDirectives(directives, definitions) {\n    return directives\n        .map((directive, i, all) => {\n        const firstAt = all.findIndex(d => d.name.value === directive.name.value);\n        if (firstAt !== i && !isRepeatableDirective(directive, definitions)) {\n            const dup = all[firstAt];\n            directive.arguments = mergeArguments(directive.arguments, dup.arguments);\n            return null;\n        }\n        return directive;\n    })\n        .filter(isSome);\n}\nexport function mergeDirectives(d1 = [], d2 = [], config, directives) {\n    const reverseOrder = config && config.reverseDirectives;\n    const asNext = reverseOrder ? d1 : d2;\n    const asFirst = reverseOrder ? d2 : d1;\n    const result = deduplicateDirectives([...asNext], directives);\n    for (const directive of asFirst) {\n        if (directiveAlreadyExists(result, directive) &&\n            !isRepeatableDirective(directive, directives)) {\n            const existingDirectiveIndex = result.findIndex(d => d.name.value === directive.name.value);\n            const existingDirective = result[existingDirectiveIndex];\n            result[existingDirectiveIndex].arguments = mergeArguments(directive.arguments || [], existingDirective.arguments || []);\n        }\n        else {\n            result.push(directive);\n        }\n    }\n    return result;\n}\nexport function mergeDirective(node, existingNode) {\n    if (existingNode) {\n        return {\n            ...node,\n            arguments: deduplicateLists(existingNode.arguments || [], node.arguments || [], (arg, existingArgs) => !nameAlreadyExists(arg.name, existingArgs.map(a => a.name))),\n            locations: [\n                ...existingNode.locations,\n                ...node.locations.filter(name => !nameAlreadyExists(name, existingNode.locations)),\n            ],\n        };\n    }\n    return node;\n}\nfunction deduplicateLists(source, target, filterFn) {\n    return source.concat(target.filter(val => filterFn(val, source)));\n}\n", "import { compareNodes } from '@graphql-tools/utils';\nimport { mergeDirectives } from './directives.js';\nexport function mergeEnumValues(first, second, config, directives) {\n    if (config?.consistentEnumMerge) {\n        const reversed = [];\n        if (first) {\n            reversed.push(...first);\n        }\n        first = second;\n        second = reversed;\n    }\n    const enumValueMap = new Map();\n    if (first) {\n        for (const firstValue of first) {\n            enumValueMap.set(firstValue.name.value, firstValue);\n        }\n    }\n    if (second) {\n        for (const secondValue of second) {\n            const enumValue = secondValue.name.value;\n            if (enumValueMap.has(enumValue)) {\n                const firstValue = enumValueMap.get(enumValue);\n                firstValue.description = secondValue.description || firstValue.description;\n                firstValue.directives = mergeDirectives(secondValue.directives, firstValue.directives, directives);\n            }\n            else {\n                enumValueMap.set(enumValue, secondValue);\n            }\n        }\n    }\n    const result = [...enumValueMap.values()];\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\n", "import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeEnumValues } from './enum-values.js';\nexport function mergeEnum(e1, e2, config, directives) {\n    if (e2) {\n        return {\n            name: e1.name,\n            description: e1['description'] || e2['description'],\n            kind: config?.convertExtensions ||\n                e1.kind === 'EnumTypeDefinition' ||\n                e2.kind === 'EnumTypeDefinition'\n                ? 'EnumTypeDefinition'\n                : 'EnumTypeExtension',\n            loc: e1.loc,\n            directives: mergeDirectives(e1.directives, e2.directives, config, directives),\n            values: mergeEnumValues(e1.values, e2.values, config),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...e1,\n            kind: Kind.ENUM_TYPE_DEFINITION,\n        }\n        : e1;\n}\n", "import { Kind, Source } from 'graphql';\nexport function isStringTypes(types) {\n    return typeof types === 'string';\n}\nexport function isSourceTypes(types) {\n    return types instanceof Source;\n}\nexport function extractType(type) {\n    let visitedType = type;\n    while (visitedType.kind === Kind.LIST_TYPE || visitedType.kind === 'NonNullType') {\n        visitedType = visitedType.type;\n    }\n    return visitedType;\n}\nexport function isWrappingTypeNode(type) {\n    return type.kind !== Kind.NAMED_TYPE;\n}\nexport function isListTypeNode(type) {\n    return type.kind === Kind.LIST_TYPE;\n}\nexport function isNonNullTypeNode(type) {\n    return type.kind === Kind.NON_NULL_TYPE;\n}\nexport function printTypeNode(type) {\n    if (isListTypeNode(type)) {\n        return `[${printTypeNode(type.type)}]`;\n    }\n    if (isNonNullTypeNode(type)) {\n        return `${printTypeNode(type.type)}!`;\n    }\n    return type.name.value;\n}\nexport var CompareVal;\n(function (CompareVal) {\n    CompareVal[CompareVal[\"A_SMALLER_THAN_B\"] = -1] = \"A_SMALLER_THAN_B\";\n    CompareVal[CompareVal[\"A_EQUALS_B\"] = 0] = \"A_EQUALS_B\";\n    CompareVal[CompareVal[\"A_GREATER_THAN_B\"] = 1] = \"A_GREATER_THAN_B\";\n})(CompareVal || (CompareVal = {}));\nexport function defaultStringComparator(a, b) {\n    if (a == null && b == null) {\n        return CompareVal.A_EQUALS_B;\n    }\n    if (a == null) {\n        return CompareVal.A_SMALLER_THAN_B;\n    }\n    if (b == null) {\n        return CompareVal.A_GREATER_THAN_B;\n    }\n    if (a < b)\n        return CompareVal.A_SMALLER_THAN_B;\n    if (a > b)\n        return CompareVal.A_GREATER_THAN_B;\n    return CompareVal.A_EQUALS_B;\n}\n", "import { compareNodes } from '@graphql-tools/utils';\nimport { mergeArguments } from './arguments.js';\nimport { mergeDirectives } from './directives.js';\nimport { extractType, isListTypeNode, isNonNullTypeNode, isWrappingTypeNode, printTypeNode, } from './utils.js';\nfunction fieldAlreadyExists(fieldsArr, otherField) {\n    const resultIndex = fieldsArr.findIndex(field => field.name.value === otherField.name.value);\n    return [resultIndex > -1 ? fieldsArr[resultIndex] : null, resultIndex];\n}\nexport function mergeFields(type, f1, f2, config, directives) {\n    const result = [];\n    if (f2 != null) {\n        result.push(...f2);\n    }\n    if (f1 != null) {\n        for (const field of f1) {\n            const [existing, existingIndex] = fieldAlreadyExists(result, field);\n            if (existing && !config?.ignoreFieldConflicts) {\n                const newField = (config?.onFieldTypeConflict &&\n                    config.onFieldTypeConflict(existing, field, type, config?.throwOnConflict)) ||\n                    preventConflicts(type, existing, field, config?.throwOnConflict);\n                newField.arguments = mergeArguments(field['arguments'] || [], existing['arguments'] || [], config);\n                newField.directives = mergeDirectives(field.directives, existing.directives, config, directives);\n                newField.description = field.description || existing.description;\n                result[existingIndex] = newField;\n            }\n            else {\n                result.push(field);\n            }\n        }\n    }\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    if (config && config.exclusions) {\n        const exclusions = config.exclusions;\n        return result.filter(field => !exclusions.includes(`${type.name.value}.${field.name.value}`));\n    }\n    return result;\n}\nfunction preventConflicts(type, a, b, ignoreNullability = false) {\n    const aType = printTypeNode(a.type);\n    const bType = printTypeNode(b.type);\n    if (aType !== bType) {\n        const t1 = extractType(a.type);\n        const t2 = extractType(b.type);\n        if (t1.name.value !== t2.name.value) {\n            throw new Error(`Field \"${b.name.value}\" already defined with a different type. Declared as \"${t1.name.value}\", but you tried to override with \"${t2.name.value}\"`);\n        }\n        if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {\n            throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);\n        }\n    }\n    if (isNonNullTypeNode(b.type) && !isNonNullTypeNode(a.type)) {\n        a.type = b.type;\n    }\n    return a;\n}\nfunction safeChangeForFieldType(oldType, newType, ignoreNullability = false) {\n    // both are named\n    if (!isWrappingTypeNode(oldType) && !isWrappingTypeNode(newType)) {\n        return oldType.toString() === newType.toString();\n    }\n    // new is non-null\n    if (isNonNullTypeNode(newType)) {\n        const ofType = isNonNullTypeNode(oldType) ? oldType.type : oldType;\n        return safeChangeForFieldType(ofType, newType.type);\n    }\n    // old is non-null\n    if (isNonNullTypeNode(oldType)) {\n        return safeChangeForFieldType(newType, oldType, ignoreNullability);\n    }\n    // old is list\n    if (isListTypeNode(oldType)) {\n        return ((isListTypeNode(newType) && safeChangeForFieldType(oldType.type, newType.type)) ||\n            (isNonNullTypeNode(newType) && safeChangeForFieldType(oldType, newType['type'])));\n    }\n    return false;\n}\n", "import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nexport function mergeInputType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'InputObjectTypeDefinition' ||\n                    existingNode.kind === 'InputObjectTypeDefinition'\n                    ? 'InputObjectTypeDefinition'\n                    : 'InputObjectTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL input type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n", "import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeInterface(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'InterfaceTypeDefinition' ||\n                    existingNode.kind === 'InterfaceTypeDefinition'\n                    ? 'InterfaceTypeDefinition'\n                    : 'InterfaceTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config, directives),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n                interfaces: node['interfaces']\n                    ? mergeNamedTypeArray(node['interfaces'], existingNode['interfaces'], config)\n                    : undefined,\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL interface \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.INTERFACE_TYPE_DEFINITION,\n        }\n        : node;\n}\n", "import { compareNodes } from '@graphql-tools/utils';\nfunction alreadyExists(arr, other) {\n    return !!arr.find(i => i.name.value === other.name.value);\n}\nexport function mergeNamedTypeArray(first = [], second = [], config = {}) {\n    const result = [...second, ...first.filter(d => !alreadyExists(second, d))];\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\n", "import { Kind, } from 'graphql';\nimport { collectComment } from '@graphql-tools/utils';\nimport { mergeDirective } from './directives.js';\nimport { mergeEnum } from './enum.js';\nimport { mergeInputType } from './input-type.js';\nimport { mergeInterface } from './interface.js';\nimport { mergeScalar } from './scalar.js';\nimport { mergeSchemaDefs } from './schema-def.js';\nimport { mergeType } from './type.js';\nimport { mergeUnion } from './union.js';\nexport const schemaDefSymbol = 'SCHEMA_DEF_SYMBOL';\nexport function isNamedDefinitionNode(definitionNode) {\n    return 'name' in definitionNode;\n}\nexport function mergeGraphQLNodes(nodes, config, directives = {}) {\n    const mergedResultMap = directives;\n    for (const nodeDefinition of nodes) {\n        if (isNamedDefinitionNode(nodeDefinition)) {\n            const name = nodeDefinition.name?.value;\n            if (config?.commentDescriptions) {\n                collectComment(nodeDefinition);\n            }\n            if (name == null) {\n                continue;\n            }\n            if (config?.exclusions?.includes(name + '.*') || config?.exclusions?.includes(name)) {\n                delete mergedResultMap[name];\n            }\n            else {\n                switch (nodeDefinition.kind) {\n                    case Kind.OBJECT_TYPE_DEFINITION:\n                    case Kind.OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeType(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.ENUM_TYPE_DEFINITION:\n                    case Kind.ENUM_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeEnum(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.UNION_TYPE_DEFINITION:\n                    case Kind.UNION_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeUnion(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.SCALAR_TYPE_DEFINITION:\n                    case Kind.SCALAR_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeScalar(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n                    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeInputType(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.INTERFACE_TYPE_DEFINITION:\n                    case Kind.INTERFACE_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeInterface(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.DIRECTIVE_DEFINITION:\n                        if (mergedResultMap[name]) {\n                            const isInheritedFromPrototype = name in {}; // i.e. toString\n                            if (isInheritedFromPrototype) {\n                                if (!isASTNode(mergedResultMap[name])) {\n                                    mergedResultMap[name] = undefined;\n                                }\n                            }\n                        }\n                        mergedResultMap[name] = mergeDirective(nodeDefinition, mergedResultMap[name]);\n                        break;\n                }\n            }\n        }\n        else if (nodeDefinition.kind === Kind.SCHEMA_DEFINITION ||\n            nodeDefinition.kind === Kind.SCHEMA_EXTENSION) {\n            mergedResultMap[schemaDefSymbol] = mergeSchemaDefs(nodeDefinition, mergedResultMap[schemaDefSymbol], config);\n        }\n    }\n    return mergedResultMap;\n}\nfunction isASTNode(node) {\n    return (node != null && typeof node === 'object' && 'kind' in node && typeof node.kind === 'string');\n}\n", "import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nexport function mergeScalar(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            name: node.name,\n            description: node['description'] || existingNode['description'],\n            kind: config?.convertExtensions ||\n                node.kind === 'ScalarTypeDefinition' ||\n                existingNode.kind === 'ScalarTypeDefinition'\n                ? 'ScalarTypeDefinition'\n                : 'ScalarTypeExtension',\n            loc: node.loc,\n            directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.SCALAR_TYPE_DEFINITION,\n        }\n        : node;\n}\n", "import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nexport const DEFAULT_OPERATION_TYPE_NAME_MAP = {\n    query: 'Query',\n    mutation: 'Mutation',\n    subscription: 'Subscription',\n};\nfunction mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {\n    const finalOpNodeList = [];\n    for (const opNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {\n        const opNode = opNodeList.find(n => n.operation === opNodeType) ||\n            existingOpNodeList.find(n => n.operation === opNodeType);\n        if (opNode) {\n            finalOpNodeList.push(opNode);\n        }\n    }\n    return finalOpNodeList;\n}\nexport function mergeSchemaDefs(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            kind: node.kind === Kind.SCHEMA_DEFINITION || existingNode.kind === Kind.SCHEMA_DEFINITION\n                ? Kind.SCHEMA_DEFINITION\n                : Kind.SCHEMA_EXTENSION,\n            description: node['description'] || existingNode['description'],\n            directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n            operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes),\n        };\n    }\n    return (config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.SCHEMA_DEFINITION,\n        }\n        : node);\n}\n", "import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'ObjectTypeDefinition' ||\n                    existingNode.kind === 'ObjectTypeDefinition'\n                    ? 'ObjectTypeDefinition'\n                    : 'ObjectTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config, directives),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n                interfaces: mergeNamedTypeArray(node.interfaces, existingNode.interfaces, config),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n", "import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeUnion(first, second, config, directives) {\n    if (second) {\n        return {\n            name: first.name,\n            description: first['description'] || second['description'],\n            // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n            directives: mergeDirectives(first.directives, second.directives, config, directives),\n            kind: config?.convertExtensions ||\n                first.kind === 'UnionTypeDefinition' ||\n                second.kind === 'UnionTypeDefinition'\n                ? Kind.UNION_TYPE_DEFINITION\n                : Kind.UNION_TYPE_EXTENSION,\n            loc: first.loc,\n            types: mergeNamedTypeArray(first.types, second.types, config),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...first,\n            kind: Kind.UNION_TYPE_DEFINITION,\n        }\n        : first;\n}\n", "import { isDefinitionNode, isSchema, Kind, parse, } from 'graphql';\nimport { getDocumentNodeFromSchema, isDocumentNode, printWithComments, resetComments, } from '@graphql-tools/utils';\nimport { mergeGraphQLNodes, schemaDefSymbol } from './merge-nodes.js';\nimport { DEFAULT_OPERATION_TYPE_NAME_MAP } from './schema-def.js';\nimport { defaultStringComparator, isSourceTypes, isStringTypes } from './utils.js';\nexport function mergeTypeDefs(typeSource, config) {\n    resetComments();\n    const doc = {\n        kind: Kind.DOCUMENT,\n        definitions: mergeGraphQLTypes(typeSource, {\n            useSchemaDefinition: true,\n            forceSchemaDefinition: false,\n            throwOnConflict: false,\n            commentDescriptions: false,\n            ...config,\n        }),\n    };\n    let result;\n    if (config?.commentDescriptions) {\n        result = printWithComments(doc);\n    }\n    else {\n        result = doc;\n    }\n    resetComments();\n    return result;\n}\nfunction visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = new Set()) {\n    if (typeSource && !visitedTypeSources.has(typeSource)) {\n        visitedTypeSources.add(typeSource);\n        if (typeof typeSource === 'function') {\n            visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (Array.isArray(typeSource)) {\n            for (const type of typeSource) {\n                visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources);\n            }\n        }\n        else if (isSchema(typeSource)) {\n            const documentNode = getDocumentNodeFromSchema(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (isStringTypes(typeSource) || isSourceTypes(typeSource)) {\n            const documentNode = parse(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (typeof typeSource === 'object' && isDefinitionNode(typeSource)) {\n            if (typeSource.kind === Kind.DIRECTIVE_DEFINITION) {\n                allDirectives.push(typeSource);\n            }\n            else {\n                allNodes.push(typeSource);\n            }\n        }\n        else if (isDocumentNode(typeSource)) {\n            visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else {\n            throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);\n        }\n    }\n    return { allDirectives, allNodes };\n}\nexport function mergeGraphQLTypes(typeSource, config) {\n    resetComments();\n    const { allDirectives, allNodes } = visitTypeSources(typeSource, config);\n    const mergedDirectives = mergeGraphQLNodes(allDirectives, config);\n    const mergedNodes = mergeGraphQLNodes(allNodes, config, mergedDirectives);\n    if (config?.useSchemaDefinition) {\n        // XXX: right now we don't handle multiple schema definitions\n        const schemaDef = mergedNodes[schemaDefSymbol] || {\n            kind: Kind.SCHEMA_DEFINITION,\n            operationTypes: [],\n        };\n        const operationTypes = schemaDef.operationTypes;\n        for (const opTypeDefNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {\n            const opTypeDefNode = operationTypes.find(operationType => operationType.operation === opTypeDefNodeType);\n            if (!opTypeDefNode) {\n                const possibleRootTypeName = DEFAULT_OPERATION_TYPE_NAME_MAP[opTypeDefNodeType];\n                const existingPossibleRootType = mergedNodes[possibleRootTypeName];\n                if (existingPossibleRootType != null && existingPossibleRootType.name != null) {\n                    operationTypes.push({\n                        kind: Kind.OPERATION_TYPE_DEFINITION,\n                        type: {\n                            kind: Kind.NAMED_TYPE,\n                            name: existingPossibleRootType.name,\n                        },\n                        operation: opTypeDefNodeType,\n                    });\n                }\n            }\n        }\n        if (schemaDef?.operationTypes?.length != null && schemaDef.operationTypes.length > 0) {\n            mergedNodes[schemaDefSymbol] = schemaDef;\n        }\n    }\n    if (config?.forceSchemaDefinition && !mergedNodes[schemaDefSymbol]?.operationTypes?.length) {\n        mergedNodes[schemaDefSymbol] = {\n            kind: Kind.SCHEMA_DEFINITION,\n            operationTypes: [\n                {\n                    kind: Kind.OPERATION_TYPE_DEFINITION,\n                    operation: 'query',\n                    type: {\n                        kind: Kind.NAMED_TYPE,\n                        name: {\n                            kind: Kind.NAME,\n                            value: 'Query',\n                        },\n                    },\n                },\n            ],\n        };\n    }\n    const mergedNodeDefinitions = Object.values(mergedNodes);\n    if (config?.sort) {\n        const sortFn = typeof config.sort === 'function' ? config.sort : defaultStringComparator;\n        mergedNodeDefinitions.sort((a, b) => sortFn(a.name?.value, b.name?.value));\n    }\n    return mergedNodeDefinitions;\n}\n", "import { mergeDeep } from '@graphql-tools/utils';\nexport { extractExtensionsFromSchema } from '@graphql-tools/utils';\nexport function mergeExtensions(extensions) {\n    return mergeDeep(extensions, false, true);\n}\nfunction applyExtensionObject(obj, extensions) {\n    if (!obj || !extensions || extensions === obj.extensions) {\n        return;\n    }\n    if (!obj.extensions) {\n        obj.extensions = extensions;\n        return;\n    }\n    obj.extensions = mergeDeep([obj.extensions, extensions], false, true);\n}\nexport function applyExtensions(schema, extensions) {\n    applyExtensionObject(schema, extensions.schemaExtensions);\n    for (const [typeName, data] of Object.entries(extensions.types || {})) {\n        const type = schema.getType(typeName);\n        if (type) {\n            applyExtensionObject(type, data.extensions);\n            if (data.type === 'object' || data.type === 'interface') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    if (field) {\n                        applyExtensionObject(field, fieldData.extensions);\n                        for (const [arg, argData] of Object.entries(fieldData.arguments)) {\n                            applyExtensionObject(field.args.find(a => a.name === arg), argData);\n                        }\n                    }\n                }\n            }\n            else if (data.type === 'input') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    applyExtensionObject(field, fieldData.extensions);\n                }\n            }\n            else if (data.type === 'enum') {\n                for (const [valueName, valueData] of Object.entries(data.values)) {\n                    const value = type.getValue(valueName);\n                    applyExtensionObject(value, valueData);\n                }\n            }\n        }\n    }\n    return schema;\n}\n", "import { asArray, extractExtensionsFromSchema, getDocumentNodeFromSchema, getResolversFromSchema, } from '@graphql-tools/utils';\nimport { makeExecutableSchema } from './makeExecutableSchema.js';\n/**\n * Synchronously merges multiple schemas, typeDefinitions and/or resolvers into a single schema.\n * @param config Configuration object\n */\nexport function mergeSchemas(config) {\n    const extractedTypeDefs = [];\n    const extractedResolvers = [];\n    const extractedSchemaExtensions = [];\n    if (config.schemas != null) {\n        for (const schema of config.schemas) {\n            extractedTypeDefs.push(getDocumentNodeFromSchema(schema));\n            extractedResolvers.push(getResolversFromSchema(schema));\n            extractedSchemaExtensions.push(extractExtensionsFromSchema(schema));\n        }\n    }\n    if (config.typeDefs != null) {\n        extractedTypeDefs.push(config.typeDefs);\n    }\n    if (config.resolvers != null) {\n        const additionalResolvers = asArray(config.resolvers);\n        extractedResolvers.push(...additionalResolvers);\n    }\n    if (config.schemaExtensions != null) {\n        const additionalSchemaExtensions = asArray(config.schemaExtensions);\n        extractedSchemaExtensions.push(...additionalSchemaExtensions);\n    }\n    return makeExecutableSchema({\n        ...config,\n        typeDefs: extractedTypeDefs,\n        resolvers: extractedResolvers,\n        schemaExtensions: extractedSchemaExtensions,\n    });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;ACAA;AA8BO,IAAM,UAAU,CAAC,QAAS,MAAM,QAAQ,GAAG,IAAI,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC;AAsDpE,SAAS,eAAe,GAAG,GAAG;AACjC,MAAI,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG;AACvB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG;AACvB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACO,SAAS,aAAa,GAAG;AA7FhC;AA8FI,MAAI;AACJ,MAAI,WAAW,GAAG;AACd,YAAO,OAAE,UAAF,mBAAS;AAAA,EACpB;AACA,MAAI,QAAQ,QAAQ,UAAU,GAAG;AAC7B,YAAO,OAAE,SAAF,mBAAQ;AAAA,EACnB;AACA,MAAI,QAAQ,MAAM;AACd,WAAO,EAAE;AAAA,EACb;AACA,SAAO;AACX;AACO,SAAS,aAAa,GAAG,GAAG,UAAU;AACzC,QAAM,OAAO,aAAa,CAAC;AAC3B,QAAM,OAAO,aAAa,CAAC;AAC3B,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,MAAM,IAAI;AAAA,EAC9B;AACA,SAAO,eAAe,MAAM,IAAI;AACpC;AACO,SAAS,OAAO,OAAO;AAC1B,SAAO,SAAS;AACpB;;;ACpHA;;;ACEA,IAAM,sBAAsB;AAIrB,SAAS,QAAQ,OAAO;AAC3B,SAAO,YAAY,OAAO,CAAC,CAAC;AAChC;AACA,SAAS,YAAY,OAAO,YAAY;AACpC,UAAQ,OAAO,OAAO;AAAA,IAClB,KAAK;AACD,aAAO,KAAK,UAAU,KAAK;AAAA,IAC/B,KAAK;AACD,aAAO,MAAM,OAAO,aAAa,MAAM,IAAI,MAAM;AAAA,IACrD,KAAK;AACD,aAAO,kBAAkB,OAAO,UAAU;AAAA,IAC9C;AACI,aAAO,OAAO,KAAK;AAAA,EAC3B;AACJ;AACA,SAAS,YAAY,OAAO;AAExB,MAAK,MAAM,OAAO,gBAAiB;AAC/B,WAAO,MAAM,SAAS;AAAA,EAC1B;AACA,SAAO,GAAG,MAAM,IAAI,KAAK,MAAM,OAAO;AAAA,GAAO,MAAM,KAAK;AAC5D;AACA,SAAS,kBAAkB,OAAO,sBAAsB;AACpD,MAAI,UAAU,MAAM;AAChB,WAAO;AAAA,EACX;AACA,MAAI,iBAAiB,OAAO;AACxB,QAAI,MAAM,SAAS,kBAAkB;AACjC,aAAQ,YAAY,KAAK,IACrB,OACA,YAAY,MAAM,QAAQ,oBAAoB;AAAA,IACtD;AACA,WAAO,YAAY,KAAK;AAAA,EAC5B;AACA,MAAI,qBAAqB,SAAS,KAAK,GAAG;AACtC,WAAO;AAAA,EACX;AACA,QAAM,aAAa,CAAC,GAAG,sBAAsB,KAAK;AAClD,MAAI,WAAW,KAAK,GAAG;AACnB,UAAM,YAAY,MAAM,OAAO;AAE/B,QAAI,cAAc,OAAO;AACrB,aAAO,OAAO,cAAc,WAAW,YAAY,YAAY,WAAW,UAAU;AAAA,IACxF;AAAA,EACJ,WACS,MAAM,QAAQ,KAAK,GAAG;AAC3B,WAAO,YAAY,OAAO,UAAU;AAAA,EACxC;AACA,SAAO,aAAa,OAAO,UAAU;AACzC;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,OAAO,MAAM,WAAW;AACnC;AACA,SAAS,aAAa,QAAQ,YAAY;AACtC,QAAM,UAAU,OAAO,QAAQ,MAAM;AACrC,MAAI,QAAQ,WAAW,GAAG;AACtB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,SAAS,qBAAqB;AACzC,WAAO,MAAM,aAAa,MAAM,IAAI;AAAA,EACxC;AACA,QAAM,aAAa,QAAQ,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,MAAM,OAAO,YAAY,OAAO,UAAU,CAAC;AAC5F,SAAO,OAAO,WAAW,KAAK,IAAI,IAAI;AAC1C;AACA,SAAS,YAAY,OAAO,YAAY;AACpC,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,SAAS,qBAAqB;AACzC,WAAO;AAAA,EACX;AACA,QAAM,MAAM,MAAM;AAClB,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,UAAM,KAAK,YAAY,MAAM,CAAC,GAAG,UAAU,CAAC;AAAA,EAChD;AACA,SAAO,MAAM,MAAM,KAAK,IAAI,IAAI;AACpC;AACA,SAAS,aAAa,QAAQ;AAC1B,QAAM,MAAM,OAAO,UAAU,SACxB,KAAK,MAAM,EACX,QAAQ,cAAc,EAAE,EACxB,QAAQ,MAAM,EAAE;AACrB,MAAI,QAAQ,YAAY,OAAO,OAAO,gBAAgB,YAAY;AAC9D,UAAM,OAAO,OAAO,YAAY;AAChC,QAAI,OAAO,SAAS,YAAY,SAAS,IAAI;AACzC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;AC/FA;;;ACDA;AACA,IAAM,iCAAiC;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,mBAAmB,OAAO;AAC/B,SAAQ,SAAS,QACb,OAAO,UAAU,YACjB,OAAO,KAAK,KAAK,EAAE,MAAM,SAAO,+BAA+B,SAAS,GAAG,CAAC;AACpF;AACO,SAAS,mBAAmB,SAAS,SAAS;AACjD,OAAI,mCAAS,kBACT,EAAE,QAAQ,yBAAyB,UACnC,mBAAmB,QAAQ,aAAa,GAAG;AAC3C,YAAQ,gBAAgB,mBAAmB,QAAQ,cAAc,SAAS,QAAQ,aAAa;AAAA,EACnG;AACA,MAAI,YAAY,SAAS,IAAI;AACzB,WAAO,IAAI,aAAa,SAAS,OAAO;AAAA,EAC5C;AACA,SAAO,IAAI,aAAa,SAAS,mCAAS,OAAO,mCAAS,QAAQ,mCAAS,WAAW,mCAAS,MAAM,mCAAS,eAAe,mCAAS,UAAU;AACpJ;;;AC5BA,IAAM,eAAe,OAAO,IAAI,0CAA0C;AACnE,SAAS,UAAU,OAAO;AAC7B,UAAO,+BAAO,SAAQ;AAC1B;AACO,SAAS,gBAAgB,OAAO;AACnC,QAAM,eAAe;AACrB,SAAO,gBAAgB,aAAa,QAAQ,aAAa,SAAS,aAAa;AACnF;AAQO,SAAS,YAAY,OAAO;AAC/B,MAAI,SAAS,gBAAgB,KAAK,GAAG;AACjC,WAAO;AAAA,EACX;AACA,MAAI,UAAU,KAAK,GAAG;AAClB,WAAO;AAAA,MACH,MAAM,CAAC,SAAS,WAAW,YAAY,MAAM,KAAK,SAAS,MAAM,CAAC;AAAA,MAClE,OAAO,YAAU,YAAY,MAAM,KAAK,SAAO,KAAK,MAAM,CAAC;AAAA,MAC3D,SAAS,QAAM,YAAY,KAAK,mBAAmB,OAAO,EAAE,IAAI,KAAK;AAAA,MACrE,CAAC,OAAO,WAAW,GAAG;AAAA,IAC1B;AAAA,EACJ;AAGA,SAAO;AAAA,IACH,KAAK,SAAS;AACV,UAAI,SAAS;AACT,YAAI;AACA,iBAAO,YAAY,QAAQ,KAAK,CAAC;AAAA,QACrC,SACO,KAAK;AACR,iBAAO,kBAAkB,GAAG;AAAA,QAChC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,QAAQ;AACJ,aAAO;AAAA,IACX;AAAA,IACA,QAAQ,IAAI;AACR,UAAI,IAAI;AACJ,YAAI;AACA,iBAAO,YAAY,GAAG,CAAC,EAAE,KAAK,MAAM,OAAO,MAAM,KAAK;AAAA,QAC1D,SACO,KAAK;AACR,iBAAO,kBAAkB,GAAG;AAAA,QAChC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,CAAC,OAAO,WAAW,GAAG;AAAA,IACtB,oBAAoB;AAAA,IACpB,CAAC,YAAY,GAAG;AAAA,EACpB;AACJ;AAmDO,SAAS,kBAAkB,OAAO;AACrC,SAAO;AAAA,IACH,KAAK,UAAU,QAAQ;AACnB,UAAI,QAAQ;AACR,YAAI;AACA,iBAAO,YAAY,OAAO,KAAK,CAAC;AAAA,QACpC,SACO,KAAK;AACR,iBAAO,kBAAkB,GAAG;AAAA,QAChC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,MAAM,QAAQ;AACV,UAAI,QAAQ;AACR,YAAI;AACA,iBAAO,YAAY,OAAO,KAAK,CAAC;AAAA,QACpC,SACO,KAAK;AACR,iBAAO,kBAAkB,GAAG;AAAA,QAChC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,QAAQ,IAAI;AACR,UAAI,IAAI;AACJ,YAAI;AACA,aAAG;AAAA,QACP,SACO,KAAK;AACR,iBAAO,kBAAkB,GAAG;AAAA,QAChC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,mBAAmB;AAAA,IACnB,CAAC,OAAO,WAAW,GAAG;AAAA,IACtB,CAAC,YAAY,GAAG;AAAA,EACpB;AACJ;AAgFO,SAAS,mBAAmB,OAAO,WAAW;AACjD,MAAI,aAAa,OAAO;AACpB,WAAO,MAAM,QAAQ,SAAS;AAAA,EAClC;AACA,SAAO,MAAM,KAAK,SAAO;AACrB,UAAM,aAAa,UAAU;AAC7B,WAAO,UAAU,UAAU,IAAI,WAAW,KAAK,MAAM,GAAG,IAAI;AAAA,EAChE,GAAG,SAAO;AACN,UAAM,aAAa,UAAU;AAC7B,QAAI,UAAU,UAAU,GAAG;AACvB,aAAO,WAAW,KAAK,MAAM;AACzB,cAAM;AAAA,MACV,CAAC;AAAA,IACL,OACK;AACD,YAAM;AAAA,IACV;AAAA,EACJ,CAAC;AACL;;;ACtPO,SAAS,iBAAiB,OAAO;AACpC,SAAO,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,YAAY;AAC5E;AACO,SAAS,aAAa,OAAO;AAChC,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AASO,SAAS,eAAe,KAAK,MAAM;AACtC,SAAO,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI;AACzD;;;AHLO,SAAS,kBAAkB,KAAK,MAAM,iBAAiB,CAAC,GAAG;AAC9D,QAAM,gBAAgB,CAAC;AACvB,QAAM,gBAAgB,KAAK,aAAa,CAAC;AACzC,QAAM,aAAa,cAAc,OAAO,CAAC,MAAM,SAAS;AAAA,IACpD,GAAG;AAAA,IACH,CAAC,IAAI,KAAK,KAAK,GAAG;AAAA,EACtB,IAAI,CAAC,CAAC;AACN,aAAW,EAAE,MAAM,MAAM,SAAS,aAAa,KAAK,IAAI,MAAM;AAC1D,UAAM,eAAe,WAAW,IAAI;AACpC,QAAI,CAAC,cAAc;AACf,UAAI,iBAAiB,QAAW;AAC5B,sBAAc,IAAI,IAAI;AAAA,MAC1B,WACS,cAAc,OAAO,GAAG;AAC7B,cAAM,mBAAmB,aAAa,IAAI,uBAAuB,QAAQ,OAAO,CAAC,uBAA4B;AAAA,UACzG,OAAO,CAAC,IAAI;AAAA,QAChB,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,UAAM,YAAY,aAAa;AAC/B,QAAI,SAAS,UAAU,SAAS,KAAK;AACrC,QAAI,UAAU,SAAS,KAAK,UAAU;AAClC,YAAM,eAAe,UAAU,KAAK;AACpC,UAAI,kBAAkB,QAAQ,CAAC,eAAe,gBAAgB,YAAY,GAAG;AACzE,YAAI,iBAAiB,QAAW;AAC5B,wBAAc,IAAI,IAAI;AAAA,QAC1B,WACS,cAAc,OAAO,GAAG;AAC7B,gBAAM,mBAAmB,aAAa,IAAI,uBAAuB,QAAQ,OAAO,CAAC,iCAC9C,YAAY,6CAA6C;AAAA,YACxF,OAAO,CAAC,SAAS;AAAA,UACrB,CAAC;AAAA,QACL;AACA;AAAA,MACJ;AACA,eAAS,eAAe,YAAY,KAAK;AAAA,IAC7C;AACA,QAAI,UAAU,cAAc,OAAO,GAAG;AAClC,YAAM,mBAAmB,aAAa,IAAI,uBAAuB,QAAQ,OAAO,CAAC,uBAA4B;AAAA,QACzG,OAAO,CAAC,SAAS;AAAA,MACrB,CAAC;AAAA,IACL;AACA,UAAM,eAAe,aAAa,WAAW,SAAS,cAAc;AACpE,QAAI,iBAAiB,QAAW;AAI5B,YAAM,mBAAmB,aAAa,IAAI,uBAAuB,MAAM,SAAS,CAAC,KAAK;AAAA,QAClF,OAAO,CAAC,SAAS;AAAA,MACrB,CAAC;AAAA,IACL;AACA,kBAAc,IAAI,IAAI;AAAA,EAC1B;AACA,SAAO;AACX;;;AInEO,SAAS,SAAS,IAAI;AACzB,QAAM,gBAAgB,oBAAI,QAAQ;AAClC,SAAO,SAAS,SAAS,IAAI;AACzB,UAAM,cAAc,cAAc,IAAI,EAAE;AACxC,QAAI,gBAAgB,QAAW;AAC3B,YAAM,WAAW,GAAG,EAAE;AACtB,oBAAc,IAAI,IAAI,QAAQ;AAC9B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AACO,SAAS,SAAS,IAAI;AACzB,QAAM,gBAAgB,oBAAI,QAAQ;AAClC,SAAO,SAAS,SAAS,IAAI,IAAI;AAC7B,QAAI,SAAS,cAAc,IAAI,EAAE;AACjC,QAAI,CAAC,QAAQ;AACT,eAAS,oBAAI,QAAQ;AACrB,oBAAc,IAAI,IAAI,MAAM;AAC5B,YAAM,WAAW,GAAG,IAAI,EAAE;AAC1B,aAAO,IAAI,IAAI,QAAQ;AACvB,aAAO;AAAA,IACX;AACA,UAAM,cAAc,OAAO,IAAI,EAAE;AACjC,QAAI,gBAAgB,QAAW;AAC3B,YAAM,WAAW,GAAG,IAAI,EAAE;AAC1B,aAAO,IAAI,IAAI,QAAQ;AACvB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AAyEO,SAAS,SAAS,IAAI;AACzB,QAAM,gBAAgB,oBAAI,QAAQ;AAClC,SAAO,SAAS,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI;AACzC,QAAI,SAAS,cAAc,IAAI,EAAE;AACjC,QAAI,CAAC,QAAQ;AACT,eAAS,oBAAI,QAAQ;AACrB,oBAAc,IAAI,IAAI,MAAM;AAC5B,YAAMA,UAAS,oBAAI,QAAQ;AAC3B,aAAO,IAAI,IAAIA,OAAM;AACrB,YAAMC,UAAS,oBAAI,QAAQ;AAC3B,MAAAD,QAAO,IAAI,IAAIC,OAAM;AACrB,YAAMC,UAAS,oBAAI,QAAQ;AAC3B,MAAAD,QAAO,IAAI,IAAIC,OAAM;AACrB,YAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AACtC,MAAAA,QAAO,IAAI,IAAI,QAAQ;AACvB,aAAO;AAAA,IACX;AACA,QAAI,SAAS,OAAO,IAAI,EAAE;AAC1B,QAAI,CAAC,QAAQ;AACT,eAAS,oBAAI,QAAQ;AACrB,aAAO,IAAI,IAAI,MAAM;AACrB,YAAMD,UAAS,oBAAI,QAAQ;AAC3B,aAAO,IAAI,IAAIA,OAAM;AACrB,YAAMC,UAAS,oBAAI,QAAQ;AAC3B,MAAAD,QAAO,IAAI,IAAIC,OAAM;AACrB,YAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AACtC,MAAAA,QAAO,IAAI,IAAI,QAAQ;AACvB,aAAO;AAAA,IACX;AACA,QAAI,SAAS,OAAO,IAAI,EAAE;AAC1B,QAAI,CAAC,QAAQ;AACT,eAAS,oBAAI,QAAQ;AACrB,aAAO,IAAI,IAAI,MAAM;AACrB,YAAMA,UAAS,oBAAI,QAAQ;AAC3B,aAAO,IAAI,IAAIA,OAAM;AACrB,YAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AACtC,MAAAA,QAAO,IAAI,IAAI,QAAQ;AACvB,aAAO;AAAA,IACX;AACA,QAAI,SAAS,OAAO,IAAI,EAAE;AAC1B,QAAI,CAAC,QAAQ;AACT,eAAS,oBAAI,QAAQ;AACrB,aAAO,IAAI,IAAI,MAAM;AACrB,YAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AACtC,aAAO,IAAI,IAAI,QAAQ;AACvB,aAAO;AAAA,IACX;AACA,UAAM,cAAc,OAAO,IAAI,EAAE;AACjC,QAAI,gBAAgB,QAAW;AAC3B,YAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AACtC,aAAO,IAAI,IAAI,QAAQ;AACvB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;;;AN5JO,SAAS,uBAAuB,eAAe,QAAQ,+BAA+B,CAAC,YAAY,GAAG;AAH7G;AAII,QAAM,sBAAsB,CAAC;AAC7B,MAAI,cAAc,YAAY;AAC1B,QAAI,yBAAyB,cAAc;AAC3C,eAAW,eAAe,8BAA8B;AACpD,+BAAyB,iEAAyB;AAAA,IACtD;AACA,QAAI,0BAA0B,MAAM;AAChC,iBAAW,qBAAqB,wBAAwB;AACpD,cAAM,gBAAgB,uBAAuB,iBAAiB;AAC9D,cAAM,gBAAgB;AACtB,YAAI,MAAM,QAAQ,aAAa,GAAG;AAC9B,qBAAW,gBAAgB,eAAe;AACtC,gBAAI,8BAA8B,oBAAoB,aAAa;AACnE,gBAAI,CAAC,6BAA6B;AAC9B,4CAA8B,CAAC;AAC/B,kCAAoB,aAAa,IAAI;AAAA,YACzC;AACA,wCAA4B,KAAK,YAAY;AAAA,UACjD;AAAA,QACJ,OACK;AACD,cAAI,8BAA8B,oBAAoB,aAAa;AACnE,cAAI,CAAC,6BAA6B;AAC9B,0CAA8B,CAAC;AAC/B,gCAAoB,aAAa,IAAI;AAAA,UACzC;AACA,sCAA4B,KAAK,aAAa;AAAA,QAClD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,oBAAoB,SAAS,SAAO,KAAK,UAAU,GAAG,CAAC;AAC7D,QAAM,WAAW,CAAC;AAClB,MAAI,cAAc,SAAS;AACvB,aAAS,KAAK,cAAc,OAAO;AAAA,EACvC;AACA,MAAI,cAAc,mBAAmB;AACjC,aAAS,KAAK,GAAG,cAAc,iBAAiB;AAAA,EACpD;AACA,aAAW,WAAW,UAAU;AAC5B,SAAI,aAAQ,eAAR,mBAAoB,QAAQ;AAC5B,iBAAW,aAAa,QAAQ,YAAY;AACxC,cAAM,gBAAgB,UAAU,KAAK;AACrC,YAAI,8BAA8B,oBAAoB,aAAa;AACnE,YAAI,CAAC,6BAA6B;AAC9B,wCAA8B,CAAC;AAC/B,8BAAoB,aAAa,IAAI;AAAA,QACzC;AACA,cAAM,oBAAoB,iCAAQ,aAAa;AAC/C,YAAI,QAAQ,CAAC;AACb,YAAI,mBAAmB;AACnB,kBAAQ,kBAAkB,mBAAmB,SAAS;AAAA,QAC1D;AACA,YAAI,UAAU,WAAW;AACrB,qBAAW,WAAW,UAAU,WAAW;AACvC,kBAAM,UAAU,QAAQ,KAAK;AAC7B,gBAAI,MAAM,OAAO,KAAK,MAAM;AACxB,oBAAM,iBAAiB,uDAAmB,KAAK,KAAK,SAAO,IAAI,SAAS;AACxE,kBAAI,gBAAgB;AAChB,sBAAM,OAAO,IAAI,aAAa,QAAQ,OAAO,eAAe,IAAI;AAAA,cACpE;AAAA,YACJ;AACA,gBAAI,MAAM,OAAO,KAAK,MAAM;AACxB,oBAAM,OAAO,IAAI,oBAAoB,QAAQ,KAAK;AAAA,YACtD;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,SAAS,SAAS,KAAK,4BAA4B,SAAS,GAAG;AAC/D,gBAAM,SAAS,kBAAkB,KAAK;AACtC,cAAI,4BAA4B,KAAK,SAAO,kBAAkB,GAAG,MAAM,MAAM,GAAG;AAC5E;AAAA,UACJ;AAAA,QACJ;AACA,oCAA4B,KAAK,KAAK;AAAA,MAC1C;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AOjFO,SAAS,0BAA0B,MAAM,+BAA+B,CAAC,YAAY,GAAG;AAC3F,QAAM,sBAAsB,uBAAuB,MAAM,QAAW,4BAA4B;AAChG,SAAO,OAAO,QAAQ,mBAAmB,EACpC,IAAI,CAAC,CAAC,eAAe,gBAAgB,MAAM,qDAAkB,IAAI,oBAAkB;AAAA,IACpF,MAAM;AAAA,IACN,MAAM;AAAA,EACV,GAAG,EACE,KAAK,QAAQ,EACb,OAAO,OAAO;AACvB;;;ACVA;;;ACAA;;;ACAA;;;ACCA;AACO,SAAS,YAAY,MAAM;AAC9B,MAAI,cAAc,IAAI,GAAG;AACrB,UAAM,YAAY,YAAY,KAAK,MAAM;AACzC,QAAI,UAAU,SAAS,KAAK,eAAe;AACvC,YAAM,IAAI,MAAM,qBAAqB,QAAQ,IAAI,CAAC,0DAA0D;AAAA,IAChH;AACA,WAAO;AAAA,MACH,MAAM,KAAK;AAAA,MACX,MAAM;AAAA,IACV;AAAA,EACJ,WACS,WAAW,IAAI,GAAG;AACvB,WAAO;AAAA,MACH,MAAM,KAAK;AAAA,MACX,MAAM,YAAY,KAAK,MAAM;AAAA,IACjC;AAAA,EACJ;AACA,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AACJ;;;ACzBA;;;ACDA;AAiBO,SAAS,oBAAoB,OAAO;AAEvC,MAAI,UAAU,MAAM;AAChB,WAAO,EAAE,MAAM,KAAK,KAAK;AAAA,EAC7B;AAEA,MAAI,UAAU,QAAW;AACrB,WAAO;AAAA,EACX;AAGA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,cAAc,CAAC;AACrB,eAAW,QAAQ,OAAO;AACtB,YAAM,WAAW,oBAAoB,IAAI;AACzC,UAAI,YAAY,MAAM;AAClB,oBAAY,KAAK,QAAQ;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO,EAAE,MAAM,KAAK,MAAM,QAAQ,YAAY;AAAA,EAClD;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,+BAAO,QAAQ;AACf,aAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IAC7C;AACA,UAAM,aAAa,CAAC;AACpB,eAAW,aAAa,OAAO;AAC3B,YAAM,aAAa,MAAM,SAAS;AAClC,YAAM,MAAM,oBAAoB,UAAU;AAC1C,UAAI,KAAK;AACL,mBAAW,KAAK;AAAA,UACZ,MAAM,KAAK;AAAA,UACX,MAAM,EAAE,MAAM,KAAK,MAAM,OAAO,UAAU;AAAA,UAC1C,OAAO;AAAA,QACX,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,EAAE,MAAM,KAAK,QAAQ,QAAQ,WAAW;AAAA,EACnD;AAEA,MAAI,OAAO,UAAU,WAAW;AAC5B,WAAO,EAAE,MAAM,KAAK,SAAS,MAAM;AAAA,EACvC;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,EAAE,MAAM,KAAK,KAAK,OAAO,OAAO,KAAK,EAAE;AAAA,EAClD;AAEA,MAAI,OAAO,UAAU,YAAY,SAAS,KAAK,GAAG;AAC9C,UAAM,YAAY,OAAO,KAAK;AAC9B,WAAO,oBAAoB,KAAK,SAAS,IACnC,EAAE,MAAM,KAAK,KAAK,OAAO,UAAU,IACnC,EAAE,MAAM,KAAK,OAAO,OAAO,UAAU;AAAA,EAC/C;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,EAAE,MAAM,KAAK,QAAQ,MAAM;AAAA,EACtC;AACA,QAAM,IAAI,UAAU,gCAAgC,KAAK,GAAG;AAChE;AAMA,IAAM,sBAAsB;;;ADtDrB,SAAS,aAAa,OAAO,MAAM;AACtC,MAAI,cAAc,IAAI,GAAG;AACrB,UAAM,WAAW,aAAa,OAAO,KAAK,MAAM;AAChD,SAAI,qCAAU,UAAS,KAAK,MAAM;AAC9B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAEA,MAAI,UAAU,MAAM;AAChB,WAAO,EAAE,MAAM,KAAK,KAAK;AAAA,EAC7B;AAEA,MAAI,UAAU,QAAW;AACrB,WAAO;AAAA,EACX;AAGA,MAAI,WAAW,IAAI,GAAG;AAClB,UAAM,WAAW,KAAK;AACtB,QAAI,iBAAiB,KAAK,GAAG;AACzB,YAAM,cAAc,CAAC;AACrB,iBAAW,QAAQ,OAAO;AACtB,cAAM,WAAW,aAAa,MAAM,QAAQ;AAC5C,YAAI,YAAY,MAAM;AAClB,sBAAY,KAAK,QAAQ;AAAA,QAC7B;AAAA,MACJ;AACA,aAAO,EAAE,MAAM,KAAK,MAAM,QAAQ,YAAY;AAAA,IAClD;AACA,WAAO,aAAa,OAAO,QAAQ;AAAA,EACvC;AAGA,MAAI,kBAAkB,IAAI,GAAG;AACzB,QAAI,CAAC,aAAa,KAAK,GAAG;AACtB,aAAO;AAAA,IACX;AACA,UAAM,aAAa,CAAC;AACpB,eAAW,SAAS,OAAO,OAAO,KAAK,UAAU,CAAC,GAAG;AACjD,YAAM,aAAa,aAAa,MAAM,MAAM,IAAI,GAAG,MAAM,IAAI;AAC7D,UAAI,YAAY;AACZ,mBAAW,KAAK;AAAA,UACZ,MAAM,KAAK;AAAA,UACX,MAAM,EAAE,MAAM,KAAK,MAAM,OAAO,MAAM,KAAK;AAAA,UAC3C,OAAO;AAAA,QACX,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,EAAE,MAAM,KAAK,QAAQ,QAAQ,WAAW;AAAA,EACnD;AACA,MAAI,WAAW,IAAI,GAAG;AAGlB,UAAM,aAAa,KAAK,UAAU,KAAK;AACvC,QAAI,cAAc,MAAM;AACpB,aAAO;AAAA,IACX;AACA,QAAI,WAAW,IAAI,GAAG;AAClB,aAAO,EAAE,MAAM,KAAK,MAAM,OAAO,WAAW;AAAA,IAChD;AAEA,QAAI,KAAK,SAAS,QACd,OAAO,eAAe,YACtBC,qBAAoB,KAAK,UAAU,GAAG;AACtC,aAAO,EAAE,MAAM,KAAK,KAAK,OAAO,WAAW;AAAA,IAC/C;AACA,WAAO,oBAAoB,UAAU;AAAA,EACzC;AAGA,UAAQ,OAAO,OAAO,4BAA4B,QAAQ,IAAI,CAAC;AACnE;AAMA,IAAMA,uBAAsB;;;AExG5B;AACO,SAAS,mBAAmB,KAAK;AADxC;AAEI,OAAI,SAAI,YAAJ,mBAAa,aAAa;AAC1B,WAAO;AAAA,MACH,GAAG,IAAI,QAAQ;AAAA,MACf,OAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,IAAI,aAAa;AACjB,WAAO;AAAA,MACH,MAAM,KAAK;AAAA,MACX,OAAO,IAAI;AAAA,MACX,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;;;ACHO,IAAM,mBAAmB,SAAS,SAASC,kBAAiB,QAAQ;AACvE,QAAM,YAAY,aAAa,MAAM;AACrC,SAAO,IAAI,IAAI,CAAC,GAAG,SAAS,EAAE,IAAI,UAAQ,KAAK,IAAI,CAAC;AACxD,CAAC;AACM,IAAM,eAAe,SAAS,SAASC,cAAa,QAAQ;AAC/D,QAAM,cAAc,eAAe,MAAM;AACzC,SAAO,IAAI,IAAI,YAAY,OAAO,CAAC;AACvC,CAAC;AACM,IAAM,iBAAiB,SAAS,SAASC,gBAAe,QAAQ;AACnE,QAAM,cAAc,oBAAI,IAAI;AAC5B,QAAM,YAAY,OAAO,aAAa;AACtC,MAAI,WAAW;AACX,gBAAY,IAAI,SAAS,SAAS;AAAA,EACtC;AACA,QAAM,eAAe,OAAO,gBAAgB;AAC5C,MAAI,cAAc;AACd,gBAAY,IAAI,YAAY,YAAY;AAAA,EAC5C;AACA,QAAM,mBAAmB,OAAO,oBAAoB;AACpD,MAAI,kBAAkB;AAClB,gBAAY,IAAI,gBAAgB,gBAAgB;AAAA,EACpD;AACA,SAAO;AACX,CAAC;;;AL3BM,SAAS,0BAA0B,QAAQ,UAAU,CAAC,GAAG;AAC5D,QAAM,+BAA+B,QAAQ;AAC7C,QAAM,WAAW,OAAO,WAAW;AACnC,QAAM,aAAa,cAAc,QAAQ,4BAA4B;AACrE,QAAM,cAAc,cAAc,OAAO,CAAC,UAAU,IAAI,CAAC;AACzD,QAAM,aAAa,OAAO,cAAc;AACxC,aAAW,aAAa,YAAY;AAChC,QAAI,qBAAqB,SAAS,GAAG;AACjC;AAAA,IACJ;AACA,gBAAY,KAAK,iBAAiB,WAAW,QAAQ,4BAA4B,CAAC;AAAA,EACtF;AACA,aAAW,YAAY,UAAU;AAC7B,UAAM,OAAO,SAAS,QAAQ;AAC9B,UAAM,qBAAqB,sBAAsB,IAAI;AACrD,UAAM,kBAAkB,oBAAoB,IAAI;AAChD,QAAI,sBAAsB,iBAAiB;AACvC;AAAA,IACJ;AACA,QAAI,aAAa,IAAI,GAAG;AACpB,kBAAY,KAAK,kBAAkB,MAAM,QAAQ,4BAA4B,CAAC;AAAA,IAClF,WACS,gBAAgB,IAAI,GAAG;AAC5B,kBAAY,KAAK,qBAAqB,MAAM,QAAQ,4BAA4B,CAAC;AAAA,IACrF,WACS,YAAY,IAAI,GAAG;AACxB,kBAAY,KAAK,iBAAiB,MAAM,QAAQ,4BAA4B,CAAC;AAAA,IACjF,WACS,kBAAkB,IAAI,GAAG;AAC9B,kBAAY,KAAK,uBAAuB,MAAM,QAAQ,4BAA4B,CAAC;AAAA,IACvF,WACS,WAAW,IAAI,GAAG;AACvB,kBAAY,KAAK,gBAAgB,MAAM,QAAQ,4BAA4B,CAAC;AAAA,IAChF,WACS,aAAa,IAAI,GAAG;AACzB,kBAAY,KAAK,kBAAkB,MAAM,QAAQ,4BAA4B,CAAC;AAAA,IAClF,OACK;AACD,YAAM,IAAI,MAAM,gBAAgB,IAAI,GAAG;AAAA,IAC3C;AAAA,EACJ;AACA,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX;AAAA,EACJ;AACJ;AAOO,SAAS,cAAc,QAAQ,8BAA8B;AAChE,QAAM,mBAAmB,oBAAI,IAAI;AAAA,IAC7B,CAAC,SAAS,MAAS;AAAA,IACnB,CAAC,YAAY,MAAS;AAAA,IACtB,CAAC,gBAAgB,MAAS;AAAA,EAC9B,CAAC;AACD,QAAM,QAAQ,CAAC;AACf,MAAI,OAAO,WAAW,MAAM;AACxB,UAAM,KAAK,OAAO,OAAO;AAAA,EAC7B;AACA,MAAI,OAAO,qBAAqB,MAAM;AAClC,eAAW,oBAAoB,OAAO,mBAAmB;AACrD,YAAM,KAAK,gBAAgB;AAAA,IAC/B;AAAA,EACJ;AACA,aAAW,QAAQ,OAAO;AACtB,QAAI,KAAK,gBAAgB;AACrB,iBAAW,+BAA+B,KAAK,gBAAgB;AAC3D,yBAAiB,IAAI,4BAA4B,WAAW,2BAA2B;AAAA,MAC3F;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,cAAc,eAAe,MAAM;AACzC,aAAW,CAAC,mBAAmB,2BAA2B,KAAK,kBAAkB;AAC7E,UAAM,WAAW,YAAY,IAAI,iBAAiB;AAClD,QAAI,YAAY,MAAM;AAClB,YAAM,cAAc,YAAY,QAAQ;AACxC,UAAI,+BAA+B,MAAM;AACrC,oCAA4B,OAAO;AAAA,MACvC,OACK;AACD,yBAAiB,IAAI,mBAAmB;AAAA,UACpC,MAAM,KAAK;AAAA,UACX,WAAW;AAAA,UACX,MAAM;AAAA,QACV,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,iBAAiB,CAAC,GAAG,iBAAiB,OAAO,CAAC,EAAE,OAAO,MAAM;AACnE,QAAM,aAAa,kBAAkB,QAAQ,QAAQ,4BAA4B;AACjF,MAAI,CAAC,eAAe,UAAU,CAAC,WAAW,QAAQ;AAC9C,WAAO;AAAA,EACX;AACA,QAAM,aAAa;AAAA,IACf,MAAM,kBAAkB,OAAO,KAAK,oBAAoB,KAAK;AAAA,IAC7D;AAAA;AAAA,IAEA;AAAA,EACJ;AACA,QAAM,kBAAkB,mBAAmB,MAAM;AACjD,MAAI,iBAAiB;AACjB,eAAW,cAAc;AAAA,EAC7B;AACA,SAAO;AACX;AACO,SAAS,iBAAiB,WAAW,QAAQ,8BAA8B;AApHlF;AAqHI,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,SAAS;AAAA,IACzC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,UAAU;AAAA,IACrB;AAAA,IACA,YAAW,eAAU,SAAV,mBAAgB,IAAI,SAAO,WAAW,KAAK,QAAQ,4BAA4B;AAAA,IAC1F,YAAY,UAAU;AAAA,IACtB,aAAW,eAAU,cAAV,mBAAqB,IAAI,eAAa;AAAA,MAC7C,MAAM,KAAK;AAAA,MACX,OAAO;AAAA,IACX,QAAO,CAAC;AAAA,EACZ;AACJ;AACO,SAAS,kBAAkB,QAAQ,QAAQ,8BAA8B;AApIhF;AAqII,MAAI,gDAAgD,CAAC;AACrD,QAAM,yBAAyB,0BAA0B,QAAQ,4BAA4B;AAC7F,MAAI;AACJ,MAAI,0BAA0B,MAAM;AAChC,iBAAa,mBAAmB,QAAQ,sBAAsB;AAAA,EAClE;AACA,MAAI,0BAA0B;AAC9B,MAAI,2BAA2B;AAC/B,MAAI,cAAc,MAAM;AACpB,oDAAgD,WAAW,OAAO,eAAa,UAAU,KAAK,UAAU,gBAAgB,UAAU,KAAK,UAAU,aAAa;AAC9J,QAAI,OAAO,qBAAqB,MAAM;AAClC,iCAA0B,gBAAW,OAAO,eAAa,UAAU,KAAK,UAAU,YAAY,MAApE,mBAAwE;AAAA,IACtG;AACA,QAAI,OAAO,kBAAkB,QAAQ,OAAO,kBAAkB,MAAM;AAChE,kCAA2B,gBAAW,OAAO,eAAa,UAAU,KAAK,UAAU,aAAa,MAArE,mBAAyE;AAAA,IACxG;AAAA,EACJ;AACA,MAAI,OAAO,qBAAqB,QAAQ,2BAA2B,MAAM;AACrE,8BAA0B,wBAAwB,OAAO,iBAAiB;AAAA,EAC9E;AACA,MAAI,OAAO,kBAAkB,QACxB,OAAO,kBAAkB,QAAQ,4BAA4B,MAAO;AACrE,UAAM,mBAAmB,OAAO,kBAAkB,OAAO;AACzD,UAAM,kBAAkB;AAAA,MACpB,KAAK;AAAA,IACT;AACA,+BAA2B,kBAAkB,eAAe,eAAe;AAAA,EAC/E;AACA,MAAI,2BAA2B,MAAM;AACjC,kDAA8C,KAAK,uBAAuB;AAAA,EAC9E;AACA,MAAI,4BAA4B,MAAM;AAClC,kDAA8C,KAAK,wBAAwB;AAAA,EAC/E;AACA,SAAO;AACX;AACO,SAAS,WAAW,KAAK,QAAQ,8BAA8B;AAClE,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,GAAG;AAAA,IACnC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,IAAI;AAAA,IACf;AAAA,IACA,MAAM,YAAY,IAAI,IAAI;AAAA;AAAA,IAE1B,cAAc,IAAI,iBAAiB,SAC5B,aAAa,IAAI,cAAc,IAAI,IAAI,KAAK,SAC7C;AAAA,IACN,YAAY,kBAAkB,KAAK,QAAQ,4BAA4B;AAAA,EAC3E;AACJ;AACO,SAAS,kBAAkB,MAAM,QAAQ,8BAA8B;AAC1E,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,IAAI;AAAA,IACpC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IAChB;AAAA,IACA,QAAQ,OAAO,OAAO,KAAK,UAAU,CAAC,EAAE,IAAI,WAAS,aAAa,OAAO,QAAQ,4BAA4B,CAAC;AAAA,IAC9G,YAAY,OAAO,OAAO,KAAK,cAAc,CAAC,EAAE,IAAI,WAAS,YAAY,KAAK,CAAC;AAAA,IAC/E,YAAY,kBAAkB,MAAM,QAAQ,4BAA4B;AAAA,EAC5E;AACJ;AACO,SAAS,qBAAqB,MAAM,QAAQ,8BAA8B;AAC7E,QAAM,OAAO;AAAA,IACT,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,IAAI;AAAA,IACpC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IAChB;AAAA,IACA,QAAQ,OAAO,OAAO,KAAK,UAAU,CAAC,EAAE,IAAI,WAAS,aAAa,OAAO,QAAQ,4BAA4B,CAAC;AAAA,IAC9G,YAAY,kBAAkB,MAAM,QAAQ,4BAA4B;AAAA,EAC5E;AACA,MAAI,mBAAmB,MAAM;AACzB,SAAK,aAAa,OAAO,OAAO,KAAK,cAAc,CAAC,EAAE,IAAI,WAAS,YAAY,KAAK,CAAC;AAAA,EACzF;AACA,SAAO;AACX;AACO,SAAS,iBAAiB,MAAM,QAAQ,8BAA8B;AACzE,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,IAAI;AAAA,IACpC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IAChB;AAAA;AAAA,IAEA,YAAY,kBAAkB,MAAM,QAAQ,4BAA4B;AAAA,IACxE,OAAO,KAAK,SAAS,EAAE,IAAI,CAAAC,UAAQ,YAAYA,KAAI,CAAC;AAAA,EACxD;AACJ;AACO,SAAS,uBAAuB,MAAM,QAAQ,8BAA8B;AAC/E,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,IAAI;AAAA,IACpC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IAChB;AAAA,IACA,QAAQ,OAAO,OAAO,KAAK,UAAU,CAAC,EAAE,IAAI,WAAS,kBAAkB,OAAO,QAAQ,4BAA4B,CAAC;AAAA;AAAA,IAEnH,YAAY,kBAAkB,MAAM,QAAQ,4BAA4B;AAAA,EAC5E;AACJ;AACO,SAAS,gBAAgB,MAAM,QAAQ,8BAA8B;AACxE,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,IAAI;AAAA,IACpC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IAChB;AAAA,IACA,QAAQ,OAAO,OAAO,KAAK,UAAU,CAAC,EAAE,IAAI,WAAS,iBAAiB,OAAO,QAAQ,4BAA4B,CAAC;AAAA;AAAA,IAElH,YAAY,kBAAkB,MAAM,QAAQ,4BAA4B;AAAA,EAC5E;AACJ;AACO,SAAS,kBAAkB,MAAM,QAAQ,8BAA8B;AAC1E,QAAM,yBAAyB,0BAA0B,MAAM,4BAA4B;AAC3F,QAAM,aAAa,mBAAmB,QAAQ,sBAAsB;AACpE,QAAM,mBAAoB,KAAK,gBAAgB,KAC3C,KAAK,gBAAgB;AACzB,MAAI,oBACA,CAAC,WAAW,KAAK,mBAAiB,cAAc,KAAK,UAAU,aAAa,GAAG;AAC/E,UAAM,kBAAkB;AAAA,MACpB,KAAK;AAAA,IACT;AACA,eAAW,KAAK,kBAAkB,eAAe,eAAe,CAAC;AAAA,EACrE;AACA,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,IAAI;AAAA,IACpC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IAChB;AAAA;AAAA,IAEA;AAAA,EACJ;AACJ;AACO,SAAS,aAAa,OAAO,QAAQ,8BAA8B;AACtE,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,KAAK;AAAA,IACrC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,MAAM;AAAA,IACjB;AAAA,IACA,WAAW,MAAM,KAAK,IAAI,SAAO,WAAW,KAAK,QAAQ,4BAA4B,CAAC;AAAA,IACtF,MAAM,YAAY,MAAM,IAAI;AAAA;AAAA,IAE5B,YAAY,kBAAkB,OAAO,QAAQ,4BAA4B;AAAA,EAC7E;AACJ;AACO,SAAS,kBAAkB,OAAO,QAAQ,8BAA8B;AAC3E,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,KAAK;AAAA,IACrC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,MAAM;AAAA,IACjB;AAAA,IACA,MAAM,YAAY,MAAM,IAAI;AAAA;AAAA,IAE5B,YAAY,kBAAkB,OAAO,QAAQ,4BAA4B;AAAA,IACzE,cAAc,aAAa,MAAM,cAAc,MAAM,IAAI,KAAK;AAAA,EAClE;AACJ;AACO,SAAS,iBAAiB,OAAO,QAAQ,8BAA8B;AAC1E,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,aAAa,mBAAmB,KAAK;AAAA,IACrC,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO,MAAM;AAAA,IACjB;AAAA,IACA,YAAY,kBAAkB,OAAO,QAAQ,4BAA4B;AAAA,EAC7E;AACJ;AACO,SAAS,wBAAwB,mBAAmB;AACvD,SAAO,kBAAkB,cAAc,EAAE,QAAQ,kBAAkB,GAAG,0BAA0B;AACpG;AACO,SAAS,kBAAkB,MAAM,MAAM,WAAW;AACrD,QAAM,qBAAqB,CAAC;AAC5B,aAAW,WAAW,MAAM;AACxB,UAAM,WAAW,KAAK,OAAO;AAC7B,QAAI;AACJ,QAAI,aAAa,MAAM;AACnB,YAAM,MAAM,UAAU,KAAK,KAAK,CAAAC,SAAOA,KAAI,SAAS,OAAO;AAC3D,UAAI,KAAK;AACL,gBAAQ,aAAa,UAAU,IAAI,IAAI;AAAA,MAC3C;AAAA,IACJ;AACA,QAAI,SAAS,MAAM;AACf,cAAQ,oBAAoB,QAAQ;AAAA,IACxC;AACA,QAAI,SAAS,MAAM;AACf,yBAAmB,KAAK;AAAA,QACpB,MAAM,KAAK;AAAA,QACX,MAAM;AAAA,UACF,MAAM,KAAK;AAAA,UACX,OAAO;AAAA,QACX;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,MAAM;AAAA,MACF,MAAM,KAAK;AAAA,MACX,OAAO;AAAA,IACX;AAAA,IACA,WAAW;AAAA,EACf;AACJ;AACO,SAAS,mBAAmB,QAAQ,iBAAiB;AACxD,QAAM,iBAAiB,CAAC;AACxB,aAAW,EAAE,MAAM,KAAK,KAAK,iBAAiB;AAC1C,UAAM,YAAY,iCAAQ,aAAa;AACvC,mBAAe,KAAK,kBAAkB,MAAM,MAAM,SAAS,CAAC;AAAA,EAChE;AACA,SAAO;AACX;;;AMvWA;;;ACAA;;;ACAA;;;ACAA;AACA,IAAM,kBAAkB;AACxB,IAAI,mBAAmB,CAAC;AACjB,SAAS,gBAAgB;AAC5B,qBAAmB,CAAC;AACxB;AACO,SAAS,eAAe,MAAM;AANrC;AAOI,QAAM,cAAa,UAAK,SAAL,mBAAW;AAC9B,MAAI,cAAc,MAAM;AACpB;AAAA,EACJ;AACA,cAAY,MAAM,UAAU;AAC5B,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK;AACD,UAAI,KAAK,QAAQ;AACb,mBAAW,SAAS,KAAK,QAAQ;AAC7B,sBAAY,OAAO,YAAY,MAAM,KAAK,KAAK;AAAA,QACnD;AAAA,MACJ;AACA;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,KAAK,QAAQ;AACb,mBAAW,SAAS,KAAK,QAAQ;AAC7B,sBAAY,OAAO,YAAY,MAAM,KAAK,KAAK;AAC/C,cAAI,sBAAsB,KAAK,KAAK,MAAM,WAAW;AACjD,uBAAW,OAAO,MAAM,WAAW;AAC/B,0BAAY,KAAK,YAAY,MAAM,KAAK,OAAO,IAAI,KAAK,KAAK;AAAA,YACjE;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA;AAAA,EACR;AACJ;AACO,SAAS,YAAY,MAAM,QAAQ,OAAO,UAAU;AACvD,QAAM,UAAU,WAAW,IAAI;AAC/B,MAAI,OAAO,YAAY,YAAY,QAAQ,WAAW,GAAG;AACrD;AAAA,EACJ;AACA,QAAM,OAAO,CAAC,MAAM;AACpB,MAAI,OAAO;AACP,SAAK,KAAK,KAAK;AACf,QAAI,UAAU;AACV,WAAK,KAAK,QAAQ;AAAA,IACtB;AAAA,EACJ;AACA,QAAM,OAAO,KAAK,KAAK,GAAG;AAC1B,MAAI,CAAC,iBAAiB,IAAI,GAAG;AACzB,qBAAiB,IAAI,IAAI,CAAC;AAAA,EAC9B;AACA,mBAAiB,IAAI,EAAE,KAAK,OAAO;AACvC;AACO,SAAS,aAAa,SAAS;AAClC,SAAO,SAAS,QAAQ,QAAQ,OAAO,MAAM;AACjD;AAeA,SAAS,KAAK,YAAY,WAAW;AACjC,SAAO,aAAa,WAAW,OAAO,OAAK,CAAC,EAAE,KAAK,aAAa,EAAE,IAAI;AAC1E;AACA,SAAS,kBAAkB,YAAY;AACnC,UAAO,yCAAY,KAAK,SAAO,IAAI,SAAS,IAAI,OAAM;AAC1D;AACA,SAAS,eAAe,IAAI;AACxB,SAAO,CAAC,MAAM,MAAM,SAAS,MAAM,cAAc;AA9ErD;AA+EQ,UAAM,OAAO,CAAC;AACd,UAAM,SAAS,KAAK,OAAO,CAAC,MAAMC,SAAQ;AACtC,UAAI,CAAC,UAAU,aAAa,QAAQ,EAAE,SAASA,IAAG,KAAK,KAAK,MAAM;AAC9D,aAAK,KAAK,KAAK,KAAK,KAAK;AAAA,MAC7B;AACA,aAAO,KAAKA,IAAG;AAAA,IACnB,GAAG,UAAU,CAAC,CAAC;AACf,UAAM,MAAM,CAAC,GAAG,OAAM,sCAAQ,SAAR,mBAAc,KAAK,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AACnE,UAAM,QAAQ,CAAC;AACf,QAAI,KAAK,KAAK,SAAS,YAAY,KAAK,iBAAiB,GAAG,GAAG;AAC3D,YAAM,KAAK,GAAG,iBAAiB,GAAG,CAAC;AAAA,IACvC;AACA,WAAO,KAAK,CAAC,GAAG,MAAM,IAAI,YAAY,GAAG,KAAK,aAAa,GAAG,MAAM,MAAM,SAAS,MAAM,SAAS,CAAC,GAAG,IAAI;AAAA,EAC9G;AACJ;AACA,SAAS,OAAO,aAAa;AACzB,SAAO,eAAe,KAAK,YAAY,QAAQ,OAAO,MAAM,CAAC;AACjE;AAKA,SAAS,MAAM,OAAO;AAClB,SAAO,SAAS,MAAM,WAAW,IAAI;AAAA,EAAM,OAAO,KAAK,OAAO,IAAI,CAAC,CAAC;AAAA,KAAQ;AAChF;AAKA,SAAS,KAAK,OAAO,aAAa,KAAK;AACnC,SAAO,cAAc,QAAQ,eAAe,OAAO,MAAM;AAC7D;AAMA,SAAS,iBAAiB,OAAO,gBAAgB,OAAO;AACpD,QAAM,UAAU,MAAM,QAAQ,OAAO,MAAM,EAAE,QAAQ,QAAQ,OAAO;AACpE,UAAQ,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,QAAS,MAAM,QAAQ,IAAI,MAAM,KACpE,MAAM,QAAQ,QAAQ,MAAM,KAAK,CAAC,QAClC;AAAA,EAAQ,gBAAgB,UAAU,OAAO,OAAO,CAAC;AAAA;AAC3D;AACA,IAAM,qBAAqB;AAAA,EACvB,MAAM,EAAE,OAAO,UAAQ,KAAK,MAAM;AAAA,EAClC,UAAU,EAAE,OAAO,UAAQ,MAAM,KAAK,KAAK;AAAA;AAAA,EAE3C,UAAU;AAAA,IACN,OAAO,UAAQ,KAAK,KAAK,aAAa,MAAM;AAAA,EAChD;AAAA,EACA,qBAAqB;AAAA,IACjB,OAAO,UAAQ;AACX,YAAM,UAAU,KAAK,KAAK,KAAK,KAAK,qBAAqB,IAAI,GAAG,GAAG;AACnE,YAAM,SAAS,KAAK,CAAC,KAAK,WAAW,KAAK,CAAC,KAAK,MAAM,OAAO,CAAC,GAAG,KAAK,KAAK,YAAY,GAAG,CAAC,GAAG,GAAG;AAEjG,aAAO,SAAS,MAAM,KAAK;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,oBAAoB;AAAA,IAChB,OAAO,CAAC,EAAE,UAAU,MAAM,cAAc,WAAW,MAAM,WAAW,OAAO,OAAO,KAAK,OAAO,YAAY,IAAI,KAAK,KAAK,KAAK,YAAY,GAAG,CAAC;AAAA,EACjJ;AAAA,EACA,cAAc,EAAE,OAAO,CAAC,EAAE,WAAW,MAAM,MAAM,UAAU,EAAE;AAAA,EAC7D,OAAO;AAAA,IACH,MAAM,EAAE,OAAO,MAAM,WAAW,MAAM,YAAY,aAAa,GAAG;AAC9D,YAAM,SAAS,KAAK,IAAI,OAAO,IAAI,IAAI;AACvC,UAAI,WAAW,SAAS,KAAK,KAAK,KAAK,MAAM,IAAI,GAAG,GAAG;AACvD,UAAI,SAAS,SAAS,iBAAiB;AACnC,mBAAW,SAAS,KAAK,OAAO,OAAO,KAAK,MAAM,IAAI,CAAC,GAAG,KAAK;AAAA,MACnE;AACA,aAAO,KAAK,CAAC,UAAU,KAAK,YAAY,GAAG,GAAG,YAAY,GAAG,GAAG;AAAA,IACpE;AAAA,EACJ;AAAA,EACA,UAAU,EAAE,OAAO,CAAC,EAAE,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM;AAAA;AAAA,EAE5D,gBAAgB;AAAA,IACZ,OAAO,CAAC,EAAE,MAAM,WAAW,MAAM,QAAQ,OAAO,KAAK,KAAK,KAAK,YAAY,GAAG,CAAC;AAAA,EACnF;AAAA,EACA,gBAAgB;AAAA,IACZ,OAAO,CAAC,EAAE,eAAe,YAAY,aAAa,MAAM,KAAK,CAAC,OAAO,KAAK,OAAO,aAAa,GAAG,KAAK,YAAY,GAAG,GAAG,YAAY,GAAG,GAAG;AAAA,EAC9I;AAAA,EACA,oBAAoB;AAAA,IAChB,OAAO,CAAC,EAAE,MAAM,eAAe,qBAAqB,YAAY,aAAa;AAAA;AAAA;AAAA,MAG7E,YAAY,IAAI,GAAG,KAAK,KAAK,KAAK,qBAAqB,IAAI,GAAG,GAAG,CAAC,OACxD,aAAa,IAAI,KAAK,IAAI,KAAK,YAAY,GAAG,GAAG,GAAG,CAAC,KAC3D;AAAA;AAAA,EACR;AAAA;AAAA,EAEA,UAAU,EAAE,OAAO,CAAC,EAAE,MAAM,MAAM,MAAM;AAAA,EACxC,YAAY,EAAE,OAAO,CAAC,EAAE,MAAM,MAAM,MAAM;AAAA,EAC1C,aAAa;AAAA,IACT,OAAO,CAAC,EAAE,OAAO,OAAO,cAAc,MAAM;AACxC,UAAI,eAAe;AACf,eAAO,iBAAiB,KAAK;AAAA,MACjC;AACA,aAAO,KAAK,UAAU,KAAK;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,cAAc,EAAE,OAAO,CAAC,EAAE,MAAM,MAAO,QAAQ,SAAS,QAAS;AAAA,EACjE,WAAW,EAAE,OAAO,MAAM,OAAO;AAAA,EACjC,WAAW,EAAE,OAAO,CAAC,EAAE,MAAM,MAAM,MAAM;AAAA,EACzC,WAAW,EAAE,OAAO,CAAC,EAAE,OAAO,MAAM,MAAM,KAAK,QAAQ,IAAI,IAAI,IAAI;AAAA,EACnE,aAAa,EAAE,OAAO,CAAC,EAAE,OAAO,MAAM,MAAM,KAAK,QAAQ,IAAI,IAAI,IAAI;AAAA,EACrE,aAAa,EAAE,OAAO,CAAC,EAAE,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM;AAAA;AAAA,EAE/D,WAAW;AAAA,IACP,OAAO,CAAC,EAAE,MAAM,WAAW,KAAK,MAAM,MAAM,OAAO,KAAK,KAAK,KAAK,MAAM,IAAI,GAAG,GAAG;AAAA,EACtF;AAAA;AAAA,EAEA,WAAW,EAAE,OAAO,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,EACvC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,MAAM,MAAM,OAAO,IAAI;AAAA,EAClD,aAAa,EAAE,OAAO,CAAC,EAAE,KAAK,MAAM,OAAO,IAAI;AAAA;AAAA,EAE/C,kBAAkB;AAAA,IACd,OAAO,CAAC,EAAE,YAAY,eAAe,MAAM,KAAK,CAAC,UAAU,KAAK,YAAY,GAAG,GAAG,MAAM,cAAc,CAAC,GAAG,GAAG;AAAA,EACjH;AAAA,EACA,yBAAyB;AAAA,IACrB,OAAO,CAAC,EAAE,WAAW,KAAK,MAAM,YAAY,OAAO;AAAA,EACvD;AAAA,EACA,sBAAsB;AAAA,IAClB,OAAO,CAAC,EAAE,MAAM,WAAW,MAAM,KAAK,CAAC,UAAU,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,GAAG;AAAA,EACtF;AAAA,EACA,sBAAsB;AAAA,IAClB,OAAO,CAAC,EAAE,MAAM,YAAY,YAAY,OAAO,MAAM,KAAK;AAAA,MACtD;AAAA,MACA;AAAA,MACA,KAAK,eAAe,KAAK,YAAY,KAAK,CAAC;AAAA,MAC3C,KAAK,YAAY,GAAG;AAAA,MACpB,MAAM,MAAM;AAAA,IAChB,GAAG,GAAG;AAAA,EACV;AAAA,EACA,iBAAiB;AAAA,IACb,OAAO,CAAC,EAAE,MAAM,WAAW,MAAM,MAAM,WAAW,MAAM,QACnD,kBAAkB,IAAI,IACjB,KAAK,OAAO,OAAO,KAAK,MAAM,IAAI,CAAC,GAAG,KAAK,IAC3C,KAAK,KAAK,KAAK,MAAM,IAAI,GAAG,GAAG,KACrC,OACA,OACA,KAAK,KAAK,KAAK,YAAY,GAAG,CAAC;AAAA,EACvC;AAAA,EACA,sBAAsB;AAAA,IAClB,OAAO,CAAC,EAAE,MAAM,MAAM,cAAc,WAAW,MAAM,KAAK,CAAC,OAAO,OAAO,MAAM,KAAK,MAAM,YAAY,GAAG,KAAK,YAAY,GAAG,CAAC,GAAG,GAAG;AAAA,EACxI;AAAA,EACA,yBAAyB;AAAA,IACrB,OAAO,CAAC,EAAE,MAAM,YAAY,YAAY,OAAO,MAAM,KAAK;AAAA,MACtD;AAAA,MACA;AAAA,MACA,KAAK,eAAe,KAAK,YAAY,KAAK,CAAC;AAAA,MAC3C,KAAK,YAAY,GAAG;AAAA,MACpB,MAAM,MAAM;AAAA,IAChB,GAAG,GAAG;AAAA,EACV;AAAA,EACA,qBAAqB;AAAA,IACjB,OAAO,CAAC,EAAE,MAAM,YAAY,MAAM,MAAM,KAAK,CAAC,SAAS,MAAM,KAAK,YAAY,GAAG,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK,CAAC,CAAC,GAAG,GAAG;AAAA,EAC5H;AAAA,EACA,oBAAoB;AAAA,IAChB,OAAO,CAAC,EAAE,MAAM,YAAY,OAAO,MAAM,KAAK,CAAC,QAAQ,MAAM,KAAK,YAAY,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,GAAG;AAAA,EAC3G;AAAA,EACA,qBAAqB;AAAA,IACjB,OAAO,CAAC,EAAE,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,GAAG;AAAA,EAC5E;AAAA,EACA,2BAA2B;AAAA,IACvB,OAAO,CAAC,EAAE,MAAM,YAAY,OAAO,MAAM,KAAK,CAAC,SAAS,MAAM,KAAK,YAAY,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,GAAG;AAAA,EAC5G;AAAA,EACA,qBAAqB;AAAA,IACjB,OAAO,CAAC,EAAE,MAAM,WAAW,MAAM,YAAY,UAAU,MAAM,gBACzD,QACC,kBAAkB,IAAI,IACjB,KAAK,OAAO,OAAO,KAAK,MAAM,IAAI,CAAC,GAAG,KAAK,IAC3C,KAAK,KAAK,KAAK,MAAM,IAAI,GAAG,GAAG,MACpC,aAAa,gBAAgB,MAC9B,SACA,KAAK,WAAW,KAAK;AAAA,EAC7B;AAAA,EACA,iBAAiB;AAAA,IACb,OAAO,CAAC,EAAE,YAAY,eAAe,MAAM,KAAK,CAAC,iBAAiB,KAAK,YAAY,GAAG,GAAG,MAAM,cAAc,CAAC,GAAG,GAAG;AAAA,EACxH;AAAA,EACA,qBAAqB;AAAA,IACjB,OAAO,CAAC,EAAE,MAAM,WAAW,MAAM,KAAK,CAAC,iBAAiB,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,GAAG;AAAA,EAC7F;AAAA,EACA,qBAAqB;AAAA,IACjB,OAAO,CAAC,EAAE,MAAM,YAAY,YAAY,OAAO,MAAM,KAAK;AAAA,MACtD;AAAA,MACA;AAAA,MACA,KAAK,eAAe,KAAK,YAAY,KAAK,CAAC;AAAA,MAC3C,KAAK,YAAY,GAAG;AAAA,MACpB,MAAM,MAAM;AAAA,IAChB,GAAG,GAAG;AAAA,EACV;AAAA,EACA,wBAAwB;AAAA,IACpB,OAAO,CAAC,EAAE,MAAM,YAAY,YAAY,OAAO,MAAM,KAAK;AAAA,MACtD;AAAA,MACA;AAAA,MACA,KAAK,eAAe,KAAK,YAAY,KAAK,CAAC;AAAA,MAC3C,KAAK,YAAY,GAAG;AAAA,MACpB,MAAM,MAAM;AAAA,IAChB,GAAG,GAAG;AAAA,EACV;AAAA,EACA,oBAAoB;AAAA,IAChB,OAAO,CAAC,EAAE,MAAM,YAAY,MAAM,MAAM,KAAK,CAAC,gBAAgB,MAAM,KAAK,YAAY,GAAG,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK,CAAC,CAAC,GAAG,GAAG;AAAA,EACnI;AAAA,EACA,mBAAmB;AAAA,IACf,OAAO,CAAC,EAAE,MAAM,YAAY,OAAO,MAAM,KAAK,CAAC,eAAe,MAAM,KAAK,YAAY,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,GAAG;AAAA,EAClH;AAAA,EACA,0BAA0B;AAAA,IACtB,OAAO,CAAC,EAAE,MAAM,YAAY,OAAO,MAAM,KAAK,CAAC,gBAAgB,MAAM,KAAK,YAAY,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,GAAG;AAAA,EACnH;AACJ;AACA,IAAM,iCAAiC,OAAO,KAAK,kBAAkB,EAAE,OAAO,CAAC,MAAM,SAAS;AAAA,EAC1F,GAAG;AAAA,EACH,CAAC,GAAG,GAAG;AAAA,IACH,OAAO,eAAe,mBAAmB,GAAG,EAAE,KAAK;AAAA,EACvD;AACJ,IAAI,CAAC,CAAC;AAKC,SAAS,kBAAkB,KAAK;AACnC,SAAO,MAAM,KAAK,8BAA8B;AACpD;AACA,SAAS,sBAAsB,MAAM;AACjC,SAAO,KAAK,SAAS;AACzB;AAUO,SAAS,WAAW,MAAM;AAC7B,QAAM,WAAW,uBAAuB,IAAI;AAC5C,MAAI,aAAa,QAAW;AACxB,WAAO,uBAAuB;AAAA,EAAK,QAAQ,EAAE;AAAA,EACjD;AACJ;AACO,SAAS,uBAAuB,MAAM;AACzC,QAAM,MAAM,KAAK;AACjB,MAAI,CAAC,KAAK;AACN;AAAA,EACJ;AACA,QAAM,WAAW,CAAC;AAClB,MAAI,QAAQ,IAAI,WAAW;AAC3B,SAAO,SAAS,QACZ,MAAM,SAAS,UAAU,WACzB,MAAM,QAAQ,QACd,MAAM,QAAQ,QACd,MAAM,OAAO,MAAM,MAAM,KAAK,QAC9B,MAAM,SAAS,MAAM,KAAK,MAAM;AAChC,UAAM,QAAQ,OAAO,MAAM,KAAK;AAChC,aAAS,KAAK,KAAK;AACnB,YAAQ,MAAM;AAAA,EAClB;AACA,SAAO,SAAS,SAAS,IAAI,SAAS,QAAQ,EAAE,KAAK,IAAI,IAAI;AACjE;AACO,SAAS,uBAAuB,WAAW;AAE9C,QAAM,QAAQ,UAAU,MAAM,cAAc;AAE5C,QAAM,eAAe,0BAA0B,KAAK;AACpD,MAAI,iBAAiB,GAAG;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,YAAY;AAAA,IAC1C;AAAA,EACJ;AAEA,SAAO,MAAM,SAAS,KAAK,QAAQ,MAAM,CAAC,CAAC,GAAG;AAC1C,UAAM,MAAM;AAAA,EAChB;AACA,SAAO,MAAM,SAAS,KAAK,QAAQ,MAAM,MAAM,SAAS,CAAC,CAAC,GAAG;AACzD,UAAM,IAAI;AAAA,EACd;AAEA,SAAO,MAAM,KAAK,IAAI;AAC1B;AAIO,SAAS,0BAA0B,OAAO;AAC7C,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAMC,UAAS,kBAAkB,IAAI;AACrC,QAAIA,YAAW,KAAK,QAAQ;AACxB;AAAA,IACJ;AACA,QAAI,iBAAiB,QAAQA,UAAS,cAAc;AAChD,qBAAeA;AACf,UAAI,iBAAiB,GAAG;AACpB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,iBAAiB,OAAO,IAAI;AACvC;AACA,SAAS,kBAAkB,KAAK;AAC5B,MAAI,IAAI;AACR,SAAO,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAO;AAC1D;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,QAAQ,KAAK;AAClB,SAAO,kBAAkB,GAAG,MAAM,IAAI;AAC1C;;;ACnYA;;;ACAO,IAAIC;AAAA,CACV,SAAUA,oBAAmB;AAE1B,EAAAA,mBAAkB,OAAO,IAAI;AAC7B,EAAAA,mBAAkB,UAAU,IAAI;AAChC,EAAAA,mBAAkB,cAAc,IAAI;AACpC,EAAAA,mBAAkB,OAAO,IAAI;AAC7B,EAAAA,mBAAkB,qBAAqB,IAAI;AAC3C,EAAAA,mBAAkB,iBAAiB,IAAI;AACvC,EAAAA,mBAAkB,iBAAiB,IAAI;AACvC,EAAAA,mBAAkB,qBAAqB,IAAI;AAE3C,EAAAA,mBAAkB,QAAQ,IAAI;AAC9B,EAAAA,mBAAkB,QAAQ,IAAI;AAC9B,EAAAA,mBAAkB,QAAQ,IAAI;AAC9B,EAAAA,mBAAkB,kBAAkB,IAAI;AACxC,EAAAA,mBAAkB,qBAAqB,IAAI;AAC3C,EAAAA,mBAAkB,WAAW,IAAI;AACjC,EAAAA,mBAAkB,OAAO,IAAI;AAC7B,EAAAA,mBAAkB,MAAM,IAAI;AAC5B,EAAAA,mBAAkB,YAAY,IAAI;AAClC,EAAAA,mBAAkB,cAAc,IAAI;AACpC,EAAAA,mBAAkB,wBAAwB,IAAI;AAClD,GAAGA,uBAAsBA,qBAAoB,CAAC,EAAE;;;ACvBhD;;;ACAO,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,mBAAmB,IAAI;AAClC,EAAAA,YAAW,eAAe,IAAI;AAC9B,EAAAA,YAAW,YAAY,IAAI;AAC3B,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,UAAU,IAAI;AACzB,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,iBAAiB,IAAI;AAChC,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,YAAY,IAAI;AAC3B,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,qBAAqB,IAAI;AACpC,EAAAA,YAAW,yBAAyB,IAAI;AACxC,EAAAA,YAAW,iBAAiB,IAAI;AAChC,EAAAA,YAAW,oBAAoB,IAAI;AACnC,EAAAA,YAAW,UAAU,IAAI;AACzB,EAAAA,YAAW,YAAY,IAAI;AAC/B,GAAG,eAAe,aAAa,CAAC,EAAE;;;AC3BlC;;;ACAA;AACO,SAAS,yBAAyB,SAAS,MAAM;AACpD,MAAI,MAAM;AACN,UAAM,kBAAkB,QAAQ,KAAK,IAAI;AACzC,QAAI,aAAa,eAAe,GAAG;AAC/B,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;;;ACRA;;;ACAA;AAkCO,SAAS,YAAY,MAAM;AAC9B,MAAI,eAAe,MAAM;AACrB,UAAM,SAAS,KAAK,UAAU;AAE9B,eAAW,aAAa,QAAQ;AAC5B,YAAM,QAAQ,OAAO,SAAS;AAC9B,aAAO,MAAM,SAAS;AAAA,IAC1B;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,kBAAkB,MAAM;AACpC,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK,WAAW;AACZ,aAAO;AAAA,IACX,KAAK,aAAa;AACd,aAAO;AAAA,IACX,KAAK,cAAc;AACf,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,UAAU;AACX,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;;;AD1DO,SAAS,YAAY,iBAAiB,YAAY;AACrD,QAAM,mBAAmB,uBAAO,OAAO,IAAI;AAC3C,aAAW,YAAY,iBAAiB;AACpC,qBAAiB,QAAQ,IAAI,gBAAgB,QAAQ;AAAA,EACzD;AACA,QAAM,aAAa,uBAAO,OAAO,IAAI;AACrC,aAAW,YAAY,kBAAkB;AACrC,UAAM,YAAY,iBAAiB,QAAQ;AAC3C,QAAI,aAAa,QAAQ,SAAS,WAAW,IAAI,GAAG;AAChD;AAAA,IACJ;AACA,UAAM,UAAU,UAAU;AAC1B,QAAI,QAAQ,WAAW,IAAI,GAAG;AAC1B;AAAA,IACJ;AACA,QAAI,WAAW,OAAO,KAAK,MAAM;AAC7B,cAAQ,KAAK,8BAA8B,OAAO,sDAAsD;AACxG;AAAA,IACJ;AACA,eAAW,OAAO,IAAI;AAAA,EAC1B;AACA,aAAW,YAAY,YAAY;AAC/B,eAAW,QAAQ,IAAI,gBAAgB,WAAW,QAAQ,CAAC;AAAA,EAC/D;AACA,QAAM,gBAAgB,WAAW,IAAI,eAAa,gBAAgB,SAAS,CAAC;AAC5E,SAAO;AAAA,IACH,SAAS;AAAA,IACT,YAAY;AAAA,EAChB;AACA,WAAS,gBAAgB,WAAW;AAChC,QAAI,qBAAqB,SAAS,GAAG;AACjC,aAAO;AAAA,IACX;AACA,UAAM,kBAAkB,UAAU,SAAS;AAC3C,oBAAgB,OAAO,WAAW,gBAAgB,IAAI;AACtD,WAAO,IAAI,iBAAiB,eAAe;AAAA,EAC/C;AACA,WAAS,WAAW,MAAM;AACtB,UAAM,cAAc,CAAC;AACrB,eAAW,WAAW,MAAM;AACxB,YAAM,MAAM,KAAK,OAAO;AACxB,YAAM,iBAAiB,WAAW,IAAI,IAAI;AAC1C,UAAI,kBAAkB,MAAM;AACxB,YAAI,OAAO;AACX,oBAAY,OAAO,IAAI;AAAA,MAC3B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,gBAAgB,MAAM;AAC3B,QAAI,aAAa,IAAI,GAAG;AACpB,YAAM,SAAS,KAAK,SAAS;AAC7B,YAAM,YAAY;AAAA,QACd,GAAG;AAAA,QACH,QAAQ,MAAM,aAAa,OAAO,MAAM;AAAA,QACxC,YAAY,MAAM,iBAAiB,OAAO,UAAU;AAAA,MACxD;AACA,aAAO,IAAI,kBAAkB,SAAS;AAAA,IAC1C,WACS,gBAAgB,IAAI,GAAG;AAC5B,YAAM,SAAS,KAAK,SAAS;AAC7B,YAAM,YAAY;AAAA,QACd,GAAG;AAAA,QACH,QAAQ,MAAM,aAAa,OAAO,MAAM;AAAA,MAC5C;AACA,UAAI,gBAAgB,WAAW;AAC3B,kBAAU,aAAa,MAAM,iBAAiB,OAAO,UAAU;AAAA,MACnE;AACA,aAAO,IAAI,qBAAqB,SAAS;AAAA,IAC7C,WACS,YAAY,IAAI,GAAG;AACxB,YAAM,SAAS,KAAK,SAAS;AAC7B,YAAM,YAAY;AAAA,QACd,GAAG;AAAA,QACH,OAAO,MAAM,iBAAiB,OAAO,KAAK;AAAA,MAC9C;AACA,aAAO,IAAI,iBAAiB,SAAS;AAAA,IACzC,WACS,kBAAkB,IAAI,GAAG;AAC9B,YAAM,SAAS,KAAK,SAAS;AAC7B,YAAM,YAAY;AAAA,QACd,GAAG;AAAA,QACH,QAAQ,MAAM,kBAAkB,OAAO,MAAM;AAAA,MACjD;AACA,aAAO,IAAI,uBAAuB,SAAS;AAAA,IAC/C,WACS,WAAW,IAAI,GAAG;AACvB,YAAM,aAAa,KAAK,SAAS;AACjC,aAAO,IAAI,gBAAgB,UAAU;AAAA,IACzC,WACS,aAAa,IAAI,GAAG;AACzB,UAAI,sBAAsB,IAAI,GAAG;AAC7B,eAAO;AAAA,MACX;AACA,YAAM,eAAe,KAAK,SAAS;AACnC,aAAO,IAAI,kBAAkB,YAAY;AAAA,IAC7C;AACA,UAAM,IAAI,MAAM,2BAA2B,IAAI,EAAE;AAAA,EACrD;AACA,WAAS,aAAa,QAAQ;AAC1B,UAAM,gBAAgB,CAAC;AACvB,eAAW,aAAa,QAAQ;AAC5B,YAAM,QAAQ,OAAO,SAAS;AAC9B,YAAM,mBAAmB,WAAW,MAAM,IAAI;AAC9C,UAAI,oBAAoB,QAAQ,MAAM,MAAM;AACxC,cAAM,OAAO;AACb,cAAM,OAAO,WAAW,MAAM,IAAI;AAClC,sBAAc,SAAS,IAAI;AAAA,MAC/B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,kBAAkB,QAAQ;AAC/B,UAAM,gBAAgB,CAAC;AACvB,eAAW,aAAa,QAAQ;AAC5B,YAAM,QAAQ,OAAO,SAAS;AAC9B,YAAM,mBAAmB,WAAW,MAAM,IAAI;AAC9C,UAAI,oBAAoB,MAAM;AAC1B,cAAM,OAAO;AACb,sBAAc,SAAS,IAAI;AAAA,MAC/B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,iBAAiB,YAAY;AAClC,UAAM,eAAe,CAAC;AACtB,eAAW,aAAa,YAAY;AAChC,YAAM,cAAc,WAAW,SAAS;AACxC,UAAI,eAAe,MAAM;AACrB,qBAAa,KAAK,WAAW;AAAA,MACjC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,WAAW,MAAM;AACtB,QAAI,WAAW,IAAI,GAAG;AAClB,YAAM,cAAc,WAAW,KAAK,MAAM;AAC1C,aAAO,eAAe,OAAO,IAAI,YAAY,WAAW,IAAI;AAAA,IAChE,WACS,cAAc,IAAI,GAAG;AAC1B,YAAM,cAAc,WAAW,KAAK,MAAM;AAC1C,aAAO,eAAe,OAAO,IAAI,eAAe,WAAW,IAAI;AAAA,IACnE,WACS,YAAY,IAAI,GAAG;AACxB,UAAI,cAAc,iBAAiB,KAAK,IAAI;AAC5C,UAAI,gBAAgB,QAAW;AAC3B,sBAAc,YAAY,IAAI,IAAI,kBAAkB,IAAI,IAAI,gBAAgB,IAAI;AAChF,mBAAW,YAAY,IAAI,IAAI,iBAAiB,KAAK,IAAI,IAAI;AAAA,MACjE;AACA,aAAO,eAAe,OAAO,WAAW,YAAY,IAAI,IAAI;AAAA,IAChE;AACA,WAAO;AAAA,EACX;AACJ;;;AE3JA;AAEO,SAAS,oBAAoB,MAAM,OAAO,4BAA4B,MAAM,8BAA8B,MAAM;AACnH,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,QAAM,eAAe,gBAAgB,IAAI;AACzC,MAAI,WAAW,YAAY,GAAG;AAC1B,WAAO,6BAA6B,OAC9B,0BAA0B,cAAc,KAAK,IAC7C;AAAA,EACV,WACS,WAAW,YAAY,GAAG;AAC/B,WAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,eAAe,oBAAoB,aAAa,QAAQ,YAAY,2BAA2B,2BAA2B,CAAC;AAAA,EAC1J,WACS,kBAAkB,YAAY,GAAG;AACtC,UAAM,SAAS,aAAa,UAAU;AACtC,UAAM,WAAW,CAAC;AAClB,eAAW,OAAO,OAAO;AACrB,YAAM,QAAQ,OAAO,GAAG;AACxB,UAAI,SAAS,MAAM;AACf,iBAAS,GAAG,IAAI,oBAAoB,MAAM,MAAM,MAAM,GAAG,GAAG,2BAA2B,2BAA2B;AAAA,MACtH;AAAA,IACJ;AACA,WAAO,+BAA+B,OAChC,4BAA4B,cAAc,QAAQ,IAClD;AAAA,EACV;AAEJ;AACO,SAAS,oBAAoB,MAAM,OAAO;AAC7C,SAAO,oBAAoB,MAAM,OAAO,CAAC,GAAG,MAAM;AAC9C,QAAI;AACA,aAAO,EAAE,UAAU,CAAC;AAAA,IACxB,QACM;AACF,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AACO,SAAS,gBAAgB,MAAM,OAAO;AACzC,SAAO,oBAAoB,MAAM,OAAO,CAAC,GAAG,MAAM;AAC9C,QAAI;AACA,aAAO,EAAE,WAAW,CAAC;AAAA,IACzB,QACM;AACF,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;;;AJ5CO,SAAS,UAAU,QAAQ,eAAe,CAAC,GAAG;AACjD,QAAM,aAAa,aAAa,UAAU,SAAS,iBAAiB,cAAc,SAAS,iBAAiB,OAAO,WAAW,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,cAAc,UAAQ,WAAW,IAAI,CAAC,GAAG,QAAQ,YAAY,GAAG,QAAQ,eAAe,GAAG,QAAQ,cAAc,UAAQ,CAAC,WAAW,IAAI,CAAC,GAAG,QAAQ,YAAY,GAAG,QAAQ,YAAY;AAC9V,QAAM,qBAAqB,OAAO,cAAc;AAChD,QAAM,gBAAgB,cAAc,oBAAoB,QAAQ,YAAY;AAC5E,QAAM,EAAE,SAAS,WAAW,IAAI,YAAY,YAAY,aAAa;AACrE,SAAO,IAAI,cAAc;AAAA,IACrB,GAAG,OAAO,SAAS;AAAA,IACnB,OAAO,yBAAyB,SAAS,yBAAyB,YAAY,OAAO,aAAa,CAAC,CAAC;AAAA,IACpG,UAAU,yBAAyB,SAAS,yBAAyB,YAAY,OAAO,gBAAgB,CAAC,CAAC;AAAA,IAC1G,cAAc,yBAAyB,SAAS,yBAAyB,YAAY,OAAO,oBAAoB,CAAC,CAAC;AAAA,IAClH,OAAO,OAAO,OAAO,OAAO;AAAA,IAC5B;AAAA,EACJ,CAAC;AACL;AACA,SAAS,SAAS,iBAAiB,QAAQ,cAAc,SAAS,MAAM,MAAM;AAC1E,QAAM,aAAa,CAAC;AACpB,aAAW,YAAY,iBAAiB;AACpC,QAAI,CAAC,SAAS,WAAW,IAAI,GAAG;AAC5B,YAAM,eAAe,gBAAgB,QAAQ;AAC7C,UAAI,gBAAgB,QAAQ,CAAC,OAAO,YAAY,GAAG;AAC/C,mBAAW,QAAQ,IAAI;AACvB;AAAA,MACJ;AACA,YAAM,aAAa,cAAc,QAAQ,cAAc,QAAQ;AAC/D,UAAI,cAAc,MAAM;AACpB,mBAAW,QAAQ,IAAI;AACvB;AAAA,MACJ;AACA,YAAM,eAAe,WAAW,cAAc,MAAM;AACpD,UAAI,iBAAiB,QAAW;AAC5B,mBAAW,QAAQ,IAAI;AACvB;AAAA,MACJ;AACA,iBAAW,QAAQ,IAAI;AAAA,IAC3B;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,iBAAiB,QAAQ,cAAc;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,MAAI,CAAC,iBAAiB;AAClB,WAAO;AAAA,EACX;AACA,SAAO,SAAS,iBAAiB,QAAQ;AAAA,IACrC,CAAC,WAAW,SAAS,GAAG,UAAQ;AAC5B,YAAM,SAAS,KAAK,SAAS;AAC7B,YAAM,6BAA6B,OAAO;AAC1C,YAAM,wBAAwB,CAAC;AAC/B,iBAAW,iBAAiB,4BAA4B;AACpD,cAAM,0BAA0B,2BAA2B,aAAa;AACxE,cAAM,kBAAkB,gBAAgB,yBAAyB,KAAK,MAAM,QAAQ,aAAa;AACjG,YAAI,oBAAoB,QAAW;AAC/B,gCAAsB,aAAa,IAAI;AAAA,QAC3C,WACS,MAAM,QAAQ,eAAe,GAAG;AACrC,gBAAM,CAAC,kBAAkB,kBAAkB,IAAI;AAC/C,gCAAsB,gBAAgB,IAClC,uBAAuB,SAAY,0BAA0B;AAAA,QACrE,WACS,oBAAoB,MAAM;AAC/B,gCAAsB,aAAa,IAAI;AAAA,QAC3C;AAAA,MACJ;AACA,aAAO,gBAAgB,IAAI,gBAAgB;AAAA,QACvC,GAAG;AAAA,QACH,QAAQ;AAAA,MACZ,CAAC,CAAC;AAAA,IACN;AAAA,EACJ,GAAG,UAAQ,WAAW,IAAI,CAAC;AAC/B;AACA,SAAS,iBAAiB,iBAAiB,QAAQ,IAAI;AACnD,QAAM,aAAa,aAAa,iBAAiB,QAAQ;AAAA,IACrD,CAAC,WAAW,QAAQ,GAAG,oBAAkB;AACrC,UAAI,eAAe,iBAAiB,QAAW;AAC3C,eAAO;AAAA,MACX;AACA,YAAM,eAAe,WAAW,iBAAiB,eAAe,IAAI;AACpE,UAAI,gBAAgB,MAAM;AACtB,eAAO;AAAA,UACH,GAAG;AAAA,UACH,cAAc,GAAG,cAAc,eAAe,YAAY;AAAA,QAC9D;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO,UAAU,YAAY,QAAQ;AAAA,IACjC,CAAC,WAAW,kBAAkB,GAAG,sBAAoB;AACjD,UAAI,iBAAiB,iBAAiB,QAAW;AAC7C,eAAO;AAAA,MACX;AACA,YAAM,eAAe,WAAW,YAAY,iBAAiB,IAAI;AACjE,UAAI,gBAAgB,MAAM;AACtB,eAAO;AAAA,UACH,GAAG;AAAA,UACH,cAAc,GAAG,cAAc,iBAAiB,YAAY;AAAA,QAChE;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,WAAW,YAAY,MAAM;AAClC,MAAI,WAAW,IAAI,GAAG;AAClB,UAAM,UAAU,WAAW,YAAY,KAAK,MAAM;AAClD,WAAO,WAAW,OAAO,IAAI,YAAY,OAAO,IAAI;AAAA,EACxD,WACS,cAAc,IAAI,GAAG;AAC1B,UAAM,UAAU,WAAW,YAAY,KAAK,MAAM;AAClD,WAAO,WAAW,OAAO,IAAI,eAAe,OAAO,IAAI;AAAA,EAC3D,WACS,YAAY,IAAI,GAAG;AACxB,UAAM,UAAU,WAAW,KAAK,IAAI;AACpC,WAAO,WAAW,OAAO,UAAU;AAAA,EACvC;AACA,SAAO;AACX;AACA,SAAS,UAAU,iBAAiB,QAAQ,cAAc;AACtD,QAAM,aAAa,CAAC;AACpB,aAAW,YAAY,iBAAiB;AACpC,QAAI,CAAC,SAAS,WAAW,IAAI,GAAG;AAC5B,YAAM,eAAe,gBAAgB,QAAQ;AAC7C,UAAI,CAAC,aAAa,YAAY,KAC1B,CAAC,gBAAgB,YAAY,KAC7B,CAAC,kBAAkB,YAAY,GAAG;AAClC,mBAAW,QAAQ,IAAI;AACvB;AAAA,MACJ;AACA,YAAM,cAAc,eAAe,QAAQ,cAAc,QAAQ;AACjE,UAAI,eAAe,MAAM;AACrB,mBAAW,QAAQ,IAAI;AACvB;AAAA,MACJ;AACA,YAAM,SAAS,aAAa,SAAS;AACrC,YAAM,yBAAyB,OAAO;AACtC,YAAM,oBAAoB,CAAC;AAC3B,iBAAW,aAAa,wBAAwB;AAC5C,cAAM,sBAAsB,uBAAuB,SAAS;AAC5D,cAAM,cAAc,YAAY,qBAAqB,WAAW,UAAU,MAAM;AAChF,YAAI,gBAAgB,QAAW;AAC3B,4BAAkB,SAAS,IAAI;AAAA,QACnC,WACS,MAAM,QAAQ,WAAW,GAAG;AACjC,gBAAM,CAAC,cAAc,cAAc,IAAI;AACvC,cAAI,eAAe,WAAW,MAAM;AAChC,2BAAe,UAAU;AAAA,cACrB,GAAG,eAAe;AAAA,cAClB,MAAM;AAAA,gBACF,GAAG,eAAe,QAAQ;AAAA,gBAC1B,OAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ;AACA,4BAAkB,YAAY,IAC1B,mBAAmB,SAAY,sBAAsB;AAAA,QAC7D,WACS,gBAAgB,MAAM;AAC3B,4BAAkB,SAAS,IAAI;AAAA,QACnC;AAAA,MACJ;AACA,UAAI,aAAa,YAAY,GAAG;AAC5B,mBAAW,QAAQ,IAAI,gBAAgB,IAAI,kBAAkB;AAAA,UACzD,GAAG;AAAA,UACH,QAAQ;AAAA,QACZ,CAAC,CAAC;AAAA,MACN,WACS,gBAAgB,YAAY,GAAG;AACpC,mBAAW,QAAQ,IAAI,gBAAgB,IAAI,qBAAqB;AAAA,UAC5D,GAAG;AAAA,UACH,QAAQ;AAAA,QACZ,CAAC,CAAC;AAAA,MACN,OACK;AACD,mBAAW,QAAQ,IAAI,gBAAgB,IAAI,uBAAuB;AAAA,UAC9D,GAAG;AAAA,UACH,QAAQ;AAAA,QACZ,CAAC,CAAC;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,aAAa,iBAAiB,QAAQ,cAAc;AACzD,QAAM,aAAa,CAAC;AACpB,aAAW,YAAY,iBAAiB;AACpC,QAAI,CAAC,SAAS,WAAW,IAAI,GAAG;AAC5B,YAAM,eAAe,gBAAgB,QAAQ;AAC7C,UAAI,CAAC,aAAa,YAAY,KAAK,CAAC,gBAAgB,YAAY,GAAG;AAC/D,mBAAW,QAAQ,IAAI;AACvB;AAAA,MACJ;AACA,YAAM,iBAAiB,kBAAkB,YAAY;AACrD,UAAI,kBAAkB,MAAM;AACxB,mBAAW,QAAQ,IAAI;AACvB;AAAA,MACJ;AACA,YAAM,SAAS,aAAa,SAAS;AACrC,YAAM,yBAAyB,OAAO;AACtC,YAAM,oBAAoB,CAAC;AAC3B,iBAAW,aAAa,wBAAwB;AAC5C,cAAM,sBAAsB,uBAAuB,SAAS;AAC5D,cAAM,4BAA4B,oBAAoB;AACtD,YAAI,6BAA6B,MAAM;AACnC,4BAAkB,SAAS,IAAI;AAC/B;AAAA,QACJ;AACA,cAAM,gBAAgB,OAAO,KAAK,yBAAyB;AAC3D,YAAI,CAAC,cAAc,QAAQ;AACvB,4BAAkB,SAAS,IAAI;AAC/B;AAAA,QACJ;AACA,cAAM,uBAAuB,CAAC;AAC9B,mBAAW,gBAAgB,eAAe;AACtC,gBAAM,yBAAyB,0BAA0B,YAAY;AACrE,gBAAM,iBAAiB,eAAe,wBAAwB,WAAW,UAAU,MAAM;AACzF,cAAI,mBAAmB,QAAW;AAC9B,iCAAqB,YAAY,IAAI;AAAA,UACzC,WACS,MAAM,QAAQ,cAAc,GAAG;AACpC,kBAAM,CAAC,iBAAiB,iBAAiB,IAAI;AAC7C,iCAAqB,eAAe,IAAI;AAAA,UAC5C,WACS,mBAAmB,MAAM;AAC9B,iCAAqB,YAAY,IAAI;AAAA,UACzC;AAAA,QACJ;AACA,0BAAkB,SAAS,IAAI;AAAA,UAC3B,GAAG;AAAA,UACH,MAAM;AAAA,QACV;AAAA,MACJ;AACA,UAAI,aAAa,YAAY,GAAG;AAC5B,mBAAW,QAAQ,IAAI,IAAI,kBAAkB;AAAA,UACzC,GAAG;AAAA,UACH,QAAQ;AAAA,QACZ,CAAC;AAAA,MACL,WACS,gBAAgB,YAAY,GAAG;AACpC,mBAAW,QAAQ,IAAI,IAAI,qBAAqB;AAAA,UAC5C,GAAG;AAAA,UACH,QAAQ;AAAA,QACZ,CAAC;AAAA,MACL,OACK;AACD,mBAAW,QAAQ,IAAI,IAAI,uBAAuB;AAAA,UAC9C,GAAG;AAAA,UACH,QAAQ;AAAA,QACZ,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,oBAAoB,QAAQ,cAAc;AAC7D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,MAAI,mBAAmB,MAAM;AACzB,WAAO,mBAAmB,MAAM;AAAA,EACpC;AACA,QAAM,gBAAgB,CAAC;AACvB,aAAW,aAAa,oBAAoB;AACxC,UAAM,kBAAkB,gBAAgB,WAAW,MAAM;AACzD,QAAI,oBAAoB,QAAW;AAC/B,oBAAc,KAAK,SAAS;AAAA,IAChC,WACS,oBAAoB,MAAM;AAC/B,oBAAc,KAAK,eAAe;AAAA,IACtC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,QAAQ,UAAU;AAjR7C;AAkRI,QAAM,OAAO,OAAO,QAAQ,QAAQ;AACpC,QAAM,aAAa,CAAC,WAAW,IAAI;AACnC,MAAI,aAAa,IAAI,GAAG;AACpB,eAAW,KAAK,WAAW,gBAAgB,WAAW,WAAW;AACjE,QAAI,eAAa,YAAO,aAAa,MAApB,mBAAuB,OAAM;AAC1C,iBAAW,KAAK,WAAW,aAAa,WAAW,KAAK;AAAA,IAC5D,WACS,eAAa,YAAO,gBAAgB,MAAvB,mBAA0B,OAAM;AAClD,iBAAW,KAAK,WAAW,aAAa,WAAW,QAAQ;AAAA,IAC/D,WACS,eAAa,YAAO,oBAAoB,MAA3B,mBAA8B,OAAM;AACtD,iBAAW,KAAK,WAAW,aAAa,WAAW,YAAY;AAAA,IACnE;AAAA,EACJ,WACS,kBAAkB,IAAI,GAAG;AAC9B,eAAW,KAAK,WAAW,iBAAiB;AAAA,EAChD,WACS,gBAAgB,IAAI,GAAG;AAC5B,eAAW,KAAK,WAAW,gBAAgB,WAAW,eAAe,WAAW,cAAc;AAAA,EAClG,WACS,YAAY,IAAI,GAAG;AACxB,eAAW,KAAK,WAAW,gBAAgB,WAAW,eAAe,WAAW,UAAU;AAAA,EAC9F,WACS,WAAW,IAAI,GAAG;AACvB,eAAW,KAAK,WAAW,SAAS;AAAA,EACxC,WACS,aAAa,IAAI,GAAG;AACzB,eAAW,KAAK,WAAW,WAAW;AAAA,EAC1C;AACA,SAAO;AACX;AACA,SAAS,cAAc,QAAQ,cAAc,UAAU;AACnD,QAAM,aAAa,kBAAkB,QAAQ,QAAQ;AACrD,MAAI;AACJ,QAAM,QAAQ,CAAC,GAAG,UAAU;AAC5B,SAAO,CAAC,cAAc,MAAM,SAAS,GAAG;AAEpC,UAAM,OAAO,MAAM,IAAI;AACvB,iBAAa,aAAa,IAAI;AAAA,EAClC;AACA,SAAO,cAAc,OAAO,aAAa;AAC7C;AACA,SAAS,mBAAmB,QAAQ,UAAU;AA5T9C;AA6TI,QAAM,OAAO,OAAO,QAAQ,QAAQ;AACpC,QAAM,aAAa,CAAC,WAAW,KAAK;AACpC,MAAI,aAAa,IAAI,GAAG;AACpB,eAAW,KAAK,WAAW,iBAAiB,WAAW,YAAY;AACnE,QAAI,eAAa,YAAO,aAAa,MAApB,mBAAuB,OAAM;AAC1C,iBAAW,KAAK,WAAW,YAAY,WAAW,gBAAgB;AAAA,IACtE,WACS,eAAa,YAAO,gBAAgB,MAAvB,mBAA0B,OAAM;AAClD,iBAAW,KAAK,WAAW,YAAY,WAAW,mBAAmB;AAAA,IACzE,WACS,eAAa,YAAO,oBAAoB,MAA3B,mBAA8B,OAAM;AACtD,iBAAW,KAAK,WAAW,YAAY,WAAW,uBAAuB;AAAA,IAC7E;AAAA,EACJ,WACS,gBAAgB,IAAI,GAAG;AAC5B,eAAW,KAAK,WAAW,iBAAiB,WAAW,eAAe;AAAA,EAC1E,WACS,kBAAkB,IAAI,GAAG;AAC9B,eAAW,KAAK,WAAW,kBAAkB;AAAA,EACjD;AACA,SAAO;AACX;AACA,SAAS,eAAe,QAAQ,cAAc,UAAU;AACpD,QAAM,aAAa,mBAAmB,QAAQ,QAAQ;AACtD,MAAI;AACJ,QAAM,QAAQ,CAAC,GAAG,UAAU;AAC5B,SAAO,CAAC,eAAe,MAAM,SAAS,GAAG;AAErC,UAAM,OAAO,MAAM,IAAI;AAEvB,kBAAc,aAAa,IAAI;AAAA,EACnC;AACA,SAAO,eAAe;AAC1B;AACA,SAAS,kBAAkB,cAAc;AACrC,QAAM,iBAAiB,aAAa,WAAW,QAAQ;AACvD,SAAO,kBAAkB,OAAO,iBAAiB;AACrD;AACA,SAAS,mBAAmB,cAAc;AACtC,QAAM,kBAAkB,aAAa,WAAW,SAAS;AACzD,SAAO,mBAAmB,OAAO,kBAAkB;AACvD;AACA,SAAS,mBAAmB,cAAc;AACtC,QAAM,kBAAkB,aAAa,WAAW,UAAU;AAC1D,SAAO,mBAAmB,OAAO,kBAAkB;AACvD;AACO,SAAS,gBAAgB,MAAM;AAClC,MAAI,aAAa,IAAI,GAAG;AACpB,UAAM,SAAS,KAAK,SAAS;AAC7B,QAAI,OAAO,WAAW,MAAM;AACxB,YAAM,SAAS,CAAC;AAChB,iBAAW,aAAa,OAAO,QAAQ;AACnC,cAAM,cAAc,OAAO,OAAO,SAAS;AAC3C,YAAI,YAAY,WAAW,MAAM;AAC7B,iBAAO,KAAK,YAAY,OAAO;AAAA,QACnC;AAAA,MACJ;AACA,aAAO,UAAU;AAAA,QACb,GAAG,OAAO;AAAA,QACV,MAAM,KAAK;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,qBAAqB,MAAM;AAClC,aAAO,oBAAoB,OAAO,kBAAkB,IAAI,WAAS;AAAA,QAC7D,GAAG;AAAA,QACH,MAAM,KAAK;AAAA,QACX,QAAQ;AAAA,MACZ,EAAE;AAAA,IACN;AACA,WAAO,IAAI,kBAAkB,MAAM;AAAA,EACvC,WACS,gBAAgB,IAAI,GAAG;AAC5B,UAAM,SAAS,KAAK,SAAS;AAC7B,QAAI,OAAO,WAAW,MAAM;AACxB,YAAM,SAAS,CAAC;AAChB,iBAAW,aAAa,OAAO,QAAQ;AACnC,cAAM,cAAc,OAAO,OAAO,SAAS;AAC3C,YAAI,YAAY,WAAW,MAAM;AAC7B,iBAAO,KAAK,YAAY,OAAO;AAAA,QACnC;AAAA,MACJ;AACA,aAAO,UAAU;AAAA,QACb,GAAG,OAAO;AAAA,QACV,MAAM,KAAK;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,qBAAqB,MAAM;AAClC,aAAO,oBAAoB,OAAO,kBAAkB,IAAI,WAAS;AAAA,QAC7D,GAAG;AAAA,QACH,MAAM,KAAK;AAAA,QACX,QAAQ;AAAA,MACZ,EAAE;AAAA,IACN;AACA,WAAO,IAAI,qBAAqB,MAAM;AAAA,EAC1C,WACS,kBAAkB,IAAI,GAAG;AAC9B,UAAM,SAAS,KAAK,SAAS;AAC7B,QAAI,OAAO,WAAW,MAAM;AACxB,YAAM,SAAS,CAAC;AAChB,iBAAW,aAAa,OAAO,QAAQ;AACnC,cAAM,cAAc,OAAO,OAAO,SAAS;AAC3C,YAAI,YAAY,WAAW,MAAM;AAC7B,iBAAO,KAAK,YAAY,OAAO;AAAA,QACnC;AAAA,MACJ;AACA,aAAO,UAAU;AAAA,QACb,GAAG,OAAO;AAAA,QACV,MAAM,KAAK;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,qBAAqB,MAAM;AAClC,aAAO,oBAAoB,OAAO,kBAAkB,IAAI,WAAS;AAAA,QAC7D,GAAG;AAAA,QACH,MAAM,KAAK;AAAA,QACX,QAAQ;AAAA,MACZ,EAAE;AAAA,IACN;AACA,WAAO,IAAI,uBAAuB,MAAM;AAAA,EAC5C,WACS,WAAW,IAAI,GAAG;AACvB,UAAM,SAAS,KAAK,SAAS;AAC7B,QAAI,OAAO,WAAW,MAAM;AACxB,YAAM,SAAS,CAAC;AAChB,iBAAW,WAAW,OAAO,QAAQ;AACjC,cAAM,kBAAkB,OAAO,OAAO,OAAO;AAC7C,YAAI,gBAAgB,WAAW,MAAM;AACjC,iBAAO,KAAK,gBAAgB,OAAO;AAAA,QACvC;AAAA,MACJ;AACA,aAAO,UAAU;AAAA,QACb,GAAG,OAAO;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,qBAAqB,MAAM;AAClC,aAAO,oBAAoB,OAAO,kBAAkB,IAAI,WAAS;AAAA,QAC7D,GAAG;AAAA,QACH,QAAQ;AAAA,MACZ,EAAE;AAAA,IACN;AACA,WAAO,IAAI,gBAAgB,MAAM;AAAA,EACrC,OACK;AACD,WAAO;AAAA,EACX;AACJ;;;AKjdA;AA6BO,SAAS,WAAW,QAAQ;AAC/B,YAAU,OAAO,WAAW,GAAG,OAAO,cAAc,CAAC;AACrD,SAAO;AACX;AACO,SAAS,UAAU,iBAAiB,YAAY;AACnD,QAAM,qBAAqB,uBAAO,OAAO,IAAI;AAI7C,aAAW,YAAY,iBAAiB;AACpC,UAAM,YAAY,gBAAgB,QAAQ;AAC1C,QAAI,aAAa,QAAQ,SAAS,WAAW,IAAI,GAAG;AAChD;AAAA,IACJ;AACA,UAAM,aAAa,UAAU;AAC7B,QAAI,WAAW,WAAW,IAAI,GAAG;AAC7B;AAAA,IACJ;AACA,QAAI,mBAAmB,UAAU,KAAK,MAAM;AACxC,cAAQ,KAAK,8BAA8B,UAAU,sDAAsD;AAC3G;AAAA,IACJ;AACA,uBAAmB,UAAU,IAAI;AAAA,EAIrC;AAEA,aAAW,YAAY,oBAAoB;AACvC,UAAM,YAAY,mBAAmB,QAAQ;AAC7C,oBAAgB,QAAQ,IAAI;AAAA,EAChC;AAEA,aAAW,QAAQ,YAAY;AAC3B,SAAK,OAAO,KAAK,KAAK,OAAO,SAAO;AAChC,UAAI,OAAO,SAAS,IAAI,IAAI;AAC5B,aAAO,IAAI,SAAS;AAAA,IACxB,CAAC;AAAA,EACL;AACA,aAAW,YAAY,iBAAiB;AACpC,UAAM,YAAY,gBAAgB,QAAQ;AAE1C,QAAI,CAAC,SAAS,WAAW,IAAI,KAAK,YAAY,oBAAoB;AAC9D,UAAI,aAAa,MAAM;AACnB,sBAAc,SAAS;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ;AACA,aAAW,YAAY,iBAAiB;AACpC,QAAI,CAAC,SAAS,WAAW,IAAI,KAAK,EAAE,YAAY,qBAAqB;AACjE,aAAO,gBAAgB,QAAQ;AAAA,IACnC;AAAA,EACJ;AACA,WAAS,cAAc,MAAM;AACzB,QAAI,aAAa,IAAI,GAAG;AACpB,iBAAW,IAAI;AACf,qBAAe,IAAI;AACnB;AAAA,IACJ,WACS,gBAAgB,IAAI,GAAG;AAC5B,iBAAW,IAAI;AACf,UAAI,mBAAmB,MAAM;AACzB,uBAAe,IAAI;AAAA,MACvB;AACA;AAAA,IACJ,WACS,YAAY,IAAI,GAAG;AACxB,0BAAoB,IAAI;AACxB;AAAA,IACJ,WACS,kBAAkB,IAAI,GAAG;AAC9B,sBAAgB,IAAI;AACpB;AAAA,IACJ,WACS,WAAW,IAAI,GAAG;AACvB;AAAA,IACJ;AACA,UAAM,IAAI,MAAM,2BAA2B,IAAI,EAAE;AAAA,EACrD;AACA,WAAS,WAAW,MAAM;AACtB,UAAM,WAAW,KAAK,UAAU;AAChC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,GAAG;AACjD,YAAM,KACD,IAAI,SAAO;AACZ,YAAI,OAAO,SAAS,IAAI,IAAI;AAC5B,eAAO,IAAI,SAAS,OAAO,OAAO;AAAA,MACtC,CAAC,EACI,OAAO,OAAO;AACnB,YAAM,OAAO,SAAS,MAAM,IAAI;AAChC,UAAI,MAAM,SAAS,MAAM;AACrB,eAAO,SAAS,GAAG;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,eAAe,MAAM;AAC1B,QAAI,mBAAmB,MAAM;AACzB,YAAM,aAAa,KAAK,cAAc;AACtC,iBAAW,KAAK,GAAG,WACd,OAAO,CAAC,EACR,IAAI,WAAS,SAAS,KAAK,CAAC,EAC5B,OAAO,OAAO,CAAC;AAAA,IACxB;AAAA,EACJ;AACA,WAAS,gBAAgB,MAAM;AAC3B,UAAM,WAAW,KAAK,UAAU;AAChC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,GAAG;AACjD,YAAM,OAAO,SAAS,MAAM,IAAI;AAChC,UAAI,MAAM,SAAS,MAAM;AACrB,eAAO,SAAS,GAAG;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,oBAAoB,MAAM;AAC/B,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,KAAK,GAAG,MACT,OAAO,CAAC,EACR,IAAI,OAAK,SAAS,CAAC,CAAC,EACpB,OAAO,OAAO,CAAC;AAAA,EACxB;AACA,WAAS,SAAS,MAAM;AAEpB,QAAI,WAAW,IAAI,GAAG;AAClB,YAAM,aAAa,SAAS,KAAK,MAAM;AACvC,aAAO,cAAc,OAAO,IAAI,YAAY,UAAU,IAAI;AAAA,IAC9D,WACS,cAAc,IAAI,GAAG;AAC1B,YAAM,aAAa,SAAS,KAAK,MAAM;AACvC,aAAO,cAAc,OAAO,IAAI,eAAe,UAAU,IAAI;AAAA,IACjE,WACS,YAAY,IAAI,GAAG;AAOxB,YAAM,eAAe,gBAAgB,KAAK,IAAI;AAC9C,UAAI,gBAAgB,SAAS,cAAc;AACvC,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;;;AC5KA;AACO,SAAS,uBAAuB,QAEvC,8BAA8B;AAH9B;AAII,QAAM,YAAY,uBAAO,OAAO,IAAI;AACpC,QAAM,UAAU,OAAO,WAAW;AAClC,aAAW,YAAY,SAAS;AAC5B,QAAI,CAAC,SAAS,WAAW,IAAI,GAAG;AAC5B,YAAM,OAAO,QAAQ,QAAQ;AAC7B,UAAI,aAAa,IAAI,GAAG;AACpB,YAAI,CAAC,sBAAsB,IAAI,GAAG;AAC9B,gBAAM,SAAS,KAAK,SAAS;AAC7B,iBAAO,OAAO;AACd,oBAAU,QAAQ,IAAI,IAAI,kBAAkB,MAAM;AAAA,QACtD;AAAA,MACJ,WACS,WAAW,IAAI,GAAG;AACvB,kBAAU,QAAQ,IAAI,CAAC;AACvB,cAAM,SAAS,KAAK,UAAU;AAC9B,mBAAW,SAAS,QAAQ;AACxB,oBAAU,QAAQ,EAAE,MAAM,IAAI,IAAI,MAAM;AAAA,QAC5C;AAAA,MACJ,WACS,gBAAgB,IAAI,GAAG;AAC5B,YAAI,KAAK,eAAe,MAAM;AAC1B,oBAAU,QAAQ,IAAI;AAAA,YAClB,eAAe,KAAK;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,WACS,YAAY,IAAI,GAAG;AACxB,YAAI,KAAK,eAAe,MAAM;AAC1B,oBAAU,QAAQ,IAAI;AAAA,YAClB,eAAe,KAAK;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,WACS,aAAa,IAAI,GAAG;AACzB,kBAAU,QAAQ,IAAI,CAAC;AACvB,YAAI,KAAK,YAAY,MAAM;AACvB,oBAAU,QAAQ,EAAE,aAAa,KAAK;AAAA,QAC1C;AACA,cAAM,SAAS,KAAK,UAAU;AAC9B,mBAAW,aAAa,QAAQ;AAC5B,gBAAM,QAAQ,OAAO,SAAS;AAC9B,cAAI,MAAM,aAAa,MAAM;AACzB,sBAAU,QAAQ,EAAE,SAAS,IAAI,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACpE,sBAAU,QAAQ,EAAE,SAAS,EAAE,YAAY,MAAM;AAAA,UACrD;AACA,cAAI,MAAM,WAAW,UAAQ,WAAM,YAAN,mBAAe,UAAS,wBAAwB;AACzE,qBAAQ,WAAM,YAAN,mBAAe,MAAM;AAAA,cACzB,KAAK;AACD,oBAAI,CAAC,8BAA8B;AAC/B;AAAA,gBACJ;AACA;AAAA,cACJ,KAAK;AACD;AAAA,YACR;AACA,sBAAU,QAAQ,EAAE,SAAS,IAAI,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACpE,sBAAU,QAAQ,EAAE,SAAS,EAAE,UAAU,MAAM;AAAA,UACnD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;ACnEA;AACO,SAAS,aAAa,QAAQ,IAAI;AACrC,QAAM,UAAU,OAAO,WAAW;AAClC,aAAW,YAAY,SAAS;AAC5B,UAAM,OAAO,QAAQ,QAAQ;AAE7B,QAAI,CAAC,aAAa,IAAI,EAAE,KAAK,WAAW,IAAI,KAAK,aAAa,IAAI,GAAG;AACjE,YAAM,SAAS,KAAK,UAAU;AAC9B,iBAAW,aAAa,QAAQ;AAC5B,cAAM,QAAQ,OAAO,SAAS;AAC9B,WAAG,OAAO,UAAU,SAAS;AAAA,MACjC;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACdA;AACO,SAAS,oBAAoB,QAAQ,IAAI;AAC5C,QAAM,UAAU,OAAO,WAAW;AAClC,aAAW,YAAY,SAAS;AAC5B,UAAM,OAAO,QAAQ,QAAQ;AAC7B,QAAI,CAAC,aAAa,IAAI,EAAE,KAAK,WAAW,IAAI,GAAG;AAC3C,UAAI,aAAa,IAAI,GAAG;AACpB,cAAM,SAAS,KAAK,UAAU;AAC9B,mBAAW,aAAa,QAAQ;AAC5B,gBAAM,QAAQ,OAAO,SAAS;AAC9B,qBAAW,OAAO,MAAM,MAAM;AAC1B,gBAAI,eAAe,GAAG,IAAI,MAAM,IAAI,YAAY;AAAA,UACpD;AAAA,QACJ;AAAA,MACJ,WACS,kBAAkB,IAAI,GAAG;AAC9B,cAAM,SAAS,KAAK,UAAU;AAC9B,mBAAW,aAAa,QAAQ;AAC5B,gBAAM,QAAQ,OAAO,SAAS;AAC9B,gBAAM,eAAe,GAAG,MAAM,MAAM,MAAM,YAAY;AAAA,QAC1D;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACGA;;;AC3BA;;;ACCO,SAAS,UAAU,SAAS,mBAAmB,OAAO,gBAAgB,OAAO,qBAAqB,OAAO;AAC5G,MAAI;AACJ,MAAI,YAAY;AAChB,QAAM,2BAA2B,QAAQ,MAAM,YAAU;AACrD,QAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,UAAI,mBAAmB,QAAW;AAC9B,yBAAiB,OAAO;AACxB,eAAO;AAAA,MACX,WACS,mBAAmB,OAAO,QAAQ;AACvC,eAAO;AAAA,MACX;AAAA,IACJ,OACK;AACD,kBAAY;AAAA,IAChB;AACA,WAAO;AAAA,EACX,CAAC;AACD,MAAI,sBAAsB,0BAA0B;AAChD,WAAO,IAAI,MAAM,cAAc,EAAE,KAAK,IAAI,EAAE,IAAI,CAAC,GAAG,UAAU,UAAU,QAAQ,IAAI,YAAU,OAAO,KAAK,CAAC,GAAG,kBAAkB,eAAe,kBAAkB,CAAC;AAAA,EACtK;AACA,MAAI,WAAW;AACX,WAAO,QAAQ,KAAK,CAAC;AAAA,EACzB;AACA,MAAI;AACJ,MAAI;AACJ,MAAI,kBAAkB;AAClB,wBAAoB,QAAQ,KAAK,YAAU,SAAS,MAAM,CAAC;AAC3D,QAAI,UAAU,MAAM;AAChB,eAAS,CAAC;AAAA,IACd;AACA,QAAI,mBAAmB;AACnB,aAAO,eAAe,QAAQ,OAAO,OAAO,OAAO,eAAe,iBAAiB,CAAC,CAAC;AAAA,IACzF;AAAA,EACJ;AACA,aAAW,UAAU,SAAS;AAC1B,QAAI,SAAS,MAAM,GAAG;AAClB,UAAI,mBAAmB;AACnB,cAAM,kBAAkB,OAAO,eAAe,MAAM;AACpD,cAAM,kBAAkB,OAAO,eAAe,MAAM;AACpD,YAAI,iBAAiB;AACjB,qBAAW,OAAO,OAAO,oBAAoB,eAAe,GAAG;AAC3D,kBAAM,aAAa,OAAO,yBAAyB,iBAAiB,GAAG;AACvE,gBAAI,OAAO,UAAU,GAAG;AACpB,qBAAO,eAAe,iBAAiB,KAAK,UAAU;AAAA,YAC1D;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,iBAAW,OAAO,QAAQ;AACtB,YAAI,UAAU,MAAM;AAChB,mBAAS,CAAC;AAAA,QACd;AACA,YAAI,OAAO,QAAQ;AACf,iBAAO,GAAG,IAAI,UAAU,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,kBAAkB,eAAe,kBAAkB;AAAA,QAC3G,OACK;AACD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ,WACS,MAAM,QAAQ,MAAM,GAAG;AAC5B,UAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,iBAAS;AAAA,MACb,OACK;AACD,iBAAS,UAAU,CAAC,QAAQ,MAAM,GAAG,kBAAkB,eAAe,kBAAkB;AAAA,MAC5F;AAAA,IACJ,OACK;AACD,eAAS;AAAA,IACb;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,SAAS,MAAM;AACpB,SAAO,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM,QAAQ,IAAI;AAClE;;;AC9EA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACAA;;;ACGO,IAAM,iBAAN,cAA6B,IAAI;AAAA,EACpC,KAAK,OAAO,WAAW,IAAI;AACvB,WAAO;AAAA,EACX;AAAA,EACA,IAAI,KAAK,MAAM;AACX,UAAM,QAAQ,KAAK,IAAI,GAAG;AAC1B,QAAI,UAAU,QAAW;AACrB,WAAK,IAAI,KAAK,CAAC,IAAI,CAAC;AAAA,IACxB,OACK;AACD,YAAM,KAAK,IAAI;AAAA,IACnB;AAAA,EACJ;AACJ;;;AChBA;AAIO,IAAM,wBAAwB,IAAI,iBAAiB;AAAA,EACtD,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW,CAAC,kBAAkB,iBAAiB,kBAAkB,eAAe;AAAA,EAChF,MAAM;AAAA,IACF,IAAI;AAAA,MACA,MAAM,IAAI,eAAe,cAAc;AAAA,MACvC,aAAa;AAAA,MACb,cAAc;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,EACJ;AACJ,CAAC;AAIM,IAAM,yBAAyB,IAAI,iBAAiB;AAAA,EACvD,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW,CAAC,kBAAkB,KAAK;AAAA,EACnC,MAAM;AAAA,IACF,IAAI;AAAA,MACA,MAAM,IAAI,eAAe,cAAc;AAAA,MACvC,aAAa;AAAA,MACb,cAAc;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,EACJ;AACJ,CAAC;;;AFvCD,SAAS,kBAAkB,QAAQ,WAAW,gBAAgB,aAAa,cAAc,QAAQ,SAAS,sBAAsB;AAC5H,aAAW,aAAa,aAAa,YAAY;AAC7C,YAAQ,UAAU,MAAM;AAAA,MACpB,KAAK,KAAK,OAAO;AACb,YAAI,CAAC,kBAAkB,gBAAgB,SAAS,GAAG;AAC/C;AAAA,QACJ;AACA,eAAO,IAAI,iBAAiB,SAAS,GAAG,SAAS;AACjD;AAAA,MACJ;AAAA,MACA,KAAK,KAAK,iBAAiB;AACvB,YAAI,CAAC,kBAAkB,gBAAgB,SAAS,KAC5C,CAAC,2BAA2B,QAAQ,WAAW,WAAW,GAAG;AAC7D;AAAA,QACJ;AACA,cAAM,QAAQ,eAAe,gBAAgB,SAAS;AACtD,YAAI,OAAO;AACP,gBAAM,cAAc,IAAI,eAAe;AACvC,4BAAkB,QAAQ,WAAW,gBAAgB,aAAa,UAAU,cAAc,aAAa,SAAS,oBAAoB;AACpI,kBAAQ,KAAK;AAAA,YACT,OAAO,MAAM;AAAA,YACb,QAAQ;AAAA,UACZ,CAAC;AAAA,QACL,OACK;AACD,4BAAkB,QAAQ,WAAW,gBAAgB,aAAa,UAAU,cAAc,QAAQ,SAAS,oBAAoB;AAAA,QACnI;AACA;AAAA,MACJ;AAAA,MACA,KAAK,KAAK,iBAAiB;AACvB,cAAM,WAAW,UAAU,KAAK;AAChC,YAAI,CAAC,kBAAkB,gBAAgB,SAAS,GAAG;AAC/C;AAAA,QACJ;AACA,cAAM,QAAQ,eAAe,gBAAgB,SAAS;AACtD,YAAI,qBAAqB,IAAI,QAAQ,KAAK,CAAC,OAAO;AAC9C;AAAA,QACJ;AACA,cAAM,WAAW,UAAU,QAAQ;AACnC,YAAI,CAAC,YAAY,CAAC,2BAA2B,QAAQ,UAAU,WAAW,GAAG;AACzE;AAAA,QACJ;AACA,YAAI,CAAC,OAAO;AACR,+BAAqB,IAAI,QAAQ;AAAA,QACrC;AACA,YAAI,OAAO;AACP,gBAAM,cAAc,IAAI,eAAe;AACvC,4BAAkB,QAAQ,WAAW,gBAAgB,aAAa,SAAS,cAAc,aAAa,SAAS,oBAAoB;AACnI,kBAAQ,KAAK;AAAA,YACT,OAAO,MAAM;AAAA,YACb,QAAQ;AAAA,UACZ,CAAC;AAAA,QACL,OACK;AACD,4BAAkB,QAAQ,WAAW,gBAAgB,aAAa,SAAS,cAAc,QAAQ,SAAS,oBAAoB;AAAA,QAClI;AACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAmBO,SAAS,kBAAkB,gBAAgB,MAAM;AACpD,QAAM,OAAO,mBAAmB,sBAAsB,MAAM,cAAc;AAC1E,OAAI,6BAAO,WAAU,MAAM;AACvB,WAAO;AAAA,EACX;AACA,QAAM,UAAU,mBAAmB,yBAAyB,MAAM,cAAc;AAChF,OAAI,mCAAU,WAAU,OAAO;AAC3B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIO,SAAS,2BAA2B,QAAQ,UAAU,MAAM;AAC/D,QAAM,oBAAoB,SAAS;AACnC,MAAI,CAAC,mBAAmB;AACpB,WAAO;AAAA,EACX;AACA,QAAM,kBAAkB,YAAY,QAAQ,iBAAiB;AAC7D,MAAI,oBAAoB,MAAM;AAC1B,WAAO;AAAA,EACX;AACA,MAAI,eAAe,eAAe,GAAG;AACjC,UAAM,gBAAgB,OAAO,iBAAiB,eAAe;AAC7D,WAAO,cAAc,SAAS,IAAI;AAAA,EACtC;AACA,SAAO;AACX;AAIO,SAAS,iBAAiB,MAAM;AACnC,SAAO,KAAK,QAAQ,KAAK,MAAM,QAAQ,KAAK,KAAK;AACrD;AAMO,SAAS,eAAe,gBAAgB,MAAM;AACjD,QAAM,QAAQ,mBAAmB,uBAAuB,MAAM,cAAc;AAC5E,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,MAAI,MAAM,IAAI,MAAM,OAAO;AACvB;AAAA,EACJ;AACA,SAAO;AAAA,IACH,OAAO,OAAO,MAAM,OAAO,MAAM,WAAW,MAAM,OAAO,IAAI;AAAA,EACjE;AACJ;AAUO,IAAM,mBAAmB,SAAS,SAAS,iBAAiB,QAAQ,WAAW,gBAAgB,YAAY,YAAY;AAC1H,QAAM,gBAAgB,IAAI,eAAe;AACzC,QAAM,uBAAuB,oBAAI,IAAI;AACrC,QAAM,aAAa,CAAC;AACpB,QAAM,sBAAsB;AAAA,IACxB,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AACA,aAAW,QAAQ,YAAY;AAC3B,QAAI,KAAK,cAAc;AACnB,wBAAkB,QAAQ,WAAW,gBAAgB,YAAY,KAAK,cAAc,eAAe,YAAY,oBAAoB;AAAA,IACvI;AAAA,EACJ;AACA,SAAO;AACX,CAAC;;;AG9JD;AAEO,SAAS,4BAA4B,cAAc,eAAe;AACrE,QAAM,MAAM,gBAAgB,cAAc,aAAa;AACvD,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,MAAM,0BAA0B,iBAAiB,EAAE,EAAE;AAAA,EACnE;AACA,SAAO;AACX;AACO,IAAM,6BAA6B,SAAS,SAASC,4BAA2B,SAAS;AAC5F,SAAO,4BAA4B,QAAQ,UAAU,QAAQ,aAAa;AAC9E,CAAC;;;ACXD;AACO,SAAS,eAAe,QAAQ;AACnC,SAAO,UAAU,OAAO,WAAW,YAAY,UAAU,UAAU,OAAO,SAAS,KAAK;AAC5F;;;ACCA,IAAM,qBAAqB,SAAS,SAASC,oBAAmB,QAAQ,cAAc;AAClF,SAAO,SAAS,eAAe,MAAM;AACjC,WAAO,QAAQ,MAAM,cAAc,QAAQ,IAAI;AAAA,EACnD;AACJ,CAAC;;;ACRD;;;ACGA,SAAS,0BAA0B,YAAY,kBAAkB;AAC7D,eAAa,cAAc,CAAC;AAC5B,QAAM,EAAE,YAAY,oBAAoB,GAAG,KAAK,IAAI;AACpD,QAAM,kBAAkB;AAAA,IACpB,GAAG;AAAA,EACP;AACA,MAAI,CAAC,kBAAkB;AACnB,QAAI,sBAAsB,MAAM;AAC5B,YAAM,aAAa,CAAC;AACpB,iBAAW,iBAAiB,oBAAoB;AAC5C,mBAAW,aAAa,IAAI,CAAC,GAAG,QAAQ,mBAAmB,aAAa,CAAC,CAAC;AAAA,MAC9E;AACA,sBAAgB,aAAa;AAAA,IACjC;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,4BAA4B,QAAQ,mBAAmB,OAAO;AAC1E,QAAM,SAAS;AAAA,IACX,kBAAkB,0BAA0B,OAAO,YAAY,gBAAgB;AAAA,IAC/E,OAAO,CAAC;AAAA,EACZ;AACA,YAAU,QAAQ;AAAA,IACd,CAAC,WAAW,WAAW,GAAG,UAAQ;AAC9B,aAAO,MAAM,KAAK,IAAI,IAAI;AAAA,QACtB,QAAQ,CAAC;AAAA,QACT,MAAM;AAAA,QACN,YAAY,0BAA0B,KAAK,YAAY,gBAAgB;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,IACA,CAAC,WAAW,cAAc,GAAG,UAAQ;AACjC,aAAO,MAAM,KAAK,IAAI,IAAI;AAAA,QACtB,QAAQ,CAAC;AAAA,QACT,MAAM;AAAA,QACN,YAAY,0BAA0B,KAAK,YAAY,gBAAgB;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,IACA,CAAC,WAAW,KAAK,GAAG,CAAC,OAAO,WAAW,aAAa;AAChD,aAAO,MAAM,QAAQ,EAAE,OAAO,SAAS,IAAI;AAAA,QACvC,WAAW,CAAC;AAAA,QACZ,YAAY,0BAA0B,MAAM,YAAY,gBAAgB;AAAA,MAC5E;AACA,YAAM,OAAO,MAAM;AACnB,UAAI,QAAQ,MAAM;AACd,mBAAW,WAAW,MAAM;AACxB,iBAAO,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,UAAU,OAAO,IACtD,0BAA0B,KAAK,OAAO,EAAE,YAAY,gBAAgB;AAAA,QAC5E;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA,CAAC,WAAW,SAAS,GAAG,UAAQ;AAC5B,aAAO,MAAM,KAAK,IAAI,IAAI;AAAA,QACtB,QAAQ,CAAC;AAAA,QACT,MAAM;AAAA,QACN,YAAY,0BAA0B,KAAK,YAAY,gBAAgB;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,IACA,CAAC,WAAW,UAAU,GAAG,CAAC,OAAO,UAAU,SAAS,cAAc;AAC9D,aAAO,MAAM,QAAQ,EAAE,OAAO,SAAS,IAAI,0BAA0B,MAAM,YAAY,gBAAgB;AACvG,aAAO;AAAA,IACX;AAAA,IACA,CAAC,WAAW,WAAW,GAAG,UAAQ;AAC9B,aAAO,MAAM,KAAK,IAAI,IAAI;AAAA,QACtB,MAAM;AAAA,QACN,YAAY,0BAA0B,KAAK,YAAY,gBAAgB;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,IACA,CAAC,WAAW,UAAU,GAAG,UAAQ;AAC7B,aAAO,MAAM,KAAK,IAAI,IAAI;AAAA,QACtB,MAAM;AAAA,QACN,YAAY,0BAA0B,KAAK,YAAY,gBAAgB;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,IACA,CAAC,WAAW,iBAAiB,GAAG,UAAQ;AACpC,aAAO,MAAM,KAAK,IAAI,IAAI;AAAA,QACtB,QAAQ,CAAC;AAAA,QACT,MAAM;AAAA,QACN,YAAY,0BAA0B,KAAK,YAAY,gBAAgB;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,IACA,CAAC,WAAW,kBAAkB,GAAG,CAAC,OAAO,WAAW,aAAa;AAC7D,aAAO,MAAM,QAAQ,EAAE,OAAO,SAAS,IAAI;AAAA,QACvC,YAAY,0BAA0B,MAAM,YAAY,gBAAgB;AAAA,MAC5E;AACA,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACD,SAAO;AACX;;;AC9FA,IAAM,4BAA4B,SAAS,SAASC,2BAA0B,QAAQ;AAClF,QAAM,YAAY,oBAAI,IAAI;AAC1B,SAAO,iBAAiB,SAAS,OAAK;AAClC,eAAW,YAAY,WAAW;AAC9B,eAAS,CAAC;AAAA,IACd;AAAA,EACJ,GAAG,EAAE,MAAM,KAAK,CAAC;AACjB,SAAO;AACX,CAAC;AAOM,SAAS,4BAA4B,QAAQ,UAAU;AAE1D,MAAI,OAAO,SAAS;AAChB,aAAS;AACT;AAAA,EACJ;AACA,4BAA0B,MAAM,EAAE,IAAI,QAAQ;AAClD;AACO,IAAM,kBAAkB,SAAS,SAASC,iBAAgB,QAAQ;AAErE,MAAI,OAAO,SAAS;AAChB,WAAO,kBAAkB,OAAO,MAAM;AAAA,EAC1C;AACA,SAAO,IAAI,QAAQ,CAAC,UAAU,WAAW;AACrC,QAAI,OAAO,SAAS;AAChB,aAAO,OAAO,MAAM;AACpB;AAAA,IACJ;AACA,gCAA4B,QAAQ,MAAM;AACtC,aAAO,OAAO,MAAM;AAAA,IACxB,CAAC;AAAA,EACL,CAAC;AACL,CAAC;;;ApDvCM,SAAS,uBAAuB,QAAQ,4BAA4B,CAAC,GAAG;AAC3E,QAAM,EAAE,yBAAyB,8BAA8B,6BAA6B,IAAI;AAChG,MAAI,iCAAiC,2BAA2B,+BAA+B;AAC3F,UAAM,IAAI,UAAU,iOAE8C;AAAA,EACtE;AACA,eAAa,QAAQ,CAAC,OAAO,UAAU,cAAc;AAEjD,QAAI,8BAA8B;AAC9B,qBAAe,gCAAgC,8BAA8B,OAAO,UAAU,SAAS;AAAA,IAC3G;AAEA,QAAI,2BAA2B,MAAM,KAAK,SAAS,GAAG;AAClD,qBAAe,2BAA2B,yBAAyB,OAAO,UAAU,SAAS;AAAA,IACjG;AAEA,QAAI,iCAAiC,YAAY,CAAC,aAAa,aAAa,MAAM,IAAI,CAAC,GAAG;AACtF,qBAAe,gCAAgC,8BAA8B,OAAO,UAAU,SAAS;AAAA,IAC3G;AAAA,EACJ,CAAC;AACL;AACA,SAAS,eAAe,WAAW,UAAU,OAAO,UAAU,WAAW;AACrE,MAAI,CAAC,MAAM,SAAS;AAChB,UAAM,UAAU,yBAAyB,QAAQ,IAAI,SAAS;AAAA;AAAA;AAAA,MAGhE,SAAS;AAAA;AAEP,QAAI,aAAa,SAAS;AACtB,YAAM,IAAI,MAAM,OAAO;AAAA,IAC3B;AACA,QAAI,aAAa,QAAQ;AACrB,cAAQ,KAAK,OAAO;AAAA,IACxB;AACA;AAAA,EACJ;AACA,MAAI,OAAO,MAAM,YAAY,YAAY;AACrC,UAAM,IAAI,MAAM,aAAa,QAAQ,IAAI,SAAS,sBAAsB;AAAA,EAC5E;AACJ;;;AqD1CA;AACO,SAAS,eAAe,WAAW;AACtC,SAAO,CAAC,MAAM,MAAM,KAAK,SAAS,UAAU,OAAO,CAAC,MAAM,gBAAgB;AACtE,QAAI,eAAe,MAAM;AACrB,aAAO,YAAY,MAAM,MAAM,KAAK,IAAI;AAAA,IAC5C;AACA,WAAO,qBAAqB,MAAM,MAAM,KAAK,IAAI;AAAA,EACrD,GAAG,IAAI;AACX;;;ACRA;;;ACEO,SAAS,4BAA4B,QAAQ,gCAAgC;AAChF,YAAU,QAAQ;AAAA,IACd,CAAC,WAAW,aAAa,GAAG,UAAQ;AAChC,UAAI,CAAC,KAAK,aAAa;AACnB,cAAM,UAAU,SAAS,KAAK,IAAI;AAElC,YAAI,mCAAmC,SAAS;AAC5C,gBAAM,IAAI,MAAM,OAAO;AAAA,QAC3B;AACA,YAAI,mCAAmC,QAAQ;AAC3C,kBAAQ,KAAK,OAAO;AAAA,QACxB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;;;AClBO,SAAS,8BAA8B,QAAQ,WAAW;AAC7D,QAAM,oBAAoB,CAAC;AAC3B,QAAM,UAAU,OAAO,WAAW;AAClC,aAAW,YAAY,SAAS;AAC5B,UAAM,OAAO,QAAQ,QAAQ;AAC7B,QAAI,mBAAmB,MAAM;AACzB,wBAAkB,QAAQ,IAAI,CAAC;AAC/B,iBAAW,SAAS,KAAK,cAAc,GAAG;AACtC,YAAI,UAAU,MAAM,IAAI,GAAG;AACvB,qBAAW,aAAa,UAAU,MAAM,IAAI,GAAG;AAC3C,gBAAI,cAAc,gBAAgB,CAAC,UAAU,WAAW,IAAI,GAAG;AAC3D,gCAAkB,QAAQ,EAAE,SAAS,IAAI,UAAU,MAAM,IAAI,EAAE,SAAS;AAAA,YAC5E;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,gBAAgB,UAAU,QAAQ;AACxC,wBAAkB,QAAQ,IAAI;AAAA,QAC1B,GAAG,kBAAkB,QAAQ;AAAA,QAC7B,GAAG;AAAA,MACP;AAAA,IACJ,OACK;AACD,YAAM,gBAAgB,UAAU,QAAQ;AACxC,UAAI,iBAAiB,MAAM;AACvB,0BAAkB,QAAQ,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AF1BO,SAAS,qBAAqB,EAAE,QAAQ,WAAW,gBAAgB,sBAAAC,uBAAsB,4BAA4B,CAAC,GAAG,iCAAiC,OAAO,yBAAyB,MAAO,GAAG;AACvM,QAAM,EAAE,gCAAgC,SAAS,+BAA+B,IAAI;AACpF,QAAM,YAAY,iCACZ,8BAA8B,QAAQ,cAAc,IACpD;AACN,aAAW,YAAY,WAAW;AAC9B,UAAM,gBAAgB,UAAU,QAAQ;AACxC,UAAM,eAAe,OAAO;AAC5B,QAAI,iBAAiB,UAAU;AAC3B,YAAM,IAAI,MAAM,IAAI,QAAQ,kDAAkD,aAAa,iDAAiD;AAAA,IAChJ;AACA,UAAM,OAAO,OAAO,QAAQ,QAAQ;AACpC,QAAI,QAAQ,MAAM;AACd,YAAM,MAAM,IAAI,QAAQ;AACxB,UAAI,iCAAiC,kCAAkC,SAAS;AAC5E,YAAI,kCAAkC,QAAQ;AAC1C,kBAAQ,KAAK,GAAG;AAAA,QACpB;AACA;AAAA,MACJ;AACA,YAAM,IAAI,MAAM,GAAG;AAAA,IACvB,WACS,sBAAsB,IAAI,GAAG;AAElC,iBAAW,aAAa,eAAe;AACnC,YAAI,UAAU,WAAW,IAAI,GAAG;AAC5B,eAAK,UAAU,UAAU,CAAC,CAAC,IAAI,cAAc,SAAS;AAAA,QAC1D,OACK;AACD,eAAK,SAAS,IAAI,cAAc,SAAS;AAAA,QAC7C;AAAA,MACJ;AAAA,IACJ,WACS,WAAW,IAAI,GAAG;AACvB,YAAM,SAAS,KAAK,UAAU;AAC9B,iBAAW,aAAa,eAAe;AACnC,YAAI,CAAC,UAAU,WAAW,IAAI,KAC1B,CAAC,OAAO,KAAK,WAAS,MAAM,SAAS,SAAS,KAC9C,iCACA,kCAAkC,UAAU;AAC5C,gBAAM,MAAM,GAAG,KAAK,IAAI,IAAI,SAAS,qDAAqD,KAAK,IAAI;AACnG,cAAI,kCAAkC,SAAS;AAC3C,kBAAM,IAAI,MAAM,GAAG;AAAA,UACvB,OACK;AACD,oBAAQ,KAAK,GAAG;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,WACS,YAAY,IAAI,GAAG;AACxB,iBAAW,aAAa,eAAe;AACnC,YAAI,CAAC,UAAU,WAAW,IAAI,KAC1B,iCACA,kCAAkC,UAAU;AAC5C,gBAAM,MAAM,GAAG,KAAK,IAAI,IAAI,SAAS,kCAAkC,KAAK,IAAI;AAChF,cAAI,kCAAkC,SAAS;AAC3C,kBAAM,IAAI,MAAM,GAAG;AAAA,UACvB,OACK;AACD,oBAAQ,KAAK,GAAG;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,WACS,aAAa,IAAI,KAAK,gBAAgB,IAAI,GAAG;AAClD,iBAAW,aAAa,eAAe;AACnC,YAAI,CAAC,UAAU,WAAW,IAAI,GAAG;AAC7B,gBAAM,SAAS,KAAK,UAAU;AAC9B,gBAAM,QAAQ,OAAO,SAAS;AAC9B,cAAI,SAAS,MAAM;AAEf,gBAAI,iCAAiC,kCAAkC,UAAU;AAC7E,oBAAM,MAAM,GAAG,QAAQ,IAAI,SAAS;AACpC,kBAAI,kCAAkC,SAAS;AAC3C,sBAAM,IAAI,MAAM,GAAG;AAAA,cACvB,OACK;AACD,wBAAQ,MAAM,GAAG;AAAA,cACrB;AAAA,YACJ;AAAA,UACJ,OACK;AAED,kBAAM,eAAe,cAAc,SAAS;AAC5C,gBAAI,OAAO,iBAAiB,cAAc,OAAO,iBAAiB,UAAU;AACxE,oBAAM,IAAI,MAAM,YAAY,QAAQ,IAAI,SAAS,6BAA6B;AAAA,YAClF;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,yBACH,6BAA6B,QAAQ,WAAWA,qBAAoB,IACpE,6BAA6B,QAAQ,WAAWA,qBAAoB;AAC1E,MAAI,kCAAkC,mCAAmC,UAAU;AAC/E,gCAA4B,QAAQ,8BAA8B;AAAA,EACtE;AACA,SAAO;AACX;AACA,SAAS,6BAA6B,QAAQ,WAAWA,uBAAsB;AAzG/E;AA0GI,QAAM,UAAU,OAAO,WAAW;AAClC,aAAW,YAAY,WAAW;AAC9B,UAAM,OAAO,OAAO,QAAQ,QAAQ;AACpC,UAAM,gBAAgB,UAAU,QAAQ;AACxC,QAAI,aAAa,IAAI,GAAG;AACpB,iBAAW,aAAa,eAAe;AACnC,YAAI,UAAU,WAAW,IAAI,GAAG;AAC5B,eAAK,UAAU,UAAU,CAAC,CAAC,IAAI,cAAc,SAAS;AAAA,QAC1D,WACS,cAAc,aAAa,KAAK,WAAW,MAAM;AACtD,eAAK,UAAU;AAAA,YACX,GAAG,KAAK;AAAA,YACR,eAAa,oDAAe,YAAf,mBAAwB,gBACjC,KAAK,QAAQ;AAAA,YACjB,aAAa,KAAK,QAAQ,cAAc,CAAC,GAAG,SAAO,oDAAe,YAAf,mBAAwB,eAAc,CAAC,CAAC;AAAA,UAC/F;AAAA,QACJ,WACS,cAAc,uBAAuB,KAAK,qBAAqB,MAAM;AAC1E,eAAK,oBAAoB,KAAK,kBAAkB,QAAO,+CAAe,sBAAqB,CAAC,CAAC;AAAA,QACjG,WACS,cAAc,gBACnB,KAAK,cAAc,QACnB,cAAc,cAAc,MAAM;AAClC,eAAK,aAAa,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG,KAAK,YAAY,cAAc,UAAU;AAAA,QAClG,OACK;AACD,eAAK,SAAS,IAAI,cAAc,SAAS;AAAA,QAC7C;AAAA,MACJ;AAAA,IACJ,WACS,WAAW,IAAI,GAAG;AACvB,YAAM,SAAS,KAAK,SAAS;AAC7B,YAAM,qBAAqB,OAAO;AAClC,iBAAW,aAAa,eAAe;AACnC,YAAI,UAAU,WAAW,IAAI,GAAG;AAC5B,iBAAO,UAAU,UAAU,CAAC,CAAC,IAAI,cAAc,SAAS;AAAA,QAC5D,WACS,cAAc,aAAa,OAAO,WAAW,MAAM;AACxD,iBAAO,UAAU;AAAA,YACb,GAAG,OAAO;AAAA,YACV,eAAa,oDAAe,YAAf,mBAAwB,gBACjC,OAAO,QAAQ;AAAA,YACnB,aAAa,OAAO,QAAQ,cAAc,CAAC,GAAG,SAAO,oDAAe,YAAf,mBAAwB,eAAc,CAAC,CAAC;AAAA,UACjG;AAAA,QACJ,WACS,cAAc,uBAAuB,OAAO,qBAAqB,MAAM;AAC5E,iBAAO,oBAAoB,OAAO,kBAAkB,QAAO,+CAAe,sBAAqB,CAAC,CAAC;AAAA,QACrG,WACS,cAAc,gBACnB,KAAK,cAAc,QACnB,cAAc,cAAc,MAAM;AAClC,eAAK,aAAa,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG,KAAK,YAAY,cAAc,UAAU;AAAA,QAClG,WACS,mBAAmB,SAAS,GAAG;AACpC,6BAAmB,SAAS,EAAE,QAAQ,cAAc,SAAS;AAAA,QACjE;AAAA,MACJ;AACA,cAAQ,QAAQ,IAAI,IAAI,gBAAgB,MAAM;AAAA,IAClD,WACS,YAAY,IAAI,GAAG;AACxB,iBAAW,aAAa,eAAe;AACnC,YAAI,UAAU,WAAW,IAAI,GAAG;AAC5B,eAAK,UAAU,UAAU,CAAC,CAAC,IAAI,cAAc,SAAS;AAAA,QAC1D;AAAA,MACJ;AAAA,IACJ,WACS,aAAa,IAAI,KAAK,gBAAgB,IAAI,GAAG;AAClD,iBAAW,aAAa,eAAe;AACnC,YAAI,UAAU,WAAW,IAAI,GAAG;AAE5B,eAAK,UAAU,UAAU,CAAC,CAAC,IAAI,cAAc,SAAS;AACtD;AAAA,QACJ;AACA,cAAM,SAAS,KAAK,UAAU;AAC9B,cAAM,QAAQ,OAAO,SAAS;AAC9B,YAAI,SAAS,MAAM;AACf,gBAAM,eAAe,cAAc,SAAS;AAC5C,cAAI,OAAO,iBAAiB,YAAY;AAEpC,kBAAM,UAAU,aAAa,KAAK,aAAa;AAAA,UACnD,OACK;AACD,+BAAmB,OAAO,YAAY;AAAA,UAC1C;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,sBAAoB,QAAQ,mBAAmB;AAE/C,aAAW,MAAM;AAEjB,sBAAoB,QAAQ,eAAe;AAC3C,MAAIA,yBAAwB,MAAM;AAC9B,iBAAa,QAAQ,WAAS;AAC1B,UAAI,CAAC,MAAM,SAAS;AAChB,cAAM,UAAUA;AAAA,MACpB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,SAAS,6BAA6B,QAAQ,WAAWA,uBAAsB;AAC3E,WAAS,UAAU,QAAQ;AAAA,IACvB,CAAC,WAAW,WAAW,GAAG,UAAQ;AAnN1C;AAoNY,YAAM,SAAS,KAAK,SAAS;AAC7B,YAAM,gBAAgB,UAAU,KAAK,IAAI;AACzC,UAAI,CAAC,sBAAsB,IAAI,KAAK,iBAAiB,MAAM;AACvD,mBAAW,aAAa,eAAe;AACnC,cAAI,UAAU,WAAW,IAAI,GAAG;AAC5B,mBAAO,UAAU,UAAU,CAAC,CAAC,IAAI,cAAc,SAAS;AAAA,UAC5D,WACS,cAAc,aAAa,OAAO,WAAW,MAAM;AACxD,mBAAO,UAAU;AAAA,cACb,GAAG,OAAO;AAAA,cACV,eAAa,oDAAe,YAAf,mBAAwB,gBACjC,OAAO,QAAQ;AAAA,cACnB,aAAa,OAAO,QAAQ,cAAc,CAAC,GAAG,SAAO,oDAAe,YAAf,mBAAwB,eAAc,CAAC,CAAC;AAAA,YACjG;AAAA,UACJ,WACS,cAAc,uBAAuB,OAAO,qBAAqB,MAAM;AAC5E,mBAAO,oBAAoB,OAAO,kBAAkB,QAAO,+CAAe,sBAAqB,CAAC,CAAC;AAAA,UACrG,WACS,cAAc,gBACnB,OAAO,cAAc,QACrB,cAAc,cAAc,MAAM;AAClC,mBAAO,aAAa,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG,KAAK,YAAY,cAAc,UAAU;AAAA,UACpG,OACK;AACD,mBAAO,SAAS,IAAI,cAAc,SAAS;AAAA,UAC/C;AAAA,QACJ;AACA,eAAO,IAAI,kBAAkB,MAAM;AAAA,MACvC;AAAA,IACJ;AAAA,IACA,CAAC,WAAW,SAAS,GAAG,UAAQ;AAlPxC;AAmPY,YAAM,gBAAgB,UAAU,KAAK,IAAI;AACzC,YAAM,SAAS,KAAK,SAAS;AAC7B,YAAM,qBAAqB,OAAO;AAClC,UAAI,iBAAiB,MAAM;AACvB,mBAAW,aAAa,eAAe;AACnC,cAAI,UAAU,WAAW,IAAI,GAAG;AAC5B,mBAAO,UAAU,UAAU,CAAC,CAAC,IAAI,cAAc,SAAS;AAAA,UAC5D,WACS,cAAc,aAAa,OAAO,WAAW,MAAM;AACxD,mBAAO,UAAU;AAAA,cACb,GAAG,OAAO;AAAA,cACV,eAAa,oDAAe,YAAf,mBAAwB,gBACjC,OAAO,QAAQ;AAAA,cACnB,aAAa,OAAO,QAAQ,cAAc,CAAC,GAAG,SAAO,oDAAe,YAAf,mBAAwB,eAAc,CAAC,CAAC;AAAA,YACjG;AAAA,UACJ,WACS,cAAc,uBAAuB,OAAO,qBAAqB,MAAM;AAC5E,mBAAO,oBAAoB,OAAO,kBAAkB,QAAO,+CAAe,sBAAqB,CAAC,CAAC;AAAA,UACrG,WACS,cAAc,gBACnB,OAAO,cAAc,QACrB,cAAc,cAAc,MAAM;AAClC,mBAAO,aAAa,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG,KAAK,YAAY,cAAc,UAAU;AAAA,UACpG,WACS,mBAAmB,SAAS,GAAG;AACpC,+BAAmB,SAAS,EAAE,QAAQ,cAAc,SAAS;AAAA,UACjE;AAAA,QACJ;AACA,eAAO,IAAI,gBAAgB,MAAM;AAAA,MACrC;AAAA,IACJ;AAAA,IACA,CAAC,WAAW,UAAU,GAAG,UAAQ;AAC7B,YAAM,gBAAgB,UAAU,KAAK,IAAI;AACzC,UAAI,iBAAiB,MAAM;AACvB,cAAM,SAAS,KAAK,SAAS;AAC7B,YAAI,cAAc,eAAe,GAAG;AAChC,iBAAO,cAAc,cAAc,eAAe;AAAA,QACtD;AACA,eAAO,IAAI,iBAAiB,MAAM;AAAA,MACtC;AAAA,IACJ;AAAA,IACA,CAAC,WAAW,WAAW,GAAG,UAAQ;AAC9B,YAAM,gBAAgB,UAAU,KAAK,IAAI;AACzC,UAAI,iBAAiB,MAAM;AACvB,cAAM,SAAS,KAAK,SAAS;AAC7B,YAAI,cAAc,YAAY,GAAG;AAC7B,iBAAO,WAAW,cAAc,YAAY;AAAA,QAChD;AACA,eAAO,IAAI,kBAAkB,MAAM;AAAA,MACvC;AAAA,IACJ;AAAA,IACA,CAAC,WAAW,cAAc,GAAG,UAAQ;AACjC,YAAM,gBAAgB,UAAU,KAAK,IAAI;AACzC,UAAI,iBAAiB,MAAM;AACvB,cAAM,SAAS,KAAK,SAAS;AAC7B,YAAI,cAAc,eAAe,GAAG;AAChC,iBAAO,cAAc,cAAc,eAAe;AAAA,QACtD;AACA,eAAO,IAAI,qBAAqB,MAAM;AAAA,MAC1C;AAAA,IACJ;AAAA,IACA,CAAC,WAAW,eAAe,GAAG,CAAC,aAAa,WAAW,aAAa;AAChE,YAAM,gBAAgB,UAAU,QAAQ;AACxC,UAAI,iBAAiB,MAAM;AACvB,cAAM,eAAe,cAAc,SAAS;AAC5C,YAAI,gBAAgB,MAAM;AACtB,gBAAM,iBAAiB,EAAE,GAAG,YAAY;AACxC,cAAI,OAAO,iBAAiB,YAAY;AAEpC,2BAAe,UAAU,aAAa,KAAK,aAAa;AAAA,UAC5D,OACK;AACD,+BAAmB,gBAAgB,YAAY;AAAA,UACnD;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,MAAIA,yBAAwB,MAAM;AAC9B,aAAS,UAAU,QAAQ;AAAA,MACvB,CAAC,WAAW,YAAY,GAAG,kBAAgB;AAAA,QACvC,GAAG;AAAA,QACH,SAAS,YAAY,WAAW,OAAO,YAAY,UAAUA;AAAA,MACjE;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO,eAAe;AAC9C,aAAW,gBAAgB,eAAe;AACtC,UAAM,YAAY,IAAI,cAAc,YAAY;AAAA,EACpD;AACJ;;;AGhVA;;;AC8BO,SAAS,eAAe,sBAAsB,SAAS;AAC1D,MAAI,CAAC,wBACA,MAAM,QAAQ,oBAAoB,KAAK,qBAAqB,WAAW,GAAI;AAC5E,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,CAAC,MAAM,QAAQ,oBAAoB,GAAG;AACtC,WAAO;AAAA,EACX;AACA,MAAI,qBAAqB,WAAW,GAAG;AACnC,WAAO,qBAAqB,CAAC,KAAK,CAAC;AAAA,EACvC;AACA,QAAM,YAAY,IAAI,MAAM;AAC5B,WAAS,uBAAuB,sBAAsB;AAClD,QAAI,MAAM,QAAQ,mBAAmB,GAAG;AACpC,4BAAsB,eAAe,mBAAmB;AAAA,IAC5D;AACA,QAAI,OAAO,wBAAwB,YAAY,qBAAqB;AAChE,gBAAU,KAAK,mBAAmB;AAAA,IACtC;AAAA,EACJ;AACA,QAAM,SAAS,UAAU,WAAW,IAAI;AACxC,MAAI,mCAAS,YAAY;AACrB,eAAW,aAAa,QAAQ,YAAY;AACxC,YAAM,CAAC,UAAU,SAAS,IAAI,UAAU,MAAM,GAAG;AACjD,UAAI,CAAC,aAAa,eAAe,WAAW,EAAE,SAAS,QAAQ,KAC3D,CAAC,aAAa,eAAe,WAAW,EAAE,SAAS,SAAS,GAAG;AAC/D;AAAA,MACJ;AACA,UAAI,CAAC,aAAa,cAAc,KAAK;AACjC,eAAO,OAAO,QAAQ;AAAA,MAC1B,WACS,OAAO,QAAQ,GAAG;AACvB,eAAO,OAAO,QAAQ,EAAE,SAAS;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AClEO,SAAS,eAAe,OAAO,OAAO,QAAQ;AACjD,QAAM,SAAS,qBAAqB,CAAC,GAAG,OAAO,GAAG,KAAK,EAAE,OAAO,MAAM,GAAG,MAAM;AAC/E,MAAI,UAAU,OAAO,MAAM;AACvB,WAAO,KAAK,YAAY;AAAA,EAC5B;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,MAAM,QAAQ;AACxC,SAAO,KAAK,OAAO,CAAC,KAAK,YAAY;AACjC,UAAM,WAAW,IAAI,UAAU,SAAO,IAAI,KAAK,UAAU,QAAQ,KAAK,KAAK;AAC3E,QAAI,aAAa,IAAI;AACjB,aAAO,IAAI,OAAO,CAAC,OAAO,CAAC;AAAA,IAC/B,WACS,EAAC,iCAAQ,mBAAkB;AAChC,UAAI,QAAQ,IAAI;AAAA,IACpB;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;;;AClBA,SAAS,uBAAuB,eAAe,gBAAgB;AAC3D,SAAO,CAAC,CAAC,cAAc,KAAK,eAAa,UAAU,KAAK,UAAU,eAAe,KAAK,KAAK;AAC/F;AACA,SAAS,sBAAsB,WAAW,YAAY;AAJtD;AAKI,SAAO,CAAC,GAAC,8CAAa,UAAU,KAAK,WAA5B,mBAAoC;AACjD;AACA,SAAS,kBAAkB,MAAM,UAAU;AACvC,SAAO,SAAS,KAAK,CAAC,EAAE,MAAM,MAAM,UAAU,KAAK,KAAK;AAC5D;AACA,SAASC,gBAAe,IAAI,IAAI;AAC5B,QAAM,SAAS,CAAC,GAAG,EAAE;AACrB,aAAW,YAAY,IAAI;AACvB,UAAM,gBAAgB,OAAO,UAAU,OAAK,EAAE,KAAK,UAAU,SAAS,KAAK,KAAK;AAChF,QAAI,gBAAgB,IAAI;AACpB,YAAM,cAAc,OAAO,aAAa;AACxC,UAAI,YAAY,MAAM,SAAS,aAAa;AACxC,cAAM,SAAS,YAAY,MAAM;AACjC,cAAM,SAAS,SAAS,MAAM;AAE9B,oBAAY,MAAM,SAAS,iBAAiB,QAAQ,QAAQ,CAAC,WAAWC,YAAW;AAC/E,gBAAM,QAAQ,UAAU;AACxB,iBAAO,CAAC,SAAS,CAACA,QAAO,KAAK,CAAC,cAAc,UAAU,UAAU,KAAK;AAAA,QAC1E,CAAC;AAAA,MACL,OACK;AACD,oBAAY,QAAQ,SAAS;AAAA,MACjC;AAAA,IACJ,OACK;AACD,aAAO,KAAK,QAAQ;AAAA,IACxB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,YAAY,aAAa;AACpD,SAAO,WACF,IAAI,CAAC,WAAW,GAAG,QAAQ;AAC5B,UAAM,UAAU,IAAI,UAAU,OAAK,EAAE,KAAK,UAAU,UAAU,KAAK,KAAK;AACxE,QAAI,YAAY,KAAK,CAAC,sBAAsB,WAAW,WAAW,GAAG;AACjE,YAAM,MAAM,IAAI,OAAO;AACvB,gBAAU,YAAYD,gBAAe,UAAU,WAAW,IAAI,SAAS;AACvE,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX,CAAC,EACI,OAAO,MAAM;AACtB;AACO,SAAS,gBAAgB,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,YAAY;AAClE,QAAM,eAAe,UAAU,OAAO;AACtC,QAAM,SAAS,eAAe,KAAK;AACnC,QAAM,UAAU,eAAe,KAAK;AACpC,QAAM,SAAS,sBAAsB,CAAC,GAAG,MAAM,GAAG,UAAU;AAC5D,aAAW,aAAa,SAAS;AAC7B,QAAI,uBAAuB,QAAQ,SAAS,KACxC,CAAC,sBAAsB,WAAW,UAAU,GAAG;AAC/C,YAAM,yBAAyB,OAAO,UAAU,OAAK,EAAE,KAAK,UAAU,UAAU,KAAK,KAAK;AAC1F,YAAM,oBAAoB,OAAO,sBAAsB;AACvD,aAAO,sBAAsB,EAAE,YAAYA,gBAAe,UAAU,aAAa,CAAC,GAAG,kBAAkB,aAAa,CAAC,CAAC;AAAA,IAC1H,OACK;AACD,aAAO,KAAK,SAAS;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,eAAe,MAAM,cAAc;AAC/C,MAAI,cAAc;AACd,WAAO;AAAA,MACH,GAAG;AAAA,MACH,WAAW,iBAAiB,aAAa,aAAa,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC,kBAAkB,IAAI,MAAM,aAAa,IAAI,OAAK,EAAE,IAAI,CAAC,CAAC;AAAA,MAClK,WAAW;AAAA,QACP,GAAG,aAAa;AAAA,QAChB,GAAG,KAAK,UAAU,OAAO,UAAQ,CAAC,kBAAkB,MAAM,aAAa,SAAS,CAAC;AAAA,MACrF;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,QAAQ,QAAQ,UAAU;AAChD,SAAO,OAAO,OAAO,OAAO,OAAO,SAAO,SAAS,KAAK,MAAM,CAAC,CAAC;AACpE;;;AC/EO,SAAS,gBAAgB,OAAO,QAAQ,QAAQ,YAAY;AAC/D,MAAI,iCAAQ,qBAAqB;AAC7B,UAAM,WAAW,CAAC;AAClB,QAAI,OAAO;AACP,eAAS,KAAK,GAAG,KAAK;AAAA,IAC1B;AACA,YAAQ;AACR,aAAS;AAAA,EACb;AACA,QAAM,eAAe,oBAAI,IAAI;AAC7B,MAAI,OAAO;AACP,eAAW,cAAc,OAAO;AAC5B,mBAAa,IAAI,WAAW,KAAK,OAAO,UAAU;AAAA,IACtD;AAAA,EACJ;AACA,MAAI,QAAQ;AACR,eAAW,eAAe,QAAQ;AAC9B,YAAM,YAAY,YAAY,KAAK;AACnC,UAAI,aAAa,IAAI,SAAS,GAAG;AAC7B,cAAM,aAAa,aAAa,IAAI,SAAS;AAC7C,mBAAW,cAAc,YAAY,eAAe,WAAW;AAC/D,mBAAW,aAAa,gBAAgB,YAAY,YAAY,WAAW,YAAY,UAAU;AAAA,MACrG,OACK;AACD,qBAAa,IAAI,WAAW,WAAW;AAAA,MAC3C;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,SAAS,CAAC,GAAG,aAAa,OAAO,CAAC;AACxC,MAAI,UAAU,OAAO,MAAM;AACvB,WAAO,KAAK,YAAY;AAAA,EAC5B;AACA,SAAO;AACX;;;ACnCA;AAGO,SAAS,UAAU,IAAI,IAAI,QAAQ,YAAY;AAClD,MAAI,IAAI;AACJ,WAAO;AAAA,MACH,MAAM,GAAG;AAAA,MACT,aAAa,GAAG,aAAa,KAAK,GAAG,aAAa;AAAA,MAClD,OAAM,iCAAQ,sBACV,GAAG,SAAS,wBACZ,GAAG,SAAS,uBACV,uBACA;AAAA,MACN,KAAK,GAAG;AAAA,MACR,YAAY,gBAAgB,GAAG,YAAY,GAAG,YAAY,QAAQ,UAAU;AAAA,MAC5E,QAAQ,gBAAgB,GAAG,QAAQ,GAAG,QAAQ,MAAM;AAAA,IACxD;AAAA,EACJ;AACA,UAAO,iCAAQ,qBACT;AAAA,IACE,GAAG;AAAA,IACH,MAAM,KAAK;AAAA,EACf,IACE;AACV;;;ACxBA;AACO,SAAS,cAAc,OAAO;AACjC,SAAO,OAAO,UAAU;AAC5B;AACO,SAAS,cAAc,OAAO;AACjC,SAAO,iBAAiB;AAC5B;AACO,SAAS,YAAY,MAAM;AAC9B,MAAI,cAAc;AAClB,SAAO,YAAY,SAAS,KAAK,aAAa,YAAY,SAAS,eAAe;AAC9E,kBAAc,YAAY;AAAA,EAC9B;AACA,SAAO;AACX;AACO,SAAS,mBAAmB,MAAM;AACrC,SAAO,KAAK,SAAS,KAAK;AAC9B;AACO,SAAS,eAAe,MAAM;AACjC,SAAO,KAAK,SAAS,KAAK;AAC9B;AACO,SAAS,kBAAkB,MAAM;AACpC,SAAO,KAAK,SAAS,KAAK;AAC9B;AACO,SAAS,cAAc,MAAM;AAChC,MAAI,eAAe,IAAI,GAAG;AACtB,WAAO,IAAI,cAAc,KAAK,IAAI,CAAC;AAAA,EACvC;AACA,MAAI,kBAAkB,IAAI,GAAG;AACzB,WAAO,GAAG,cAAc,KAAK,IAAI,CAAC;AAAA,EACtC;AACA,SAAO,KAAK,KAAK;AACrB;AACO,IAAI;AAAA,CACV,SAAUE,aAAY;AACnB,EAAAA,YAAWA,YAAW,kBAAkB,IAAI,EAAE,IAAI;AAClD,EAAAA,YAAWA,YAAW,YAAY,IAAI,CAAC,IAAI;AAC3C,EAAAA,YAAWA,YAAW,kBAAkB,IAAI,CAAC,IAAI;AACrD,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,SAAS,wBAAwB,GAAG,GAAG;AAC1C,MAAI,KAAK,QAAQ,KAAK,MAAM;AACxB,WAAO,WAAW;AAAA,EACtB;AACA,MAAI,KAAK,MAAM;AACX,WAAO,WAAW;AAAA,EACtB;AACA,MAAI,KAAK,MAAM;AACX,WAAO,WAAW;AAAA,EACtB;AACA,MAAI,IAAI;AACJ,WAAO,WAAW;AACtB,MAAI,IAAI;AACJ,WAAO,WAAW;AACtB,SAAO,WAAW;AACtB;;;ACjDA,SAAS,mBAAmB,WAAW,YAAY;AAC/C,QAAM,cAAc,UAAU,UAAU,WAAS,MAAM,KAAK,UAAU,WAAW,KAAK,KAAK;AAC3F,SAAO,CAAC,cAAc,KAAK,UAAU,WAAW,IAAI,MAAM,WAAW;AACzE;AACO,SAAS,YAAY,MAAM,IAAI,IAAI,QAAQ,YAAY;AAC1D,QAAM,SAAS,CAAC;AAChB,MAAI,MAAM,MAAM;AACZ,WAAO,KAAK,GAAG,EAAE;AAAA,EACrB;AACA,MAAI,MAAM,MAAM;AACZ,eAAW,SAAS,IAAI;AACpB,YAAM,CAAC,UAAU,aAAa,IAAI,mBAAmB,QAAQ,KAAK;AAClE,UAAI,YAAY,EAAC,iCAAQ,uBAAsB;AAC3C,cAAM,YAAY,iCAAQ,wBACtB,OAAO,oBAAoB,UAAU,OAAO,MAAM,iCAAQ,eAAe,KACzE,iBAAiB,MAAM,UAAU,OAAO,iCAAQ,eAAe;AACnE,iBAAS,YAAY,eAAe,MAAM,WAAW,KAAK,CAAC,GAAG,SAAS,WAAW,KAAK,CAAC,GAAG,MAAM;AACjG,iBAAS,aAAa,gBAAgB,MAAM,YAAY,SAAS,YAAY,QAAQ,UAAU;AAC/F,iBAAS,cAAc,MAAM,eAAe,SAAS;AACrD,eAAO,aAAa,IAAI;AAAA,MAC5B,OACK;AACD,eAAO,KAAK,KAAK;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,UAAU,OAAO,MAAM;AACvB,WAAO,KAAK,YAAY;AAAA,EAC5B;AACA,MAAI,UAAU,OAAO,YAAY;AAC7B,UAAM,aAAa,OAAO;AAC1B,WAAO,OAAO,OAAO,WAAS,CAAC,WAAW,SAAS,GAAG,KAAK,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;AAAA,EAChG;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,MAAM,GAAG,GAAG,oBAAoB,OAAO;AAC7D,QAAM,QAAQ,cAAc,EAAE,IAAI;AAClC,QAAM,QAAQ,cAAc,EAAE,IAAI;AAClC,MAAI,UAAU,OAAO;AACjB,UAAM,KAAK,YAAY,EAAE,IAAI;AAC7B,UAAM,KAAK,YAAY,EAAE,IAAI;AAC7B,QAAI,GAAG,KAAK,UAAU,GAAG,KAAK,OAAO;AACjC,YAAM,IAAI,MAAM,UAAU,EAAE,KAAK,KAAK,yDAAyD,GAAG,KAAK,KAAK,sCAAsC,GAAG,KAAK,KAAK,GAAG;AAAA,IACtK;AACA,QAAI,CAAC,uBAAuB,EAAE,MAAM,EAAE,MAAM,CAAC,iBAAiB,GAAG;AAC7D,YAAM,IAAI,MAAM,UAAU,KAAK,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK,wBAAwB,KAAK,SAAS,KAAK,GAAG;AAAA,IAC3G;AAAA,EACJ;AACA,MAAI,kBAAkB,EAAE,IAAI,KAAK,CAAC,kBAAkB,EAAE,IAAI,GAAG;AACzD,MAAE,OAAO,EAAE;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,SAAS,SAAS,oBAAoB,OAAO;AAEzE,MAAI,CAAC,mBAAmB,OAAO,KAAK,CAAC,mBAAmB,OAAO,GAAG;AAC9D,WAAO,QAAQ,SAAS,MAAM,QAAQ,SAAS;AAAA,EACnD;AAEA,MAAI,kBAAkB,OAAO,GAAG;AAC5B,UAAM,SAAS,kBAAkB,OAAO,IAAI,QAAQ,OAAO;AAC3D,WAAO,uBAAuB,QAAQ,QAAQ,IAAI;AAAA,EACtD;AAEA,MAAI,kBAAkB,OAAO,GAAG;AAC5B,WAAO,uBAAuB,SAAS,SAAS,iBAAiB;AAAA,EACrE;AAEA,MAAI,eAAe,OAAO,GAAG;AACzB,WAAS,eAAe,OAAO,KAAK,uBAAuB,QAAQ,MAAM,QAAQ,IAAI,KAChF,kBAAkB,OAAO,KAAK,uBAAuB,SAAS,QAAQ,MAAM,CAAC;AAAA,EACtF;AACA,SAAO;AACX;;;AC7EA;AAGO,SAAS,eAAe,MAAM,cAAc,QAAQ,YAAY;AACnE,MAAI,cAAc;AACd,QAAI;AACA,aAAO;AAAA,QACH,MAAM,KAAK;AAAA,QACX,aAAa,KAAK,aAAa,KAAK,aAAa,aAAa;AAAA,QAC9D,OAAM,iCAAQ,sBACV,KAAK,SAAS,+BACd,aAAa,SAAS,8BACpB,8BACA;AAAA,QACN,KAAK,KAAK;AAAA,QACV,QAAQ,YAAY,MAAM,KAAK,QAAQ,aAAa,QAAQ,MAAM;AAAA,QAClE,YAAY,gBAAgB,KAAK,YAAY,aAAa,YAAY,QAAQ,UAAU;AAAA,MAC5F;AAAA,IACJ,SACO,GAAG;AACN,YAAM,IAAI,MAAM,uCAAuC,KAAK,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE;AAAA,IAC3F;AAAA,EACJ;AACA,UAAO,iCAAQ,qBACT;AAAA,IACE,GAAG;AAAA,IACH,MAAM,KAAK;AAAA,EACf,IACE;AACV;;;AC7BA;;;ACCA,SAAS,cAAc,KAAK,OAAO;AAC/B,SAAO,CAAC,CAAC,IAAI,KAAK,OAAK,EAAE,KAAK,UAAU,MAAM,KAAK,KAAK;AAC5D;AACO,SAAS,oBAAoB,QAAQ,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG;AACtE,QAAM,SAAS,CAAC,GAAG,QAAQ,GAAG,MAAM,OAAO,OAAK,CAAC,cAAc,QAAQ,CAAC,CAAC,CAAC;AAC1E,MAAI,UAAU,OAAO,MAAM;AACvB,WAAO,KAAK,YAAY;AAAA,EAC5B;AACA,SAAO;AACX;;;ADNO,SAAS,eAAe,MAAM,cAAc,QAAQ,YAAY;AACnE,MAAI,cAAc;AACd,QAAI;AACA,aAAO;AAAA,QACH,MAAM,KAAK;AAAA,QACX,aAAa,KAAK,aAAa,KAAK,aAAa,aAAa;AAAA,QAC9D,OAAM,iCAAQ,sBACV,KAAK,SAAS,6BACd,aAAa,SAAS,4BACpB,4BACA;AAAA,QACN,KAAK,KAAK;AAAA,QACV,QAAQ,YAAY,MAAM,KAAK,QAAQ,aAAa,QAAQ,QAAQ,UAAU;AAAA,QAC9E,YAAY,gBAAgB,KAAK,YAAY,aAAa,YAAY,QAAQ,UAAU;AAAA,QACxF,YAAY,KAAK,YAAY,IACvB,oBAAoB,KAAK,YAAY,GAAG,aAAa,YAAY,GAAG,MAAM,IAC1E;AAAA,MACV;AAAA,IACJ,SACO,GAAG;AACN,YAAM,IAAI,MAAM,sCAAsC,KAAK,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE;AAAA,IAC1F;AAAA,EACJ;AACA,UAAO,iCAAQ,qBACT;AAAA,IACE,GAAG;AAAA,IACH,MAAM,KAAK;AAAA,EACf,IACE;AACV;;;AEjCA;;;ACAA;AAEO,SAAS,YAAY,MAAM,cAAc,QAAQ,YAAY;AAChE,MAAI,cAAc;AACd,WAAO;AAAA,MACH,MAAM,KAAK;AAAA,MACX,aAAa,KAAK,aAAa,KAAK,aAAa,aAAa;AAAA,MAC9D,OAAM,iCAAQ,sBACV,KAAK,SAAS,0BACd,aAAa,SAAS,yBACpB,yBACA;AAAA,MACN,KAAK,KAAK;AAAA,MACV,YAAY,gBAAgB,KAAK,YAAY,aAAa,YAAY,QAAQ,UAAU;AAAA,IAC5F;AAAA,EACJ;AACA,UAAO,iCAAQ,qBACT;AAAA,IACE,GAAG;AAAA,IACH,MAAM,KAAK;AAAA,EACf,IACE;AACV;;;ACtBA;AAEO,IAAM,kCAAkC;AAAA,EAC3C,OAAO;AAAA,EACP,UAAU;AAAA,EACV,cAAc;AAClB;AACA,SAAS,oBAAoB,aAAa,CAAC,GAAG,qBAAqB,CAAC,GAAG;AACnE,QAAM,kBAAkB,CAAC;AACzB,aAAW,cAAc,iCAAiC;AACtD,UAAM,SAAS,WAAW,KAAK,OAAK,EAAE,cAAc,UAAU,KAC1D,mBAAmB,KAAK,OAAK,EAAE,cAAc,UAAU;AAC3D,QAAI,QAAQ;AACR,sBAAgB,KAAK,MAAM;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,gBAAgB,MAAM,cAAc,QAAQ,YAAY;AACpE,MAAI,cAAc;AACd,WAAO;AAAA,MACH,MAAM,KAAK,SAAS,KAAK,qBAAqB,aAAa,SAAS,KAAK,oBACnE,KAAK,oBACL,KAAK;AAAA,MACX,aAAa,KAAK,aAAa,KAAK,aAAa,aAAa;AAAA,MAC9D,YAAY,gBAAgB,KAAK,YAAY,aAAa,YAAY,QAAQ,UAAU;AAAA,MACxF,gBAAgB,oBAAoB,KAAK,gBAAgB,aAAa,cAAc;AAAA,IACxF;AAAA,EACJ;AACA,UAAQ,iCAAQ,qBACV;AAAA,IACE,GAAG;AAAA,IACH,MAAM,KAAK;AAAA,EACf,IACE;AACV;;;ACnCA;AAIO,SAAS,UAAU,MAAM,cAAc,QAAQ,YAAY;AAC9D,MAAI,cAAc;AACd,QAAI;AACA,aAAO;AAAA,QACH,MAAM,KAAK;AAAA,QACX,aAAa,KAAK,aAAa,KAAK,aAAa,aAAa;AAAA,QAC9D,OAAM,iCAAQ,sBACV,KAAK,SAAS,0BACd,aAAa,SAAS,yBACpB,yBACA;AAAA,QACN,KAAK,KAAK;AAAA,QACV,QAAQ,YAAY,MAAM,KAAK,QAAQ,aAAa,QAAQ,QAAQ,UAAU;AAAA,QAC9E,YAAY,gBAAgB,KAAK,YAAY,aAAa,YAAY,QAAQ,UAAU;AAAA,QACxF,YAAY,oBAAoB,KAAK,YAAY,aAAa,YAAY,MAAM;AAAA,MACpF;AAAA,IACJ,SACO,GAAG;AACN,YAAM,IAAI,MAAM,iCAAiC,KAAK,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE;AAAA,IACrF;AAAA,EACJ;AACA,UAAO,iCAAQ,qBACT;AAAA,IACE,GAAG;AAAA,IACH,MAAM,KAAK;AAAA,EACf,IACE;AACV;;;AC/BA;AAGO,SAAS,WAAW,OAAO,QAAQ,QAAQ,YAAY;AAC1D,MAAI,QAAQ;AACR,WAAO;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,aAAa,MAAM,aAAa,KAAK,OAAO,aAAa;AAAA;AAAA,MAEzD,YAAY,gBAAgB,MAAM,YAAY,OAAO,YAAY,QAAQ,UAAU;AAAA,MACnF,OAAM,iCAAQ,sBACV,MAAM,SAAS,yBACf,OAAO,SAAS,wBACd,KAAK,wBACL,KAAK;AAAA,MACX,KAAK,MAAM;AAAA,MACX,OAAO,oBAAoB,MAAM,OAAO,OAAO,OAAO,MAAM;AAAA,IAChE;AAAA,EACJ;AACA,UAAO,iCAAQ,qBACT;AAAA,IACE,GAAG;AAAA,IACH,MAAM,KAAK;AAAA,EACf,IACE;AACV;;;AJfO,IAAM,kBAAkB;AACxB,SAAS,sBAAsB,gBAAgB;AAClD,SAAO,UAAU;AACrB;AACO,SAAS,kBAAkB,OAAO,QAAQ,aAAa,CAAC,GAAG;AAdlE;AAeI,QAAM,kBAAkB;AACxB,aAAW,kBAAkB,OAAO;AAChC,QAAI,sBAAsB,cAAc,GAAG;AACvC,YAAM,QAAO,oBAAe,SAAf,mBAAqB;AAClC,UAAI,iCAAQ,qBAAqB;AAC7B,uBAAe,cAAc;AAAA,MACjC;AACA,UAAI,QAAQ,MAAM;AACd;AAAA,MACJ;AACA,YAAI,sCAAQ,eAAR,mBAAoB,SAAS,OAAO,YAAS,sCAAQ,eAAR,mBAAoB,SAAS,QAAO;AACjF,eAAO,gBAAgB,IAAI;AAAA,MAC/B,OACK;AACD,gBAAQ,eAAe,MAAM;AAAA,UACzB,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACN,4BAAgB,IAAI,IAAI,UAAU,gBAAgB,gBAAgB,IAAI,GAAG,QAAQ,UAAU;AAC3F;AAAA,UACJ,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACN,4BAAgB,IAAI,IAAI,UAAU,gBAAgB,gBAAgB,IAAI,GAAG,QAAQ,UAAU;AAC3F;AAAA,UACJ,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACN,4BAAgB,IAAI,IAAI,WAAW,gBAAgB,gBAAgB,IAAI,GAAG,QAAQ,UAAU;AAC5F;AAAA,UACJ,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACN,4BAAgB,IAAI,IAAI,YAAY,gBAAgB,gBAAgB,IAAI,GAAG,QAAQ,UAAU;AAC7F;AAAA,UACJ,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACN,4BAAgB,IAAI,IAAI,eAAe,gBAAgB,gBAAgB,IAAI,GAAG,QAAQ,UAAU;AAChG;AAAA,UACJ,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AACN,4BAAgB,IAAI,IAAI,eAAe,gBAAgB,gBAAgB,IAAI,GAAG,QAAQ,UAAU;AAChG;AAAA,UACJ,KAAK,KAAK;AACN,gBAAI,gBAAgB,IAAI,GAAG;AACvB,oBAAM,2BAA2B,QAAQ,CAAC;AAC1C,kBAAI,0BAA0B;AAC1B,oBAAI,CAAC,UAAU,gBAAgB,IAAI,CAAC,GAAG;AACnC,kCAAgB,IAAI,IAAI;AAAA,gBAC5B;AAAA,cACJ;AAAA,YACJ;AACA,4BAAgB,IAAI,IAAI,eAAe,gBAAgB,gBAAgB,IAAI,CAAC;AAC5E;AAAA,QACR;AAAA,MACJ;AAAA,IACJ,WACS,eAAe,SAAS,KAAK,qBAClC,eAAe,SAAS,KAAK,kBAAkB;AAC/C,sBAAgB,eAAe,IAAI,gBAAgB,gBAAgB,gBAAgB,eAAe,GAAG,MAAM;AAAA,IAC/G;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,UAAU,MAAM;AACrB,SAAQ,QAAQ,QAAQ,OAAO,SAAS,YAAY,UAAU,QAAQ,OAAO,KAAK,SAAS;AAC/F;;;AK7EA;AAKO,SAAS,cAAc,YAAY,QAAQ;AAC9C,gBAAc;AACd,QAAM,MAAM;AAAA,IACR,MAAM,KAAK;AAAA,IACX,aAAa,kBAAkB,YAAY;AAAA,MACvC,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,MACrB,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AACA,MAAI;AACJ,MAAI,iCAAQ,qBAAqB;AAC7B,aAAS,kBAAkB,GAAG;AAAA,EAClC,OACK;AACD,aAAS;AAAA,EACb;AACA,gBAAc;AACd,SAAO;AACX;AACA,SAAS,iBAAiB,YAAY,SAAS,gBAAgB,CAAC,GAAG,WAAW,CAAC,GAAG,qBAAqB,oBAAI,IAAI,GAAG;AAC9G,MAAI,cAAc,CAAC,mBAAmB,IAAI,UAAU,GAAG;AACnD,uBAAmB,IAAI,UAAU;AACjC,QAAI,OAAO,eAAe,YAAY;AAClC,uBAAiB,WAAW,GAAG,SAAS,eAAe,UAAU,kBAAkB;AAAA,IACvF,WACS,MAAM,QAAQ,UAAU,GAAG;AAChC,iBAAW,QAAQ,YAAY;AAC3B,yBAAiB,MAAM,SAAS,eAAe,UAAU,kBAAkB;AAAA,MAC/E;AAAA,IACJ,WACS,SAAS,UAAU,GAAG;AAC3B,YAAM,eAAe,0BAA0B,YAAY,OAAO;AAClE,uBAAiB,aAAa,aAAa,SAAS,eAAe,UAAU,kBAAkB;AAAA,IACnG,WACS,cAAc,UAAU,KAAK,cAAc,UAAU,GAAG;AAC7D,YAAM,eAAe,MAAM,YAAY,OAAO;AAC9C,uBAAiB,aAAa,aAAa,SAAS,eAAe,UAAU,kBAAkB;AAAA,IACnG,WACS,OAAO,eAAe,YAAY,iBAAiB,UAAU,GAAG;AACrE,UAAI,WAAW,SAAS,KAAK,sBAAsB;AAC/C,sBAAc,KAAK,UAAU;AAAA,MACjC,OACK;AACD,iBAAS,KAAK,UAAU;AAAA,MAC5B;AAAA,IACJ,WACS,eAAe,UAAU,GAAG;AACjC,uBAAiB,WAAW,aAAa,SAAS,eAAe,UAAU,kBAAkB;AAAA,IACjG,OACK;AACD,YAAM,IAAI,MAAM,6EAA6E,OAAO,UAAU,EAAE;AAAA,IACpH;AAAA,EACJ;AACA,SAAO,EAAE,eAAe,SAAS;AACrC;AACO,SAAS,kBAAkB,YAAY,QAAQ;AA/DtD;AAgEI,gBAAc;AACd,QAAM,EAAE,eAAe,SAAS,IAAI,iBAAiB,YAAY,MAAM;AACvE,QAAM,mBAAmB,kBAAkB,eAAe,MAAM;AAChE,QAAM,cAAc,kBAAkB,UAAU,QAAQ,gBAAgB;AACxE,MAAI,iCAAQ,qBAAqB;AAE7B,UAAM,YAAY,YAAY,eAAe,KAAK;AAAA,MAC9C,MAAM,KAAK;AAAA,MACX,gBAAgB,CAAC;AAAA,IACrB;AACA,UAAM,iBAAiB,UAAU;AACjC,eAAW,qBAAqB,iCAAiC;AAC7D,YAAM,gBAAgB,eAAe,KAAK,mBAAiB,cAAc,cAAc,iBAAiB;AACxG,UAAI,CAAC,eAAe;AAChB,cAAM,uBAAuB,gCAAgC,iBAAiB;AAC9E,cAAM,2BAA2B,YAAY,oBAAoB;AACjE,YAAI,4BAA4B,QAAQ,yBAAyB,QAAQ,MAAM;AAC3E,yBAAe,KAAK;AAAA,YAChB,MAAM,KAAK;AAAA,YACX,MAAM;AAAA,cACF,MAAM,KAAK;AAAA,cACX,MAAM,yBAAyB;AAAA,YACnC;AAAA,YACA,WAAW;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AACA,UAAI,4CAAW,mBAAX,mBAA2B,WAAU,QAAQ,UAAU,eAAe,SAAS,GAAG;AAClF,kBAAY,eAAe,IAAI;AAAA,IACnC;AAAA,EACJ;AACA,OAAI,iCAAQ,0BAAyB,GAAC,uBAAY,eAAe,MAA3B,mBAA8B,mBAA9B,mBAA8C,SAAQ;AACxF,gBAAY,eAAe,IAAI;AAAA,MAC3B,MAAM,KAAK;AAAA,MACX,gBAAgB;AAAA,QACZ;AAAA,UACI,MAAM,KAAK;AAAA,UACX,WAAW;AAAA,UACX,MAAM;AAAA,YACF,MAAM,KAAK;AAAA,YACX,MAAM;AAAA,cACF,MAAM,KAAK;AAAA,cACX,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,wBAAwB,OAAO,OAAO,WAAW;AACvD,MAAI,iCAAQ,MAAM;AACd,UAAM,SAAS,OAAO,OAAO,SAAS,aAAa,OAAO,OAAO;AACjE,0BAAsB,KAAK,CAAC,GAAG,MAAG;AArH1C,UAAAC,KAAAC;AAqH6C,qBAAOD,MAAA,EAAE,SAAF,gBAAAA,IAAQ,QAAOC,MAAA,EAAE,SAAF,gBAAAA,IAAQ,KAAK;AAAA,KAAC;AAAA,EAC7E;AACA,SAAO;AACX;;;ACnHA,SAAS,qBAAqB,KAAK,YAAY;AAC3C,MAAI,CAAC,OAAO,CAAC,cAAc,eAAe,IAAI,YAAY;AACtD;AAAA,EACJ;AACA,MAAI,CAAC,IAAI,YAAY;AACjB,QAAI,aAAa;AACjB;AAAA,EACJ;AACA,MAAI,aAAa,UAAU,CAAC,IAAI,YAAY,UAAU,GAAG,OAAO,IAAI;AACxE;AACO,SAAS,gBAAgB,QAAQ,YAAY;AAChD,uBAAqB,QAAQ,WAAW,gBAAgB;AACxD,aAAW,CAAC,UAAU,IAAI,KAAK,OAAO,QAAQ,WAAW,SAAS,CAAC,CAAC,GAAG;AACnE,UAAM,OAAO,OAAO,QAAQ,QAAQ;AACpC,QAAI,MAAM;AACN,2BAAqB,MAAM,KAAK,UAAU;AAC1C,UAAI,KAAK,SAAS,YAAY,KAAK,SAAS,aAAa;AACrD,mBAAW,CAAC,WAAW,SAAS,KAAK,OAAO,QAAQ,KAAK,MAAM,GAAG;AAC9D,gBAAM,QAAQ,KAAK,UAAU,EAAE,SAAS;AACxC,cAAI,OAAO;AACP,iCAAqB,OAAO,UAAU,UAAU;AAChD,uBAAW,CAAC,KAAK,OAAO,KAAK,OAAO,QAAQ,UAAU,SAAS,GAAG;AAC9D,mCAAqB,MAAM,KAAK,KAAK,OAAK,EAAE,SAAS,GAAG,GAAG,OAAO;AAAA,YACtE;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,WACS,KAAK,SAAS,SAAS;AAC5B,mBAAW,CAAC,WAAW,SAAS,KAAK,OAAO,QAAQ,KAAK,MAAM,GAAG;AAC9D,gBAAM,QAAQ,KAAK,UAAU,EAAE,SAAS;AACxC,+BAAqB,OAAO,UAAU,UAAU;AAAA,QACpD;AAAA,MACJ,WACS,KAAK,SAAS,QAAQ;AAC3B,mBAAW,CAAC,WAAW,SAAS,KAAK,OAAO,QAAQ,KAAK,MAAM,GAAG;AAC9D,gBAAM,QAAQ,KAAK,SAAS,SAAS;AACrC,+BAAqB,OAAO,SAAS;AAAA,QACzC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AjBCO,SAAS,qBAAqB,EAAE,UAAU,YAAY,CAAC,GAAG,4BAA4B,CAAC,GAAG,iCAAiC,OAAO,yBAAyB,OAAO,kBAAkB,sBAAAC,uBAAsB,GAAG,aAAa,GAAG;AAEhO,MAAI,OAAO,8BAA8B,UAAU;AAC/C,UAAM,IAAI,MAAM,sDAAsD;AAAA,EAC1E;AACA,MAAI,CAAC,UAAU;AACX,UAAM,IAAI,MAAM,uBAAuB;AAAA,EAC3C;AACA,MAAI;AACJ,MAAI,SAAS,QAAQ,GAAG;AACpB,aAAS;AAAA,EACb,WACS,6CAAc,qBAAqB;AACxC,UAAM,iBAAiB,cAAc,UAAU;AAAA,MAC3C,GAAG;AAAA,MACH,qBAAqB;AAAA,IACzB,CAAC;AACD,aAAS,YAAY,gBAAgB,YAAY;AAAA,EACrD,OACK;AACD,UAAM,iBAAiB,cAAc,UAAU,YAAY;AAC3D,aAAS,eAAe,gBAAgB,YAAY;AAAA,EACxD;AAEA,WAAS,qBAAqB;AAAA,IAC1B;AAAA,IACA,WAAW,eAAe,SAAS;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA,sBAAAA;AAAA,EACJ,CAAC;AACD,MAAI,OAAO,KAAK,yBAAyB,EAAE,SAAS,GAAG;AACnD,2BAAuB,QAAQ,yBAAyB;AAAA,EAC5D;AACA,MAAI,kBAAkB;AAClB,eAAW,mBAAmB,QAAQ,gBAAgB,GAAG;AACrD,sBAAgB,QAAQ,eAAe;AAAA,IAC3C;AAAA,EACJ;AACA,SAAO;AACX;;;AkBnFO,SAAS,aAAa,QAAQ;AACjC,QAAM,oBAAoB,CAAC;AAC3B,QAAM,qBAAqB,CAAC;AAC5B,QAAM,4BAA4B,CAAC;AACnC,MAAI,OAAO,WAAW,MAAM;AACxB,eAAW,UAAU,OAAO,SAAS;AACjC,wBAAkB,KAAK,0BAA0B,MAAM,CAAC;AACxD,yBAAmB,KAAK,uBAAuB,MAAM,CAAC;AACtD,gCAA0B,KAAK,4BAA4B,MAAM,CAAC;AAAA,IACtE;AAAA,EACJ;AACA,MAAI,OAAO,YAAY,MAAM;AACzB,sBAAkB,KAAK,OAAO,QAAQ;AAAA,EAC1C;AACA,MAAI,OAAO,aAAa,MAAM;AAC1B,UAAM,sBAAsB,QAAQ,OAAO,SAAS;AACpD,uBAAmB,KAAK,GAAG,mBAAmB;AAAA,EAClD;AACA,MAAI,OAAO,oBAAoB,MAAM;AACjC,UAAM,6BAA6B,QAAQ,OAAO,gBAAgB;AAClE,8BAA0B,KAAK,GAAG,0BAA0B;AAAA,EAChE;AACA,SAAO,qBAAqB;AAAA,IACxB,GAAG;AAAA,IACH,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,EACtB,CAAC;AACL;", "names": ["cache3", "cache4", "cache5", "integerStringRegExp", "getRootTypeNames", "getRootTypes", "getRootTypeMap", "type", "arg", "key", "indent", "DirectiveLocation", "MapperKind", "getOperationASTFromRequest", "proxyMethodFactory", "getListenersOfAbortSignal", "getAbortPromise", "defaultFieldResolver", "mergeArguments", "source", "CompareVal", "_a", "_b", "defaultFieldResolver"]}