<script lang="ts">
  import {
    Target,
    Clock,
    Shield,
    CheckCircle,
    FileText,
    Award,
    ArrowRight,
    MessageSquare,
    Sparkles,
  } from 'lucide-svelte';

  // Features object with improved wording
  const services = [
    {
      id: 'automation',
      subheader: 'AUTOMATION',
      title: 'Job Applications',
      description:
        'Submit applications to hundreds of positions with our streamlined one-click system. Spend less time on repetitive tasks and more time targeting high-value opportunities.',
      href: '/auto-apply',
      theme: 'blue',
      icon: Target,
      imageText: 'Automated Applications',
      imageLeft: false,
      features: [
        {
          icon: Clock,
          title: 'Reclaim Your Time',
          description:
            'Save hours daily by automating repetitive tasks and focus on interview preparation.',
        },
        {
          icon: Shield,
          title: 'Resume Enhancement',
          description:
            'Receive tailored suggestions to strengthen your resume for specific opportunities.',
        },
      ],
    },
    {
      id: 'tracking',
      subheader: 'COMPREHENSIVE',
      title: 'Application Tracking',
      description:
        'Monitor all your job applications in one intuitive, centralized dashboard. Save time and stay organized. Real-time notifications alert you to new recruiter messages, upcoming deadlines, or expired postings.',
      href: '/job-tracker',
      theme: 'green',
      icon: CheckCircle,
      imageText: 'Application Tracking',
      imageLeft: true,
      features: [
        {
          icon: CheckCircle,
          title: 'Real-time Status Updates',
          description: 'Track your progress through each stage of the hiring process with clarity.',
        },
        {
          icon: Clock,
          title: 'Interview Management',
          description:
            'Organize and prepare for upcoming interviews with smart scheduling and reminders.',
        },
      ],
    },
    {
      id: 'resume',
      subheader: 'PROFESSIONAL',
      title: 'Resume Builder',
      description:
        'Craft standout resumes that capture attention with our intuitive builder. Our builder offers AI-driven prompts to highlight quantifiable achievements, suggest stronger action verbs, and tailor your skills to each role',
      href: '/resume-builder',
      theme: 'purple',
      icon: FileText,
      imageText: 'Resume Builder',
      imageLeft: false,
      features: [
        {
          icon: Target,
          title: 'ATS-Friendly Formatting',
          description:
            'Ensure your resume successfully navigates through automated screening systems.',
        },
        {
          icon: Award,
          title: 'Strategic Skills Showcase',
          description:
            'Automatically highlight relevant qualifications based on target job descriptions.',
        },
      ],
    },
    {
      id: 'ai-assistant',
      subheader: 'AI ASSISTANT',
      title: 'Career Co-Pilot',
      description:
        'Navigate your career journey with AI-powered guidance every step of the way. Identify growth opportunities and next-step roles based on market trends, your skills, and past experience',
      href: '/co-pilot',
      theme: 'orange',
      icon: Sparkles,
      imageText: 'AI Co-Pilot',
      imageLeft: true,
      features: [
        {
          icon: MessageSquare,
          title: 'AI Interview Coach',
          description:
            'Practice with realistic mock interviews tailored to your industry with instant feedback.',
        },
        {
          icon: Sparkles,
          title: 'Personalized Insights',
          description: 'Receive custom career advice based on your skills, experience, and goals.',
        },
      ],
    },
  ];

  const getThemeClasses = (theme: string) => {
    const themes = {
      blue: {
        subheader: 'text-blue-600',
        button: 'bg-blue-600 hover:bg-blue-700',
        iconBg: 'bg-blue-100',
        iconColor: 'text-blue-600',
        imageBg: 'bg-gradient-to-br from-blue-50 to-indigo-100',
        circleColor: 'bg-blue-600',
      },
      green: {
        subheader: 'text-green-600',
        button: 'bg-green-600 hover:bg-green-700',
        iconBg: 'bg-green-100',
        iconColor: 'text-green-600',
        imageBg: 'bg-gradient-to-br from-green-50 to-emerald-100',
        circleColor: 'bg-green-600',
      },
      purple: {
        subheader: 'text-purple-600',
        button: 'bg-purple-600 hover:bg-purple-700',
        iconBg: 'bg-purple-100',
        iconColor: 'text-purple-600',
        imageBg: 'bg-gradient-to-br from-purple-50 to-violet-100',
        circleColor: 'bg-purple-600',
      },
      orange: {
        subheader: 'text-orange-600',
        button: 'bg-orange-600 hover:bg-orange-700',
        iconBg: 'bg-orange-100',
        iconColor: 'text-orange-600',
        imageBg: 'bg-gradient-to-br from-orange-50 to-amber-100',
        circleColor: 'bg-orange-600',
      },
    };
    return themes[theme] || themes.blue;
  };
</script>

<section id="services" class="px-4">
  <div class="relative mx-auto max-w-[90rem] space-y-8">
    {#each services as service}
      {@const themeClasses = getThemeClasses(service.theme)}
      <div class="overflow-hidden rounded-3xl bg-white">
        <div class="grid grid-cols-1 lg:grid-cols-2">
          <!-- Text Content -->
          <div
            class="flex flex-col justify-between space-y-6 p-[5rem]"
            class:lg:order-2={service.imageLeft}>
            <div class="flex flex-col justify-center gap-6">
              <!-- Subheader -->
              <div class="flex flex-col gap-2">
                <p class="text-sm font-medium uppercase tracking-wide {themeClasses.subheader}">
                  {service.subheader}
                </p>

                <!-- Header -->
                <h3 class="text-4xl !font-semibold text-gray-900 md:text-5xl">
                  {service.title}
                </h3>
              </div>

              <!-- Description -->
              <p class="text-lg leading-relaxed text-gray-600">
                {service.description}
              </p>

              <a
                href={service.href}
                class="inline-flex w-[10rem] items-center rounded-lg px-6 py-3 text-white transition-colors {themeClasses.button}">
                Learn More <ArrowRight class="ml-2 h-4 w-4" />
              </a>
            </div>

            <!-- 2-column features -->
            <div class="grid grid-cols-1 space-x-8 divide-x md:grid-cols-2">
              {#each service.features as feature}
                <div class="flex flex-col items-start gap-3 pr-8">
                  <div class="flex-shrink-0 rounded-lg {themeClasses.iconBg} p-2">
                    <svelte:component
                      this={feature.icon}
                      class="h-4 w-4 {themeClasses.iconColor}" />
                  </div>
                  <div>
                    <h4 class="mb-1 text-lg !font-semibold">{feature.title}</h4>
                    <p class="text-primary/60 text-[13px] !font-normal leading-[1.5]">
                      {feature.description}
                    </p>
                  </div>
                </div>
              {/each}
            </div>
          </div>

          <div
            class="relative flex items-center justify-center overflow-hidden {themeClasses.imageBg} my-[4rem] h-[40rem]"
            class:lg:order-1={service.imageLeft}>
            <div class="text-center">
              <div
                class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full {themeClasses.circleColor}">
                <svelte:component this={service.icon} class="h-8 w-8 text-white" />
              </div>
              <p class="font-medium text-gray-700">{service.imageText}</p>
            </div>
          </div>
        </div>
      </div>
    {/each}
  </div>
</section>
