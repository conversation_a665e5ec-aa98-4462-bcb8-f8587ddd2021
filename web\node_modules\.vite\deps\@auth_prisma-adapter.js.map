{"version": 3, "sources": ["../../@auth/prisma-adapter/index.js"], "sourcesContent": ["/**\n * <div style={{display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", padding: 16}}>\n *  Official <a href=\"https://www.prisma.io/docs\">Prisma</a> adapter for Auth.js / NextAuth.js.\n *  <a href=\"https://www.prisma.io/\">\n *   <img style={{display: \"block\"}} src=\"https://authjs.dev/img/adapters/prisma.svg\" width=\"38\" />\n *  </a>\n * </div>\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @prisma/client @auth/prisma-adapter\n * npm install prisma --save-dev\n * ```\n *\n * @module @auth/prisma-adapter\n */\nimport { Prisma } from \"@prisma/client\";\nexport function PrismaAdapter(prisma) {\n    const p = prisma;\n    return {\n        // We need to let <PERSON>rism<PERSON> generate the ID because our default UUID is incompatible with MongoDB\n        createUser: ({ id, ...data }) => p.user.create(stripUndefined(data)),\n        getUser: (id) => p.user.findUnique({ where: { id } }),\n        getUserByEmail: (email) => p.user.findUnique({ where: { email } }),\n        async getUserByAccount(provider_providerAccountId) {\n            const account = await p.account.findUnique({\n                where: { provider_providerAccountId },\n                include: { user: true },\n            });\n            return account?.user ?? null;\n        },\n        updateUser: ({ id, ...data }) => p.user.update({\n            where: { id },\n            ...stripUndefined(data),\n        }),\n        deleteUser: (id) => p.user.delete({ where: { id } }),\n        linkAccount: (data) => p.account.create({ data }),\n        unlinkAccount: (provider_providerAccountId) => p.account.delete({\n            where: { provider_providerAccountId },\n        }),\n        async getSessionAndUser(sessionToken) {\n            const userAndSession = await p.session.findUnique({\n                where: { sessionToken },\n                include: { user: true },\n            });\n            if (!userAndSession)\n                return null;\n            const { user, ...session } = userAndSession;\n            return { user, session };\n        },\n        createSession: (data) => p.session.create(stripUndefined(data)),\n        updateSession: (data) => p.session.update({\n            where: { sessionToken: data.sessionToken },\n            ...stripUndefined(data),\n        }),\n        deleteSession: (sessionToken) => p.session.delete({ where: { sessionToken } }),\n        async createVerificationToken(data) {\n            const verificationToken = await p.verificationToken.create(stripUndefined(data));\n            if (\"id\" in verificationToken && verificationToken.id)\n                delete verificationToken.id;\n            return verificationToken;\n        },\n        async useVerificationToken(identifier_token) {\n            try {\n                const verificationToken = await p.verificationToken.delete({\n                    where: { identifier_token },\n                });\n                if (\"id\" in verificationToken && verificationToken.id)\n                    delete verificationToken.id;\n                return verificationToken;\n            }\n            catch (error) {\n                // If token already used/deleted, just return null\n                // https://www.prisma.io/docs/reference/api-reference/error-reference#p2025\n                if (error instanceof Prisma.PrismaClientKnownRequestError &&\n                    error.code === \"P2025\")\n                    return null;\n                throw error;\n            }\n        },\n        async getAccount(providerAccountId, provider) {\n            return p.account.findFirst({\n                where: { providerAccountId, provider },\n            });\n        },\n        async createAuthenticator(data) {\n            return p.authenticator.create(stripUndefined(data));\n        },\n        async getAuthenticator(credentialID) {\n            return p.authenticator.findUnique({\n                where: { credentialID },\n            });\n        },\n        async listAuthenticatorsByUserId(userId) {\n            return p.authenticator.findMany({\n                where: { userId },\n            });\n        },\n        async updateAuthenticatorCounter(credentialID, counter) {\n            return p.authenticator.update({\n                where: { credentialID },\n                data: { counter },\n            });\n        },\n    };\n}\n/** @see https://www.prisma.io/docs/orm/prisma-client/special-fields-and-types/null-and-undefined */\nfunction stripUndefined(obj) {\n    const data = {};\n    for (const key in obj)\n        if (obj[key] !== undefined)\n            data[key] = obj[key];\n    return { data };\n}\n"], "mappings": ";;;;;;;;AAiBA,oBAAuB;AAChB,SAAS,cAAc,QAAQ;AAClC,QAAM,IAAI;AACV,SAAO;AAAA;AAAA,IAEH,YAAY,CAAC,EAAE,IAAI,GAAG,KAAK,MAAM,EAAE,KAAK,OAAO,eAAe,IAAI,CAAC;AAAA,IACnE,SAAS,CAAC,OAAO,EAAE,KAAK,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;AAAA,IACpD,gBAAgB,CAAC,UAAU,EAAE,KAAK,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AAAA,IACjE,MAAM,iBAAiB,4BAA4B;AAC/C,YAAM,UAAU,MAAM,EAAE,QAAQ,WAAW;AAAA,QACvC,OAAO,EAAE,2BAA2B;AAAA,QACpC,SAAS,EAAE,MAAM,KAAK;AAAA,MAC1B,CAAC;AACD,cAAO,mCAAS,SAAQ;AAAA,IAC5B;AAAA,IACA,YAAY,CAAC,EAAE,IAAI,GAAG,KAAK,MAAM,EAAE,KAAK,OAAO;AAAA,MAC3C,OAAO,EAAE,GAAG;AAAA,MACZ,GAAG,eAAe,IAAI;AAAA,IAC1B,CAAC;AAAA,IACD,YAAY,CAAC,OAAO,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;AAAA,IACnD,aAAa,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE,KAAK,CAAC;AAAA,IAChD,eAAe,CAAC,+BAA+B,EAAE,QAAQ,OAAO;AAAA,MAC5D,OAAO,EAAE,2BAA2B;AAAA,IACxC,CAAC;AAAA,IACD,MAAM,kBAAkB,cAAc;AAClC,YAAM,iBAAiB,MAAM,EAAE,QAAQ,WAAW;AAAA,QAC9C,OAAO,EAAE,aAAa;AAAA,QACtB,SAAS,EAAE,MAAM,KAAK;AAAA,MAC1B,CAAC;AACD,UAAI,CAAC;AACD,eAAO;AACX,YAAM,EAAE,MAAM,GAAG,QAAQ,IAAI;AAC7B,aAAO,EAAE,MAAM,QAAQ;AAAA,IAC3B;AAAA,IACA,eAAe,CAAC,SAAS,EAAE,QAAQ,OAAO,eAAe,IAAI,CAAC;AAAA,IAC9D,eAAe,CAAC,SAAS,EAAE,QAAQ,OAAO;AAAA,MACtC,OAAO,EAAE,cAAc,KAAK,aAAa;AAAA,MACzC,GAAG,eAAe,IAAI;AAAA,IAC1B,CAAC;AAAA,IACD,eAAe,CAAC,iBAAiB,EAAE,QAAQ,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;AAAA,IAC7E,MAAM,wBAAwB,MAAM;AAChC,YAAM,oBAAoB,MAAM,EAAE,kBAAkB,OAAO,eAAe,IAAI,CAAC;AAC/E,UAAI,QAAQ,qBAAqB,kBAAkB;AAC/C,eAAO,kBAAkB;AAC7B,aAAO;AAAA,IACX;AAAA,IACA,MAAM,qBAAqB,kBAAkB;AACzC,UAAI;AACA,cAAM,oBAAoB,MAAM,EAAE,kBAAkB,OAAO;AAAA,UACvD,OAAO,EAAE,iBAAiB;AAAA,QAC9B,CAAC;AACD,YAAI,QAAQ,qBAAqB,kBAAkB;AAC/C,iBAAO,kBAAkB;AAC7B,eAAO;AAAA,MACX,SACO,OAAO;AAGV,YAAI,iBAAiB,qBAAO,iCACxB,MAAM,SAAS;AACf,iBAAO;AACX,cAAM;AAAA,MACV;AAAA,IACJ;AAAA,IACA,MAAM,WAAW,mBAAmB,UAAU;AAC1C,aAAO,EAAE,QAAQ,UAAU;AAAA,QACvB,OAAO,EAAE,mBAAmB,SAAS;AAAA,MACzC,CAAC;AAAA,IACL;AAAA,IACA,MAAM,oBAAoB,MAAM;AAC5B,aAAO,EAAE,cAAc,OAAO,eAAe,IAAI,CAAC;AAAA,IACtD;AAAA,IACA,MAAM,iBAAiB,cAAc;AACjC,aAAO,EAAE,cAAc,WAAW;AAAA,QAC9B,OAAO,EAAE,aAAa;AAAA,MAC1B,CAAC;AAAA,IACL;AAAA,IACA,MAAM,2BAA2B,QAAQ;AACrC,aAAO,EAAE,cAAc,SAAS;AAAA,QAC5B,OAAO,EAAE,OAAO;AAAA,MACpB,CAAC;AAAA,IACL;AAAA,IACA,MAAM,2BAA2B,cAAc,SAAS;AACpD,aAAO,EAAE,cAAc,OAAO;AAAA,QAC1B,OAAO,EAAE,aAAa;AAAA,QACtB,MAAM,EAAE,QAAQ;AAAA,MACpB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAEA,SAAS,eAAe,KAAK;AACzB,QAAM,OAAO,CAAC;AACd,aAAW,OAAO;AACd,QAAI,IAAI,GAAG,MAAM;AACb,WAAK,GAAG,IAAI,IAAI,GAAG;AAC3B,SAAO,EAAE,KAAK;AAClB;", "names": []}