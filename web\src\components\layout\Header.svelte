<script lang="ts">
  import Logo from '$components/ui/Logo.svelte';
  import { Button } from '$lib/components/ui/button';
  import { ArrowRightIcon, Zap, Target, FileText, Bot, TrendingUp } from 'lucide-svelte';
  import * as NavigationMenu from '$lib/components/ui/navigation-menu';
  import { navigationMenuTriggerStyle } from '$lib/components/ui/navigation-menu/navigation-menu-trigger.svelte';
  import { cn } from '$lib/utils';
  import { graphqlRequest, COLLECTIONS_QUERY } from '$lib/graphql/client';
  import type { Collection } from '$lib/graphql/types';
  import { getStores } from '$app/stores';
  import { browser } from '$app/environment';
  import { goto } from '$app/navigation';

  const { currentUser = null, data = {} } = $props();

  let jobCollections = $state<Collection[]>([]);
  let topCollections = $state<Collection[]>([]);
  let isAtTop = $state(true);
  let scrollY = $state(0);

  // Get page store safely for browser environment
  let pageStore = browser ? getStores().page : null;

  // Products dropdown links with icons
  const productLinks = [
    {
      title: 'Auto Apply',
      href: '/auto-apply',
      description: 'Automatic application processing',
      icon: Zap,
    },
    {
      title: 'Job Tracker',
      href: '/job-tracker',
      description: 'Track your applications and interviews',
      icon: Target,
    },
    {
      title: 'Resume Builder',
      href: '/resume-builder',
      description: 'Resume builder with AI/ATS assistance',
      icon: FileText,
    },
    {
      title: 'AI Co-Pilot',
      href: '/co-pilot',
      description: 'Browser extension for job search',
      icon: Bot,
    },
    {
      title: 'Matches',
      href: '/matches',
      description: 'Personalized automated job matches',
      icon: TrendingUp,
    },
  ];

  // News sections for the right side
  const newsItems = [
    {
      title: 'Resume is now compatible with Figma Sites',
      date: 'May 21, 2025',
      description: 'Build better resumes with our new Figma integration',
      image: '/news/figma-integration.jpg',
    },
  ];

  // Resources dropdown links
  const resourceLinks = [
    { title: 'Free Tools', href: '/resources' },
    {
      title: 'Resume Templates',
      href: '/resources/resume-templates',
    },
    {
      title: 'Cover Letter Templates',
      href: '/resources/cover-letters',
    },
    {
      title: 'ATS Resume Checker',
      href: '/resources/ats-optimization/checker',
    },
    {
      title: 'Interview Questions',
      href: '/resources/interview-prep/question-database',
    },
    {
      title: 'Salary Tools',
      href: '/resources/salary-tools',
    },
  ];

  // Load job collections
  async function loadJobCollections() {
    try {
      // Try to use collections from data prop first
      if (data?.jobCollections && Array.isArray(data.jobCollections)) {
        jobCollections = data.jobCollections;
      } else {
        // Fallback to GraphQL request
        const result = await graphqlRequest<{ collections: Collection[] }>(COLLECTIONS_QUERY);
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }
        jobCollections = result.data?.collections || [];
      }

      // Get top 10 collections (alphabetically sorted)
      topCollections = jobCollections.slice(0, 10);
    } catch (error) {
      console.error('Error loading job collections for header:', error);
    }
  }

  // Load collections on component initialization
  $effect(() => {
    loadJobCollections();
  });

  // Function to determine if a nav item is active
  function isActive(href: string, exact = false) {
    if (!browser || !pageStore || !$pageStore) return false;
    if (exact) {
      return $pageStore.url.pathname === href;
    }
    return $pageStore.url.pathname.includes(href);
  }

  // Function to check if a collection is active
  function isCollectionActive(collectionSlug: string) {
    if (!browser || !pageStore || !$pageStore) return false;
    return isActive('/jobs') && $pageStore.url.searchParams.get('collection') === collectionSlug;
  }

  // Scroll to top function
  function scrollToTop() {
    if (browser) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }

  // Handle launch button click
  function handleLaunchClick() {
    scrollToTop();
    // Small delay to allow scroll to start before navigation
    setTimeout(() => {
      goto('/dashboard');
    }, 100);
  }

  // Handle scroll events with proper Svelte 5 reactivity
  function handleScroll() {
    if (!browser) return;
    scrollY = window.scrollY;
    isAtTop = scrollY === 0;
  }

  // Set up scroll event listener with proper cleanup
  $effect(() => {
    if (!browser) return;

    // Initial check in case the page loads scrolled down
    handleScroll();

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Return cleanup function
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  });
</script>

<div
  class="top-4.5 fixed left-10 z-50 flex transform items-center transition-transform duration-500 ease-in-out
    {isAtTop ? 'translate-y-9' : 'translate-y-0'}">
  <button
    class="rounded-sm bg-gradient-to-r from-orange-500 to-purple-600 p-0.5 {isAtTop
      ? ''
      : 'transition-200 cursor-pointer transition-transform hover:scale-105'}"
    aria-label="Scroll to top"
    onclick={isAtTop ? undefined : scrollToTop}>
    <Logo fill="white" stroke="black" class="h-6 w-6" />
  </button>
</div>

<div
  class="z-100 fixed right-10 top-3 transform transition-transform duration-500 ease-in-out
    {isAtTop ? 'translate-y-9' : 'translate-y-1'}">
  {#if currentUser}
    <Button
      variant="default"
      size="lg"
      onclick={handleLaunchClick}
      class=" bg-purple-500 hover:bg-purple-600">
      Launch <ArrowRightIcon class="h-4 w-4" />
    </Button>
  {:else}
    <Button variant="default" size="lg" href="/auth/sign-up">Start for free</Button>
  {/if}
</div>

<div class="text-secondary font-lighter w-full bg-green-300 p-2 text-center text-xs">
  We're currently in private beta. Apply for early access today!
</div>
<header class="relative z-50 mt-1">
  <div class="mx-auto px-4 sm:px-6 lg:px-8">
    <div
      class="flex grid h-16 auto-cols-fr auto-rows-auto grid-cols-[0.5fr_1fr_0.5fr] items-center justify-between gap-0">
      <!-- Logo -->
      <a href="/" class="text-primary/90 absolute ml-11 text-xl font-bold">Hirli</a>
      <div></div>
      <NavigationMenu.Root class="m-auto">
        <NavigationMenu.List class="gap-1">
          <!-- Products Megamenu -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger
              class="font-roboto text-foreground/80 bg-transparent text-[15px]"
              >Products</NavigationMenu.Trigger>
            <NavigationMenu.Content class="-left-14">
              <div class="grid w-[600px] grid-cols-2 gap-4 p-3">
                <ul class="space-y-2 p-1">
                  {#each productLinks as product}
                    {@const Icon = product.icon}
                    <li>
                      <NavigationMenu.Link
                        href={product.href}
                        class={cn(
                          'flex flex-row items-center gap-3 rounded-md',
                          isActive(product.href) ? 'bg-accent text-accent-foreground' : ''
                        )}>
                        <div
                          class="rounded-lg bg-gradient-to-r from-orange-500 to-purple-600 p-0.5">
                          <div
                            class="border-primary bg-secondary flex items-center justify-center rounded-md border-2 p-1.5">
                            <Icon class="text-primary h-10 w-10" />
                          </div>
                        </div>
                        <div class="flex flex-1 flex-col align-middle">
                          <div class="text-primary/90 text-sm font-medium">{product.title}</div>
                          <p class="text-primary/90 text-[11px]">{product.description}</p>
                        </div>
                      </NavigationMenu.Link>
                    </li>
                  {/each}
                </ul>

                <div class="bg-secondary rounded-md p-4">
                  <div class="mb-4 flex items-center justify-between">
                    <h3 class="font-lighter text-primary text-sm">What's New</h3>
                    <a href="/blog" class="text-primary/80 text-xs">View all</a>
                  </div>
                  <ul class="space-y-2">
                    {#each newsItems as news}
                      <li>
                        <NavigationMenu.Link
                          href="/news/{news.title.toLowerCase().replace(/\s+/g, '-')}"
                          class="flex flex-col gap-2 transition-colors">
                          <div
                            class="border-border h-20 w-full rounded-md border bg-gradient-to-br from-blue-100 to-purple-100">
                          </div>
                          <div class="flex flex-col gap-0">
                            <div class="text-primary mb-1 text-[10px]">{news.date}</div>
                            <div class=" font-lighter text-primary text-sm">{news.title}</div>
                          </div>
                        </NavigationMenu.Link>
                      </li>
                    {/each}
                  </ul>
                </div>
              </div>
            </NavigationMenu.Content>
          </NavigationMenu.Item>

          <!-- Categories Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Trigger
              class="font-roboto text-foreground/80 bg-transparent text-[15px]"
              >Sectors</NavigationMenu.Trigger>
            <NavigationMenu.Content class="w-[200px]">
              <ul class="grid gap-0 p-2">
                {#each topCollections as collection}
                  <li>
                    <NavigationMenu.Link
                      href="/jobs?collection={collection.slug}"
                      class={cn(
                        'hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 leading-none no-underline outline-none transition-colors',
                        isCollectionActive(collection.slug)
                          ? 'bg-accent text-accent-foreground'
                          : ''
                      )}>
                      {collection.name}
                    </NavigationMenu.Link>
                  </li>
                {/each}
                <li>
                  <NavigationMenu.Link
                    href="/jobs"
                    class={cn(
                      'hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 leading-none no-underline outline-none transition-colors',
                      isActive('/jobs', true) ? 'bg-accent text-accent-foreground' : ''
                    )}>
                    Browse All Jobs
                  </NavigationMenu.Link>
                </li>
              </ul>
            </NavigationMenu.Content>
          </NavigationMenu.Item>
          <NavigationMenu.Item>
            <NavigationMenu.Link
              href="/pricing"
              class={cn(
                navigationMenuTriggerStyle(),
                isActive('/pricing')
                  ? 'bg-accent text-accent-foreground'
                  : 'font-roboto text-foreground/80 bg-transparent text-[15px]'
              )}>
              Pricing
            </NavigationMenu.Link>
          </NavigationMenu.Item>
          <!-- Resources Dropdown -->
          <NavigationMenu.Item>
            <NavigationMenu.Link
              href="/resources"
              class={cn(
                navigationMenuTriggerStyle(),
                isActive('/resources')
                  ? 'bg-accent text-accent-foreground'
                  : 'font-roboto text-foreground/80 bg-transparent text-[15px]'
              )}>
              Learn
            </NavigationMenu.Link>
          </NavigationMenu.Item>
          <NavigationMenu.Item>
            <NavigationMenu.Link
              href="/recruiters"
              class={cn(
                navigationMenuTriggerStyle(),
                isActive('/recruiters')
                  ? 'bg-accent text-accent-foreground'
                  : 'font-roboto text-foreground/80 bg-transparent text-[15px]'
              )}>
              Recruiters
            </NavigationMenu.Link>
          </NavigationMenu.Item>
          <NavigationMenu.Item>
            <NavigationMenu.Link
              href="/employers"
              class={cn(
                navigationMenuTriggerStyle(),
                isActive('/employers')
                  ? 'bg-accent text-accent-foreground'
                  : 'font-roboto text-foreground/80 bg-transparent text-[15px]'
              )}>
              Employers
            </NavigationMenu.Link>
          </NavigationMenu.Item>
        </NavigationMenu.List>
      </NavigationMenu.Root>

      {#if !currentUser}
        <Button variant="ghost" size="lg" href="/auth/sign-in" class="mr-16">Log in</Button>
      {/if}
    </div>
  </div>
</header>
